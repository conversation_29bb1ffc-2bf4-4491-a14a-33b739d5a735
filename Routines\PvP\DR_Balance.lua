--[[
Balance Druid PvP Rotation
PLACEHOLDER FOR NOW
]]--
function P_102(...)
    ---@class MainAddon
    local MainAddon = MainAddon
    local M = MainAddon
    local HL = HeroLibEx
    ---@class HealingEngine
    local HealingEngine = MainAddon.HealingEngine
    ---@class Unit
    local Unit = HL.Unit
    ---@class Unit
    local Player = Unit.Player
    ---@class Unit
    local Target = Unit.Target
    ---@class Unit
    local MouseOver = Unit.MouseOver
    ---@class Unit
    local Focus = Unit.Focus
    ---@class Spell
    local Spell = HL.Spell
    ---@class Item
    local Item = HL.Item
    -- HeroRotation
    local Cast = M.Cast
    local CastAlly = M.CastAlly
    local CastCycleAlly = MainAddon.CastCycleAlly
    local CastPooling = M.CastPooling
    local CastTargetIf = M.CastTargetIf
    local CastCycle = M.CastCycle
    local AoEON = M.AoEON
    local num = M.num
    -- LUA
    local C_Timer = _G['C_Timer']
    local GetTime = _G['GetTime']
    local GetMouseFoci = _G['GetMouseFoci']
    local pairs = pairs
    local math = math
    local mathfloor   = math['floor']
    local mathmax     = math['max']
    local mathmin     = math['min']
    local Delay       = C_Timer.After

    local S = Spell.Druid.Feral
    local I = Item.Druid.Feral
    
        ---GUI SETTINGS
    local GetSetting = MainAddon.Config.GetClassSetting
    local Config_Key = MainAddon.GetClassVariableName()
    local Config_Color = 'FF7C0A'
    local Config_Table = {
        key = Config_Key,
        title = 'Druid - Feral',
        subtitle = '?? ' .. MainAddon.Version,
        width = 600,
        height = 700,
        profiles = true,
        config = {
            { type = 'header', text = '', size = 24, align = 'Center', color = Config_Color },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },

            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
        }
    }
    Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
    Config_Table.config = MainAddon.BuildDPSTrinketUI(Config_Table.config, Config_Color)
    Config_Table.config = MainAddon.BuildCombatPotionUI(Config_Table.config, "Feral", Config_Color)
    M.SetConfig(102, Config_Table)

    local function Main()
    end

    local function OnInit()
        MainAddon:Print('PvP Routine loaded!')
    end
    M.SetAPL(102, Main, OnInit)
end