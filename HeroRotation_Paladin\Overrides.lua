--- ============================ HEADER ============================
-- HeroLib
local HL      = HeroLib
local Cache   = HeroCache
local Unit    = HL.Unit
local Player  = Unit.Player
local Pet     = Unit.Pet
local Target  = Unit.Target
local Spell   = HL.Spell
local Item    = HL.Item
-- HeroRotation
local HR      = HeroRotation
-- Spells
local SpellProt = Spell.Paladin.Protection
local SpellRet  = Spell.Paladin.Retribution
-- Lua

--- ============================ CONTENT ============================
-- Protection, ID: 66
local ProtPalBuffUp
ProtPalBuffUp = HL.AddCoreOverride("Player.BuffUp",
  function(self, Spell, AnyCaster, BypassRecovery)
    local BaseCheck = ProtPalBuffUp(self, Spell, AnyCaster, BypassRecovery)
    if Spell == SpellProt.AvengingWrathBuff and SpellProt.Sentinel:IsAvailable() then
      return Player:BuffUp(SpellProt.SentinelBuff)
    else
      return BaseCheck
    end
  end
, 66)

local ProtPalBuffRemains
ProtPalBuffRemains = HL.AddCoreOverride("Player.BuffRemains",
  function(self, Spell, AnyCaster, BypassRecovery)
    local BaseCheck = ProtPalBuffRemains(self, Spell, AnyCaster, BypassRecovery)
    if Spell == SpellProt.AvengingWrathBuff and SpellProt.Sentinel:IsAvailable() then
      return Player:BuffRemains(SpellProt.SentinelBuff)
    else
      return BaseCheck
    end
  end
, 66)

local ProtPalCDRemains
ProtPalCDRemains = HL.AddCoreOverride("Spell.CooldownRemains",
  function(self, BypassRecovery)
    local BaseCheck = ProtPalCDRemains(self, BypassRecovery)
    if self == SpellProt.AvengingWrath and SpellProt.Sentinel:IsAvailable() then
      return SpellProt.Sentinel:CooldownRemains()
    else
      return BaseCheck
    end
  end
, 66)

local ProtPalIsAvail
ProtPalIsAvail = HL.AddCoreOverride("Spell.IsAvailable",
  function(self, CheckPet)
    local BaseCheck = ProtPalIsAvail(self, CheckPet)
    if self == SpellProt.AvengingWrath and SpellProt.Sentinel:IsAvailable() then
      return SpellProt.Sentinel:IsAvailable()
    else
      return BaseCheck
    end
  end
, 66)

local ProtPalIsCastable
ProtPalIsCastable = HL.AddCoreOverride("Spell.IsCastable",
  function (self, BypassRecovery, Range, AoESpell, ThisUnit, Offset)
    local BaseCheck = ProtPalIsCastable(self, BypassRecovery, Range, AoESpell, ThisUnit, Offset)
    if self == SpellProt.RiteofAdjuration then
      return BaseCheck and Player:BuffDown(SpellProt.RiteofAdjurationBuff)
    elseif self == SpellProt.RiteofSanctification then
      return BaseCheck and Player:BuffDown(SpellProt.RiteofSanctificationBuff)
    else
      return BaseCheck
    end
  end
, 66)

HL.AddCoreOverride("Player.JudgmentPower",
  function(self)
    local JP = 1
    if Player:BuffUp(SpellProt.AvengingWrathBuff) or Player:BuffUp(SpellProt.SentinelBuff) then
      JP = JP + 1
    end
    if Player:BuffUp(SpellProt.BastionofLightBuff) then
      JP = JP + 2
    end
    return JP
  end
, 66)

-- Retribution, ID: 70
HL.AddCoreOverride("Player.JudgmentPower",
  function(self)
    local JP = 1
    if Player:BuffUp(SpellRet.AvengingWrathBuff) then
      JP = JP + 1
    end
    if Player:BuffUp(SpellRet.BastionofLightBuff) then
      JP = JP + 2
    end
    return JP
  end
, 70)

-- Example (Arcane Mage)
-- HL.AddCoreOverride ("Spell.IsCastableP",
-- function (self, Range, AoESpell, ThisUnit, BypassRecovery, Offset)
--   if Range then
--     local RangeUnit = ThisUnit or Target;
--     return self:IsLearned() and self:CooldownRemainsP( BypassRecovery, Offset or "Auto") == 0 and RangeUnit:IsInRange( Range, AoESpell );
--   elseif self == SpellArcane.MarkofAluneth then
--     return self:IsLearned() and self:CooldownRemainsP( BypassRecovery, Offset or "Auto") == 0 and not Player:IsCasting(self);
--   else
--     return self:IsLearned() and self:CooldownRemainsP( BypassRecovery, Offset or "Auto") == 0;
--   end;
-- end
-- , 62);