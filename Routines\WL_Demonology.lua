function A_266()
    -- HR UPDATE: fix(Demonology): Minor tweak to GreaterDreadstalkerTime() 09.06.2025
    -- REMEMBER: CastMagic(S.Guillotine, nil, "386833-Magic", magicgroundspell_guillotine)
    -- REMEMBER: CastMagic(<PERSON><PERSON>, nil, "267171-Magic", magicgroundspell_bombers)
    ---@class MainAddon
    local MainAddon = MainAddon
    ---@class MainAddon
    local M = MainAddon
    -- HeroLib
    local HL = HeroLibEx
    ---@class Unit
    local Unit = HL.Unit
    ---@class Unit
    local Player = Unit.Player
    ---@class Unit
    local Target = Unit.Target
    ---@class Unit
    local MouseOver = Unit.MouseOver
    ---@class Unit
    local Pet = Unit.Pet
    ---@class Spell
    local Spell = HL.Spell
    ---@class Item
    local Item = HL.Item
    ---@class Action
    local Action = HL.Action
    -- HeroRotation
    local Cast = M.Cast
    local CastMagic = M.CastMagic
    local CastCycle = M.CastCycle
    local CastTargetIf = M.CastTargetIf
    local AoEON = M.AoEON
    local CDsON = M.CDsON
    local Warlock = M.Warlock
    -- Lua
    local mathmin    = math.min
    local mathmax    = math.max
    local floor   = math.floor
    local GetTime = _G['GetTime']
    local IsFalling = _G['IsFalling']
    local C_Timer = _G['C_Timer']
    local GetMouseFoci = _G['GetMouseFoci']
    local StaticPopupDialogs = _G['StaticPopupDialogs']
    local StaticPopup_Show = _G.StaticPopup_Show
    local pairs = pairs
    local num = M.num
    local Delay = C_Timer.After
    
    -- Define S/I for spell and item arrays
    local S = Spell.Warlock.Demonology
    local I = Item.Warlock.Demonology

    -- Create table to exclude above trinkets from On Use function
    local OnUseExcludes = {
        -- DF Trinkets
        I.MirrorofFracturedTomorrows:ID(),
        -- TWW Trinkets
        I.ImperfectAscendancySerum:ID(),
        I.SpymastersWeb:ID()
      }

    -- Toggle Setting
    MainAddon.Toggle.Special["HoldingBurst"] = {
        Icon = MainAddon.GetTexture(S.Implosion),
        Name = "Holding Burst",
        Description = "Holding Burst.",
        Spec = 266
    }

    ---GUI SETTINGS
    local GetSetting = MainAddon.Config.GetClassSetting
    local Config_Key = MainAddon.GetClassVariableName()
    local Config_Color = '8788EE'
    local Config_Table = {
    key = Config_Key,
    title = 'Warlock - Demonology',
    subtitle = '?? ' .. MainAddon.Version,
    width = 600,
    height = 700,
    profiles = true,
    config = {
            { type = 'header', text = '', size = 24, align = 'Center', color = Config_Color },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
            { type = 'header', text = 'DPS', color = Config_Color },
            { type = 'dropdown',
                text = 'Enter combat with...', key = 'OpeningSpell',
                list = {
                    { text = 'Demonbolt', key = 'OP_Demonbolt' },
                    { text = 'Shadow Bolt', key = 'OP_ShadowBolt' },
                },
                default = 'OP_ShadowBolt',
            },
            { type = 'checkbox', text = ' Force sync Vilefiend and Tyrant', key = 'ForceTyrantVilefiendSync', default = false },
            { type = 'spacer' },
            { type = 'header', text = 'Defensives', color = Config_Color },
            { type = 'checkspin', text = ' Unending Resolve', key = 'UnendingResolve', icon = S.UnendingResolve:ID(), min = 1, max = 99, default_spin = 30, default_check = true },
            { type = 'checkspin', text = 'Dark Pact', key = 'dpact', min = 1, max = 99, default_spin = 50, default_check = true },
            { type = 'checkspin', text = ' Mortal Coil (Healing)', key = 'MortalCoil', icon = S.MortalCoil:ID(), min = 1, max = 99, default_spin = 40, default_check = false },
            { type = 'spacer' },
            { type = 'header', text = 'Utilities',  color = Config_Color },
            { type = 'dropdown',
                text = 'Fel Domination', key = 'fel_domination',
                multiselect = true,
                list = {
                    { text = 'In Combat', key = 1 },
                    { text = 'Out of Combat', key = 2 },
                },
                default = {
                    1, 2
                },
            },
            { type = 'checkbox', text = ' Auto Cancel Burning Rush when no movement', icon = S.BurningRush:ID(), key = 'auto_cancel_br', default = true },
            { type = 'dropdown',
                text = ' Soulstone', key = 'autorebirth',
                multiselect = true,
                icon = S.Soulstone:ID(),
                list = {
                    { text = 'Target', key = 'autorebirth_target' },
                    { text = 'MouseOver', key = 'autorebirth_mouseover' },
                },
                default = {
                },
            },
            { type = 'spacer' },
            {
                type = "checkbox",
                text = "Cast Groundspell only when MouseOver enemies or tank.",
                key = "MOOption",
                default = false
            },
            { type = 'checkbox', text = ' Magic Groundspell - Bilescourge Bombers', icon = S.BilescourgeBombers:ID(), key = 'magicgroundspell_bombers', default = false },
            { type = 'checkbox', text = ' Magic Groundspell - Guillotine', icon = S.Guillotine:ID(), key = 'magicgroundspell_guillotine', default = false },
            { type = 'spacer' },
            { type = 'header', text = 'WeakAuras', color = Config_Color },
            { type = 'button', text = 'Import Enemies Count', width = 150, callback = function()
                if not _G['WeakAuras'] then
                    return false
                end
                _G['WeakAuras'].Import("!WA:2!9z1tVTrUv84ySiWc7HuTlCd2MfywxKaBSic2EB2doihKShhNwLyJrs15pBQeNHpPHXJiji5yBLBrOh6P9GVS3f2JTaf6JWEONhy0pb5q)aKpb9roJTLDghDqd5794p(47)ZTD1HvPvP)JNmHfj4TePQi4U3qD8U97RbZCQrflMs4rXc1Ecg3eUP)ZB7hi9OmTmHmQnCSPBFHAiX0v21Wgcf7MRHf0EY)yjcAaTzivzguqObxWHpEaaY6AjezcigMiwE3soA0AfRYwycf0etQIyG4g9fCZ0NWsuIrEnej0gjcc9)CsQg6YHdbvCOHKaCZIbdttmSkYQwwrjeTUlHt7AV3En0S3blMvTYjNZS3hUSm3puZ4dsGF92zvx8xVDpNKKe2HqVX2Lf4yLm73)fiutCqjggsm9cCyHmu7xpO5UB(xqmc3PJ1G2t9o0g8uQ(MNQJjuXrVi34p)efmGj4ThjHgg0qixQeRYA1mx22)oHy4CtmIi8PRXJFN5(x)7asImMm)6Pm601LQ1Fj9hgLCWOj9zhd09zut8F)lLFBPOZp3ORePMeghu72PDZN(C)R5aNTmWQv0GirIq9NNh)njkvBedTsl)U(P8i0tZxELkE4pfGEto(HKux4xb40cBXlZTf39gNgjqFR4iERJysON82xGwhjfdecqFn38bsksfdIICpRg1XTJnk2GbO14NVVQy5)RHbTQH5ymn)txhjTbdS0NrkMrHSAns5mtO9PcQShgUdQLM4pOtdDx5EkaTJbT2ZVzZXO7pjb9LvkETpNmeo(MSR8Cz994cJ3ZimEDkvW9mXa3XzgJXs5SScAJOb6sojqBJ7BIiIKmdep2RjlSLjnC5LQhboAp4hQT6sRSXtaJB7YVQR7RvNwzgm2PjE4DaLabW)4zyu3PZwMnRLVEgMDqBsol7QzySNZoHSS0RLVBg2TSwO8d6wollKSJwT9jkK0b12cgk4ye0GroXM9waZMja5qOEilHzgHhD5CDS22moTXihqpDRLBvB)yg670XBG7wzfpmF27cIR4juECwYf4JoNpf8hBLPC30lfPECaOEgHhcze4Dg2EllbJhjhd8I4EJWkTEKCZAirv7YU0ceZnzw3MphgYa9t5Uht(ZlGWhalFvnCfBotLkvYdj1YVYfo2TZEBvVTF3oTQ3OPF2JKELN3zuPqoabrXq0bHPUuQZdXBL2hdXN2DZ6TA3Tv76bTd4yqegLtHqKNTeLAh)M7TDNMNKYlsYwm7r3CkwJ4TU78qyDcFKSArEBUinfdyrYhCUwvCYROCoAVE938ipRsk)DehCfq8mbf(N3q(hkTRurTRAFe7Fbkoj5VMxu8xoHWz2Yec(pgGP8kZ2zBKTWVben0YOa(at8ToLA7WGI4QmO0a2vJQhBLX(GZwOXqm7ZDWSxFRSFk7nz)TSfc7Z4mD8vjxA3WI2MsfeXSQ18Y795kXFHCtqptGR7G(BwmBJtXTHKOdgG1O50WyGni28(PZG1s3Jp(SkOXYVVSoZ53bDe6zzrDnXkqhJhy0yBd2wy7XhhQDdkeYgkfktPM8lAxiFqPpf51DnLQuxV037ZH(mg0BNpbZ2kmE19N8B2hihuhDT6nwD1UDm5z4ocxN5)kdTGpUVRebl6EDUqbhzBb9(tXwsOgSR0gkPRuQzZCEKQ8RpZnz5VfttctaAC5dt9jV2h85cF(uR4VD2D5thaXQ8ct6Fm7vvAWSf1Z6vr9wmFL1Few)y72xZeixE6VLyu1w(pB3URU2jAiPF(WJn2TD7DF20(wxaMHrmK5)4mUgBcvyRnd89FEPdyE5HCgFKqr3xrKJ3VyHIXTZ7aXxZujxukyA(GfB6MlzU5MdNlbtRz5oNPmEUC4U7pfxgb(UcQ9K3HnGluqHB0r1xPekDVqeG(SbvQQdFyT1EyT)u1d)VV4))")
            end },
            { type = 'button', text = 'Import Holding Burst Tracker', width = 150, callback = function()
                if not _G['WeakAuras'] then
                    return false
                end
                _G['WeakAuras'].Import("!WA:2!1r1xVnorq8lksCcl(tVGufc4KSks9p6URuDN4GRshO4GBZb5pfNKRfEXzT3X2l1zxRDx32WBeHq8C)ieb8epfXNaEGNTI4daQFeUpbmEDO6oGZp4DMzND2D(n)MP2bnM0G2G(JBjf56ughK9hnSZt65UKKRteY(zAMGRSKt7hfPaDTfeEiA)ibJRdA52BORxMnLPYsjthcxO9JeYjeTFMVMnbwPvlBTWCLwmP0JrzuIg8GZaU(k8weOdSWJzuDIttuvscn35d9uAIuB5W4mTvqeUOsSMPLS4yqQEJnLRe)dh90miO6gwuT4BmHbqNRkE1G2ajvNCLkpWCThjHi2fEdoYTtNRnoipcnUWVvZbd9hmSP3WzQmin9juLvLhQSB1Eu3M98)YrUJC9DFkM(ECYeqz5fMaHNgKBYnNC8fhuIiGS4TY(ROCUjLS3EhlB8lvessT7sy8MukA(X2Dybd05bBVrZqWy7Epy392yN9pe0g1T)AFZAp8YEHyGN964ySZIS7U7qrCCkuE6kPT3QTiLY4Xo5sLERDS1ja324F5Ne05sUTwMdgBqQc(p7grqRvBZPv7AzKSSMtHae8gIqUSTBNJoyuNlZ5RQowz3QSGEgmSsVRGc)6nYE3)xotLGxj1H(mKHbsoj9Py1grVV7scNvYve8hw86hu0WHl4WVdefmqlbESo5MlP5sJhM6VubHcovnR0NYxxXDDMGOfE2I7wCVBwSBXhuShk(M)BlorcUEXHSuPyQTdIDZrwIhexYlFN1lASevdiHNgJTnCQtQGq)LRctjkLpHt9rEt4MbkeWtHFBnVj5PA26)0AJd0KuKhTzXhF7IhT(ThN1ixb(V45sU8ABJDWEba9EwpwCI2nncpvXNm2ZSlAxECtVo9B9fOzpV(hoYDStziwV4r7pE8ytGiPi0xjYrsSmXrX(wa9WAwzooavECGsKldHa2KmHuVuLqOIZpPQHVE2AvD8hirUN5x2DogiN2erA1(7TNF78Y6KFlbctIZ52hjfXsqPSnEmxAqTs03rJ16zNlK0JLKSzhVsi79F5JpYKqiRS4x)sfKgzM6u8kZX(uGAMy89Vw2DE5hNof7ozH(6e8bLGVVPYVbhpWIMIbrlcpRIz921(HFEdgv(zUD77V39Lx8pZ6IktxKCr0K6p75GHYSjyqlpx3EbidlIfBD)CgDHG3TtW2N0zK7qVqrQq(51Q3(tN3W8vFfW(vvr)9UXIk9wvowRw95LSvw1a3fmEvUGARVafdbxZmOXlEU8D5Q(ABtJDdL3hU7d29JAC2FEYF)")
            end },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
        }
    }
    Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
    Config_Table.config = MainAddon.BuildDPSTrinketUI(Config_Table.config, Config_Color)
    Config_Table.config = MainAddon.BuildCombatPotionUI(Config_Table.config, "Demonology", Config_Color)
    M.SetConfig(266, Config_Table)

    -- Custom Var
    local IsStandingStill = false
    local IsFallingvar = false
    local TimeStampFalling = GetTime()
    local SummoningFelguard = false
    local MOCheck = false
    local macro = "/cast [@cursor] " .. S.BilescourgeBombers:Name() .. "\n/cast " .. S.DemonicStrength:Name()
    local magicgroundspell_bombers = GetSetting('magicgroundspell_bombers', false)
    local magicgroundspell_guillotine = GetSetting('magicgroundspell_guillotine', false)

    -- Custom func
    local function Defensives()
        if S.UnendingResolve:IsReady(Player) and GetSetting('UnendingResolve_check', true) and (Player:HealthPercentage() < GetSetting('UnendingResolve_spin', 30)) then
            if Cast(S.UnendingResolve, true) then
                return "Unending Resolve defensive"
            end
        end

        if S.DarkPact:IsReady(Player) and GetSetting('dpact_check', true) and (Player:HealthPercentage() < GetSetting('dpact_spin', 50)) then
            if Cast(S.DarkPact, true) then
                return "Dark Pact defensive"
            end
        end

        -- Mortal Coil
        if S.MortalCoil:IsReady() and GetSetting('MortalCoil_check', true) and (Player:HealthPercentage() < GetSetting('MortalCoil_spin', 30)) then
            if Cast(S.MortalCoil) then
                return "Mortal Coil defensive"
            end
        end
    end
    
    local function Utilities()
        -- PET
        if IsStandingStill and (not Pet:IsActive() or Pet:IsDeadOrGhost()) and GetTime() > TimeStampFalling + 1 then
            if S.FelDomination:IsReady(Player) and Player:DebuffDown(S.FelDomination) and not S.SummonPet:IsBlocked() then
                if Player:AffectingCombat() and GetSetting('fel_domination', {})[1] or not Player:AffectingCombat() and GetSetting('fel_domination', {})[2] then
                    if Cast(S.FelDomination, true) then
                        return "FelDomination in combat ";
                    end
                end
            elseif S.SummonPet:IsReady(Player) then
                if Cast(S.SummonPet) then
                    return "SummonPet Pet"
                end
            end
        end
        if Player:AffectingCombat() then
            local autorebith = GetSetting('autorebirth', {})
            if S.Soulstone:IsReady(MouseOver) and autorebith['autorebirth_mouseover'] then
                local GetMouseFociCache = GetMouseFoci()
                ---@class Frame
                local MouseFocus = GetMouseFociCache[1]

                local FrameName = MouseFocus and not MouseFocus:IsForbidden() and MouseFocus:GetName() or "None"
                if FrameName ~= "WorldFrame" and FrameName ~= "None" then
                    if MouseOver:EvaluateRebirth() then
                        if Cast(S.Soulstone) then 
                            MainAddon.UI:ShowToast("Soulstone", MouseOver:Name(), MainAddon.GetTexture(S.Soulstone))
                            return "Soulstone MouseOver" 
                        end
                    end
                end
            end
            if S.Soulstone:IsReady() and autorebith['autorebirth_target'] then
                if Target:EvaluateRebirth() then
                    if Cast(S.Soulstone) then 
                        MainAddon.UI:ShowToast("Soulstone", Target:Name(), MainAddon.GetTexture(S.Soulstone))
                        return "Soulstone Target" 
                    end
                end
            end
        end
    end

    --- ===== Rotation Variables =====
    local VarNextTyrantCD = 0
    local VarInOpener = true
    local VarImpDespawn = 0
    local VarImpl = false
    local VarPoolCoresForTyrant = false
    local VarDiabolicRitualRemains = 0
    local VilefiendAbility = S.MarkofFharg:IsAvailable() and S.SummonCharhound or (S.MarkofShatug:IsAvailable() and S.SummonGloomhound or S.SummonVilefiend)
    local SoulShards = 0
    local DemonicCoreStacks = 0
    local BossFightRemains = 11111
    local FightRemains = 11111
    local CombatTime = 0
  
    -- Enemy Variables
    local Enemies40y
    local Enemies8ySplash, EnemiesCount8ySplash

    --- ===== Non-Trinket Precombat Variables =====
    local VarFirstTyrantTime = 12
    local function SetPrecombatVariables()
    VarFirstTyrantTime = 12
    VarFirstTyrantTime = VarFirstTyrantTime + (S.GrimoireFelguard:IsAvailable() and S.GrimoireFelguard:ExecuteTime() or 0)
    VarFirstTyrantTime = VarFirstTyrantTime + (S.SummonVilefiend:IsAvailable() and S.SummonVilefiend:ExecuteTime() or 0)
    VarFirstTyrantTime = VarFirstTyrantTime + ((S.GrimoireFelguard:IsAvailable() or S.SummonVilefiend:IsAvailable()) and Player:GCD() or 0)
    VarFirstTyrantTime = VarFirstTyrantTime - (S.SummonDemonicTyrant:ExecuteTime() + S.ShadowBolt:ExecuteTime())
    VarFirstTyrantTime = mathmin(VarFirstTyrantTime, 10)
    end
    SetPrecombatVariables()

    --- ===== Trinket Variables =====
    local Trinket1, Trinket2
    local VarTrinket1ID, VarTrinket2ID
    local VarTrinket1Level, VarTrinket2Level
    local VarTrinket1Spell, VarTrinket2Spell
    local VarTrinket1Range, VarTrinket2Range
    local VarTrinket1CastTime, VarTrinket2CastTime
    local VarTrinket1CD, VarTrinket2CD
    local VarTrinket1Ex, VarTrinket2Ex
    local VarTrinket1Buffs, VarTrinket2Buffs
    local VarTrinket1Exclude, VarTrinket2Exclude
    local VarTrinket1Manual, VarTrinket2Manual
    local VarTrinket1BuffDuration, VarTrinket2BuffDuration
    local VarTrinket1Sync, VarTrinket2Sync
    local VarDamageTrinketPriority, VarTrinketPriority
    local VarTrinketFailures = 0
    local function SetTrinketVariables()
    local T1, T2 = Player:GetTrinketData(OnUseExcludes)

    -- If we don't have trinket items, try again in 5 seconds.
    if VarTrinketFailures < 5 and ((T1.ID == 0 or T2.ID == 0) or (T1.Level == 0 or T2.Level == 0) or (T1.SpellID > 0 and not T1.Usable or T2.SpellID > 0 and not T2.Usable)) then
        VarTrinketFailures = VarTrinketFailures + 1
        Delay(5, function()
            SetTrinketVariables()
        end
        )
        return
    end

    Trinket1 = T1.Object
    Trinket2 = T2.Object

    VarTrinket1ID = T1.ID
    VarTrinket2ID = T2.ID

    VarTrinket1Level = T1.Level
    VarTrinket2Level = T2.Level

    VarTrinket1Spell = T1.Spell
    VarTrinket1Range = T1.Range
    VarTrinket1CastTime = T1.CastTime
    VarTrinket2Spell = T2.Spell
    VarTrinket2Range = T2.Range
    VarTrinket2CastTime = T2.CastTime

    VarTrinket1CD = T1.Cooldown
    VarTrinket2CD = T2.Cooldown

    VarTrinket1Ex = T1.Excluded
    VarTrinket2Ex = T2.Excluded

    VarTrinket1Buffs = Trinket1:HasUseBuff()
    VarTrinket2Buffs = Trinket2:HasUseBuff()

    VarTrinket1Exclude = VarTrinket1ID == 193757
    VarTrinket2Exclude = VarTrinket2ID == 193757

    VarTrinket1Manual = VarTrinket1ID == I.SpymastersWeb:ID() or VarTrinket1ID == I.ImperfectAscendancySerum:ID()
    VarTrinket2Manual = VarTrinket2ID == I.SpymastersWeb:ID() or VarTrinket2ID == I.ImperfectAscendancySerum:ID()

    VarTrinket1BuffDuration = Trinket1:BuffDuration() + (VarTrinket1ID == I.MirrorofFracturedTomorrows:ID() and 20 or 0)
    VarTrinket2BuffDuration = Trinket2:BuffDuration() + (VarTrinket2ID == I.MirrorofFracturedTomorrows:ID() and 20 or 0)

    VarTrinket1Sync = 0.5
    if VarTrinket1Buffs and (VarTrinket1CD % 60 == 0 or 60 % VarTrinket1CD == 0) then
        VarTrinket1Sync = 1
    end
    VarTrinket2Sync = 0.5
    if VarTrinket2Buffs and (VarTrinket2CD % 60 == 0 or 60 % VarTrinket2CD == 0) then
        VarTrinket2Sync = 1
    end

    VarDamageTrinketPriority = 1
    if not VarTrinket1Buffs and not VarTrinket2Buffs and VarTrinket2Level > VarTrinket1Level then
        VarDamageTrinketPriority = 2
    end

    -- Note: If BuffDuration is 0, set to 1 to avoid divide by zero errors.
    local T1BuffDur = VarTrinket1BuffDuration > 0 and VarTrinket1BuffDuration or 1
    local T2BuffDur = VarTrinket2BuffDuration > 0 and VarTrinket2BuffDuration or 1
    VarTrinketPriority = 1
    if not VarTrinket1Buffs and VarTrinket2Buffs or VarTrinket2Buffs and ((VarTrinket2CD / T2BuffDur) * (VarTrinket2Sync)) > (((VarTrinket1CD / T1BuffDur) * (VarTrinket1Sync)) * (1 + ((VarTrinket1Level - VarTrinket2Level) / 100))) then
        VarTrinketPriority = 2
    end
    end
    SetTrinketVariables()

    HL:RegisterForEvent(function()
        VarNextTyrantCD = 0
        VarInOpener = true
        VarImpDespawn = 0
        VarImpl = false
        VarPoolCoresForTyrant = false
        VarDiabolicRitualRemains = 0
        BossFightRemains = 11111
        FightRemains = 11111
      end, "PLAYER_REGEN_ENABLED")
      
      HL:RegisterForEvent(function()
        SetTrinketVariables()
      end, "PLAYER_EQUIPMENT_CHANGED")
      
      HL:RegisterForEvent(function()
        S.Demonbolt:RegisterInFlight()
        S.HandofGuldan:RegisterInFlight()
        VilefiendAbility = S.MarkofFharg:IsAvailable() and S.SummonCharhound or (S.MarkofShatug:IsAvailable() and S.SummonGloomhound or S.SummonVilefiend)
        SetPrecombatVariables()
      end, "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")
      S.Demonbolt:RegisterInFlight()
      S.HandofGuldan:RegisterInFlight()

    --- ===== Helper Functions =====
    local function WildImpsCount()
        return Warlock.GuardiansTable.ImpCount or 0
    end
    
    -- Function to check two_cast_imps or last_cast_imps
    local function CheckImpCasts(count)
        local ImpCount = 0
        for _, Pet in pairs(Warlock.GuardiansTable.Pets) do
        if Pet.ImpCasts <= count then
            ImpCount = ImpCount + 1
        end
        end
        return ImpCount
    end
    
    -- Function to check for Grimoire Felguard
    local function GrimoireFelguardTime()
        return Warlock.GuardiansTable.FelguardDuration or 0
    end
    
    local function GrimoireFelguardActive()
        return GrimoireFelguardTime() > 0
    end
    
    -- Function to check for Demonic Tyrant
    local function DemonicTyrantTime()
        return Warlock.GuardiansTable.DemonicTyrantDuration or 0
    end
    
    local function DemonicTyrantActive()
        return DemonicTyrantTime() > 0
    end
    
    -- Function to check for Dreadstalkers
    local function DreadstalkerTime()
        return Warlock.GuardiansTable.DreadstalkerDuration or 0
    end
    
    local function DreadstalkerActive()
        return DreadstalkerTime() > 0
    end

    -- Function to check for Greater Dreadstalkers (TWW Set Bonus Spawns)
    -- Note: Greater Dreadstalkers are force-spawned by Summon Demonic Tyrant, so force full duration if we're casting SDT.
    local function GreaterDreadstalkerTime()
        local TableTime = Warlock.GuardiansTable.GreaterDreadstalkerDuration or 0
        local GDTime = (Player:HasTier("TWW2", 2) and Player:IsCasting(S.SummonDemonicTyrant)) and 12 or TableTime
        return GDTime
    end
    
    local function GreaterDreadstalkerActive()
        return GreaterDreadstalkerTime() > 0
    end
    
    -- Function to check for Vilefiend
    local function VilefiendTime()
        return Warlock.GuardiansTable.VilefiendDuration or 0
    end
    
    local function VilefiendActive()
        return VilefiendTime() > 0
    end
        
    --- ===== CastTargetIf Condition Functions =====
    local function EvaluateTargetIfDemonbolt(TargetUnit)
        -- target_if=min:debuff.doom.remains
        return TargetUnit:DebuffRemains(S.DoomDebuff)
    end
    
    --- ===== CastCycle Functions =====
    local function EvaluateCycleDemonbolt(TargetUnit)
        -- target_if=min:debuff.doom.remains,if=buff.demonic_core.react&(!talent.doom|buff.demonic_core.react>1|debuff.doom.remains>10|debuff.doom.down)
        return TargetUnit:DebuffRemains(S.DoomDebuff)
    end
    
    local function EvaluateCycleDemonbolt2(TargetUnit)
        -- target_if=(!debuff.doom.up)|active_enemies<4
        return TargetUnit:DebuffDown(S.DoomDebuff) or EnemiesCount8ySplash < 4
    end
    
    local function EvaluateCycleDemonbolt3(TargetUnit)
        -- target_if=min:debuff.doom.remains,if=!variable.pool_cores_for_tyrant
        return TargetUnit:DebuffRemains(S.DoomDebuff)
    end
    
    local function EvaluateCycleDoom(TargetUnit)
        -- target_if=refreshable
        return TargetUnit:DebuffRefreshable(S.Doom)
    end
    
    local function EvaluateCycleDoomBrand(TargetUnit)
        -- target_if=!debuff.doom_brand.up
        return TargetUnit:DebuffDown(S.DoomBrandDebuff)
    end


    local function Precombat()
        -- flask
        -- food
        -- augmentation
        -- summon_pet
        -- snapshot_stats

        -- variable,name=trinket_1_buffs,value=trinket.1.has_use_buff
        -- variable,name=trinket_2_buffs,value=trinket.2.has_use_buff
        -- variable,name=trinket_1_exclude,value=trinket.1.is.ruby_whelp_shell
        -- variable,name=trinket_2_exclude,value=trinket.2.is.ruby_whelp_shell
        -- variable,name=trinket_1_manual,value=trinket.1.is.spymasters_web|trinket.1.is.imperfect_ascendancy_serum
        -- variable,name=trinket_2_manual,value=trinket.2.is.spymasters_web|trinket.2.is.imperfect_ascendancy_serum
        -- variable,name=trinket_1_buff_duration,value=trinket.1.proc.any_dps.duration+(trinket.1.is.mirror_of_fractured_tomorrows*20)
        -- variable,name=trinket_2_buff_duration,value=trinket.2.proc.any_dps.duration+(trinket.2.is.mirror_of_fractured_tomorrows*20)
        -- variable,name=trinket_1_sync,op=setif,value=1,value_else=0.5,condition=variable.trinket_1_buffs&(trinket.1.cooldown.duration%%cooldown.summon_demonic_tyrant.duration=0|cooldown.summon_demonic_tyrant.duration%%trinket.1.cooldown.duration=0)
        -- variable,name=trinket_2_sync,op=setif,value=1,value_else=0.5,condition=variable.trinket_2_buffs&(trinket.2.cooldown.duration%%cooldown.summon_demonic_tyrant.duration=0|cooldown.summon_demonic_tyrant.duration%%trinket.2.cooldown.duration=0)
        -- variable,name=damage_trinket_priority,op=setif,value=2,value_else=1,condition=!variable.trinket_1_buffs&!variable.trinket_2_buffs&trinket.2.ilvl>trinket.1.ilvl
        -- variable,name=trinket_priority,op=setif,value=2,value_else=1,condition=!variable.trinket_1_buffs&variable.trinket_2_buffs|variable.trinket_2_buffs&((trinket.2.cooldown.duration%variable.trinket_2_buff_duration)*(1.5+trinket.2.has_buff.intellect)*(variable.trinket_2_sync)*(1-0.5*trinket.2.is.mirror_of_fractured_tomorrows))>(((trinket.1.cooldown.duration%variable.trinket_1_buff_duration)*(1.5+trinket.1.has_buff.intellect)*(variable.trinket_1_sync)*(1-0.5*trinket.1.is.mirror_of_fractured_tomorrows))*(1+((trinket.1.ilvl-trinket.2.ilvl)%100)))
        -- power_siphon
        -- Note: Only suggest Power Siphon if we won't overcap buff stacks, unless the buff is about to expire.
        if S.PowerSiphon:IsReady() and (Player:BuffStack(S.DemonicCoreBuff) + mathmax(WildImpsCount(), 2) <= 4 or Player:BuffRemains(S.DemonicCoreBuff) < 3) then
            if Cast(S.PowerSiphon) then return "power_siphon precombat 2"; end
        end
        -- Manually added: demonbolt,if=!target.is_boss&buff.demonic_core.up
        -- Note: This is to avoid suggesting ShadowBolt on a new pack of mobs when we have Demonic Core buff stacks.
        if S.Demonbolt:IsReady() and not Target:IsInBossList() and Player:BuffUp(S.DemonicCoreBuff) then
            if Cast(S.Demonbolt) then return "demonbolt precombat 4"; end
        end
        if GetSetting('OpeningSpell', 'OP_ShadowBolt') == 'OP_Demonbolt' then
            if S.Demonbolt:IsReady() and Player:BuffDown(S.DemonicCoreBuff) and not Player:PrevGCDP(1, S.PowerSiphon) then
                if Cast(S.Demonbolt) then return "demonbolt precombat 6"; end
            end
        elseif GetSetting('OpeningSpell', 'OP_ShadowBolt') == 'OP_ShadowBolt' then
            -- shadow_bolt
            if S.ShadowBolt:IsReady() then
                if Cast(S.ShadowBolt) then return "shadow_bolt precombat 8"; end
            end
        end
    end

    local function FightEnd()
        -- grimoire_felguard,if=fight_remains<20
        if S.GrimoireFelguard:IsReady() and BossFightRemains < 20 then
          if Cast(S.GrimoireFelguard) then return "grimoire_felguard fight_end 2"; end
        end
        -- ruination
        if S.RuinationAbility:IsReady() then
          if Cast(S.RuinationAbility) then return "ruination fight_end 4"; end
        end
        -- implosion,if=fight_remains<2*gcd.max&!prev_gcd.1.implosion
        if S.Implosion:IsReady() and (BossFightRemains < 2 * Player:GCD() and not Player:PrevGCDP(1, S.Implosion)) then
          if Cast(S.Implosion) then return "implosion fight_end 6"; end
        end
        -- demonbolt,if=fight_remains<gcd.max*2*buff.demonic_core.stack+9&buff.demonic_core.react&(soul_shard<4|fight_remains<buff.demonic_core.stack*gcd.max)
        if S.Demonbolt:IsReady() and (BossFightRemains < Player:GCD() * 2 * DemonicCoreStacks + 9 and Player:BuffUp(S.DemonicCoreBuff) and (SoulShards < 4 or BossFightRemains < DemonicCoreStacks * Player:GCD())) then
          if Cast(S.Demonbolt) then return "demonbolt fight_end 8"; end
        end
        if BossFightRemains < 20 then
          -- call_dreadstalkers,if=fight_remains<20
          if S.CallDreadstalkers:IsReady() then
            if Cast(S.CallDreadstalkers) then return "call_dreadstalkers fight_end 10"; end
          end
          -- summon_vilefiend,if=fight_remains<20
          if VilefiendAbility:IsReady() then
            if Cast(VilefiendAbility) then return "summon_vilefiend fight_end 12"; end
          end
          -- summon_demonic_tyrant,if=fight_remains<20
          if S.SummonDemonicTyrant:IsReady() and (BossFightRemains < 20) then
            if Cast(S.SummonDemonicTyrant) then return "summon_demonic_tyrant fight_end 14"; end
          end
        end
        -- demonic_strength,if=fight_remains<10
        if S.DemonicStrength:IsReady() and (BossFightRemains < 10) then
          if Cast(S.DemonicStrength) then return "demonic_strength fight_end 16"; end
        end
        -- power_siphon,if=buff.demonic_core.stack<3&fight_remains<20
        if S.PowerSiphon:IsReady() and (DemonicCoreStacks < 3 and BossFightRemains < 20) then
          if Cast(S.PowerSiphon) then return "power_siphon fight_end 18"; end
        end
        -- demonbolt,if=fight_remains<gcd.max*2*buff.demonic_core.stack+9&buff.demonic_core.react&(soul_shard<4|fight_remains<buff.demonic_core.stack*gcd.max)
        if S.Demonbolt:IsReady() and (BossFightRemains < Player:GCD() * 2 * DemonicCoreStacks + 9 and Player:BuffUp(S.DemonicCoreBuff) and (SoulShards < 4 or BossFightRemains < DemonicCoreStacks * Player:GCD())) then
          if Cast(S.Demonbolt) then return "demonbolt fight_end 20"; end
        end
        -- hand_of_guldan,if=soul_shard>2&fight_remains<gcd.max*2*buff.demonic_core.stack+9
        if S.HandofGuldan:IsReady() and (SoulShards > 2 and BossFightRemains < Player:GCD() * 2 * DemonicCoreStacks + 9) then
          if Cast(S.HandofGuldan) then return "hand_of_guldan fight_end 22"; end
        end
        -- infernal_bolt
        if S.InfernalBolt:IsReady() then
          if Cast(S.InfernalBolt) then return "infernal_bolt fight_end 24"; end
        end
    end

    local function Items()
        -- use_item,use_off_gcd=1,slot=trinket1,if=variable.trinket_1_buffs&!variable.trinket_1_manual&(!pet.demonic_tyrant.active&trinket.1.cast_time>0|!trinket.1.cast_time>0)&(pet.demonic_tyrant.active|!talent.summon_demonic_tyrant|variable.trinket_priority=2&cooldown.summon_demonic_tyrant.remains>20&!pet.demonic_tyrant.active&trinket.2.cooldown.remains<cooldown.summon_demonic_tyrant.remains+5)&(variable.trinket_2_exclude|!trinket.2.has_cooldown|trinket.2.cooldown.remains|variable.trinket_priority=1&!variable.trinket_2_manual)|variable.trinket_1_buff_duration>=fight_remains
        if Trinket1 and Trinket1:IsReady() and not VarTrinket1Ex and not Player:IsItemBlacklisted(Trinket1) and (VarTrinket1Buffs and not VarTrinket1Manual and (not DemonicTyrantActive() and VarTrinket1CastTime > 0 or not (VarTrinket1CastTime > 0)) and (DemonicTyrantActive() or not S.SummonDemonicTyrant:IsAvailable() or VarTrinketPriority == 2 and S.SummonDemonicTyrant:CooldownRemains() > 20 and not DemonicTyrantActive() and Trinket2:CooldownRemains() < S.SummonDemonicTyrant:CooldownRemains() + 5) and (VarTrinket2Exclude or not Trinket2:HasCooldown() or Trinket2:CooldownDown() or VarTrinketPriority == 1 and not VarTrinket2Manual) or VarTrinket1BuffDuration >= FightRemains) then
          if Cast(Trinket1) then return "trinket1 (" .. Trinket1:Name() .. ") items 2"; end
        end
        -- use_item,use_off_gcd=1,slot=trinket2,if=variable.trinket_2_buffs&!variable.trinket_2_manual&(!pet.demonic_tyrant.active&trinket.2.cast_time>0|!trinket.2.cast_time>0)&(pet.demonic_tyrant.active|!talent.summon_demonic_tyrant|variable.trinket_priority=1&cooldown.summon_demonic_tyrant.remains>20&!pet.demonic_tyrant.active&trinket.1.cooldown.remains<cooldown.summon_demonic_tyrant.remains+5)&(variable.trinket_1_exclude|!trinket.1.has_cooldown|trinket.1.cooldown.remains|variable.trinket_priority=2&!variable.trinket_1_manual)|variable.trinket_2_buff_duration>=fight_remains
        if Trinket2 and Trinket2:IsReady() and not VarTrinket2Ex and not Player:IsItemBlacklisted(Trinket2) and (VarTrinket2Buffs and not VarTrinket2Manual and (not DemonicTyrantActive() and VarTrinket2CastTime > 0 or not (VarTrinket2CastTime > 0)) and (DemonicTyrantActive() or not S.SummonDemonicTyrant:IsAvailable() or VarTrinketPriority == 1 and S.SummonDemonicTyrant:CooldownRemains() > 20 and not DemonicTyrantActive() and Trinket1:CooldownRemains() < S.SummonDemonicTyrant:CooldownRemains() + 5) and (VarTrinket1Exclude or not Trinket1:HasCooldown() or Trinket1:CooldownDown() or VarTrinketPriority == 2 and not VarTrinket1Manual) or VarTrinket2BuffDuration >= FightRemains) then
          if Cast(Trinket2) then return "trinket2 (" .. Trinket2:Name() .. ") items 4"; end
        end
        -- use_item,use_off_gcd=1,slot=trinket1,if=!variable.trinket_1_buffs&!variable.trinket_1_manual&((variable.damage_trinket_priority=1|trinket.2.cooldown.remains)&(trinket.1.cast_time>0&!pet.demonic_tyrant.active|!trinket.1.cast_time>0)|(time<20&variable.trinket_2_buffs)|cooldown.summon_demonic_tyrant.remains_expected>20)
        if Trinket1 and Trinket1:IsReady() and not VarTrinket1Ex and not Player:IsItemBlacklisted(Trinket1) and (not VarTrinket1Buffs and not VarTrinket1Manual and ((VarDamageTrinketPriority == 1 or Trinket2:CooldownDown()) and (VarTrinket1CastTime > 0 and not DemonicTyrantActive() or not (VarTrinket1CastTime > 0)) or (HL.CombatTime() < 20 and VarTrinket2Buffs) or S.SummonDemonicTyrant:CooldownRemains() > 20)) then
          if Cast(Trinket1) then return "trinket1 (" .. Trinket1:Name() .. ") items 6"; end
        end
        -- use_item,use_off_gcd=1,slot=trinket2,if=!variable.trinket_2_buffs&!variable.trinket_2_manual&((variable.damage_trinket_priority=2|trinket.1.cooldown.remains)&(trinket.2.cast_time>0&!pet.demonic_tyrant.active|!trinket.2.cast_time>0)|(time<20&variable.trinket_1_buffs)|cooldown.summon_demonic_tyrant.remains_expected>20)
        if Trinket2 and Trinket2:IsReady() and not VarTrinket2Ex and not Player:IsItemBlacklisted(Trinket2) and (not VarTrinket2Buffs and not VarTrinket2Manual and ((VarDamageTrinketPriority == 2 or Trinket1:CooldownDown()) and (VarTrinket2CastTime > 0 and not DemonicTyrantActive() or not (VarTrinket2CastTime > 0)) or (HL.CombatTime() < 20 and VarTrinket1Buffs) or S.SummonDemonicTyrant:CooldownRemains() > 20)) then
          if Cast(Trinket2) then return "trinket2 (" .. Trinket2:Name() .. ") items 8"; end
        end
        -- use_item,use_off_gcd=1,name=spymasters_web,if=pet.demonic_tyrant.active&fight_remains<=80&buff.spymasters_report.stack>=30&(!variable.trinket_1_buffs&trinket.2.is.spymasters_web|!variable.trinket_2_buffs&trinket.1.is.spymasters_web)|fight_remains<=20&(trinket.1.cooldown.remains&trinket.2.is.spymasters_web|trinket.2.cooldown.remains&trinket.1.is.spymasters_web|!variable.trinket_1_buffs|!variable.trinket_2_buffs)
        if I.SpymastersWeb:IsEquippedAndReady() and (DemonicTyrantActive() and BossFightRemains <= 80 and Player:BuffStack(S.SpymastersReportBuff) >= 30 and (not VarTrinket1Buffs and VarTrinket2ID == I.SpymastersWeb:ID() or not VarTrinket2Buffs and VarTrinket1ID == I.SpymastersWeb:ID()) or BossFightRemains <= 20 and (Trinket1:CooldownDown() and VarTrinket2ID == I.SpymastersWeb:ID() or Trinket2:CooldownDown() and VarTrinket1ID == I.SpymastersWeb:ID() or not VarTrinket1Buffs or not VarTrinket2Buffs)) then
          if Cast(I.SpymastersWeb) then return "spymasters_web items 10"; end
        end
        -- use_item,use_off_gcd=1,name=imperfect_ascendancy_serum,if=pet.demonic_tyrant.active|fight_remains<=30
        if I.ImperfectAscendancySerum:IsEquippedAndReady() and (DemonicTyrantActive() or BossFightRemains <= 30) then
          if Cast(I.ImperfectAscendancySerum) then return "imperfect_ascendancy_serum items 12"; end
        end
        -- use_item,name=mirror_of_fractured_tomorrows,if=trinket.1.is.mirror_of_fractured_tomorrows&variable.trinket_priority=2|trinket.2.is.mirror_of_fractured_tomorrows&variable.trinket_priority=1
        if I.MirrorofFracturedTomorrows:IsEquippedAndReady() and (VarTrinket1ID == I.MirrorofFracturedTomorrows:ID() and VarTrinketPriority == 2 or VarTrinket2ID == I.MirrorofFracturedTomorrows:ID() and VarTrinketPriority == 1) then
          if Cast(I.MirrorofFracturedTomorrows) then return "mirror_of_fractured_tomorrows items 16"; end
        end
        -- use_item,slot=trinket1,if=!variable.trinket_1_buffs&(variable.damage_trinket_priority=1|trinket.2.cooldown.remains)
        if Trinket1 and Trinket1:IsReady() and not VarTrinket1Ex and not Player:IsItemBlacklisted(Trinket1) and (not VarTrinket1Buffs and (VarDamageTrinketPriority == 1 or Trinket2:CooldownDown())) then
          if Cast(Trinket1) then return "trinket1 (" .. Trinket1:Name() .. ") items 18"; end
        end
        -- use_item,slot=trinket2,if=!variable.trinket_2_buffs&(variable.damage_trinket_priority=2|trinket.1.cooldown.remains)
        if Trinket2 and Trinket2:IsReady() and not VarTrinket2Ex and not Player:IsItemBlacklisted(Trinket2) and (not VarTrinket2Buffs and (VarDamageTrinketPriority == 2 or Trinket1:CooldownDown())) then
          if Cast(Trinket2) then return "trinket2 (" .. Trinket2:Name() .. ") items 20"; end
        end
        -- use_item,use_off_gcd=1,slot=main_hand,name=!neural_synapse_enhancer
        -- Note: neural_synapse_enhancer is excluded via OnUseExcludes, so won't be included in the below.
        local MainHandToUse, _, MainHandRange = Player:GetUseableItems(OnUseExcludes, 16)
        if MainHandToUse then
          if Cast(MainHandToUse) then return "use_item main_hand items 14"; end
        end
        if I.NeuralSynapseEnhancer:IsEquippedAndReady() and (
          -- use_item,use_off_gcd=1,slot=main_hand,name=neural_synapse_enhancer,if=(pet.demonic_tyrant.active|fight_remains<=15)&!variable.trinket_1_buffs&!variable.trinket_2_buffs
          ((DemonicTyrantActive() or BossFightRemains <= 15) and not VarTrinket1Buffs and not VarTrinket2Buffs) or
          -- use_item,use_off_gcd=1,slot=main_hand,name=neural_synapse_enhancer,if=(pet.demonic_tyrant.active|fight_remains<=15|trinket.2.cooldown.remains>cooldown.summon_demonic_tyrant.remains)&variable.trinket_2_buffs
          ((DemonicTyrantActive() or BossFightRemains <= 15 or Trinket2:CooldownRemains() > S.SummonDemonicTyrant:CooldownRemains()) and VarTrinket2Buffs) or
          -- use_item,use_off_gcd=1,slot=main_hand,name=neural_synapse_enhancer,if=(pet.demonic_tyrant.active|fight_remains<=15|trinket.1.cooldown.remains>cooldown.summon_demonic_tyrant.remains)&variable.trinket_1_buffs
          ((DemonicTyrantActive() or BossFightRemains <= 15 or Trinket1:CooldownRemains() > S.SummonDemonicTyrant:CooldownRemains()) and VarTrinket1Buffs)
        ) then
          if Cast(I.NeuralSynapseEnhancer) then return "neural_synapse_enhancer main_hand items 16"; end
        end
    end

    local function Opener()
        -- grimoire_felguard,if=soul_shard>=5-talent.fel_invocation
        if S.GrimoireFelguard:IsReady() and (SoulShards >= 5 - num(S.FelInvocation:IsAvailable())) then
            if Cast(S.GrimoireFelguard) then return "grimoire_felguard opener 2"; end
        end
        -- summon_vilefiend,if=soul_shard=5
        if VilefiendAbility:IsReady() and (SoulShards == 5) then
            if Cast(VilefiendAbility) then return "summon_vilefiend opener 4"; end
        end
        -- shadow_bolt,if=soul_shard<5&cooldown.call_dreadstalkers.up
        if S.ShadowBolt:IsReady() and (SoulShards < 5 and S.CallDreadstalkers:CooldownUp()) then
            if Cast(S.ShadowBolt) then return "shadow_bolt opener 6"; end
        end
        -- call_dreadstalkers,if=soul_shard=5
        if S.CallDreadstalkers:IsReady() and (SoulShards == 5) then
            if Cast(S.CallDreadstalkers) then return "call_dreadstalkers opener 8"; end
        end
        -- Ruination
        if S.RuinationAbility:IsReady() then
            if Cast(S.RuinationAbility) then return "ruination opener 10"; end
        end
    end
      
    local function Racials()
        -- berserking,use_off_gcd=1
        if S.Berserking:IsReady() then
            if Cast(S.Berserking) then return "berserking racials 2"; end
        end
        -- blood_fury
        if S.BloodFury:IsReady() then
            if Cast(S.BloodFury) then return "blood_fury racials 4"; end
        end
        -- fireblood
        if S.Fireblood:IsReady() then
            if Cast(S.Fireblood) then return "fireblood racials 6"; end
        end
        -- ancestral_call
        if S.AncestralCall:IsReady() then
            if Cast(S.AncestralCall) then return "ancestral_call racials 8"; end
        end
    end

    local function Tyrant()
        -- call_action_list,name=racials,if=variable.imp_despawn&variable.imp_despawn<time+gcd.max*2+action.summon_demonic_tyrant.cast_time&(prev_gcd.1.hand_of_guldan|prev_gcd.1.ruination)&(variable.imp_despawn&variable.imp_despawn<time+gcd.max+action.summon_demonic_tyrant.cast_time|soul_shard<2)
        if (VarImpDespawn > 0 and VarImpDespawn < Player:GCD() * 2 + S.SummonDemonicTyrant:CastTime() and (Player:PrevGCDP(1, S.HandofGuldan) or Player:PrevGCDP(1, S.RuinationAbility)) and (VarImpDespawn > 0 and VarImpDespawn < Player:GCD() + S.SummonDemonicTyrant:CastTime() or SoulShards < 2)) then
          local ShouldReturn = Racials(); if ShouldReturn then return ShouldReturn; end
        end
        -- invoke_external_buff,name=power_infusion,if=variable.imp_despawn&variable.imp_despawn<time+gcd.max*2+action.summon_demonic_tyrant.cast_time&(prev_gcd.1.hand_of_guldan|prev_gcd.1.ruination)&(variable.imp_despawn&variable.imp_despawn<time+gcd.max+action.summon_demonic_tyrant.cast_time|soul_shard<2)
        -- Note: Not handling external buffs.
        -- summon_demonic_tyrant,if=buff.power_infusion.up
        if S.SummonDemonicTyrant:IsReady() and (Player:PowerInfusionUp()) then
          if Cast(S.SummonDemonicTyrant) then return "summon_demonic_tyrant tyrant 4"; end
        end
        -- power_siphon,if=cooldown.summon_demonic_tyrant.remains<15
        if S.PowerSiphon:IsReady() and (S.SummonDemonicTyrant:CooldownRemains() < 15) then
          if Cast(S.PowerSiphon) then return "power_siphon tyrant 6"; end
        end
        -- ruination,if=buff.dreadstalkers.remains>gcd.max+action.summon_demonic_tyrant.cast_time&(soul_shard=5|variable.imp_despawn)
        if S.RuinationAbility:IsReady() and (DreadstalkerTime() > Player:GCD() + S.SummonDemonicTyrant:CastTime() and (SoulShards == 5 or VarImpDespawn > 0)) then
          if Cast(S.RuinationAbility) then return "ruination tyrant 8"; end
        end
        -- infernal_bolt,if=!buff.demonic_core.react&variable.imp_despawn>time+gcd.max*2+action.summon_demonic_tyrant.cast_time&soul_shard<3
        if S.InfernalBolt:IsReady() and (Player:BuffDown(S.DemonicCoreBuff) and VarImpDespawn > Player:GCD() * 2 + S.SummonDemonicTyrant:CastTime() and SoulShards < 3) then
          if Cast(S.InfernalBolt) then return "infernal_bolt tyrant 10"; end
        end
        if S.ShadowBolt:IsReady() and (
        -- shadow_bolt,if=prev_gcd.1.call_dreadstalkers&soul_shard<4&buff.demonic_core.react<4
        (Player:PrevGCDP(1, S.CallDreadstalkers) and SoulShards < 4 and DemonicCoreStacks < 4) or
        -- shadow_bolt,if=prev_gcd.2.call_dreadstalkers&prev_gcd.1.shadow_bolt&buff.bloodlust.up&soul_shard<5
        (Player:PrevGCDP(2, S.CallDreadstalkers) and Player:PrevGCDP(1, S.ShadowBolt) and Player:BloodlustUp() and SoulShards < 5) or
        -- shadow_bolt,if=prev_gcd.1.summon_vilefiend&(buff.demonic_calling.down|prev_gcd.2.grimoire_felguard)
        (Player:PrevGCDP(1, VilefiendAbility) and (Player:BuffDown(S.DemonicCallingBuff) or Player:PrevGCDP(2, S.GrimoireFelguard))) or
        -- shadow_bolt,if=prev_gcd.1.grimoire_felguard&buff.demonic_core.react<3&buff.demonic_calling.remains>gcd.max*3
        (Player:PrevGCDP(1, S.GrimoireFelguard) and DemonicCoreStacks < 3 and Player:BuffRemains(S.DemonicCallingBuff) > Player:GCD() * 3)
        ) then
          if Cast(S.ShadowBolt) then return "shadow_bolt tyrant 12"; end
        end
        -- hand_of_guldan,if=variable.imp_despawn>time+gcd.max*2+action.summon_demonic_tyrant.cast_time&!buff.demonic_core.react&buff.demonic_art_pit_lord.up&variable.imp_despawn<time+gcd.max*5+action.summon_demonic_tyrant.cast_time
        if S.HandofGuldan:IsReady() and (VarImpDespawn > Player:GCD() * 2 + S.SummonDemonicTyrant:CastTime() and Player:BuffDown(S.DemonicCoreBuff) and Player:BuffUp(S.DemonicArtPitLordBuff) and VarImpDespawn < Player:GCD() * 5 + S.SummonDemonicTyrant:CastTime()) then
          if Cast(S.HandofGuldan) then return "hand_of_guldan tyrant 14"; end
        end
        -- hand_of_guldan,if=variable.imp_despawn>time+gcd.max+action.summon_demonic_tyrant.cast_time&variable.imp_despawn<time+gcd.max*2+action.summon_demonic_tyrant.cast_time&buff.dreadstalkers.remains>gcd.max+action.summon_demonic_tyrant.cast_time&soul_shard>1
        if S.HandofGuldan:IsReady() and (VarImpDespawn > Player:GCD() + S.SummonDemonicTyrant:CastTime() and VarImpDespawn < Player:GCD() * 2 + S.SummonDemonicTyrant:CastTime() and DreadstalkerTime() > Player:GCD() + S.SummonDemonicTyrant:CastTime() and SoulShards > 1) then
          if Cast(S.HandofGuldan) then return "hand_of_guldan tyrant 16"; end
        end
        -- shadow_bolt,if=!buff.demonic_core.react&variable.imp_despawn>time+gcd.max*2+action.summon_demonic_tyrant.cast_time&variable.imp_despawn<time+gcd.max*4+action.summon_demonic_tyrant.cast_time&soul_shard<3&buff.dreadstalkers.remains>gcd.max*2+action.summon_demonic_tyrant.cast_time
        if S.ShadowBolt:IsReady() and (Player:BuffDown(S.DemonicCoreBuff) and VarImpDespawn > Player:GCD() * 2 + S.SummonDemonicTyrant:CastTime() and VarImpDespawn < Player:GCD() * 4 + S.SummonDemonicTyrant:CastTime() and SoulShards < 3 and DreadstalkerTime() > Player:GCD() * 2 + S.SummonDemonicTyrant:CastTime()) then
          if Cast(S.ShadowBolt) then return "shadow_bolt tyrant 18"; end
        end
        -- grimoire_felguard,if=cooldown.summon_demonic_tyrant.remains<13+gcd.max&cooldown.summon_vilefiend.remains<gcd.max&cooldown.call_dreadstalkers.remains<gcd.max*3.33&(soul_shard=5-(pet.felguard.cooldown.soul_strike.remains<gcd.max)&talent.fel_invocation|soul_shard=5)
        if S.GrimoireFelguard:IsReady() and (S.SummonDemonicTyrant:CooldownRemains() < 13 + Player:GCD() and VilefiendAbility:CooldownRemains() < Player:GCD() and S.CallDreadstalkers:CooldownRemains() < Player:GCD() * 3.33 and (SoulShards == 5 - num(S.SoulStrikePetAbility:CooldownRemains() < Player:GCD()) and S.FelInvocation:IsAvailable() or SoulShards == 5)) then
          if Cast(S.GrimoireFelguard) then return "grimoire_felguard tyrant 20"; end
        end
        -- summon_vilefiend,if=(buff.grimoire_felguard.up|cooldown.grimoire_felguard.remains>10|!talent.grimoire_felguard)&cooldown.summon_demonic_tyrant.remains<13&cooldown.call_dreadstalkers.remains<gcd.max*2.33&(soul_shard=5|soul_shard=4&(buff.demonic_core.react=4)|buff.grimoire_felguard.up)
        if VilefiendAbility:IsReady() and ((GrimoireFelguardActive() or S.GrimoireFelguard:CooldownRemains() > 10 or not S.GrimoireFelguard:IsAvailable()) and S.SummonDemonicTyrant:CooldownRemains() < 13 and S.CallDreadstalkers:CooldownRemains() < Player:GCD() * 2.33 and (SoulShards == 5 or SoulShards == 4 and (DemonicCoreStacks == 4) or GrimoireFelguardActive())) then
          if Cast(VilefiendAbility) then return "summon_vilefiend tyrant 22"; end
        end
        -- call_dreadstalkers,if=(!talent.summon_vilefiend|buff.vilefiend.up)&cooldown.summon_demonic_tyrant.remains<10&soul_shard>=(5-(buff.demonic_core.react>=3))|prev_gcd.3.grimoire_felguard
        if S.CallDreadstalkers:IsReady() and ((not S.SummonVilefiend:IsAvailable() or VilefiendActive()) and S.SummonDemonicTyrant:CooldownRemains() < 10 and SoulShards >= (5 - num(DemonicCoreStacks >= 3)) or Player:PrevGCDP(3, S.GrimoireFelguard)) then
          if Cast(S.CallDreadstalkers) then return "call_dreadstalkers tyrant 24"; end
        end
        -- summon_demonic_tyrant,if=variable.imp_despawn&variable.imp_despawn<time+gcd.max*2+cast_time|buff.dreadstalkers.up&buff.dreadstalkers.remains<gcd.max*2+cast_time
        -- Note: Manually added the Vilefiend check. Also, added a 1.5 second buffer to Dreadstalker/Vilefiend check.
        local DSCheck = false
        if GetSetting('ForceTyrantVilefiendSync', false) then
          DSCheck = DreadstalkerActive() and VilefiendActive() and mathmin(DreadstalkerTime(), VilefiendTime()) < Player:GCD() * 2 + S.SummonDemonicTyrant:CastTime() + 1.5
        else
          DSCheck = DreadstalkerActive() and DreadstalkerTime() < Player:GCD() * 2 + S.SummonDemonicTyrant:CastTime() + 1.5
        end
        if S.SummonDemonicTyrant:IsReady() and ((VarImpDespawn > 0 and VarImpDespawn < Player:GCD() * 2 + S.SummonDemonicTyrant:CastTime() + 1.5) or DSCheck) then
          if Cast(S.SummonDemonicTyrant) then return "summon_demonic_tyrant tyrant 26"; end
        end
        -- hand_of_guldan,if=(variable.imp_despawn|buff.dreadstalkers.remains)&soul_shard>=3|soul_shard=5
        if S.HandofGuldan:IsReady() and ((VarImpDespawn > 0 or DreadstalkerActive()) and SoulShards >= 3 or SoulShards == 5) then
          if Cast(S.HandofGuldan) then return "hand_of_guldan tyrant 28"; end
        end
        -- infernal_bolt,if=variable.imp_despawn&soul_shard<3
        if S.InfernalBolt:IsReady() and (VarImpDespawn > 0 and SoulShards < 3) then
          if Cast(S.InfernalBolt) then return "infernal_bolt tyrant 30"; end
        end
        -- demonbolt,target_if=min:debuff.doom.remains,if=variable.imp_despawn&buff.demonic_core.react&soul_shard<4|prev_gcd.1.call_dreadstalkers&soul_shard<4&buff.demonic_core.react=4|buff.demonic_core.react=4&soul_shard<4|buff.demonic_core.react>=2&cooldown.power_siphon.remains<5
        if S.Demonbolt:IsReady() and ((VarImpDespawn > 0 and Player:BuffUp(S.DemonicCoreBuff) and SoulShards < 4) or (Player:PrevGCDP(1, S.CallDreadstalkers) and SoulShards < 4 and DemonicCoreStacks == 4) or (DemonicCoreStacks == 4 and SoulShards < 4) or (DemonicCoreStacks >= 2 and S.PowerSiphon:CooldownRemains() < 5)) then
          if CastTargetIf(S.Demonbolt, Enemies8ySplash, "min", EvaluateTargetIfDemonbolt, nil) then return "demonbolt tyrant 32"; end
        end
        -- ruination,if=variable.imp_despawn|soul_shard=5&cooldown.summon_vilefiend.remains>gcd.max*3
        if S.RuinationAbility:IsReady() and (VarImpDespawn > 0 or SoulShards == 5 and VilefiendAbility:CooldownRemains() > Player:GCD() * 3) then
          if Cast(S.RuinationAbility) then return "ruination tyrant 34"; end
        end
        -- shadow_bolt
        if S.ShadowBolt:IsReady() then
          if Cast(S.ShadowBolt) then return "shadow_bolt tyrant 36"; end
        end
        -- infernal_bolt
        if S.InfernalBolt:IsReady() then
          if Cast(S.InfernalBolt) then return "infernal_bolt tyrant 38"; end
        end
    end

    local function Variables()
        -- variable,name=next_tyrant_cd,op=set,value=cooldown.summon_demonic_tyrant.remains_expected
        VarNextTyrantCD = S.SummonDemonicTyrant:CooldownRemains()
        -- variable,name=in_opener,op=set,value=0,if=pet.demonic_tyrant.active
        if VarInOpener and DemonicTyrantActive() then
          VarInOpener = false
        end
        -- variable,name=imp_despawn,op=set,value=2*spell_haste*6+0.58+time,if=prev_gcd.1.hand_of_guldan&buff.dreadstalkers.up&cooldown.summon_demonic_tyrant.remains<13&variable.imp_despawn=0
        -- Note: Removed 'time' from the calculation, as it needlessly complicates its usage.
        if Player:PrevGCDP(1, S.HandofGuldan) and DreadstalkerActive() and S.SummonDemonicTyrant:CooldownRemains() < 13 and VarImpDespawn == 0 then
          VarImpDespawn = 2 * Player:SpellHaste() * 6 + 0.58
        end
        -- variable,name=imp_despawn,op=set,value=(variable.imp_despawn>?buff.dreadstalkers.remains+time),if=variable.imp_despawn
        -- Note: Removed 'time' from the calculation, as it needlessly complicates its usage.
        if VarImpDespawn > 0 then
          VarImpDespawn = mathmin(VarImpDespawn, DreadstalkerTime())
        end
        -- Manually added: variable,name=imp_despawn,op=set,value=(variable.imp_despawn>?buff.vilefiend.remains+time),if=variable.imp_despawn&setting.force_tyrant_vilefiend_sync.enabled
        -- Note: Removed 'time' from the calculation, as it needlessly complicates its usage.
        if VarImpDespawn > 0 and GetSetting('ForceTyrantVilefiendSync', false) then
          VarImpDespawn = mathmin(VarImpDespawn, VilefiendTime())
        end
        -- variable,name=imp_despawn,op=set,value=variable.imp_despawn>?buff.grimoire_felguard.remains+time,if=variable.imp_despawn&buff.grimoire_felguard.up
        -- Note: Removed 'time' from the calculation, as it needlessly complicates its usage.
        if VarImpDespawn > 0 and GrimoireFelguardActive() then
          VarImpDespawn = mathmin(VarImpDespawn, GrimoireFelguardTime())
        end
        -- variable,name=imp_despawn,op=set,value=0,if=buff.tyrant.up
        if DemonicTyrantActive() then
          VarImpDespawn = 0
        end
        -- Note: Rest VarImpl to false before the following checks to ensure it doesn't end up as true in a situation where AoE has whittled down to ST.
        VarImpl = false
        -- variable,name=impl,op=set,value=buff.tyrant.down,if=active_enemies>1+(talent.sacrificed_souls.enabled)
        if EnemiesCount8ySplash > 1 + num(S.SacrificedSouls:IsAvailable()) then
          VarImpl = not DemonicTyrantActive()
        end
        -- variable,name=impl,op=set,value=buff.tyrant.remains<6,if=active_enemies>2+(talent.sacrificed_souls.enabled)&active_enemies<5+(talent.sacrificed_souls.enabled)
        if EnemiesCount8ySplash > 2 + num(S.SacrificedSouls:IsAvailable()) and EnemiesCount8ySplash < 5 + num(S.SacrificedSouls:IsAvailable()) then
          VarImpl = DemonicTyrantTime() < 6
        end
        -- variable,name=impl,op=set,value=buff.tyrant.remains<8,if=active_enemies>4+(talent.sacrificed_souls.enabled)
        if EnemiesCount8ySplash > 4 + num(S.SacrificedSouls:IsAvailable()) then
          VarImpl = DemonicTyrantTime() < 8
        end
        -- variable,name=pool_cores_for_tyrant,op=set,value=cooldown.summon_demonic_tyrant.remains<20&variable.next_tyrant_cd<20&(buff.demonic_core.stack<=2|!buff.demonic_core.up)&cooldown.summon_vilefiend.remains<gcd.max*8&cooldown.call_dreadstalkers.remains<gcd.max*8
        VarPoolCoresForTyrant = S.SummonDemonicTyrant:CooldownRemains() < 20 and VarNextTyrantCD < 20 and (DemonicCoreStacks <= 2 or Player:BuffDown(S.DemonicCoreBuff)) and VilefiendAbility:CooldownRemains() < Player:GCD() * 8 and S.CallDreadstalkers:CooldownRemains() < Player:GCD() * 8
        -- variable,name=diabolic_ritual_remains,value=buff.diabolic_ritual_mother_of_chaos.remains,if=buff.diabolic_ritual_mother_of_chaos.up
        if Player:BuffUp(S.DiabolicRitualMotherBuff) then
          VarDiabolicRitualRemains = Player:BuffRemains(S.DiabolicRitualMotherBuff)
        end
        -- variable,name=diabolic_ritual_remains,value=buff.diabolic_ritual_overlord.remains,if=buff.diabolic_ritual_overlord.up
        if Player:BuffUp(S.DiabolicRitualOverlordBuff) then
          VarDiabolicRitualRemains = Player:BuffRemains(S.DiabolicRitualOverlordBuff)
        end
        -- variable,name=diabolic_ritual_remains,value=buff.diabolic_ritual_pit_lord.remains,if=buff.diabolic_ritual_pit_lord.up
        if Player:BuffUp(S.DiabolicRitualPitLordBuff) then
          VarDiabolicRitualRemains = Player:BuffRemains(S.DiabolicRitualPitLordBuff)
        end
    end      

    --- ======= ACTION LISTS =======
    local function APL()
        -- Update CombatTime, which is used in many spell suggestions
        magicgroundspell_bombers = GetSetting('magicgroundspell_bombers', false)
        magicgroundspell_guillotine = GetSetting('magicgroundspell_guillotine', false)
        CombatTime = HL.CombatTime()
        IsStandingStill = not Player:IsMoving()
        IsFallingvar = IsFalling() and true or false
        if IsFallingvar then
            TimeStampFalling = GetTime()
        end

        -- Enemies Update
        local PetCleaveAbility = (Action.FindBySpellID(S.Whiplash:ID()) and S.Whiplash) or nil

        if AoEON() then
            Enemies40y = Player:GetEnemiesInRange(40)
            local PetNPCID = Pet:NPCID()
            if (PetNPCID == 17252 or PetNPCID == 1860 or PetNPCID == 1863 or PetNPCID == 417 or PetNPCID == 58964) and PetCleaveAbility then
                Enemies8ySplash = Player:GetEnemiesInSpellActionRange(PetCleaveAbility)
            else
                Enemies8ySplash = Player:GetEnemiesInRange(40)
            end
        else
            Enemies40y = {}
            Enemies8ySplash = {Target}
        end
        EnemiesCount8ySplash = #Enemies8ySplash
        
        -- call_action_list,name=variables
        Variables()

        if M.TargetIsValid() or Player:AffectingCombat() then
            -- Calculate fight_remains
            BossFightRemains = HL.BossFightRemains()
            FightRemains = BossFightRemains
            if FightRemains == 11111 then
                FightRemains = HL.FightRemains(Enemies8ySplash, false)
            end

            -- Update Demonology-specific Tables
            Warlock.UpdatePetTable()

            -- Update CombatTime, which is used in many spell suggestions
            CombatTime = HL.CombatTime()

            -- Calculate Soul Shards
            SoulShards = Player:SoulShardsP()

            -- Calculate Demonic Core Stacks
            DemonicCoreStacks = Player:BuffStack(S.DemonicCoreBuff)

            -- Safety for nil VilefiendAbility
            if not VilefiendAbility then
                VilefiendAbility = S.MarkofFharg:IsAvailable() and S.SummonCharhound or (S.MarkofShatug:IsAvailable() and S.SummonGloomhound or S.SummonVilefiend)
            end        
        end

        MOCheck = (MouseOver:IsEnemy() or MouseOver:IsATank() or MouseOver:IsAMelee())

        -- Burst Potion
        if Target:IsSpellInRange(S.Corruption) then
            if MainAddon.UsePotion() then
                MainAddon.SetTopColor(1, "Combat Potion")
            end
        end

        local ShouldReturn = Defensives();
        if ShouldReturn then
            return ShouldReturn;
        end
        local ShouldReturn = Utilities();
        if ShouldReturn then
            return ShouldReturn;
        end

        if M.TargetIsValid() then
            -- Trinkets
            local shouldReturn = MainAddon.TrinketDPS()
            if shouldReturn then
                return shouldReturn
            end  

            -- call precombat
            if not Player:AffectingCombat() and not (Player:IsCasting(S.Demonbolt) or Player:IsCasting(S.ShadowBolt)) then
                local ShouldReturn = Precombat(); if ShouldReturn then return ShouldReturn; end
            end
            -- Manually added: unending_resolve
            if S.UnendingResolve:IsReady() and (Player:HealthPercentage() < 40) then
                if Cast(S.UnendingResolve) then return "unending_resolve defensive"; end
            end        
            -- call_action_list,name=variables
            Variables()    
            -- call_action_list,name=racials,if=pet.demonic_tyrant.active|fight_remains<22,use_off_gcd=1
            if (DemonicTyrantActive() or FightRemains < 22) then
                local ShouldReturn = Racials(); if ShouldReturn then return ShouldReturn; end
            end
            -- call_action_list,name=items,use_off_gcd=1
                local ShouldReturn = Items(); if ShouldReturn then return ShouldReturn;
            end
            -- invoke_external_buff,name=power_infusion,if=fight_remains<15|pet.demonic_tyrant.active&fight_remains<100|fight_remains<25|(pet.demonic_tyrant.active|!talent.summon_demonic_tyrant&buff.dreadstalkers.up)
            -- Note: Not handling external buffs
            -- call_action_list,name=fight_end,if=fight_remains<30
            if FightRemains < 30 then
                local ShouldReturn = FightEnd(); if ShouldReturn then return ShouldReturn; end
            end
            -- call_action_list,name=opener,if=time<variable.first_tyrant_time
            if HL.CombatTime() < VarFirstTyrantTime then
                local ShouldReturn = Opener(); if ShouldReturn then return ShouldReturn; end
            end
            -- call_action_list,name=tyrant,if=cooldown.summon_demonic_tyrant.remains<gcd.max*14
            if S.SummonDemonicTyrant:CooldownRemains() < Player:GCD() * 14 then
                local ShouldReturn = Tyrant(); if ShouldReturn then return ShouldReturn; end
            end
            -- hand_of_guldan,if=active_enemies>3&(pet.greater_dreadstalker.remains&pet.greater_dreadstalker.remains>gcd.max&pet.greater_dreadstalker.remains<gcd.max*3)&cooldown.call_dreadstalkers.remains>gcd.max*3&cooldown.summon_vilefiend.remains>gcd.max*2
            if S.HandofGuldan:IsReady() and (EnemiesCount8ySplash > 3 and (GreaterDreadstalkerActive() and GreaterDreadstalkerTime() > Player:GCD() and GreaterDreadstalkerTime() < Player:GCD() * 3) and S.CallDreadstalkers:CooldownRemains() > Player:GCD() * 3 and S.SummonVilefiend:CooldownRemains() > Player:GCD() * 2) then
                if Cast(S.HandofGuldan) then return "hand_of_guldan main 4"; end
            end
            -- call_dreadstalkers,if=cooldown.summon_demonic_tyrant.remains>25|variable.next_tyrant_cd>25
            if S.CallDreadstalkers:IsReady() and (S.SummonDemonicTyrant:CooldownRemains() > 25 or VarNextTyrantCD > 25) then
                if Cast(S.CallDreadstalkers) then return "call_dreadstalkers main 6"; end
            end
            -- summon_vilefiend,if=cooldown.summon_demonic_tyrant.remains>30
            if VilefiendAbility:IsReady() and (S.SummonDemonicTyrant:CooldownRemains() > 30) then
                if Cast(VilefiendAbility) then return "summon_vilefiend main 8"; end
            end
            -- demonbolt,target_if=min:debuff.doom.remains,if=buff.demonic_core.react&(!talent.doom|buff.demonic_core.react>1|debuff.doom.remains>10|debuff.doom.down)&(((!talent.fel_invocation|pet.felguard.cooldown.soul_strike.remains>gcd.max*2)&soul_shard<4))&!prev_gcd.1.demonbolt&!variable.pool_cores_for_tyrant
            if S.Demonbolt:IsReady() and (Player:BuffUp(S.DemonicCoreBuff) and (not S.Doom:IsAvailable() or DemonicCoreStacks > 1 or Target:DebuffRemains(S.DoomDebuff) > 10 or Target:DebuffDown(S.DoomDebuff)) and ((not S.FelInvocation:IsAvailable() or S.SoulStrikePetAbility:CooldownRemains() > Player:GCD() * 2) and SoulShards < 4) and not Player:PrevGCDP(1, S.Demonbolt) and not VarPoolCoresForTyrant) then
                if CastTargetIf(S.Demonbolt, Enemies8ySplash, "min", EvaluateCycleDemonbolt, nil) then return "demonbolt main 10"; end
            end
            -- demonbolt,target_if=min:debuff.doom.remains,if=buff.demonic_core.stack>=3-(talent.doom&debuff.doom.down)*2&soul_shard<=3&!variable.pool_cores_for_tyrant
            if S.Demonbolt:IsReady() and (DemonicCoreStacks >= 3 - num(S.Doom:IsAvailable() and Target:DebuffDown(S.DoomDebuff)) * 2 and SoulShards <= 3 and not VarPoolCoresForTyrant) then
                if CastTargetIf(S.Demonbolt, Enemies8ySplash, "min", EvaluateTargetIfDemonbolt, nil) then return "demonbolt main 12"; end
            end
            -- power_siphon,if=buff.demonic_core.stack<3&cooldown.summon_demonic_tyrant.remains>25
            if S.PowerSiphon:IsReady() and (DemonicCoreStacks < 3 and S.SummonDemonicTyrant:CooldownRemains() > 25) then
                if Cast(S.PowerSiphon) then return "power_siphon main 14"; end
            end
            -- demonic_strength,if=active_enemies>1
            if S.DemonicStrength:IsReady() and (EnemiesCount8ySplash > 1) then
                if Cast(S.DemonicStrength) then return "demonic_strength main 16"; end
            end
            -- bilescourge_bombers,if=active_enemies>1
            if S.BilescourgeBombers:IsReady() and (EnemiesCount8ySplash > 1) then
                if CastMagic(S.BilescourgeBombers, nil, "267171-Magic", magicgroundspell_bombers) then return "bilescourge_bombers main 18"; end
            end
            -- guillotine,if=active_enemies>1&(cooldown.demonic_strength.remains|!talent.demonic_strength)&(!raid_event.adds.exists|raid_event.adds.exists&raid_event.adds.remains>6)
            if S.Guillotine:IsReady() and (EnemiesCount8ySplash > 1 and (S.DemonicStrength:CooldownDown() or not S.DemonicStrength:IsAvailable())) then
                if CastMagic(S.Guillotine, nil, "386833-Magic", magicgroundspell_guillotine) then return "guillotine main 20"; end
            end
            -- ruination
            if S.RuinationAbility:IsReady() then
                if Cast(S.RuinationAbility) then return "ruination main 22"; end
            end
            -- hand_of_guldan,if=active_enemies>3&active_enemies<8&soul_shard>=2&set_bonus.tww2_4pc&buff.dreadstalkers.up|active_enemies>7&active_enemies<16&soul_shard>=2&set_bonus.tww2_4pc&buff.tyrant.remains>cast_time
            if S.HandofGuldan:IsReady() and (EnemiesCount8ySplash > 3 and EnemiesCount8ySplash < 8 and SoulShards >= 2 and Player:HasTier("TWW2", 4) and DreadstalkerActive() or EnemiesCount8ySplash > 7 and EnemiesCount8ySplash < 16 and SoulShards >= 2 and Player:HasTier("TWW2", 4) and DemonicTyrantTime() > S.HandofGuldan:CastTime()) then
                if Cast(S.HandofGuldan) then return "hand_of_guldan main 24"; end
            end
            -- implosion,if=active_enemies>3&set_bonus.tww2_4pc&buff.wild_imps.stack>7&!buff.demonic_core.react&!prev_gcd.1.implosion|!set_bonus.tww2_4pc&active_enemies>2&two_cast_imps>2&!prev_gcd.1.implosion&variable.impl
            if S.Implosion:IsReady() and (EnemiesCount8ySplash > 3 and Player:HasTier("TWW2", 4) and WildImpsCount() > 7 and Player:BuffDown(S.DemonicCoreBuff) and not Player:PrevGCDP(1, S.Implosion) or not Player:HasTier("TWW2", 4) and EnemiesCount8ySplash > 2 and CheckImpCasts(2) > 2 and not Player:PrevGCDP(1, S.Implosion) and VarImpl) then
                if Cast(S.Implosion) then return "implosion main 26"; end
            end
            -- infernal_bolt,if=soul_shard<3&cooldown.summon_demonic_tyrant.remains>20
            if S.InfernalBolt:IsReady() and (SoulShards < 3 and S.SummonDemonicTyrant:CooldownRemains() > 20) then
                if Cast(S.InfernalBolt) then return "infernal_bolt main 28"; end
            end
            -- demonbolt,if=variable.diabolic_ritual_remains>gcd.max&variable.diabolic_ritual_remains<gcd.max+gcd.max&buff.demonic_core.up&soul_shard<=3
            if S.Demonbolt:IsReady() and (VarDiabolicRitualRemains > Player:GCD() and VarDiabolicRitualRemains < Player:GCD() * 2 and Player:BuffUp(S.DemonicCoreBuff) and SoulShards <= 3) then
                if Cast(S.Demonbolt) then return "demonbolt main 30"; end
            end
            -- shadow_bolt,if=variable.diabolic_ritual_remains>gcd.max&variable.diabolic_ritual_remains<soul_shard.deficit*cast_time+gcd.max&soul_shard<5
            if S.ShadowBolt:IsReady() and (VarDiabolicRitualRemains > Player:GCD() and VarDiabolicRitualRemains < (5 - SoulShards) * S.ShadowBolt:CastTime() + Player:GCD() and SoulShards < 5) then
                if Cast(S.ShadowBolt) then return "shadow_bolt main 32"; end
            end
            -- hand_of_guldan,if=((soul_shard>2&(cooldown.call_dreadstalkers.remains>gcd.max*4|buff.demonic_calling.remains-gcd.max>cooldown.call_dreadstalkers.remains)&cooldown.summon_demonic_tyrant.remains>17)|soul_shard=5|soul_shard=4&talent.fel_invocation)&(active_enemies=1)
            if S.HandofGuldan:IsReady() and (((SoulShards > 2 and (S.CallDreadstalkers:CooldownRemains() > Player:GCD() * 4 or Player:BuffRemains(S.DemonicCallingBuff) - Player:GCD() > S.CallDreadstalkers:CooldownRemains()) and S.SummonDemonicTyrant:CooldownRemains() > 17) or SoulShards == 5 or SoulShards == 4 and S.FelInvocation:IsAvailable()) and (EnemiesCount8ySplash == 1)) then
                if Cast(S.HandofGuldan) then return "hand_of_guldan main 34"; end
            end
            -- hand_of_guldan,if=soul_shard>2&!(active_enemies=1)
            if S.HandofGuldan:IsReady() and (SoulShards > 2 and not (EnemiesCount8ySplash == 1)) then
                if Cast(S.HandofGuldan) then return "hand_of_guldan main 36"; end
            end
            -- demonbolt,target_if=(!debuff.doom.up)|active_enemies<4,if=buff.demonic_core.stack>1&((soul_shard<4&!talent.soul_strike|pet.felguard.cooldown.soul_strike.remains>gcd.max*2&talent.fel_invocation)|soul_shard<3)&!variable.pool_cores_for_tyrant
            if S.Demonbolt:IsReady() and (DemonicCoreStacks > 1 and ((SoulShards < 4 and not S.SoulStrike:IsAvailable() or S.SoulStrike:CooldownRemains() > Player:GCD() * 2 and S.FelInvocation:IsAvailable()) or SoulShards < 3) and not VarPoolCoresForTyrant) then
                if CastCycle(S.Demonbolt, Enemies8ySplash, EvaluateCycleDemonbolt2) then return "demonbolt main 38"; end
            end
            -- demonbolt,if=buff.demonic_core.react&buff.tyrant.up&soul_shard<3
            if S.Demonbolt:IsReady() and (Player:BuffUp(S.DemonicCoreBuff) and DemonicTyrantActive() and SoulShards < 3) then
                if Cast(S.Demonbolt) then return "demonbolt main 40"; end
            end
            -- demonbolt,if=buff.demonic_core.react>1&soul_shard<4
            if S.Demonbolt:IsReady() and (DemonicCoreStacks > 1 and SoulShards < 4) then
                if Cast(S.Demonbolt) then return "demonbolt main 42"; end
            end
            -- shadow_bolt
            if S.ShadowBolt:IsReady() then
                if Cast(S.ShadowBolt) then return "shadow_bolt main 44"; end
            end
            -- infernal_bolt
            if S.InfernalBolt:IsReady() then
                if Cast(S.InfernalBolt) then return "infernal_bolt main 46"; end
            end
        end
    end

    local function Init()
        Warlock.LoadEvent()
        S.DoomBrandDebuff:RegisterAuraTracking()

        C_Timer.After(5, function()
            if Player:Level() >= 19 and S.PetSuccubus:IsAvailable() and not Action.FindBySpellID(S.Whiplash:ID()) then
                StaticPopupDialogs["LOCKPOPUP"] = {
                    text = "??: This rotations needs the Whiplash (Succubus/Sayaad Spell) on your action bar to function properly. Put on your action bar, regardless if you're going to use the succubus or not.",
                    button1 = "OK",
                    OnShow = function(self)
                        self.button1:Disable()
                    end,
                    OnUpdate = function(self)
                        if Action.FindBySpellID(S.Whiplash:ID()) then
                            self.button1:Enable()
                        end
                    end,
                }
                StaticPopup_Show("LOCKPOPUP")
            end
        end)
    end
    M.SetAPL(266, APL, Init)

    HL:RegisterForEvent(function(_, source, destName, _, spellID)
        if source == "player" then
            if spellID == 30146 then
                SummoningFelguard = true
            end
        end
    end, "UNIT_SPELLCAST_SENT")

    HL:RegisterForEvent(function(_, unitID, _, spellID)
        if unitID == "player" then
            if spellID == 30146 then
                C_Timer.After(1.3, function()
                    SummoningFelguard = false
                end)
            end
        end
    end, "UNIT_SPELLCAST_STOP")

    HL:RegisterForEvent(function(_, unitID, arg3, spellID)
        if unitID == "player" then
            if spellID == 30146 then
                C_Timer.After(1.3, function()
                    SummoningFelguard = false
                end)
            end
        end
    end, "UNIT_SPELLCAST_SUCCEEDED")

      -- Override
      local DemoOldSpellIsCastable
      DemoOldSpellIsCastable = HL.AddCoreOverride("Spell.IsCastable",
          function (self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
            local BaseCheck, Reason = DemoOldSpellIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
            if MainAddon.PlayerSpecID() == 266 then
                if self == S.SummonPet and SummoningFelguard then
                    return false, "Summoning Felguard already"
                end
                
                if self == S.Demonbolt and Player:BuffUp(S.DemonicCoreBuff) then
                    ignoreMovement = true
                end
                
                if self == S.SummonPet then
                    return BaseCheck and (not Pet:IsActive()) and Player:SoulShardsP() > 0 and not Player:IsCasting(self)
                elseif self == S.SummonDemonicTyrant then
                    return BaseCheck and not Player:IsCasting(self)
                -- Custom addition
                elseif self == S.InfernalBolt or self == S.RuinationAbility then
                    return BaseCheck and not Player:IsCasting(self)
                end
                return BaseCheck, Reason
            end
            return BaseCheck, Reason
          end
      , 266)

      local DemoOldSpellIsReady
      DemoOldSpellIsReady = HL.AddCoreOverride ("Spell.IsReady",
          function (self, TargetetedUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
              local BaseCheck, Reason = DemoOldSpellIsReady(self, TargetetedUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
              if MainAddon.PlayerSpecID() == 266 then
                if GetSetting('MOOption', false) then
                    if self == S.Guillotine or self == S.BilescourgeBombers then
                        if not MOCheck or HL.CombatTime() < 1 then
                            return false, "MOCheck is false"
                        end
                    end
                end

              if M.Toggle:GetToggle('HoldingBurst') then
                 if self == S.CallDreadstalkers or self == S.Implosion then
                     return false, "Holding Burst"
                 end
              end
              if (self == S.DemonicStrength or self == S.Guillotine) and Pet:NPCID() ~= 17252 then
                  return false, "Wrong Pet"
              end

              if self == S.SummonVilefiend or self == S.SummonCharhound or self == S.SummonGloomhound or self == S.GrimoireFelguard then
                return BaseCheck and Player:SoulShardsP() >= 1 and not Player:IsCasting(self), "SoulShardsP"
              elseif self == S.CallDreadstalkers then
                return BaseCheck and (Player:SoulShardsP() >= 2 or Player:BuffUp(S.DemonicCallingBuff)) and not Player:IsCasting(self)
              elseif self == S.SummonSoulkeeper then
                return BaseCheck and not Player:IsCasting(self)
              elseif self == S.HandofGuldan then
                return (BaseCheck or Player:IsCasting(S.RuinationAbility)) and Player:SoulShardsP() >= 1
            elseif self == S.PowerSiphon then
                return BaseCheck and Warlock.GuardiansTable.ImpCount > 0
              else
                return BaseCheck, Reason
              end
            end
            return BaseCheck, Reason
          end
      , 266)

      HL.AddCoreOverride ("Player.SoulShardsP",
        function ()
            local Shard = Player:SoulShards()
            Shard = floor(Shard)
            if MainAddon.PlayerSpecID() == 266 then
                if not Player:IsCasting() then
                    return Shard
                else
                    if Player:IsCasting(S.SummonDemonicTyrant) and S.SoulboundTyrant:IsAvailable() then
                        return mathmin(Shard + 3, 5)
                    elseif Player:IsCasting(S.Demonbolt) then
                        return mathmin(Shard + 2, 5)
                    elseif Player:IsCasting(S.ShadowBolt) or Player:IsCasting(S.SoulStrike) then
                        return mathmin(Shard + 1, 5)
                    elseif Player:IsCasting(S.HandofGuldan) then
                        return mathmin(Shard - 3, 0)
                    elseif Player:IsCasting(S.CallDreadstalkers) then
                        return Shard - 2
                    elseif Player:IsCasting(S.SummonVilefiend) or Player:IsCasting(S.SummonPet) or Player:IsCasting(S.NetherPortal) then
                        return Shard - 1
                    else
                        return Shard
                    end
                end
            end
            return Shard
        end
        , 266)

        local AffliOldPlayerAffectingCombat
        AffliOldPlayerAffectingCombat = HL.AddCoreOverride("Player.AffectingCombat",
        function (self)
            if MainAddon.PlayerSpecID() == 266 then
                return AffliOldPlayerAffectingCombat(self)
                or Player:IsCasting(S.Demonbolt)
                or Player:IsCasting(S.ShadowBolt)
            end
            return AffliOldPlayerAffectingCombat(self)
        end
        , 266)

        local DemonologyOldSpellCooldownRemains
        DemonologyOldSpellCooldownRemains = HL.AddCoreOverride("Spell.CooldownRemains",
                function(self, BypassRecovery, BypassCD)
                    if MainAddon.PlayerSpecID() == 266 then
                        if self == S.SummonDemonicTyrant and Player:IsCasting(self) then
                            return 90
                        else
                            return DemonologyOldSpellCooldownRemains(self, BypassRecovery, BypassCD)
                        end
                    end
                    return DemonologyOldSpellCooldownRemains(self, BypassRecovery, BypassCD)
                end
        , 266)
end