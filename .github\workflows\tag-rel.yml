name: Create Release

on:
  push:
    tags:
      - '*'

jobs:
  release:
    runs-on: ubuntu-latest
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      WAGO_API_TOKEN: ${{ secrets.WAGO_API_TOKEN }}
    steps:
      # Do the release checkout
      - name: Clone Project
        uses: actions/checkout@v1
      - name: Get Tag
        id: vars
        run: |
          echo "tag=${GITHUB_REF#refs/*/}" >> $GITHUB_ENV
          echo "oldtag=$(git describe --abbrev=0 --tags $(git rev-list --tags --skip=1 --max-count=1))" >> $GITHUB_ENV
      # Make a zip file for GitHub
      - name: Archive Release
        uses: thedoctor0/zip-release@master
        with:
          filename: HeroRotation-${{ env.tag }}.zip
          exclusions: '*.git* *.pkgmeta *.svn* .editorconfig LICENSE README.md symlink.bat symlink.sh HeroRotation_ClassTemplate HeroRotation_ClassTemplate/*'
      # Create a release on GitHub
      - name: Create Release
        id: create_release
        uses: ncipollo/release-action@v1.11.1
        with:
          tag: ${{ github.ref }}
          name: HeroRotation
          body: |
            Changes: https://github.com/herotc/hero-rotation/compare/${{ env.oldtag }}...${{ env.tag }}
          draft: false
          prerelease: false
      # Upload the zip to the GitHub release
      - name: Upload Zip
        id: upload-zip
        uses: shogo82148/actions-upload-release-asset@v1.6.3
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./HeroRotation-${{ env.tag }}.zip
          asset_name: HeroRotation-${{ env.tag }}.zip
          asset_content_type: application/zip
      # Use the BigWigs packager for Wago output
      - name: Create Package
        uses: BigWigsMods/packager@master
        env:
            WAGO_API_TOKEN: ${{ secrets.WAGO_API_TOKEN }}
        with:
            args: -g retail -a jK8XJxKy -n "${{ env.tag }}"