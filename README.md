# HeroLib

[![GitHub license](https://img.shields.io/badge/license-EUPL-blue.svg)](https://raw.githubusercontent.com/herotc/hero-lib/master/LICENSE)
[![GitHub contributors](https://img.shields.io/github/contributors/herotc/hero-lib)](https://github.com/herotc/hero-lib/graphs/contributors)
[![GitHub forks](https://img.shields.io/github/forks/herotc/hero-lib.svg)](https://github.com/herotc/hero-lib/network)
[![GitHub stars](https://img.shields.io/github/stars/herotc/hero-lib.svg)](https://github.com/herotc/hero-lib/stargazers)\
[![GitHub issues](https://img.shields.io/github/issues/herotc/hero-lib.svg)](https://github.com/herotc/hero-lib/issues?q=is%3Aopen+is%3Aissue)
[![GitHub pull requests](https://img.shields.io/github/issues-pr/herotc/hero-lib)](https://github.com/herotc/hero-lib/pulls?q=is%3Aopen+is%3Apr)
[![GitHub closed issues](https://img.shields.io/github/issues-closed/herotc/hero-lib)](https://github.com/herotc/hero-lib/issues?q=is%3Aissue+is%3Aclosed)
[![GitHub closed pull requests](https://img.shields.io/github/issues-pr-closed/herotc/hero-lib)](https://github.com/herotc/hero-lib/pulls?q=is%3Apr+is%3Aclosed)\
[![GitHub release](https://img.shields.io/github/v/release/herotc/hero-lib)](https://github.com/herotc/hero-lib/releases)
[![GitHub Release Date](https://img.shields.io/github/release-date/herotc/hero-lib)](https://github.com/herotc/hero-lib/releases)
[![GitHub commits since latest release (by date)](https://img.shields.io/github/commits-since/herotc/hero-lib/latest)](https://github.com/herotc/hero-lib/commits/master)
[![GitHub last commit](https://img.shields.io/github/last-commit/herotc/hero-lib)](https://github.com/herotc/hero-lib/commits/master)

HeroLib is a World of Warcraft addon to provide a simple and clean API to get most advanced WoW features.\
It acts as a complete replacement of the WoW API and makes easy-to-use some non directly available informations in-game like the TimeToDie of an unit.\

It is really simple to use and is used by [HeroRotation](https://github.com/herotc/hero-rotation) and [AethysTools](https://github.com/aethys256/AethysTools).\
You can also use anything from the addon in a WeakAura.\
The project is hosted on [GitHub](https://github.com/herotc/hero-lib) and powered by [HeroDBC](https://github.com/herotc/hero-dbc).\
It is maintained by [Aethys](https://github.com/aethys256/) and the [HeroTC](https://github.com/herotc) team.\
Also, you can find it on [CurseForge](https://www.curseforge.com/wow/addons/herolib).\

Feel free to join our [Discord](https://discord.gg/tFR2uvK). Feedback is highly appreciated !

## Advanced Users / Developer Notes

If you want to use the addon directly from the [GitHub repository](https://github.com/herotc/hero-lib), you would have to symlink every folders from this repository (HeroLib & HeroCache folders) to your WoW Addons folder.

## Special Thanks

- [SimulationCraft](http://simulationcraft.org/) for everything the project gives to the whole WoW Community.
- [KutiKuti](https://github.com/Kutikuti) & [Nia](https://github.com/Nianel) for their daily support.
- [Skasch](https://github.com/skasch) for what we built together and the motivation he gave to me.
- [Mystler](https://github.com/Mystler) & [Kojiyama](https://github.com/EvanMichaels) & [Fuu](https://github.com/fuu1) for their work on everything related to rogues that frees me a lot of time.
- [NuoHep](https://github.com/nuoHep) for his help on performance improvements.

Stay tuned !
Aethys
