function A_105(...)
     -- Addon Initialization
     ---@class MainAddon
     local MainAddon = MainAddon
     ---@class MainAddon
     local M = MainAddon
     ---@class HealingEngine
     local HealingEngine = MainAddon.HealingEngine
     
     -- HeroLib Setup
     local HL = HeroLibEx
     ---@class HeroCache
     local Cache = HeroCache
     ---@class Unit
     local Unit = HL.Unit
     ---@class Unit
     local Player = Unit.Player
     ---@class Unit
     local Target = Unit.Target
     ---@class Unit
     local MouseOver = Unit.MouseOver
     ---@class Unit
     local Focus = Unit.Focus
     ---@class Spell
     local Spell = HL.Spell
     ---@class Item
     local Item = HL.Item
     
     -- HeroRotation Functions
     local AoEON = M.AoEON
     local Cast = M.Cast
     local ForceCastDisplay = M.ForceCastDisplay
     local CastCycleAlly = M.CastCycleAlly
     local CastTargetIfAlly = M.CastTargetIfAlly
     local CastAlly = M.CastAlly
     local CastMagicAlly = M.CastMagicAlly
     local CastMagic = M.CastMagic
     local CastCycle = M.CastCycle
     
     -- LUA Functions
     local GetMouseFoci = _G['GetMouseFoci']
     local GetTotemInfo = _G['GetTotemInfo']
     local GetSpellBonusDamage = _G['GetSpellBonusDamage']
     local GetTime = _G['GetTime']
     local IsIndoors = _G['IsIndoors']
     local CombatLogGetCurrentEventInfo = _G['CombatLogGetCurrentEventInfo']
     local IsInGroup = _G['IsInGroup']
     local wipe = _G['wipe']
     local C_Timer = _G['C_Timer']
     local pairs = _G['pairs']
     local GetNumGroupMembers = _G['GetNumGroupMembers']
     local UnitCanAttack = _G['UnitCanAttack']
 
     -- Class Specific Setup
     local Druid = MainAddon.Druid
     local S = Spell.Druid.Restoration
     local I = Item.Druid.Restoration
  
     -- Exclude these items from automatic trinket usage
     local OnUseExcludes = {
         I.IridaltheEarthsMaster:ID(),
         I.Dreambinder:ID()
     }
 
     -- =============================================
     -- Toggle Settings Configuration
     -- =============================================
     MainAddon.Toggle.Special["BearMode"] = {
         Icon = MainAddon.GetTexture(S.BearForm),
         Name = "Bear Mode",
         Description = "Bear Mode.",
         Spec = 105,
     }
     
     MainAddon.Toggle.Special["SpreadRejuvenation"] = {
         Icon = MainAddon.GetTexture(S.Rejuvenation),
         Name = "Rejuvenation",
         Description = "Spread Rejuvenation.",
         Spec = 105,
     }
     
     MainAddon.Toggle.Special["Ramp"] = {
         Icon = MainAddon.GetTexture(S.WildGrowth),
         Name = "Ramp logic toggle",
         Description = "Ramp logic toggle.",
         Spec = 105,
     }
     
     -- YUNO: commented out for now, usually we dont do this
     MainAddon.Toggle.Special["ReforestationPooling"] = {
         Icon = MainAddon.GetTexture(S.Reforestation),
         Name = "Reforestation",
         Description = "Pooling Reforestation.",
         Spec = 105,
     }
     
     MainAddon.Toggle.Special["ForceDPS"] = {
         Icon = MainAddon.GetTexture(S.CatForm),
         Name = "Force DPS",
         Description = "This toggle will force DPS.",
         Spec = 105,
     }
     
     -- =============================================
     -- GUI Configuration
     -- =============================================
     local GetSetting = MainAddon.Config.GetClassSetting
     local Config_Key = MainAddon.GetClassVariableName()
     local Config_Color = 'FF7C0A'
     local Config_Table = {
         key = Config_Key,
         title = 'Druid - Restoration',
         subtitle = '?? ' .. MainAddon.Version,
         width = 600,
         height = 700,
         profiles = true,
         config = {
             -- General Header
             { type = 'header', text = '', size = 24, align = 'Center', color = Config_Color },
             { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
             
             -- Single Target Healing Section
             { type = 'header', text = 'Single Target Healing', color = Config_Color },
             { type = 'spacer' },
             
             -- Regrowth Configuration
             { type = 'header', text = 'Regrowth', size = 14, align = 'Left', color = Config_Color },
             { type = 'spinner', text = 'Health Threshold (%)', key = 'RegrowthHP', icon = S.Regrowth:ID(), min = 1, max = 100, default = 75 },
             { type = 'spinner', text = 'Tree of Life Threshold (%)', key = 'RegrowthHPToL', icon = S.Regrowth:ID(), min = 1, max = 100, default = 85 },
             { type = 'spinner', text = 'Emergency Threshold (%)', key = 'RegrowthHP2', icon = S.Regrowth:ID(), min = 1, max = 100, default = 50 },
             { type = 'spinner', text = 'Clear Casting Threshold (%)', key = 'RegrowthHPClearCasting', icon = S.Regrowth:ID(), min = 1, max = 100, default = 85 },
             { type = 'spinner', text = 'Filler Threshold (%)', key = 'RegrowthHPFiller', icon = S.Regrowth:ID(), min = 1, max = 100, default = 85 },
             { type = 'spinner', text = 'Abundance Stack Count', key = 'RegrowthAbundanceStack', icon = S.Abundance:ID(), min = 1, max = 20, default = 15 },
             { type = 'spacer' },
             
             -- Rejuvenation Configuration
             { type = 'header', text = 'Rejuvenation', size = 14, align = 'Left', color = Config_Color },
             { type = 'spinner', text = 'Health Threshold (%)', key = 'RejuvenationHP', icon = S.Rejuvenation:ID(), min = 1, max = 100, default = 85 },
             { type = 'spinner', text = 'Filler Threshold (%)', key = 'RejuvenationHPFiller', icon = S.Rejuvenation:ID(), min = 1, max = 100, default = 90 },
             { type = 'spacer' },
             
             -- Swiftmend Configuration
             { type = 'header', text = 'Swiftmend', size = 14, align = 'Left', color = Config_Color },
             { type = 'spinner', text = 'Without Soul of the Forest (%)', key = 'SwiftmendHP', icon = S.Swiftmend:ID(), min = 1, max = 100, default = 80 },
             { type = 'spacer' },
             
             -- Cenarion Ward Configuration
             { type = 'header', text = 'Cenarion Ward', size = 14, align = 'Left', color = Config_Color },
             { type = 'checkbox', text = 'Keep on Tank', key = 'tank_cenarion', icon = S.CenarionWard:ID(), default = false },
             { type = 'spacer' },
             
             -- Ironbark Configuration
             { type = 'header', text = 'Ironbark', size = 14, align = 'Left', color = Config_Color },
             { type = 'spinner', text = 'Group Member Threshold (%)', key = 'IronbarkHP', icon = S.Ironbark:ID(), min = 1, max = 100, default = 40 },
             { type = 'spinner', text = 'Tank Threshold (%)', key = 'IronbarkHP_Tanks', icon = S.Ironbark:ID(), min = 1, max = 100, default = 55 },
             { type = 'spacer' },
             
             -- Grove Guardians Configuration
             { type = 'header', text = 'Grove Guardians', size = 14, align = 'Left', color = Config_Color },
             { type = 'spinner', text = 'Health Threshold (%)', key = 'GroveGuardiansHP', icon = S.GroveGuardians:ID(), min = 1, max = 100, default = 75 },
             { type = 'spacer' },
             
             -- Group Healing Section
             { type = 'header', text = 'Group Healing', color = Config_Color },
             { type = 'spacer' },
             
             -- Wild Growth Configuration
             { type = 'header', text = 'Wild Growth', size = 14, align = 'Left', color = Config_Color },
             { type = 'spinner', text = 'Targets Required (%)', key = 'WildGrowth_underX', icon = S.WildGrowth:ID(), min = 1, max = 100, default = 45 },
             { type = 'spinner', text = 'Health Threshold (%)', key = 'WildGrowth_underX_val', icon = S.WildGrowth:ID(), min = 1, max = 100, default = 85 },
             { type = 'spacer' },
             
             -- Efflorescence Configuration
             { type = 'header', text = 'Efflorescence', size = 14, align = 'Left', color = Config_Color },
             { type = 'spinner', text = 'Targets Required (%)', key = 'Efflorescence_underX', icon = S.Efflorescence:ID(), min = 1, max = 100, default = 20 },
             { type = 'spinner', text = 'Health Threshold (%)', key = 'Efflorescence_underX_val', icon = S.Efflorescence:ID(), min = 1, max = 100, default = 97 },
             { type = 'dropdown', text = 'Placement Target', key = 'magicgroundspell', icon = S.Efflorescence:ID(),
                 list = {
                     { text = 'Friend', key = 1 },
                     { text = 'Enemy', key = 2 },
                     { text = 'No Magic', key = 3 },
                 },
                 default = 3
             },
             { type = 'spacer' },
             
             -- Flourish Configuration
             { type = 'header', text = 'Flourish', size = 14, align = 'Left', color = Config_Color },
             { type = 'spinner', text = 'Targets Required (%)', key = 'Flourish_underX', icon = S.Flourish:ID(), min = 1, max = 100, default = 45 },
             { type = 'spinner', text = 'Health Threshold (%)', key = 'Flourish_underX_val', icon = S.Flourish:ID(), min = 1, max = 100, default = 65 },
             { type = 'spinner', text = 'Minimum Active HoTs', key = 'Flourish_ActiveHots', icon = S.Flourish:ID(), min = 1, max = 5, default = 2 },
             { type = 'spacer' },
             
             -- Grove Guardians (Emergency) Configuration
             { type = 'header', text = 'Grove Guardians (Emergency)', size = 14, align = 'Left', color = Config_Color },
             { type = 'spinner', text = 'Targets Required (%)', key = 'GGEmergency_underX', icon = S.GroveGuardians:ID(), min = 1, max = 100, default = 45 },
             { type = 'spinner', text = 'Health Threshold (%)', key = 'GGEmergency_underX_val', icon = S.GroveGuardians:ID(), min = 1, max = 100, default = 60 },
             { type = 'spacer' },
             
             -- Major Cooldowns Section
             { type = 'header', text = 'Major Cooldowns', color = Config_Color },
             { type = 'spacer' },
             
             -- Convoke the Spirits Configuration
             { type = 'header', text = 'Convoke the Spirits', size = 14, align = 'Left', color = Config_Color },
             { type = 'spinner', text = 'Targets Required (%)', key = 'Convoke_underX', icon = S.ConvoketheSpirits:ID(), min = 1, max = 100, default = 30 },
             { type = 'spinner', text = 'Health Threshold (%)', key = 'Convoke_underX_val', icon = S.ConvoketheSpirits:ID(), min = 1, max = 100, default = 75 },
             { type = 'dropdown', text = 'Usage Mode', key = 'ConvokeOption', icon = S.ConvoketheSpirits:ID(), multiselect = true,
                 list = {
                     { text = 'Heal', key = 'ConvokeHeal' },
                     { text = 'Damage', key = 'ConvokeDamage' },
                 },
                 default = {
                     "ConvokeHeal",
                 },
             },
             { type = 'spacer' },
             
             -- Tree of Life Configuration
             { type = 'header', text = 'Tree of Life', size = 14, align = 'Left', color = Config_Color },
             { type = 'spinner', text = 'Targets Required (%)', key = 'INC_underX', icon = S.IncarnationTreeofLife:ID(), min = 1, max = 100, default = 45 },
             { type = 'spinner', text = 'Health Threshold (%)', key = 'INC_underX_val', icon = S.IncarnationTreeofLife:ID(), min = 1, max = 100, default = 60 },
             { type = 'spacer' },
             
             -- Tranquility Configuration
             { type = 'header', text = 'Tranquility', size = 14, align = 'Left', color = Config_Color },
             { type = 'spinner', text = 'Targets Required (%)', key = 'Tranquility_underX', icon = S.Tranquility:ID(), min = 1, max = 100, default = 45 },
             { type = 'spinner', text = 'Health Threshold (%)', key = 'Tranquility_underX_val', icon = S.Tranquility:ID(), min = 1, max = 100, default = 55 },
             { type = 'spinner', text = 'Stand Still Time (seconds)', key = 'Tranquility_StandStill', icon = S.Tranquility:ID(), min = 0, max = 10, default = 1 },
             { type = 'spacer' },
             
             -- Mana Management Section
             { type = 'header', text = 'Mana Management', color = Config_Color },
             { type = 'spacer' },
             
             -- Innervate Configuration
             { type = 'header', text = 'Innervate', size = 14, align = 'Left', color = Config_Color },
             { type = 'checkbox', text = 'Enable Mana Management', key = 'ManaManagement', default = true },
             { type = 'spinner', text = 'Mana Threshold (%)', key = 'InnervateMana', icon = S.Innervate:ID(), min = 1, max = 100, default = 60 },
             { type = 'checkbox', text = 'Use During Incarnation', key = 'ManaIncarnation', icon = S.Innervate:ID(), default = false },
             { type = 'spacer' },
             
             -- Defensive Abilities Section
             { type = 'header', text = 'Defensive Abilities', color = Config_Color },
             { type = 'spacer' },
             
             -- Barkskin Configuration
             { type = 'header', text = 'Barkskin', size = 14, align = 'Left', color = Config_Color },
             { type = 'checkspin', text = 'Health Threshold (%)', icon = S.Barkskin:ID(), key = 'BarkSkinHP', min = 1, max = 100, default_spin = 60, default_check = false },
             { type = 'spacer' },
             
             -- Renewal Configuration
             { type = 'header', text = 'Renewal', size = 14, align = 'Left', color = Config_Color },
             { type = 'spinner', text = 'Health Threshold (%)', key = 'RenewalHP', icon = S.Renewal:ID(), min = 1, max = 100, default = 70 },
             { type = 'spacer' },
             
             -- Bear Form Configuration
             { type = 'header', text = 'Bear Form', size = 14, align = 'Left', color = Config_Color },
             { type = 'checkbox', text = 'Enable Smart Bear', key = 'smartbear', icon = S.BearForm:ID(), default = true },
             { type = 'spinner', text = 'Above Key Level', key = 'smart_bear_above_key_level', icon = S.BearForm:ID(), min = 1, max = 40, default = 2 },
             { type = 'checkbox', text = 'Combine with Other Defensives', key = 'combine_bearform', icon = S.BearForm:ID(), default = false },
             { type = 'dropdown', text = 'Hold Bear Form Until', key = 'smartholdbear', icon = S.BearForm:ID(), multiselect = true,
                 list = {
                     { text = 'Ursine Vigor falls off', key = 'smartholdbear_uv' },
                     { text = 'Frenzied Regeneration falls off', key = 'smartholdbear_fg' },
                     { text = 'Danger is over', key = 'smartholdbear_over' },
                 },
                 default = {
                     'smartholdbear_uv',
                     'smartholdbear_fg'
                 },
             },
             { type = 'spacer' },
             
             -- DPS Options Section
             { type = 'header', text = 'DPS Options', color = Config_Color },
             { type = 'spacer' },
             
             -- DPS Method & Form Configuration
             { type = 'header', text = 'DPS Settings', size = 14, align = 'Left', color = Config_Color },
             { type = 'dropdown', text = 'DPS Method', key = 'DpsMethod', icon = 37406,
                 list = {
                     { text = 'Auto', key = 1 },
                     { text = 'Toggle', key = 2 },
                     { text = 'Disable', key = 3 },
                 },
                 default = 1
             },
             { type = 'dropdown', text = 'DPS Form', key = 'DpsForm', icon = 11975,
                 list = {
                     { text = 'Auto', key = 1 },
                     { text = 'Cat', key = 2 },
                     { text = 'Moonkin', key = 3 },
                     { text = 'Humanoid', key = 4 },
                 },
                 default = 1
             },
             { type = 'spacer' },
             
             -- Cat Form Options Configuration
             { type = 'header', text = 'Cat Form Options', size = 14, align = 'Left', color = Config_Color },
             { type = 'spinner', text = 'Maximum Range', key = 'cat_range', icon = S.CatForm:ID(), min = 5, max = 40, default = 10 },
             { type = 'checkbox', text = 'Override Cat Form', key = 'override_cat_form', icon = S.CatForm:ID(), default = false },
             { type = 'dropdown', text = 'Override Condition', key = 'override_cat_form_condition', icon = S.CatForm:ID(),
                 list = {
                     { text = '>=', key = 1 },
                     { text = '<=', key = 2 },
                     { text = '==', key = 3 },
                     { text = '>', key = 4 },
                     { text = '<', key = 5 },
                 },
                 default = 5
             },
             { type = 'spinner', text = 'Override Units Count', key = 'override_cat_form_units', icon = S.CatForm:ID(), min = 0, max = 20, default = 2 },
             { type = 'spacer' },
             
             -- Cat Weaving Configuration
             { type = 'header', text = 'Cat Weaving', size = 14, align = 'Left', color = Config_Color },
             { type = 'spinner', text = 'Stop - Targets Required', key = 'StopCatWeaving_underX', icon = S.CatForm:ID(), min = 1, max = 40, default = 1 },
             { type = 'spinner', text = 'Stop - Health Threshold (%)', key = 'StopCatWeaving_underX_val', icon = S.CatForm:ID(), min = 1, max = 100, default = 90 },
             { type = 'checkbox', text = 'Use Shadowmeld for DPS', key = 'shadowmeld_dps', icon = S.Shadowmeld:ID(), default = true },
             { type = 'spacer' },
             
             -- Movement & Utility Section
             { type = 'header', text = 'Movement & Utility', color = Config_Color },
             { type = 'spacer' },
             
             -- Forms Configuration
             { type = 'header', text = 'Forms', size = 14, align = 'Left', color = Config_Color },
             { type = 'checkbox', text = 'Auto-Travel Form (Out of Combat)', key = 'autotravel', icon = S.TravelForm:ID(), default = true },
             { type = 'checkbox', text = 'Auto Prowl', key = 'AutoProwl', icon = S.Prowl:ID(), default = false },
             { type = 'dropdown', text = 'Out of Combat Form', key = 'OOCForm', icon = S.CatForm:ID(),
                 list = {
                     { text = 'Bear Form', key = 'BearForm' },
                     { text = 'Cat Form', key = 'CatForm' },
                     { text = 'Travel Form', key = 'TravelForm' },
                     { text = 'Treant Form', key = 'TreantForm' },
                     { text = 'None', key = 'NoneForm' },
                 },
                 default = "NoneForm"
             },
             { type = 'spacer' },
             
             -- Utility Spells Configuration
             { type = 'header', text = 'Utility Spells', size = 14, align = 'Left', color = Config_Color },
             { type = 'dropdown', text = 'Rebirth', key = 'autorebirth', multiselect = true, icon = S.Rebirth:ID(),
                 list = {
                     { text = 'Target', key = 'autorebirth_target' },
                     { text = 'MouseOver', key = 'autorebirth_mouseover' },
                 },
                 default = {},
             },
             { type = 'dropdown', text = 'Mark of the Wild', key = 'motw', icon = S.MarkoftheWild:ID(), multiselect = true,
                 list = {
                     { text = 'Self', key = 'motw_self' },
                     { text = 'Friends', key = 'motw_friends' },
                 },
                 default = {
                     'motw_self',
                     'motw_friends'
                 },
             },
             { type = 'checkbox', text = "Typhoon only with Ursol's Vortex", key = 'typhoon_vortex', icon = S.Typhoon:ID(), default = false },
             { type = 'spacer' },
             { type = 'spacer' },
             { type = 'ruler' },
             { type = 'spacer' },
         }
     }
     
     -- Build additional UI components
     Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
     Config_Table.config = MainAddon.BuildHealingTrinketUI(Config_Table.config, Config_Color)
     Config_Table.config = MainAddon.BuildCombatManaPotionUI(Config_Table.config, "Restoration", Config_Color)
     M.SetConfig(105, Config_Table)
  
     -- =============================================
     -- Variable Declarations
     -- =============================================
     local ShouldReturn
     local Reason, SpellID = nil, nil
     
     -- Lifebloom spell selection based on availability
     local Lifebloom = S.LifebloomResto:IsAvailable() and S.LifebloomResto or S.LifebloomResto2
     HL:RegisterForEvent(function()
         Lifebloom = S.LifebloomResto:IsAvailable() and S.LifebloomResto or S.LifebloomResto2
     end, "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")
     
     -- Group member categories
     local Tanks, Healers, Members, Damagers, Melees, TargetIfAlly
     
     -- Settings and variables storage
     local Settings = {}
     local Var = {}
     
     -- Temporary blacklists
     local TempBlackListRegrowth = {}
     local TempBlackListSymbioticRelationship = {}
     
     -- Enemy tracking variables
     local Enemies5y = {}
     local EnemiesCount5y = 0
     local Enemies10ySplash = {}
     local EnemiesCount10ySplash = 0
     local Enemies40y = {}
     
     -- Special variables
     Var['TankUnit'] = Player
     Var['LastTimeEfflorescenceHealed'] = 0
     Var['LastTimeEfflorescenceCast'] = 0
     Var['LastTimeGroveGuardiansCast'] = 0
     Var['CombatMonitor_TimeStamp'] = GetTime()
     Var['CombatMonitor_State'] = false
     Var['UpdateLifebloom'] = true
  
     -- =============================================
     -- Helper Functions
     -- =============================================
 
     --- Evaluates priority healing status for mouseover/target
     local function EvaluatePrioHealing()
         local priohealing_target = MainAddon.Config.GetSetting("CoreUI", 'priohealing_target')
 
         if priohealing_target[1] and MouseOver:Exists() or priohealing_target[2] and Target:Exists() then
             ---@param ThisUnit Unit
             for i, ThisUnit in pairs(Members) do
                 if priohealing_target[1] and MouseOver:Exists() and MouseOver:GUID() == ThisUnit:GUID() and MouseOver:HealthPercentage() ~= ThisUnit:HealthPercentage() then
                     return true
                 end
 
                 if priohealing_target[2] and Target:Exists() and Target:GUID() == ThisUnit:GUID() and Target:HealthPercentage() ~= ThisUnit:HealthPercentage() then
                     return true
                 end
             end
         end
         return false
     end
 
     --- Evaluates if Rake should be applied to a target
     ---@param ThisUnit Unit
     local function EvaluateRake(ThisUnit)
         return ThisUnit:DebuffRefreshable(S.RakeDebuff) and ThisUnit:TimeToDie() > 8
     end
 
     --- Evaluates if Rip should be applied to a target
     ---@param ThisUnit Unit
     local function EvaluateRip(ThisUnit)
         return ThisUnit:DebuffRefreshable(S.Rip) and ThisUnit:TimeToDie() > 12
     end
 
     --- Monitors combat state based on tank activity
     local function combatMonitor()
         if GetTime() - Var['CombatMonitor_TimeStamp'] < 1 then
             return Var['CombatMonitor_State']
         end
         
         if Tanks then
             ---@param TankUnit Unit
             for _, TankUnit in pairs(Tanks) do
                 if TankUnit:AffectingCombat() then
                     Var['CombatMonitor_TimeStamp'] = GetTime()
                     Var['CombatMonitor_State'] = true
                     return true
                 end
             end
         end
  
         Var['CombatMonitor_TimeStamp'] = GetTime()
         Var['CombatMonitor_State'] = false
         return false
     end
     
     --- Updates all dynamic variables used in the rotation
     local function UpdateVars()
         -- Group composition variables
         Tanks, Healers, Members, Damagers, Melees, _, _, _, TargetIfAlly = HealingEngine:Fetch()
         Var['AverageHPInRange'] = HealingEngine:MedianHP()
         Var['TargetIsValid'] = M.TargetIsValid()
         Var['IsInCombat'] = Player:AffectingCombat()
         Var['IsInCat'] = Player:BuffUp(S.CatForm)
         Var['ComboPoints'] = Player:ComboPoints()
         Var['ManaPct'] = Player:ManaPercentage()
         Var['IsInDungeon'] = Player:IsInDungeonArea()
         Var['IsInRaid'] = Player:IsInRaidArea()
         Var['TargetTTD'] = Target:TimeToDie()
         Var['partySize'] = GetNumGroupMembers()
         Var['MembersAmount'] = Var['partySize'] < 5 and 5 or Var['partySize']
         Var['HoldInBear'] = false
         Var['PlayerInBossFight'] = Player:InBossEncounter()
         Var['IncarnationTreeofLifeActive'] = Player:BuffUp(S.IncarnationTreeofLifeBuff) or Player:BuffUp(S.IncarnationTreeofLife)
 
         -- DPS settings
         Settings['DpsForm'] = GetSetting("DpsForm", 1)
         Settings['DpsMethod'] = GetSetting("DpsMethod", 1)
         Settings['cat_range'] = GetSetting("cat_range", 10)
         if Settings['cat_range'] < 5 then
             Settings['cat_range'] = 5
         end
         if Settings['cat_range'] > 40 then
             Settings['cat_range'] = 40
         end
         
         Var['TargetIsInMeleeRange'] = Target:IsSpellInRange(S.Shred)
         Var['override_cat_form'] = GetSetting('override_cat_form', false)
         Var['override_cat_form_units'] = GetSetting('override_cat_form_units', 2)
         Var['override_cat_form_condition'] = GetSetting('override_cat_form_condition', 5)
         
         -- Set override condition value based on selected operator
         if Var['override_cat_form_condition'] == 1 then
             Var['override_cat_form_condition_value'] = EnemiesCount5y >= Var['override_catf_orm_units']
         elseif Var['override_cat_form_condition'] == 2 then
             Var['override_cat_form_condition_value'] = EnemiesCount5y <= Var['override_cat_form_units']
         elseif Var['override_cat_form_condition'] == 3 then
             Var['override_cat_form_condition_value'] = EnemiesCount5y == Var['override_cat_form_units']
         elseif Var['override_cat_form_condition'] == 4 then
             Var['override_cat_form_condition_value'] = EnemiesCount5y > Var['override_cat_form_units']
         elseif Var['override_cat_form_condition'] == 5 then
             Var['override_cat_form_condition_value'] = EnemiesCount5y < Var['override_cat_form_units']
         end
 
         -- Calculate thresholds based on group size
         if Var['MembersAmount'] then
             Settings['StopCatWeaving_underX'] = GetSetting('StopCatWeaving_underX', 1)
             Settings['StopCatWeaving_underX_val'] = GetSetting('StopCatWeaving_underX_val', 30)
             Settings['Convoke_underX'] = (GetSetting('Convoke_underX', 30) * Var['MembersAmount']) / 100
             Settings['Convoke_underX_val'] = GetSetting('Convoke_underX_val', 30)
             Settings['Tranquility_underX'] = (GetSetting('Tranquility_underX', 30) * Var['MembersAmount']) / 100
             Settings['Tranquility_underX_val'] = GetSetting('Tranquility_underX_val', 30)
             Settings['Flourish_underX'] = (GetSetting('Flourish_underX', 30) * Var['MembersAmount']) / 100
             Settings['Flourish_underX_val'] = GetSetting('Flourish_underX_val', 30)
             Settings['WildGrowth_underX'] = (GetSetting('WildGrowth_underX', 30) * Var['MembersAmount']) / 100
             Settings['WildGrowth_underX_val'] = GetSetting('WildGrowth_underX_val', 30)
             Settings['Efflorescence_underX'] = (GetSetting('Efflorescence_underX', 30) * Var['MembersAmount']) / 100
             Settings['Efflorescence_underX_val'] = GetSetting('Efflorescence_underX_val', 30)
             Settings['GGEmergency_underX'] = (GetSetting('GGEmergency_underX', 30) * Var['MembersAmount']) / 100
             Settings['GGEmergency_underX_val'] = GetSetting('GGEmergency_underX_val', 30)
             Settings['INC_underX'] = (GetSetting('INC_underX', 30) * Var['MembersAmount']) / 100
             Settings['INC_underX_val'] = GetSetting('INC_underX_val', 30)
         end
     
         -- Health thresholds for spells
         Settings["GroveGuardiansHP"] = GetSetting('GroveGuardiansHP', 30)
         Settings['IronbarkHP'] = GetSetting('IronbarkHP', 30)
         Settings['IronbarkHP_Tanks'] = GetSetting('IronbarkHP_Tanks', 30)
         Settings['SwiftmendHP'] = GetSetting('SwiftmendHP', 30)
         Settings['RegrowthHP'] = GetSetting('RegrowthHP', 30)
         Settings['RegrowthHPToL'] = GetSetting('RegrowthHPToL', 30)
         Settings['RegrowthHP2'] = GetSetting('RegrowthHP2', 30)
         Settings['RegrowthHPClearCasting'] = GetSetting('RegrowthHPClearCasting', 30)
         Settings['RegrowthHPFiller'] = GetSetting('RegrowthHPFiller', 30)
         Settings['RejuvenationHP'] = GetSetting('RejuvenationHP', 30)
         Settings['RejuvenationHPFiller'] = GetSetting('RejuvenationHPFiller', 30)
         Settings['RegrowthAbundanceStack'] = GetSetting('RegrowthAbundanceStack', 30)
     
         -- Defensive settings
         Settings['BarkSkinHPCheck'] = GetSetting("BarkSkinHP_check", false)
         Settings['BarkSkinHPSpin'] = GetSetting("BarkSkinHP_spin", 30)
         Settings['smartbear'] = GetSetting("smartbear", false)
         Settings['smartholdbear'] = GetSetting('smartholdbear', {})
         Settings['DefensiveDown'] = Player:BuffDown(S.Barkskin) and Player:BuffDown(S.UrsineVigorBuff) and Player:BuffDown(S.Ironbark, true)
         Settings['RenewalHP'] = GetSetting("RenewalHP", 30)
 
         -- Misc settings
         Settings['InnervateMana'] = GetSetting('InnervateMana', 60)
         Settings['ManaManagement'] = GetSetting('ManaManagement', true)
         Settings['ManaIncarnation'] = GetSetting('ManaIncarnation', false)
         Settings['Flourish_ActiveHots'] = GetSetting('Flourish_ActiveHots', 2)
         Settings['ConvokeOption'] = GetSetting('ConvokeOption', {})
 
         -- Utility settings
         Settings['autotravel'] = GetSetting('autotravel', false)
         Settings['autorebirth'] = GetSetting('autorebirth', {})
         Settings['motw'] = GetSetting('motw', {})
         Settings['OOCForm'] = GetSetting('OOCForm', "NoneForm")
         Settings['AutoProwl'] = GetSetting('AutoProwl', false)
         Settings['magicgroundspell'] = GetSetting('magicgroundspell', 1)
 
         -- Dynamic mana management variable
         Var['ManaOK'] = (not Settings['ManaManagement'] or 
                         Var['ManaPct'] >= Unit("boss1"):HealthPercentage() + 10 or 
                         (not Var['PlayerInBossFight'] and Var['ManaPct'] > 60) or 
                         (Player:BuffUp(S.Innervate, true) or Player:BuffUp(S.PotionofChilledClarity) or Player:BuffUp(S.MoltenRadiance)))
         
         -- Fallback combat detection if not in combat but tanks are
         if not Var['IsInCombat'] then
             Var['IsInCombat'] = combatMonitor()
         end
     end
 
     --- Counts active HoTs on a unit
     ---@param TargetedUnit Unit
     local function ActiveHots(TargetedUnit)
         local ActiveHotsCount = 0
         if TargetedUnit:BuffUp(S.Rejuvenation) then
             ActiveHotsCount = ActiveHotsCount + 1
         end
         if TargetedUnit:BuffUp(S.RejuvenationGermimation) then
             ActiveHotsCount = ActiveHotsCount + 1
         end
         if TargetedUnit:BuffUp(Lifebloom) then
             ActiveHotsCount = ActiveHotsCount + 1
         end
         if TargetedUnit:BuffUp(S.Regrowth) then
             ActiveHotsCount = ActiveHotsCount + 1
         end
         if TargetedUnit:BuffUp(S.WildGrowth) then
             ActiveHotsCount = ActiveHotsCount + 1
         end
         return ActiveHotsCount
     end
 
     --- Determines if Flourish should be used on a unit based on active HoTs
     ---@param TargetedUnit Unit
     local function EvaluateFlourish(TargetedUnit)
         return ActiveHots(TargetedUnit) >= Settings['Flourish_ActiveHots']
     end
       
     -- Special tables for tracking
     local SetInnervate = {}
     local LifebloomPriority = {}
     local LifebloomMembers = {}
 
     --- Compares two units for priority in Lifebloom application
     ---@param x Unit
     ---@param y Unit
     local function Compare(x, y)
         if Reason == "SOON" then
             if x:IsUnit(Player) and not y:IsUnit(Player) then
                 return true
             elseif not x:IsUnit(Player) and y:IsUnit(Player) then
                 return false
             end
             return x:Health() < y:Health()
         end
     
         -- Manual override priorities (keep existing logic)
         if LifebloomPriority then
             if LifebloomPriority[x:FullName()] and not LifebloomPriority[y:FullName()] then
                 return true
             elseif not LifebloomPriority[x:FullName()] and LifebloomPriority[y:FullName()] then
                 return false
             end
         end
     
         if GetSetting('dynamic_lifebloom', false) then
             return x:Health() < y:Health()
         end
     
         local ScoreX = 0
         local ScoreY = 0
         
         -- Double Bloom Build (Harmonious Blooming + Verdancy)
         if S.HarmoniousBlooming:IsAvailable() and S.Verdancy:IsAvailable() then
             if Var['IsInDungeon'] then
                 -- In Dungeon: Prioritize two DPS
                 if x:IsADamager() then
                     ScoreX = 3000 + ((100 - x:HealthPercentage()) * 5)  -- Add slight health factor to differentiate DPS
                 end
                 if y:IsADamager() then
                     ScoreY = 3000 + ((100 - y:HealthPercentage()) * 5)
                 end
             else 
                 -- In Raid: One on player, one on DPS
                 if x:IsUnit(Player) then
                     ScoreX = 4000
                 end
                 if y:IsUnit(Player) then
                     ScoreY = 4000
                 end
                 
                 if x:IsADamager() then
                     ScoreX = (ScoreX > 0) and ScoreX or (3000 + ((100 - x:HealthPercentage()) * 5))
                 end
                 if y:IsADamager() then
                     ScoreY = (ScoreY > 0) and ScoreY or (3000 + ((100 - y:HealthPercentage()) * 5))
                 end
             end
         else 
             -- Ramping Rejuv Build
             if Var['IsInDungeon'] then
                 -- In Dungeon: One on tank, one on DPS
                 if x:IsATank() then
                     ScoreX = 4000
                 end
                 if y:IsATank() then
                     ScoreY = 4000
                 end
                 
                 if x:IsADamager() then
                     ScoreX = (ScoreX > 0) and ScoreX or (3000 + ((100 - x:HealthPercentage()) * 5))
                 end
                 if y:IsADamager() then
                     ScoreY = (ScoreY > 0) and ScoreY or (3000 + ((100 - y:HealthPercentage()) * 5))
                 end
             else 
                 -- In Raid: Both on tanks
                 if x:IsATank() then
                     ScoreX = 4000 + ((100 - x:HealthPercentage()) * 5)  -- Differentiate between tanks
                 end
                 if y:IsATank() then
                     ScoreY = 4000 + ((100 - y:HealthPercentage()) * 5)
                 end
             end
         end
     
         -- Fall back to original scoring logic if no special case matched
         if ScoreX == 0 then
             if x:IsATank() then
                 ScoreX = 2000
             elseif x:IsADamager() then
                 ScoreX = 200
             elseif x:IsUnit(Player) then
                 ScoreX = S.Photosynthesis:IsAvailable() and 1500 or 750
             end
         end
     
         if ScoreY == 0 then
             if y:IsATank() then
                 ScoreY = 2000
             elseif y:IsADamager() then
                 ScoreY = 200
             elseif y:IsUnit(Player) then
                 ScoreY = S.Photosynthesis:IsAvailable() and 1500 or 750
             end
         end
     
         if ScoreX ~= ScoreY then
             return ScoreX > ScoreY
         else
             return x:HealthPercentage() < y:HealthPercentage()
         end
     end
 
     --- Sorts the Lifebloom target priority list
     local function Sort_LifebloomList()
        Tanks, Healers, Members, Damagers, Melees, _, _, _, TargetIfAlly = HealingEngine:Fetch()
        LifebloomMembers = {}
     
        if not Members or #Members < 2 then
            return
        end
 
        for i, UnitMembers in pairs(Members) do
            table.insert(LifebloomMembers, UnitMembers)
        end
     
        if not LifebloomMembers or #LifebloomMembers < 2 then
            return
        end
     
        table.sort(LifebloomMembers, Compare)
        Var['UpdateLifebloom'] = false
     end
 
     --- Counts active Grove Guardian totems
     local function GroveGuardianAmount()
         local Total = 0
         for index = 1, 4 do
             local haveTotem = GetTotemInfo(index)
             if haveTotem then
                 Total = Total + 1
             end
         end
         return Total
     end
 
     --- Counts active Rejuvenation applications
     local function RejuvenationCount()
         local Total = 0
         if S.RejuvenationGermimation:IsAvailable() then
             ---@param ThisUnit Unit
             for i, ThisUnit in pairs(Members) do
                 if ThisUnit:BuffUp(S.RejuvenationGermimation) then
                     Total = Total + 1
                 end
             end
             return Total
         end
         ---@param ThisUnit Unit
         for i, ThisUnit in pairs(Members) do
             if ThisUnit:BuffUp(S.Rejuvenation) then
                 Total = Total + 1
             end
         end
         return Total
     end
 
     --- Counts active Regrowth applications
     local function RegrowthCount()
         local Total = 0
         ---@param ThisUnit Unit
         for i, ThisUnit in pairs(Members) do
             if ThisUnit:BuffUp(S.Regrowth) then
                 Total = Total + 1
             end
         end
         return Total
     end
 
     --- Checks if anyone is standing on current Efflorescence
     local function AnyoneOnCurrentEfflorescence()
         if Player:BuffDown(S.EfflorescenceIsActive) then
             return false, "No Efflorescence"
         end
         if GetTime() - Var['LastTimeEfflorescenceHealed'] > 3 then
             return false, "Nobody on Efflorescence"
         end
         return true
     end
 
     --- Calculates current Protector of the Pack stored amount
     local function PotPAmountStored()
         if Player:BuffUp(S.ProtectorofthePackBuff) then
             ---@diagnostic disable-next-line: undefined-field
             local Points = Player:AuraInfo(S.ProtectorofthePackBuff, nil, true).points
             local PotPAmount = Points and Points[1] or 0
             return PotPAmount
         end
         return 0
     end
 
     --- Calculates maximum Protector of the Pack amount
     local function PotPMaxAmount()
         return GetSpellBonusDamage(7) * 3
     end
 
     -- =============================================
     -- Evaluation Functions for Spell Targeting
     -- =============================================
 
     ---@param TargetedUnit Unit
     local function EvaluateRejuvRamp(TargetedUnit)
         return TargetedUnit:BuffRefreshable(S.Rejuvenation) or S.Germination:IsAvailable() and TargetedUnit:BuffRefreshable(S.RejuvenationGermimation)
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateRegrowthRamp(TargetedUnit)
         return TargetedUnit:BuffRefreshable(S.Regrowth)
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateCycleEfflorescence(TargetedUnit)
         return TargetedUnit:IsSplashedByFriend(Var['TankUnit']) and not TargetedUnit:IsMoving()
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateInnervate(TargetedUnit)
         return TargetedUnit:BuffDown(S.Innervate, true) 
         and TargetedUnit:IsUnit(Player) 
         and TargetedUnit:ManaPercentage() <= Settings['InnervateMana']
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateInnervateToggle(TargetedUnit)
         return TargetedUnit:BuffDown(S.Innervate, true) 
         and TargetedUnit:UnfilterName() == Druid.InnervateTargetName
         and TargetedUnit:ManaPercentage() <= Settings['InnervateMana']
         and Player:IsFriend(TargetedUnit) and TargetedUnit:IsInRange(40)
         and (TargetedUnit:IsCasting() or TargetedUnit:IsChanneling())
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateInnervateIncarnation(TargetedUnit)
         return TargetedUnit:BuffDown(S.Innervate, true) 
         and TargetedUnit:IsUnit(Player)
     end
     
     local function EvaluateRejuvIntoWildGrowth()
         return true
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateSwiftmend(TargetedUnit)
         return TargetedUnit:HealthPercentage() < Settings['SwiftmendHP'] and (TargetedUnit:BuffUp(S.Regrowth) or TargetedUnit:BuffUp(S.WildGrowth) or TargetedUnit:BuffUp(S.Rejuvenation) or TargetedUnit:BuffUp(S.RejuvenationGermimation)) 
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateSwiftmendSotF(TargetedUnit)
         return (TargetedUnit:BuffUp(S.Regrowth) or TargetedUnit:BuffUp(S.WildGrowth) or TargetedUnit:BuffUp(S.Rejuvenation) or TargetedUnit:BuffUp(S.RejuvenationGermimation)) 
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateCenarionWard(TargetedUnit)
         return TargetedUnit:BuffDown(S.CenarionWard)
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateRegrowthEmergency(TargetedUnit)
         return TargetedUnit:HealthPercentage() < Settings['RegrowthHP2']
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateRejuvenationEmergency(TargetedUnit)
         return TargetedUnit:HealthPercentage() < Settings['RegrowthHP2'] and (TargetedUnit:BuffDown(S.Rejuvenation) or S.Germination:IsAvailable() and TargetedUnit:BuffDown(S.RejuvenationGermimation))
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateRejuvMembers(TargetedUnit)
         return ((TargetedUnit:BuffDown(S.Rejuvenation) or S.Germination:IsAvailable() and TargetedUnit:BuffDown(S.RejuvenationGermimation)) and TargetedUnit:HealthPercentage() < Settings['RejuvenationHP'] )
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateGerminationMembers(TargetedUnit)
         return ((TargetedUnit:BuffDown(S.RejuvenationGermimation)) and TargetedUnit:HealthPercentage() < Settings['RejuvenationHP'] )
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateRejuvMembersFiller(TargetedUnit)
         return ((TargetedUnit:BuffDown(S.Rejuvenation) or S.Germination:IsAvailable() and TargetedUnit:BuffDown(S.RejuvenationGermimation)) and TargetedUnit:HealthPercentage() <= Settings['RejuvenationHPFiller'] )
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateRegrowthMembers(TargetedUnit)
         return (TargetedUnit:HealthPercentage() < Settings['RegrowthHP'] or (Player:BuffUp(S.IncarnationTreeofLifeBuff) and TargetedUnit:HealthPercentage() < Settings['RegrowthHPToL'])) 
         and TargetedUnit:BuffDown(S.Regrowth) and not TempBlackListRegrowth[TargetedUnit:UnfilterName()]
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateRegrowthMembersClearCasting(TargetedUnit)
         return (TargetedUnit:HealthPercentage() < Settings['RegrowthHPClearCasting']) 
         and TargetedUnit:BuffDown(S.Regrowth) and not TempBlackListRegrowth[TargetedUnit:UnfilterName()]
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateRegrowthMembersFiller(TargetedUnit)
         return (TargetedUnit:HealthPercentage() < Settings['RegrowthHPFiller'] and TargetedUnit:BuffDown(S.Regrowth) and not TempBlackListRegrowth[TargetedUnit:UnfilterName()])
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateRegrowthSpread(TargetedUnit)
         return TargetedUnit:BuffDown(S.Regrowth) and not TempBlackListRegrowth[TargetedUnit:UnfilterName()]
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateRegrowthAbundanceHighPrio(TargetedUnit)
         return TargetedUnit:HealthPercentage() < 80 and not TempBlackListRegrowth[TargetedUnit:UnfilterName()]
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateRegrowthAbundanceLowPrio(TargetedUnit)
         return TargetedUnit:HealthPercentage() < 100 and not TempBlackListRegrowth[TargetedUnit:UnfilterName()]
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateSwarm0(TargetedUnit)
         return TargetedUnit:BuffStack(S.AdaptiveSwarmBuff) == 0
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateSwarm1(TargetedUnit)
         return TargetedUnit:BuffStack(S.AdaptiveSwarmBuff) == 1
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateSwarm2(TargetedUnit)
         return TargetedUnit:BuffStack(S.AdaptiveSwarmBuff) == 2
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateSwarm3(TargetedUnit)
         return TargetedUnit:BuffStack(S.AdaptiveSwarmBuff) == 3
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateIronBark(TargetedUnit)
         return TargetedUnit:HealthPercentage() < Settings['IronbarkHP'] and TargetedUnit:BuffDown(S.Barkskin, true)
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateIronBarkTanks(TargetedUnit)
         return TargetedUnit:HealthPercentage() < Settings['IronbarkHP_Tanks'] and TargetedUnit:BuffDown(S.Barkskin, true)
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateRejuvSpread(TargetedUnit)
         return TargetedUnit:BuffDown(S.Rejuvenation)
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateRejuvSpreadGermination(TargetedUnit)
         return TargetedUnit:BuffDown(S.RejuvenationGermimation)
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateLifeBloomTanks(TargetedUnit)
         return TargetedUnit:BuffDown(Lifebloom) or TargetedUnit:BuffUp(Lifebloom) and TargetedUnit:BuffRemains(Lifebloom) < 2
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateLifeBloomMembers(TargetedUnit)
         return (TargetedUnit:BuffDown(Lifebloom) or TargetedUnit:BuffUp(Lifebloom) and TargetedUnit:BuffRemains(Lifebloom) < 2) and TargetedUnit:HealthPercentage() < 80
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateLifebloomEmergency(TargetedUnit)
         return TargetedUnit:BuffUp(Lifebloom) and TargetedUnit:BuffRefreshable(Lifebloom) and (TargetedUnit:HealthPercentage() <= 70)
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateLifeBloomSelf(TargetedUnit)
         return TargetedUnit:GUID() == Player:GUID()
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateGG(TargetedUnit)
         return TargetedUnit:HealthPercentage() < Settings['GroveGuardiansHP']
     end
     
     ---@param TargetUnit Unit
     local function EvaluateTargetIfHP(TargetUnit)
         return TargetUnit:HealthPercentage()
     end
     
     ---@param TargetedUnit Unit
     local function EvaluateSymbioticRelationship(TargetedUnit)
         return TargetedUnit:BuffDown(S.SymbioticRelationship) and not TempBlackListSymbioticRelationship[TargetedUnit:UnfilterName()]
     end
     
     local function EvaluateTrue()
         return true
     end
 
     -- =============================================
     -- Rotation Components
     -- =============================================
 
     --- Handles utility spells and out-of-combat actions
     local function Utilities()
         local smartholdbear = Settings['smartholdbear']
         if Player:BuffUp(S.BearForm) then
             if smartholdbear['smartholdbear_fg'] then
                 if S.FrenziedRegeneration:IsReady(Player) and Player:BuffDown(S.FrenziedRegenerationBuff) and Player:RealHealthPercentage() <= 80 then
                     if Cast(S.FrenziedRegeneration) then
                         return "Frenzied Regeneration";
                     end
                 end
                 if S.Ironfur:IsReady(Player) and Player:BuffDown(S.IronfurBuff) and Player:RealHealthPercentage() <= 80 then
                     if Cast(S.Ironfur) then
                         return "Ironfur";
                     end
                 end
             end
             local Should = Player:ShouldUseDefensive()
             if not Player:PrevGCD(1, S.IncapacitatingRoar) then
                 if smartholdbear['smartholdbear_uv'] and Player:BuffUp(S.UrsineVigorBuff) then
                     Var['HoldInBear'] = true
                     return
                 elseif smartholdbear['smartholdbear_fg'] and Player:BuffUp(S.FrenziedRegenerationBuff) then
                     Var['HoldInBear'] = true
                     return
                 elseif smartholdbear['smartholdbear_over'] and Should then
                     Var['HoldInBear'] = true
                     return
                 end
             end
             Var['HoldInBear'] = false
         end
 
         if Var['IsInCombat'] then
             -- Innervate handling (self or toggle target)
             if S.Innervate:IsReady(Player) then
                 if Druid.InnervateTargetName == "None" or Druid.InnervateTargetName == Player:UnfilterName() then
                     if CastCycleAlly(S.Innervate, Members, EvaluateInnervate, nil, true) then
                         return "Innervate - Self"
                     end
                 else
                     if CastCycleAlly(S.Innervate, Members, EvaluateInnervateToggle, nil, true) then
                         return "Innervate - Toggle"
                     end
                 end
 
                 -- Optional Innervate during Incarnation
                 if Settings['ManaIncarnation'] and Player:BuffUp(S.IncarnationTreeofLifeBuff) then
                     if CastCycleAlly(S.Innervate, Members, EvaluateInnervateIncarnation, nil, true) then
                         return "Innervate - Incarnation"
                     end
                 end
             end
 
             -- Rebirth handling (mouseover or target)
             if Settings['autorebirth']['autorebirth_mouseover'] and S.Rebirth:IsReady(MouseOver) then
                 local GetMouseFociCache = GetMouseFoci()
                 ---@class Frame
                 local MouseFocus = GetMouseFociCache[1]
 
                 local FrameName = MouseFocus and not MouseFocus:IsForbidden() and MouseFocus:GetName() or "None"
                 if FrameName ~= "WorldFrame" and FrameName ~= "None" then
                     if MouseOver:EvaluateRebirth() then
                         MainAddon.UI:ShowToast("Rebirth", MouseOver:Name(), MainAddon.GetTexture(S.Rebirth))
                         if Cast(S.Rebirth) then return "Rebirth MouseOver" end
                     end
                 end
             end
             if Settings['autorebirth']['autorebirth_target'] and S.Rebirth:IsReady() then
                 if Target:EvaluateRebirth() then
                     if Cast(S.Rebirth) then 
                         MainAddon.UI:ShowToast("Rebirth", Target:Name(), MainAddon.GetTexture(S.Rebirth))
                         return "Rebirth Target" 
                     end
                 end
             end
         else
             -- Revitalize out of combat
             if Target:IsAPlayer() and Target:IsDeadOrGhost() and Player:IsFriend(Target) and (Target:IsInParty() or Target:IsInRaid()) and Target:EvaluateRebirth() then
                 if S.Revitalize:IsReady(Player) then
                     if Cast(S.Revitalize) then
                         return 'Revitalize';
                     end
                 end
             end
         end
 
         -- Mark of the Wild handling (self or group)
         if S.MarkoftheWild:IsReady(Player)
         and (Settings['motw']['motw_self'] and Player:BuffDown(S.MarkoftheWild, true)
         or Settings['motw']['motw_friends'] and M.GroupBuffMissing(S.MarkoftheWild)) then
             if Cast(S.MarkoftheWild) then return 'Mark of the Wild' end
         end
 
         -- Symbiotic Relationship handling (tank priority)
         if S.SymbioticRelationship:IsReady(Player) and not Var['IsInCombat'] then
             if Var['IsInDungeon'] and Tanks and #Tanks > 0 then
                 -- In dungeon pick the tank
                 local tank = Tanks[1]
                 if tank
                     and tank:Exists()
                     and not tank:IsDeadOrGhost()
                     and tank:IsInRange(40)
                     and EvaluateSymbioticRelationship(tank)  -- needed to avoid double casts
                 then
                     if CastAlly(S.SymbioticRelationship, tank) then
                         return 'Symbiotic Relationship (Dungeon)'
                     end
                 end
         
             elseif Var['IsInRaid'] and Tanks and #Tanks > 0 then
                 -- In raids pick lowest hp tank
                 local lowestTankHP, lowestTankUnit = HealingEngine:LowestHP(true, 40, Tanks, "Tank")
                 if lowestTankUnit
                     and lowestTankUnit:Exists()
                     and not lowestTankUnit:IsDeadOrGhost()
                     and lowestTankUnit:IsInRange(40)
                     and EvaluateSymbioticRelationship(lowestTankUnit)  -- needed to avoid double casts
                 then
                     if CastAlly(S.SymbioticRelationship, lowestTankUnit) then
                         return 'Symbiotic Relationship (Raid)'
                     end
                 end
             end
         end
 
         -- Auto Travel Form when moving out of combat
         if Settings['autotravel'] and not Var['IsInCombat'] and not Var['TargetIsValid'] then
             if S.TravelForm:IsReady(Player) and Player:IsOutdoors() and not Player:BuffUp(S.TravelForm) and not Player:AnyStealthUp() and not Player:IsInInstancedPvP() and Player:IsMoving() then
                 if Cast(S.TravelForm) then
                     return "Travel Form";
                 end
             end
         end
     end
 
     --- Handles defensive cooldowns and bear form management
     local function Defensives()
         -- Barkskin usage when health threshold is met
         if S.Barkskin:IsReady(Player) and Settings['DefensiveDown']
         and (Settings['BarkSkinHPCheck'] and Player:RealHealthPercentage() <= Settings['BarkSkinHPSpin'])  then
             if Cast(S.Barkskin, true) then
                 return 'Barkskin Defensive'
             end
         end
         
         -- Smart Bear Form activation based on danger level
         local Should, CastID = Player:ShouldUseDefensive()
         if Should then
             if Player:MythicDifficulty() >= GetSetting('smart_bear_above_key_level', 2) or not Var['IsInDungeon'] then
                 if Settings['DefensiveDown'] and S.UrsineVigor:IsAvailable() and S.BearForm:IsReady(Player) and Settings['smartbear'] and Player:BuffDown(S.BearForm) then
                     if Player:IsCasting(S.Wrath) or Player:IsCasting(S.Starfire) then
                         MainAddon.SetTopColor(1, "Stop Casting")
                     end
                     if Cast(S.BearForm) then
                         MainAddon.UI:ShowToast("Defensive", MainAddon.GetSpellInfo(CastID), MainAddon.GetTexture(S.BearForm))
                         return 'Smart Bear' 
                     end
                 end
             end
         end
 
         -- Renewal usage when health threshold is met
         if S.Renewal:IsReady(Player) and Player:RealHealthPercentage() < Settings['RenewalHP'] then
             if Cast(S.Renewal, true) then
                 return 'Renewal Deffensive'
             end
         end
     end
 
     --- Handles trinket usage
     local function Trinkets()
         -- Iridal, the Earth's Master (low health execute)
         if I.IridaltheEarthsMaster:IsEquippedAndReady() and Target:HealthPercentage() < 35 then
             if Cast(I.IridaltheEarthsMaster) then return "iridal_the_earths_master"; end
         end
 
         -- Dreambinder (weapon on-use)
         if I.Dreambinder:IsEquippedAndReady() then
             if MainAddon.SetTopTexture(1, "Weapon On-Use") then return "dreambinder_loom_of_the_great_cycle"; end
         end
     end
  
     --- Handles special healing cases (focus/mouseover priority)
     local function HealingSpecial()
         local shouldHeal, isReadyToBeHealed, type = HealingEngine.ShouldHealTargetOrMouseover()
         if shouldHeal and isReadyToBeHealed then
             if Focus:IsInRange(40) then
                 -- Regrowth Refreshable
                 if S.Regrowth:IsReady(Focus) and Focus:BuffRefreshable(S.Regrowth) then
                     if CastAlly(S.Regrowth, Focus) then
                         return 'Special Healing: Regrowth'
                     end
                 end
                 -- Swiftmend
                 if S.Swiftmend:IsReady(Focus) and Focus:CastRemains() < S.Regrowth:CastTime() and (Focus:BuffUp(S.Regrowth) or Focus:BuffUp(S.WildGrowth) or Focus:BuffUp(S.Rejuvenation)) then
                     if CastAlly(S.Swiftmend, Focus) then
                         return 'Special Healing: Swiftmend'
                     end
                 end
                 -- Grove Guardians
                 if S.GroveGuardians:IsReady(Focus) then
                     if S.GroveGuardians:ChargesFractional() >= 2 then
                         if CastAlly(S.GroveGuardians, Focus) then
                             return 'Special Healing: Grove Guardian'
                         end
                     end
                 end
                 -- Rejuvenation
                 if S.Rejuvenation:IsReady(Focus) then
                     if Focus:BuffDown(S.Rejuvenation) then
                         if CastAlly(S.Rejuvenation, Focus) then
                             return 'Special Healing: Rejuvenation'
                         end
                     end
                     if S.Germination:IsAvailable() and Focus:BuffDown(S.RejuvenationGermimation) then
                         if CastAlly(S.Rejuvenation, Focus) then
                             return 'Special Healing: Rejuvenation Germination'
                         end
                     end
                 end
                 -- Regrowth Spam
                 if S.Regrowth:IsReady(Focus) then
                     if CastAlly(S.Regrowth, Focus) then
                         return 'Special Healing: Regrowth'
                     end
                 end
             end
         elseif not isReadyToBeHealed then
             if type == "MouseOver" then
                 MainAddon.SetTopColor(1, "Focus Mouseover")
             elseif type == "Target" then
                 MainAddon.SetTopColor(1, "Focus Target")
             end
         end
     end
 
     --- Handles preemptive healing when damage is incoming
     local function DamageIncoming()
         -- Pre-cast Grove Guardians if charges are available
         if S.GroveGuardians:IsReady(Player) then
             if S.GroveGuardians:ChargesFractional() >= 1.8 and S.GroveGuardians:TimeSinceLastCast() > 2.5 then
                 if CastCycleAlly(S.GroveGuardians, Members, EvaluateTrue, nil, nil, nil, nil, true) then
                     return 'Damage Incoming: Grove Guardians'
                 end
             end
         end
         
         -- Swiftmend to prepare for Wild Growth
         if S.Swiftmend:IsReady(Player) and (S.WildGrowth:IsCastable() or not S.SouloftheForest:IsAvailable()) then
             if CastCycleAlly(S.Swiftmend, Members, EvaluateSwiftmendSotF) then
                 return 'Damage Incoming: Swiftmend'
             end
         end
         
         -- Wild Growth with Soul of the Forest if available
         if Player:BuffUp(S.SouloftheForestBuff) or not S.SouloftheForest:IsAvailable() then
             if S.WildGrowth:IsReady(Player) then
                 if CastCycleAlly(S.WildGrowth, Members, EvaluateTrue, nil, nil, nil, nil, true) then
                     return 'Damage Incoming: Wild Growth before Flourish'
                 end
             end
         end
         
         -- Spread Regrowths if no Soul of the Forest
         if S.Regrowth:IsReady(Player) and not Player:BuffUp(S.SouloftheForestBuff) then
            if CastCycleAlly(S.Regrowth, Members, EvaluateRegrowthSpread) then
                 return 'Damage Incoming: Regrowth'
             end
         end
         
         -- Spread Rejuvenations
         if S.Rejuvenation:IsReady(Player) then
             if CastCycleAlly(S.Rejuvenation, Members, EvaluateRejuvSpread) then
                 return 'Damage Incoming: Rejuvenation'
             end
             if S.Germination:IsAvailable() then
                 if CastCycleAlly(S.Rejuvenation, Members, EvaluateRejuvSpreadGermination) then
                     return 'Damage Incoming: Rejuvenation Germination'
                 end
             end
         end
     end
 
     --- Handles out-of-combat healing cooldowns
     local function HealingCDsOOC()
         -- Adaptive Swarm spreading when not in combat
         if S.AdaptiveSwarm:IsReady(Player) and Player:BuffDown(S.Prowl) then
             if CastCycleAlly(S.AdaptiveSwarm, Members, EvaluateSwarm2) then
                 return 'Adaptive Swarm Members 2 Stacks'
             end
             if CastCycleAlly(S.AdaptiveSwarm, Members, EvaluateSwarm1) then
                 return 'Adaptive Swarm Members 1 Stacks'
             end
             if CastCycleAlly(S.AdaptiveSwarm, Members, EvaluateSwarm3) then
                 return 'Adaptive Swarm Members 3 Stacks'
             end
             if CastCycleAlly(S.AdaptiveSwarm, Members, EvaluateSwarm0) then
                 return 'Adaptive Swarm Members 0 Stacks'
             end
         end
     end
  
     --- Handles in-combat healing cooldowns
     local function HealingCDsCombat()         
        -- Grove Guardians charge management
        if S.GroveGuardians:IsReady(Player) then
            if S.GroveGuardians:ChargesFractional() == 3 then
                if CastAlly(S.GroveGuardians, Player) then
                    return 'Grove Guardians - overcapping'
                end
            end
        end

         -- Ironbark usage (tanks first, then others)
         if S.Ironbark:IsReady(Player) then
             if CastCycleAlly(S.Ironbark, Tanks, EvaluateIronBarkTanks) then
                 return 'Ironbark Tanks'
             end
             if CastCycleAlly(S.Ironbark, Members, EvaluateIronBark) then
                 return 'Ironbark Members'
             end
         end
 
         -- Convoke the Spirits for healing
         if S.ConvoketheSpirits:IsReady(Player) and Settings['ConvokeOption']['ConvokeHeal'] and HealingEngine:MembersUnderPercentage(Settings['Convoke_underX_val']) >= Settings['Convoke_underX'] then
             -- Cancel Moonkin form before Convoke
             if Player:BuffUp(S.MoonkinForm) then
                 if ForceCastDisplay(S.MoonkinForm) then
                     return 'Cancel Moonkin Form before Convoke'
                 end
             end
 
             -- Cancel Cat form before Convoke
             if Var['IsInCat'] then
                 if ForceCastDisplay(S.CatForm) then
                     return 'Cancel Cat Form before Convoke'
                 end
             end
 
             -- Cancel Bear form before Convoke
             if Player:BuffUp(S.BearForm) then
                 if ForceCastDisplay(S.BearForm) then
                     return 'Cancel Bear Form before Convoke'
                 end
             end
 
             -- Check forms before casting Convoke
             if not Var['IsInCat'] and not Var['IsInBear'] and Player:BuffDown(S.MoonkinForm) then
                 -- Convoke to heal
                 if Cast(S.ConvoketheSpirits) then
                     return 'Convoke the Spirits'
                 end
             end
         end
 
         -- Grove Guardians placement logic
         if S.GroveGuardians:IsReady(Player) then
             if GroveGuardianAmount() < 1 then
                 if CastCycleAlly(S.GroveGuardians, Members, EvaluateGG) then
                     return 'Grove Guardians'
                 end
             end
             -- Emergency Grove Guardians when many players are low
             if HealingEngine:MembersUnderPercentage(Settings['GGEmergency_underX_val']) >= Settings['GGEmergency_underX'] then
                 if CastCycleAlly(S.GroveGuardians, Members, EvaluateTrue, nil, nil, nil, nil, true) then
                     return 'Grove Guardians - Emergency'
                 end
             end
         end
 
         -- Flourish with proper setup
         if HealingEngine:MembersUnderPercentageWithCondition(EvaluateFlourish, Settings['Flourish_underX_val']) >= Settings['Flourish_underX'] then
             if S.Flourish:IsReady(Player) then
                 -- Natures Vigil before Flourish if appropriate
                 if S.NaturesVigil:IsReady() and (Var['PlayerInBossFight'] or HL.FightRemains(Enemies10ySplash) > 30) then
                     if Cast(S.NaturesVigil, true) then
                         return 'Natures Vigil x Flourish'
                     end
                 end
                 -- Wild Growth before Flourish for maximum effect
                 if S.WildGrowth:IsReady(Player) then
                     if CastCycleAlly(S.WildGrowth, Members, EvaluateTrue, nil, nil, nil, nil, true) then
                         return 'Wild Growth before Flourish'
                     end
                 end
                 if Cast(S.Flourish) then
                     return 'Flourish'
                 end
             end
         end
 
         -- Tree of Life (Incarnation) for emergency healing
         if S.IncarnationTreeofLife:IsReady(Player) and HealingEngine:MembersUnderPercentage(Settings['INC_underX_val']) >= Settings['INC_underX'] and not Player:BuffUp(S.IncarnationTreeofLifeBuff) then
             -- Natures Vigil before Incarnation if appropriate
             if S.NaturesVigil:IsReady() and (Var['PlayerInBossFight'] or HL.FightRemains(Enemies10ySplash) > 30) then
                 if Cast(S.NaturesVigil, true) then
                     return 'Natures Vigil x Tree of Life'
                 end
             end
             if Cast(S.IncarnationTreeofLife) then
                 return 'Incarnation Tree of Life'
             end	
         end
 
         -- Tranquility for group healing
         if HealingEngine:MembersUnderPercentage(Settings['Tranquility_underX_val']) >= Settings['Tranquility_underX'] then
             if S.Tranquility:IsReady(Player) then
                 if Cast(S.Tranquility) then
                     return 'Tranquility'
                 end
             end
         end
     end
  
     --- Main healing rotation logic
     local function HealingRotation()
         -- Wild Growth with Soul of the Forest buff
         if S.SouloftheForest:IsAvailable() then
             if Player:BuffUp(S.SouloftheForestBuff) then
                 if S.WildGrowth:IsReady(Player) then
                     if CastCycleAlly(S.WildGrowth, Members, EvaluateTrue, nil, nil, nil, nil, true) then
                         return 'Wild Growth Members'
                     end
                 end
             end
         end
 
         -- Swiftmend usage
         if S.Swiftmend:IsReady(Player) then
             -- Swiftmend without Soul of the Forest
             if not S.SouloftheForest:IsAvailable() then
                 if CastCycleAlly(S.Swiftmend, Members, EvaluateSwiftmend) then
                     return 'Swiftmend Members'
                 end
             else
                 -- Swiftmend to prepare for Wild Growth when not moving
                 if not Player:IsMoving() then
                     if (S.WildGrowth:IsCastable() or not S.SouloftheForest:IsAvailable()) 
                     and (HealingEngine:MembersUnderPercentage(Settings['WildGrowth_underX_val']) >= Settings['WildGrowth_underX']
                     or Player:BuffUp(S.IncarnationTreeofLifeBuff)) then
                         -- Swiftmend before Wild Growth
                         if CastCycleAlly(S.Swiftmend, Members, EvaluateSwiftmendSotF) then
                             return 'Swiftmend Before Wild Growth'
                         end
                     end
                 end
             end
         end
 
         -- Regrowth with Clear Casting proc
         if S.Regrowth:IsReady(Player) and Player:BuffRemains(S.ClearCasting) > S.Regrowth:CastTime() then
             if CastCycleAlly(S.Regrowth, Members, EvaluateRegrowthMembersClearCasting) then
                 return 'Regrowth Members - Clear Casting'
             end
         end	
 
         -- Nature's Swiftness for emergency Regrowth
         if HealingEngine:LowestHP() < Settings['RegrowthHP2'] then
             if S.NaturesSwiftness:IsReady(Player) then
                 if Cast(S.NaturesSwiftness, true) then
                     return "Nature's Swiftness - Emergency Regrowth"
                 end
             end
         end
 
         -- Efflorescence placement logic
         if S.Efflorescence:IsReady(Player) and Var['IsInCombat']
         and (Player:BuffUp(S.EfflorescenceIsActive) and Player:BuffRemains(S.EfflorescenceIsActive) < 2 or not AnyoneOnCurrentEfflorescence()) and HealingEngine:MembersUnderPercentage(Settings['Efflorescence_underX_val']) >= Settings['Efflorescence_underX']
         and ((not Var['IsInCat'] and Player:BuffDown(S.BearForm))) 
         then
             local magicgroundspell_target = Settings['magicgroundspell']
 
             if magicgroundspell_target == 1 then
                 if #Tanks > 0 then
                     if CastMagicAlly(S.Efflorescence, Tanks[1], nil, "145205-Magic") then 
                         return "Efflorescence - Friend"
                     end
                 end
             end
 
             if magicgroundspell_target == 2 then
                 if Var['TargetIsValid'] then
                     if CastMagic(S.Efflorescence, nil, "145205-Magic") then 
                         return "Efflorescence - Enemy"
                     end
                 end
             end
 
             if magicgroundspell_target == 3 then
                 if Cast(S.Efflorescence) then
                     return "Efflorescence - Regular"
                 end
             end
         end
 
         -- Lifebloom management
         if Var['IsInCombat'] or Player:IsInPvEActivity() then
             if Lifebloom:IsReady(Player) 
             and (not Player:PrevGCD(1, S.Moonfire) and not Player:PrevGCD(2, S.Moonfire) 
             and not Player:PrevGCD(1, S.Sunfire) and not Player:PrevGCD(2, S.Sunfire) or Settings['DpsForm'] == 3 or Settings['DpsForm'] == 4) then
                 if (not Var['IsInCat'] and Player:BuffDown(S.BearForm) and Player:BuffDown(S.TravelForm) and Player:BuffDown(S.MountForm) and Player:BuffDown(S.MoonkinForm) and Player:BuffDown(S.CatForm)) then
                     ---@class Unit
                     local LifebloomTarget = LifebloomMembers[1]
                     ---@class Unit
                     local LifebloomTarget2 = LifebloomMembers[2]
 
                     if LifebloomTarget and Lifebloom:IsReady(LifebloomTarget) and EvaluateLifeBloomTanks(LifebloomTarget) then
                         if CastAlly(Lifebloom, LifebloomTarget) then return "Lifebloom ->" .. LifebloomTarget:Name(); end
                     end
             
                     if S.Undergrowth:IsAvailable() then
                         if LifebloomTarget2 and Lifebloom:IsReady(LifebloomTarget2) and EvaluateLifeBloomTanks(LifebloomTarget2) then
                             if CastAlly(Lifebloom, LifebloomTarget2) then return "Lifebloom ->" .. LifebloomTarget2:Name(); end
                         end
                     end
                 end
             end
         end
 
         -- Cenarion Ward placement (tanks or others)
         if S.CenarionWard:IsReady(Player) and Var['IsInCombat'] then
             if GetSetting('tank_cenarion', false) then
                 if CastCycleAlly(S.CenarionWard, Tanks, EvaluateCenarionWard) then
                     return 'Cenarion Ward Tank'
                 end
             else
                 if CastCycleAlly(S.CenarionWard, Members, EvaluateCenarionWard) then
                     return 'Cenarion Ward'
                 end
             end
         end	
 
         -- Regrowth usage
         if S.Regrowth:IsReady(Player) then
             -- Emergency Regrowth spam
             if CastCycleAlly(S.Regrowth, Members, EvaluateRegrowthEmergency) then
                 return 'Regrowth Members - Spam'
             end
             -- Regrowth with Abundance stacks
             if S.Abundance:IsAvailable() and Player:BuffStack(S.AbundanceBuff) >= Settings['RegrowthAbundanceStack'] then
                 if CastCycleAlly(S.Regrowth, Members, EvaluateRegrowthAbundanceHighPrio) then
                     return 'Regrowth - Abundance High Prio'
                 end
                 if CastCycleAlly(S.Regrowth, Members, EvaluateRegrowthSpread) then
                     return 'Regrowth - Abundance Spread'
                 end
                 if CastCycleAlly(S.Regrowth, Members, EvaluateRegrowthAbundanceLowPrio) then
                     return 'Regrowth - Abundance Low Prio'
                 end
             end
         end
 
         -- Rejuvenation after Regrowth if emergency
         if S.Rejuvenation:IsReady(Player) and Player:PrevGCDP(1, S.Regrowth) then
             if CastCycleAlly(S.Rejuvenation, Members, EvaluateRejuvenationEmergency) then
                 return 'Rejuvenation Members - Emergency'
             end
         end
 
         -- Germination handling
         if S.Germination:IsAvailable() then
             if S.Rejuvenation:IsReady(Player) 
             and ((not Var['IsInCat'] and Player:BuffDown(S.BearForm)) or not Var['IsInCombat']) then
                 if CastCycleAlly(S.Rejuvenation, Members, EvaluateGerminationMembers) then
                     return 'Rejuvenation Members - Germination'
                 end
             end
         end
 
         -- Wild Growth without Soul of the Forest
         if not S.SouloftheForest:IsAvailable() then
             if S.WildGrowth:IsReady(Player)
             and (HealingEngine:MembersUnderPercentage(Settings['WildGrowth_underX_val']) >= Settings['WildGrowth_underX'] or Player:BuffUp(S.IncarnationTreeofLifeBuff)) then
                 if CastCycleAlly(S.WildGrowth, Members, EvaluateTrue, nil, nil, nil, nil, true) then
                     return 'Wild Growth Members'
                 end
             end
         end
 
         -- Normal Rejuvenation application (respecting mana and forms)
         if S.Rejuvenation:IsReady(Player) and (Var['ManaOK']
         and ((not Var['IsInCat'] and Player:BuffDown(S.BearForm)) or not Var['IsInCombat'])) then
             if CastCycleAlly(S.Rejuvenation, Members, EvaluateRejuvMembers) then
                 return 'Rejuvenation Members'
             end
         end
 
         -- Standard Regrowth application
         if S.Regrowth:IsReady(Player) then
             if CastCycleAlly(S.Regrowth, Members, EvaluateRegrowthMembers) then
                 return 'Regrowth Members'
             end
         end
 
         -- Healing Special (focus/mouseover priority)
         ShouldReturn = HealingSpecial()
         if ShouldReturn then
             return ShouldReturn;
         end
         
         -- Spread Rejuvenation under Innervate or Potion of Chilled Clarity
         if (Player:BuffUp(S.Innervate, true)
         or Player:BuffUp(S.PotionofChilledClarity)) then
             if S.Rejuvenation:IsReady(Player) then
                 if CastCycleAlly(S.Rejuvenation, Members, EvaluateRejuvSpread) then
                     return 'Rejuvenation Spread Members - Innervate'
                 end
                 if S.Germination:IsAvailable() then
                     if CastCycleAlly(S.Rejuvenation, Members, EvaluateRejuvSpreadGermination) then
                         return 'Rejuvenation Germination Spread Members - Innervate'
                     end
                 end
             end
             if S.Regrowth:IsReady(Player) then
                 if CastCycleAlly(S.Regrowth, Members, EvaluateRegrowthSpread) then
                     return 'Regrowth Spread Members - Innervate'
                 end
             end
         end
 
         -- Filler Regrowth when mana is sufficient
         if Var['ManaOK'] and ((not Var['IsInCat'] and Player:BuffDown(S.BearForm)) or not Var['IsInCombat']) then
             if S.Regrowth:IsReady(Player) then
                 if CastCycleAlly(S.Regrowth, Members, EvaluateRegrowthMembersFiller) then
                     return 'Regrowth Members - Filler'
                 end
             end
         end
     end
 
     --- Filler healing when moving or between combat
     local function HealingFiller()
         -- Rejuvenation Filler when mana is sufficient
         if Var['ManaOK'] and ((not Var['IsInCat'] and Player:BuffDown(S.BearForm)) or not Var['IsInCombat']) then
             if S.Rejuvenation:IsReady(Player) then
                 if CastCycleAlly(S.Rejuvenation, Members, EvaluateRejuvMembersFiller) then
                     return 'Rejuvenation'
                 end
             end
         end
     end
 
     -- =============================================
     -- Damage Rotation Components
     -- =============================================
 
     -- Custom NPC exclusion list for DoT application
     local ExcludeNPCList = {
         -- Spiteful Shades
         [174773] = true
     }
 
     --- Evaluates Sunfire application on a target
     ---@param TargetUnit Unit
     local function EvaluateCycleSunfireST(TargetUnit)
         local Remains = TargetUnit:DebuffRemains(S.SunfireDebuff)
         return (TargetUnit:DebuffRefreshable(S.SunfireDebuff) and Remains < 2 and (TargetUnit:TimeToDie() - Remains) > 6)
         and not ExcludeNPCList[TargetUnit:NPCID()]
     end
     
     --- Evaluates Moonfire application on a target
     ---@param TargetUnit Unit
     local function EvaluateCycleMoonfireST(TargetUnit)
         local Remains = TargetUnit:DebuffRemains(S.MoonfireDebuff)
         return (TargetUnit:DebuffRefreshable(S.MoonfireDebuff) and Remains < 2 and (TargetUnit:TimeToDie() - Remains) > 6)
         and (PotPAmountStored() >= (PotPMaxAmount()*80/100) or PotPAmountStored() >= (PotPMaxAmount()*50/100) and Target:DebuffRefreshable(S.MoonfireDebuff) or not S.ProtectorofthePack:IsAvailable())
         and not ExcludeNPCList[TargetUnit:NPCID()]
     end
 
     --- Cat Form damage rotation
     local function CatDamage()
         -- Apply DoTs before entering cat form if not already in cat
         if not Var['IsInCat'] then
             if S.Sunfire:IsReady() and Target:DebuffRemains(S.SunfireDebuff) < 9 then
                 if Cast(S.Sunfire) then
                     return 'Sunfire - Target'
                 end
             end
             if S.Moonfire:IsReady() and Target:DebuffRemains(S.MoonfireDebuff) < 9 and EnemiesCount5y < 4 then
                 if Cast(S.Moonfire) then
                     return 'Moonfire - Target'
                 end
             end
         end
 
         -- Enter Cat Form if not already in it
         if S.CatForm:IsReady() and not Var['IsInCat'] and not S.FluidForm:IsAvailable() 
         and not Var['IncarnationTreeofLifeActive'] then
             if Cast(S.CatForm) then 
                 return 'Cat Form DPS'
             end
         end
 
         -- Stealth opener (Rake)
         if Player:StealthUp(true, true) and not S.Rake:IsBlocked() and not Var['IncarnationTreeofLifeActive'] then
             if S.Rake:IsReady() then
                 if Cast(S.Rake) then
                     return 'Rake - Stealth'
                 end
             end
             return
         end
 
         -- Shadowmeld for stealth opener if enabled
         if S.Shadowmeld:IsReady() and Var['shadowmeld_dps'] and not Player:StealthUp(false, true) then
             if Cast(S.Shadowmeld) then return "Shadowmeld"; end
         end
 
         -- Single target rotation
         if EnemiesCount5y == 1 then
             -- Finishers at 5 combo points
             if Var['ComboPoints'] >= 5 then
                 if Target:DebuffRemains(S.RipDebuff) < 10 and Var['TargetTTD'] > 12 then
                     if S.Rip:IsReady() then
                         if Cast(S.Rip) then
                             return '(ST) Rip'
                         end
                     else
                         if S.FerociousBite:IsReady() then
                             if Cast(S.FerociousBite) then
                                 return '(ST) Ferocious Bite - No Rip'
                             end
                         end
                     end
                 else
                     if S.FerociousBite:IsReady() then
                         if Cast(S.FerociousBite) then
                             return '(ST) Ferocious Bite'
                         end
                     end
                 end
             end
 
             -- Builder rotation when not in Tree of Life
             if not Var['IncarnationTreeofLifeActive'] then
                 if S.Rake:IsReady() and Var['TargetTTD'] > 8 and Target:DebuffRefreshable(S.RakeDebuff) then
                     if Cast(S.Rake) then
                         return '(ST) Rake'
                     end
                 end
 
                 if S.Shred:IsReady() then
                     if Cast(S.Shred) then
                         return '(ST) Shred'
                     end
                 end    
             end      
         else
             -- Multi-target rotation
             if Var['ComboPoints'] >= 5 then
                 if S.Rip:IsReady() then
                     if CastCycle(S.Rip, Enemies5y, EvaluateRip) then
                         return "(AoE) Rip";
                     end
                 else
                     if S.FerociousBite:IsReady() then
                         if Cast(S.FerociousBite) then
                             return '(AoE) Ferocious Bite - No Rip'
                         end
                     end
                 end
                 if Player:Energy() >= 90 and Target:DebuffUp(S.Rip) then
                     if S.FerociousBite:IsReady() then
                         if Cast(S.FerociousBite) then
                             return '(AoE) Ferocious Bite'
                         end
                     end
                 end
             end
 
             -- AoE abilities
             if S.ThrashCat:IsReady() and (Target:DebuffDown(S.ThrashDebuff)) then
                 if Cast(S.ThrashCat) then
                     return '(AoE) Thrash'
                 end
             end
 
             if S.Rake:IsReady() then
                 if CastCycle(S.Rake, Enemies5y, EvaluateRake) then
                     return "(AoE) Rake Cycle";
                 end
             end
 
             if S.Swipe:IsReady() and (EnemiesCount5y >= 3 or EnemiesCount5y >= 4) and Player:Energy() >= 40 then
                 if Cast(S.Swipe) then
                     return '(AoE) Swipe'
                 end
             end
 
             if S.Shred:IsReady() and not Var['IncarnationTreeofLifeActive'] then
                 if Cast(S.Shred) then
                     return '(AoE) Shred'
                 end
             end
         end
 
         -- Moonfire application in small AoE situations
         if EnemiesCount5y < 4 then
             if S.Moonfire:IsCastable() then
                 if CastCycle(S.Moonfire, Enemies40y, EvaluateCycleMoonfireST) then
                     return "moonfire cycle";
                 end
             end
         end
 
         -- Sunfire application
         if S.Sunfire:IsCastable() then
             if CastCycle(S.Sunfire, Enemies40y, EvaluateCycleSunfireST) then
                 return "sunfire cycle";
             end
         end
     end
 
     --- Humanoid/Balance damage rotation
     local function HumanRotation()              
         -- Enter Moonkin Form if configured and not already in it
         if S.MoonkinForm:IsReady(Player) and Player:BuffDown(S.MoonkinForm) and (Settings['DpsForm'] == 3 or Settings['DpsForm'] == 1) then
             if Cast(S.MoonkinForm) then
                 return 'MoonkinForm - DPS - Range'
             end
         end
 
         -- Sunfire application
         if S.Sunfire:IsCastable() then
             if CastCycle(S.Sunfire, Enemies40y, EvaluateCycleSunfireST) then
                 return "sunfire st 4";
             end
         end
 
         -- Moonfire application
         if S.Moonfire:IsCastable() then
             if CastCycle(S.Moonfire, Enemies40y, EvaluateCycleMoonfireST) then
                 return "moonfire st 6";
             end
         end
 
         -- Moonfire with Protector of the Pack consideration
         if S.Moonfire:IsReady() and S.ProtectorofthePack:IsAvailable() 
         and (PotPAmountStored() >= (PotPMaxAmount()*80/100) or PotPAmountStored() >= (PotPMaxAmount()*50/100) and Target:DebuffDown(S.MoonfireDebuff)) then
             if Cast(S.Moonfire) then
                 return 'Moonfire - DPS - Range'
             end
         end
 
         -- Sunfire if not applied
         if S.Sunfire:IsReady() and Target:DebuffDown(S.SunfireDebuff) then
             if Cast(S.Sunfire) then
                 return 'Sunfire - DPS - Range'
             end
         end
 
         -- Moonfire in small AoE situations without Protector of the Pack
         if S.Moonfire:IsReady() and EnemiesCount10ySplash < 4 and Target:DebuffDown(S.MoonfireDebuff) and not S.ProtectorofthePack:IsAvailable() then
             if Cast(S.Moonfire) then
                 return 'Moonfire - DPS - Range'
             end
         end
 
         -- Starsurge in single target or with Master Shapeshifter
         if S.Starsurge:IsReady() and (EnemiesCount10ySplash < 4 or S.MasterShapeshifter:IsAvailable()) then
             if Cast(S.Starsurge) then
                 return 'Starsurge - DPS - Range'
             end
         end
 
         -- Starfire with Master Shapeshifter in AoE
         if S.MasterShapeshifter:IsAvailable() then
             if S.Starfire:IsReady() and EnemiesCount10ySplash >= 4 then
                 if Cast(S.Starfire) then
                     return 'Starfire - DPS - Master Shapeshifter'
                 end
             end	
 
             -- Wrath with Master Shapeshifter
             if S.Wrath:IsReady() then
                 if Cast(S.Wrath) then
                     return 'Wrath - DPS - Master Shapeshifter'
                 end
             end
         end
 
         -- Starfire in AoE without Master Shapeshifter
         if S.Starfire:IsReady() and EnemiesCount10ySplash >= 4 then
             if Cast(S.Starfire) then
                 return 'Starfire - DPS - Range'
             end
         end	
         
         -- Wrath as filler
         if S.Wrath:IsReady() and (EnemiesCount10ySplash < 4 or not S.Starfire:IsReady()) then
             if Cast(S.Wrath) then
                 return 'Wrath - DPS - Range'
             end
         end
 
         -- Moonfire as last resort filler
         if S.Moonfire:IsReady() and not S.ProtectorofthePack:IsAvailable() then
             if Cast(S.Moonfire) then
                 return 'Moonfire - DPS - Filler'
             end
         end
     end
 
     --- Main damage rotation handler
     local function DamageRotation()
         -- Convoke the Spirits for damage if configured
         if S.ConvoketheSpirits:IsReady() and ((Player:BuffUp(S.MoonkinForm) and Target:IsInRange(40)) or (Var['IsInCat'] and Var['TargetIsInMeleeRange'])) and Settings['ConvokeOption']['ConvokeDamage'] and (Var['PlayerInBossFight'] or HL.FightRemains(Enemies10ySplash) > 30) and HL.CombatTime() > 5 then         
             if Player:BuffDown(S.MoonkinForm) then
                 -- Enter Moonkin Form for Convoke if configured
                 if S.MoonkinForm:IsReady(Player) and (Settings['DpsForm'] == 1 or Settings['DpsForm'] == 3) and not Settings['DpsForm'] == 2 then
                     if Cast(S.MoonkinForm) then
                         return 'MoonkinForm for Convoke'
                     end
                 end
                 -- Enter Cat Form for Convoke if configured
                 if S.CatForm:IsReady(Player) and (Settings['DpsForm'] == 1 or Settings['DpsForm'] == 2) and not Settings['DpsForm'] == 3 and not Var['IsInCat'] and not Player:BuffUp(S.IncarnationTreeofLifeBuff) 
                 and not Var['IncarnationTreeofLifeActive'] then
                     if Cast(S.CatForm) then 
                         return 'Cat Form for Convoke'
                     end
                 end
             end
             if Cast(S.ConvoketheSpirits) then
                 return "Convoke the Spirits DPS"
             end
         end
 
         -- Cat Form damage if configured or override conditions met
         if (Settings['DpsForm'] == 2 
         or Settings['DpsForm'] == 1 
         or Var['override_cat_form'] and Var['override_cat_form_condition_value']) 
         and Target:IsInRange(Settings['cat_range']) then
             ShouldReturn = CatDamage()
             if ShouldReturn then
                 return "Cat - " .. ShouldReturn
             end
         end
 
         -- Filler Cat DPS but out of range - use ranged abilities
         if (not Target:IsInRange(Settings['cat_range']) and Settings['DpsForm'] == 2) then
             if S.Sunfire:IsCastable() then
                 if CastCycle(S.Sunfire, Enemies40y, EvaluateCycleSunfireST) then
                     return "(Filler Cat) Sunfire";
                 end
             end
 
             if S.Moonfire:IsCastable() then
                 if CastCycle(S.Moonfire, Enemies40y, EvaluateCycleMoonfireST) then
                     return "(Filler Cat) Moonfire";
                 end
             end
         end
 
         -- Humanoid/Balance rotation when not in cat or out of range
         if (not Target:IsInRange(Settings['cat_range']) and Settings['DpsForm'] == 1 or Settings['DpsForm'] == 3 or Settings['DpsForm'] == 4) 
         and (not Var['override_cat_form'] or not Var['override_cat_form_condition_value'] or not Var['TargetIsInMeleeRange']) then
             ShouldReturn = HumanRotation()
             if ShouldReturn then
                 return "Humanoid - " .. ShouldReturn
             end
         end
     end
  
     --- Main Action Priority List (APL) function
     local function APL()
         -- Update all dynamic variables
         UpdateVars()
         
         -- Update enemy tracking based on AoE setting
         if AoEON() then
             Enemies5y = Player:GetEnemiesInRange(10)
             Enemies10ySplash = Target:GetEnemiesInSplashRange(10)
             Enemies40y = Player:GetEnemiesInRange(40)
         else
             Enemies5y = { Target }
             Enemies10ySplash = { Target }
             Enemies40y = { Target }
         end
         EnemiesCount5y = #Enemies5y
         EnemiesCount10ySplash = #Enemies10ySplash
 
         -- Adjust splash count for small AoE situations
         if AoEON() and EnemiesCount10ySplash < 3 then
             EnemiesCount10ySplash = #Enemies40y
         end
         
         -- Don't interrupt channeling
         if Player:IsChanneling() then
             return
         end
 
         -- Bear Mode toggle handling
         if M.Toggle:GetToggle('BearMode') then
             if S.BearForm:IsReady(Player) and Player:BuffDown(S.BearForm) then
                 if Cast(S.BearForm) then
                     return "Bear Mode: Bear Form"
                 end
             end
             return "Holding Bear"
         end
 
         -- Swiftmend queue override handling
         do
             ---@diagnostic disable-next-line: assign-type-mismatch
             local IsQueued, QueueTarget = S.Swiftmend:IsQueued()
             if IsQueued then
                 ---@diagnostic disable-next-line: undefined-field
                 if QueueTarget:BuffDown(S.Rejuvenation) and QueueTarget:BuffDown(S.Regrowth) and QueueTarget:BuffDown(S.WildGrowth) then
                     ---@diagnostic disable-next-line: param-type-mismatch
                     if S.Rejuvenation:IsReady(QueueTarget) then
                         if Cast(S.Rejuvenation, QueueTarget) then
                             return "Swiftmend Reju Override"
                         end
                     end
                 end
             end
         end
 
         -- Trinket usage
         ShouldReturn = MainAddon.TrinketHealing(Members, OnUseExcludes)
         if ShouldReturn then
             return ShouldReturn
         end
 
         -- Mana potion usage
         if MainAddon.UseManaPotion() then
             MainAddon.SetTopColor(1, "Combat Potion")
         end
 
         -- Utilities (rebirth, innervate, etc.)
         ShouldReturn = Utilities()
         if ShouldReturn then
             return "Utilities: " .. ShouldReturn
         end
 
         -- Defensive cooldowns
         ShouldReturn = Defensives()
         if ShouldReturn then
             return "Defensives: " .. ShouldReturn
         end
 
         -- Hold in Bear Form if conditions met
         if Player:BuffUp(S.BearForm) and Var['HoldInBear'] then
             return
         end
 
         -- DPS trinkets and forced DPS mode
         if Var['TargetIsValid'] then
             ShouldReturn = Trinkets();
             if ShouldReturn then
                 return ShouldReturn;
             end
             if MainAddon.Toggle:GetToggle('ForceDPS') then
                 ShouldReturn = DamageRotation();
                 if ShouldReturn then
                     return "Force DPS Toggle: " .. ShouldReturn;
                 end
             end
         end
 
         -- Update Lifebloom target list if needed
         if Var['UpdateLifebloom'] then
             Sort_LifebloomList()
         end
         
         -- Ramp healing toggle logic (preparing for incoming damage)
         if MainAddon.Toggle:GetToggle("Ramp") then
             -- Spread Rejuvenations if below threshold
             if S.Rejuvenation:IsReady(Player) and RejuvenationCount() < 5 then
                 if CastCycleAlly(S.Rejuvenation, Members, EvaluateRejuvRamp) then
                     return 'Ramp Healing Toggle: Rejuv'
                 end
             end
 
             -- Swiftmend to prepare for Wild Growth
             if S.Swiftmend:IsReady(Player) and (S.WildGrowth:IsCastable() or not S.SouloftheForest:IsAvailable()) then
                 if CastCycleAlly(S.Swiftmend, Members, EvaluateSwiftmendSotF) then
                     return 'Ramp: Swiftmend'
                 end
             end
 
             -- Place first Grove Guardian if needed
             if S.GroveGuardians:IsReady(Player) then
                 if GroveGuardianAmount() <= 1 then
                     if CastCycleAlly(S.GroveGuardians, Members, EvaluateTrue, nil, nil, nil, nil, true) then
                         return 'Ramp: First Guardian'
                     end
                 end
             end
 
             -- Wild Growth with Soul of the Forest if available
             if Player:BuffUp(S.SouloftheForestBuff) then
                 if S.WildGrowth:IsReady(Player) then
                     if CastCycleAlly(S.WildGrowth, Members, EvaluateTrue, nil, nil, nil, nil, true) then
                         return 'Ramp: Wild Growth'
                     end
                 end
                 return
             end
 
             -- Place additional Grove Guardians if needed
             if S.GroveGuardians:IsReady(Player) then
                 if GroveGuardianAmount() <= 3 then
                     if CastCycleAlly(S.GroveGuardians, Members, EvaluateTrue, nil, nil, nil, nil, true) then
                         return 'Ramp: Second Guardian'
                     end
                 end
             end
 
             -- Flourish to extend all HoTs
             if S.Flourish:IsReady(Player) then
                 if Cast(S.Flourish) then
                     return 'Ramp: Flourish'
                 end
             end
 
             -- Spread Regrowths for full coverage
             if S.Regrowth:IsReady(Player) then
                 if CastCycleAlly(S.Regrowth, Members, EvaluateRegrowthRamp) then
                     return 'Ramp: Regrowths'
                 end
             end
             -- Turn off ramp toggle if all members have Regrowth
             if RegrowthCount() >= #Members then
                 MainAddon.Toggle:SetToggle("Ramp", false)
             end
         end
 
         -- Spread Rejuvenation when Toggle enabled
         if MainAddon.Toggle:GetToggle("SpreadRejuvenation") then
             if S.Rejuvenation:IsReady(Player) then
                 -- Lifebloom management
                 if Var['IsInCombat'] or Player:IsInPvEActivity() then
                     if Lifebloom:IsReady(Player) then
                         if (not Var['IsInCat'] and Player:BuffDown(S.BearForm) and Player:BuffDown(S.TravelForm) and Player:BuffDown(S.MountForm) and Player:BuffDown(S.MoonkinForm) and Player:BuffDown(S.CatForm)) then
                             ---@class Unit
                             local LifebloomTarget = LifebloomMembers[1]
                             ---@class Unit
                             local LifebloomTarget2 = LifebloomMembers[2]
 
                             if LifebloomTarget and Lifebloom:IsReady(LifebloomTarget) and EvaluateLifeBloomTanks(LifebloomTarget) then
                                 if CastAlly(Lifebloom, LifebloomTarget) then return "Lifebloom ->" .. LifebloomTarget:Name(); end
                             end
                     
                             if S.Undergrowth:IsAvailable() then
                                 if LifebloomTarget2 and Lifebloom:IsReady(LifebloomTarget2) and EvaluateLifeBloomTanks(LifebloomTarget2) then
                                     if CastAlly(Lifebloom, LifebloomTarget2) then return "Lifebloom ->" .. LifebloomTarget2:Name(); end
                                 end
                             end
                         end
                     end
                 end
 
                 -- Efflorescence placement
                 if S.Efflorescence:IsReady(Player) and Var['IsInCombat']
                 and (Player:BuffUp(S.EfflorescenceIsActive) and Player:BuffRemains(S.EfflorescenceIsActive) < 2 or not AnyoneOnCurrentEfflorescence()) and HealingEngine:MembersUnderPercentage(Settings['Efflorescence_underX_val']) >= Settings['Efflorescence_underX']
                 and ((not Var['IsInCat'] and Player:BuffDown(S.BearForm))) then
                     local magicgroundspell_target = Settings['magicgroundspell']
 
                     if magicgroundspell_target == 1 then
                         if #Tanks > 0 then
                             if CastMagicAlly(S.Efflorescence, Tanks[1], nil, "145205-Magic") then 
                                 return "Efflorescence - Friend"
                             end
                         end
                     end
 
                     if magicgroundspell_target == 2 then
                         if Var['TargetIsValid'] then
                             if CastMagic(S.Efflorescence, nil, "145205-Magic") then 
                                 return "Efflorescence - Enemy"
                             end
                         end
                     end
 
                     if magicgroundspell_target == 3 then
                         if Cast(S.Efflorescence) then
                             return "Efflorescence - Regular"
                         end
                     end
                 end
 
                 -- Spread Rejuvenations
                 if CastCycleAlly(S.Rejuvenation, Members, EvaluateRejuvSpread) then
                     return 'Rejuv Spread - Toggle'
                 end
                 if S.Germination:IsAvailable() then
                     if CastCycleAlly(S.Rejuvenation, Members, EvaluateRejuvSpreadGermination) then
                         return 'Rejuv Germination Spread - Toggle'
                     end
                 end
             end
         end
 
         -- Tree of Life form management
         if Var['IsInCombat'] or (not Var['IsInCat'] and Player:BuffDown(S.BearForm) and Player:BuffDown(S.TravelForm) and Player:BuffDown(S.TravelForm)) then
             if Player:BuffUp(S.IncarnationTreeofLifeBuff) and not Player:BuffUp(S.IncarnationTreeofLife) then
                 if Cast(S.IncarnationTreeofLife) then
                     return 'Go back to ToL'
                 end
             end
         end
 
         -- Healing cooldowns in combat
         if Var['IsInCombat'] then 
             ShouldReturn = HealingCDsCombat()
             if ShouldReturn then
                 return "Healing CDs: " .. ShouldReturn
             end
         end
 
         -- Heart of the Wild under Bloodlust when healing is stable
         if Var['IsInCombat'] and Player:BloodlustUp() then
             if S.HeartoftheWild:IsReady() and Var['AverageHPInRange'] >= 89 then
                 if Cast(S.HeartoftheWild) then
                     return 'Heart of the Wild under Bloodlust'
                 end
             end
         end
 
         -- Healing cooldowns out of combat
         if Var['IsInCombat'] or Player:IsInPvEActivity() then
             ShouldReturn = HealingCDsOOC()
             if ShouldReturn then
                 return "Healing CDs: " .. ShouldReturn
             end
         end
 
         -- Main healing rotation when not cat weaving or when priority healing needed
         if HealingEngine:MembersUnderPercentage(Settings['StopCatWeaving_underX_val']) >= Settings['StopCatWeaving_underX'] or not Var['IsInCat'] or EvaluatePrioHealing() then
             ShouldReturn = HealingRotation()
             if ShouldReturn then
                 return "Healing Rotation: " .. ShouldReturn
             end
         end
 
         -- Damage incoming prediction handling
         Reason, SpellID = MainAddon:DamageIncoming()
         if Reason == "SOON" then
             MainAddon.UI:ShowToast(MainAddon.GetSpellInfo(SpellID), "Predicting major incoming damage.", MainAddon.GetTexture(Spell(SpellID)), 10)
 
             ShouldReturn = DamageIncoming();
             if ShouldReturn then
                 return ShouldReturn;
             end
         end
 
         -- Filler healing when moving in combat with group
         if Player:IsMoving() and not Var['IsInCat'] and Var['IsInCombat'] and IsInGroup() then
             ShouldReturn = HealingFiller()
             if ShouldReturn then
                 return "Filler: " .. ShouldReturn
             end
         end
 
         -- Break rotation to prepare for Convoke the Spirits healing
         if S.ConvoketheSpirits:IsReady(Player) and Settings['ConvokeOption']['ConvokeHeal'] and HealingEngine:MembersUnderPercentage(Settings['Convoke_underX_val']) >= Settings['Convoke_underX']
         and (Player:BuffUp(S.NaturesSwiftness) or S.NaturesSwiftness:CooldownRemains() > 0 or not S.NaturesSwiftness:IsAvailable()) then
             return 
         end
 
         -- Form management when not in Innervate
         if Player:BuffDown(S.Innervate, true) then
             -- Auto Prowl if enabled
             if S.Prowl:IsReady(Player) and Settings['AutoProwl'] and Player:BuffDown(S.Prowl) then
                 if Cast(S.Prowl) then return "Prowl - Stealth" end
             end
 
             -- Out of combat form management
             if not Settings['AutoProwl'] then
                 if not Var['IsInCombat'] and Settings['OOCForm'] ~= "NoneForm" and not Player:IsCasting() and Player:GCDRemains() == 0 then
                     if S.BearForm:IsReady(Player) and Settings['OOCForm'] == "BearForm" and Player:BuffDown(S.BearForm) then
                         if Cast(S.BearForm) then return "Bear Form OOC" end
                     elseif S.CatForm:IsReady(Player) and Settings['OOCForm'] == "CatForm" and not Var['IsInCat'] and not Settings['AutoProwl'] then
                         if Cast(S.CatForm) then return "Cat Form OOC" end
                     elseif S.TravelForm:IsReady(Player) and Settings['OOCForm'] == "TravelForm" and (Player:BuffDown(S.TravelForm) and Player:BuffDown(S.MountForm)) and not IsIndoors() and Player:IsMoving() then
                         if Cast(S.TravelForm) then return "Travel Form OOC" end	
                     elseif S.TreantForm:IsReady(Player) and Settings['OOCForm'] == "TreantForm" and Player:BuffDown(S.TreantForm) then
                         if Cast(S.TreantForm) then return "Treant Form OOC" end	
                     end
                 end
             end
         end
 
         -- Damage rotation when target is valid and DPS method is Auto
         if Var['TargetIsValid'] and Settings['DpsMethod'] == 1 then
             ShouldReturn = DamageRotation()
             if ShouldReturn then
                 return "Damage: " .. ShouldReturn
             end
         end
     end
 
     -- =============================================
     -- Initialization and Event Handlers
     -- =============================================
 
     -- Necrolord covenant warning popup
     StaticPopupDialogs["RDRUID_POPUP"] = {
         text = "??: It seems you are Necrolord.\nYou may have issues with the talent Adaptive Swarm.\n\nPlease change your Covenant to Venthyr in Oribos.",
         button1 = "OK",
     }
     if C_Covenants.GetActiveCovenantID() == 4 then
         _G.StaticPopup_Show("RDRUID_POPUP")
     end
 
     -- Initialization function
     local function Init()
     end
     
     -- Set the APL for Restoration Druid (105)
     M.SetAPL(105, APL, Init)
  
     -- Core Overrides for Restoration Druid specific behavior
 
     -- Swiftmend returns not usable for off targets.
     local OldIsUsable
     OldIsUsable = HL.AddCoreOverride("Spell.IsUsable",
         function(self)
             if MainAddon.PlayerSpecID() == 105 then
                 if self == S.Swiftmend then
                     return true
                 end
             end
             local BaseCheck, ReasonUsable = OldIsUsable(self)
             return BaseCheck, ReasonUsable
         end
     , 105);
 
     -- Form cooldown override (forms shouldn't have cooldowns when not blocked)
     local OldCooldownRemains
     OldCooldownRemains = HL.AddCoreOverride("Spell.CooldownRemains",
         function(self, BypassRecovery, BypassCD)
             local BaseCheck = OldCooldownRemains(self, BypassRecovery, BypassCD)
             if MainAddon.PlayerSpecID() == 105 then
                 if self == S.CatForm then
                     if not S.CatForm:IsBlocked() then
                         return 0
                     end
                 end
 
                 if self == S.BearForm then
                     if not S.BearForm:IsBlocked() then
                         return 0
                     end
                 end
             end
             return BaseCheck
         end
     , 105)
 
     -- Castable override with special conditions
     local OldIsCastable
     OldIsCastable = HL.AddCoreOverride("Spell.IsCastable",
         function(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
             if MainAddon.PlayerSpecID() == 105 then
                 -- Reforestation pooling check
                 if MainAddon.Toggle:GetToggle("ReforestationPooling") and S.Reforestation:IsAvailable() and self == S.Swiftmend and Player:BuffStack(S.ReforestationBuff) == 2 then
                     return false, 'Pooling for Reforestation'
                 end                    
                 
                 -- Efflorescence cooldown check
                 if self == S.Efflorescence and GetTime() - Var['LastTimeEfflorescenceCast'] < 3 then
                     return false, "Efflorescence just casted"
                 end
 
                 -- Grove Guardians cooldown check
                 if self == S.GroveGuardians and GetTime() - Var['LastTimeGroveGuardiansCast'] < 3 then
                     return false, "Grove Tending just casted"
                 end
 
                 -- Mark of the Wild stealth check
                 if self == S.MarkoftheWild and Player:StealthUp(true, true) then
                     return false, "Stealth"
                 end
 
                 -- Cat form ability range checks
                 if self == S.ThrashCat or self == S.Swipe then
                     if not Var['TargetIsInMeleeRange'] then
                         return false, "Target not in melee range"
                     end
                 end 
 
                 -- Tranquility movement check
                 if self == S.Tranquility then
                     if Player:IsStandingStillFor() < GetSetting('Tranquility_StandStill', 1) then
                         return false, "Tranquility - Stand Still"
                     end
                 end
 
                 -- Mana management checks in group content
                 if IsInGroup() then
                     if self == S.CatForm then
                         if Player:BuffUp(S.Innervate, true) then
                             return false, "Innervate"
                         end
                     end
 
                     if Settings['ManaManagement'] then
                         if self == S.Moonfire or self == S.Sunfire or self == S.Starfire then
                             if Player:ManaPercentage() < 40 then
                                 return false, "Mana Management"
                             end
                         end
                     end
                 end
             end
             local BaseCheck, Reason = OldIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
             return BaseCheck, Reason
         end
     , 105);
 
     -- Tank unit tracking for Efflorescence
     HL:RegisterForEvent(function()
         if S.Efflorescence:IsAvailable() then
             if Tanks then
                 for i, ThisUnit in pairs(Tanks) do
                     Var['TankUnit'] = ThisUnit
                 end
             end
         end
     end, "GROUP_ROSTER_UPDATE", "PLAYER_ENTERING_WORLD", "ZONE_CHANGED")
 
     -- Efflorescence healing event tracking
     HL:RegisterForEvent(function(...)
        local _, subevent, _, sourceGUID, _, _, _, _, destName, _, _, spellId = CombatLogGetCurrentEventInfo()
        if sourceGUID == Player:GUID() then
            if spellId == 81269 and subevent == "SPELL_HEAL" then
                Var['LastTimeEfflorescenceHealed'] = GetTime()
            end
            -- Lifebloom tracking and cleanup on combat end
            if subevent == 'SPELL_AURA_REMOVED' then
                if spellId == 774 then
                    Var['UpdateLifebloom'] = true
                end
            end
        end
     end, "COMBAT_LOG_EVENT_UNFILTERED")
 
     -- Spell cast tracking for various purposes
     HL:RegisterForEvent(function(arg1, arg2, arg3, arg4, arg5)
         if arg2 == "player" then
             if arg5 == 145205 then
                 Var['LastTimeEfflorescenceCast'] = GetTime()
             end
 
             if arg5 == 102693 then
                 Var['LastTimeGroveGuardiansCast'] = GetTime()
             end
 
             -- Regrowth blacklist to prevent spam
             if arg5 == 8936 then
                 TempBlackListRegrowth[arg3] = true
                 C_Timer.After(S.Regrowth:CastTime() + (HL.Latency()*7), function()
                     TempBlackListRegrowth[arg3] = nil
                 end)
             end
 
             -- Symbiotic Relationship blacklist to prevent spam
             if arg5 == 474750 then
                 TempBlackListSymbioticRelationship[arg3] = true
                 C_Timer.After(S.SymbioticRelationship:CastTime() + (HL.Latency()*7), function()
                     TempBlackListSymbioticRelationship[arg3] = nil
                 end)
             end
         end
     end, "UNIT_SPELLCAST_SENT")
    
     -- General cleanup and reset on various events
     HL:RegisterForEvent(function()
         wipe(TempBlackListRegrowth)
         Var['UpdateLifebloom'] = true
     end, "PLAYER_REGEN_ENABLED", "PLAYER_ENTERING_WORLD", "UPDATE_CHAT_WINDOWS", "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB", "GROUP_ROSTER_UPDATE")
    
     -- Group composition update tracking
     HL:RegisterForEvent(function()
        Tanks, Healers, Members, Damagers, Melees, _, _, _, TargetIfAlly = HealingEngine:Fetch()
     end, "PLAYER_ENTERING_WORLD", "GROUP_ROSTER_UPDATE")
       
     -- =============================================
     -- Slash Commands
     -- =============================================
 
     -- DPS Form slash command handler
     SLASH_DPSFORM1 = "/dpsform"
     _G.SlashCmdList["DPSFORM"] = function(msg)
         msg = string.lower(msg)
         local args = { _G.strsplit("-", msg) } -- Split the message by hyphen to allow dual form settings
         local validForms = { ["auto"] = 1, ["cat"] = 2, ["moonkin"] = 3, ["humanoid"] = 4 }
         local formNames = { "Auto", "Cat", "Moonkin", "Humanoid" }
     
         local forms = {}
         for _, arg in ipairs(args) do
             if validForms[arg] then
                 table.insert(forms, validForms[arg])
             else
                 MainAddon:Print("Invalid input: " .. arg .. ". Please use Auto, Cat, Moonkin, or Humanoid.")
                 return
             end
         end
     
         if #forms == 1 then
             -- Single form setting
             MainAddon.Config.SetSetting(Config_Key, 'DpsForm', forms[1])
             MainAddon:Print("DPS Form set to: |cFFFFA500" .. formNames[forms[1]] .. "|r")
             MainAddon.UI[105].elements.DpsForm:SetValue(forms[1])
         elseif #forms == 2 then
             -- Dual form setting (toggle between two forms)
             if GetSetting("DpsForm", 1) ~= forms[1] then
                 MainAddon.Config.SetSetting(Config_Key, 'DpsForm', forms[1])
                 MainAddon:Print("DPS Form set to: |cFFFFA500" .. formNames[forms[1]] .. "|r")
                 MainAddon.UI[105].elements.DpsForm:SetValue(forms[1])
             else
                 MainAddon.Config.SetSetting(Config_Key, 'DpsForm', forms[2])
                 MainAddon:Print("DPS Form set to: |cFFFFA500" .. formNames[forms[2]] .. "|r")
                 MainAddon.UI[105].elements.DpsForm:SetValue(forms[2])
             end
         end
     end
     
     -- Innervate slash command handler
     function MainAddon.Druid.InnervateSlashCommand()
         MainAddon.Druid.InnervateTargetName = Target:UnfilterName() or "None"
     end
     SLASH_INNERVATE1 = "/innervate"
     SlashCmdList["INNERVATE"] = MainAddon.Druid.InnervateSlashCommand
 
     -- =============================================
     -- Right Click Menu Modifications
     -- =============================================
 
     --- Right click menu handler for unit frames
     ---@param rootDescription Menu
     local function RightClick(_, rootDescription, contextData)
         local unit = contextData.unit
         local unitName = contextData.name
     
         if MainAddon.PlayerSpecID() ~= 105 then
             return
         end
     
         if not unit or UnitCanAttack(Player:ID(), unit) then
             return
         end
 
         -- Lifebloom button creation
         local SetUnset_Lifebloom = LifebloomPriority[unitName] and "Unset" or "Set"
         local colorCode_Lifebloom = LifebloomPriority[unitName] and "|cffff0000" or "|cff00ff00"
         
         rootDescription:CreateButton("|T134206:24|t" .. " " .. colorCode_Lifebloom .. SetUnset_Lifebloom .. " Lifebloom" .. "|r", function()
             Var['UpdateLifebloom'] = true
             if not LifebloomPriority[unitName] then
                 if not S.Undergrowth:IsAvailable() then
                     LifebloomPriority = {}
                 end
                 LifebloomPriority[unitName] = true
             else
                 LifebloomPriority[unitName] = false
             end
             MainAddon:Print("Lifebloom Priority on " .. unitName .. ": ", LifebloomPriority[unitName], 2)
         end)
           
         -- Innervate button creation
         local SetUnset_Innervate = MainAddon.Druid.InnervateTargetName == "None" and "Set" or MainAddon.Druid.InnervateTargetName == unitName and "Unset" or "Set"
         local colorCode_Innervate = MainAddon.Druid.InnervateTargetName ~= "None" and "|cffff0000" or "|cff00ff00"
         
         rootDescription:CreateButton("|T136048:24|t" .. " " .. colorCode_Innervate .. SetUnset_Innervate .. " Innervate" .. "|r", function()
             if MainAddon.Druid.InnervateTargetName == "None" then
                 MainAddon.Druid.InnervateTargetName = unitName
                 MainAddon:Print("Innervate on: " .. unitName .. " ", true, 2)
             else
                 if MainAddon.Druid.InnervateTargetName == unitName then
                     MainAddon.Druid.InnervateTargetName = "None"
                     MainAddon:Print("Innervate removed for: " .. unitName .. " ", false, 2)
                 else
                     MainAddon:Print("Innervate removed for: " .. MainAddon.Druid.InnervateTargetName .. " ", false, 2)
                     MainAddon.Druid.InnervateTargetName = unitName
                     MainAddon:Print("Innervate on: " .. unitName .. " ", true, 2)
                 end
             end
         end)
     end
     
     -- Apply right click menu modifications to various unit frame types
     Menu.ModifyMenu("MENU_UNIT_SELF", RightClick);
     Menu.ModifyMenu("MENU_UNIT_PLAYER", RightClick);
     Menu.ModifyMenu("MENU_UNIT_TARGET", RightClick);
     Menu.ModifyMenu("MENU_UNIT_FOCUS", RightClick);
     Menu.ModifyMenu("MENU_UNIT_RAID_PLAYER", RightClick);
     Menu.ModifyMenu("MENU_UNIT_PARTY", RightClick);
 end