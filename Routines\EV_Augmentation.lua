function A_1473()
    -- HR UPDATE: feat(Augmentation): Updates to BlisteringScalesCheck(); Add disable option (10/02/24)
    -- REMEMBER: Custom BreathofEons
    -- Addon
    ---@class MainAddon
    local MainAddon = MainAddon
    local M = MainAddon
    ---@class HeroCache
    local Cache = HeroCache
    ---@class HealingEngine
    local HealingEngine = MainAddon.HealingEngine
    -- HeroLib
    local HL = HeroLibEx
    local Utils = HL.Utils
    ---@class Unit
    local Unit = HL.Unit
    ---@class Unit
    local Player = Unit.Player
    ---@class Unit
    local Target = Unit.Target
    ---@class Unit[]
    local Nameplate = Unit.Nameplate
    ---@class Spell
    local Spell = HL.Spell
    local CreateSpell = M.CreateSpell
    ---@class Item
    local Item = HL.Item
    -- HeroRotation
    local CastAlly = M.CastAlly
    local CastCycleAlly = M.CastCycleAlly
    local AoEON = M.AoEON
    local Cast = M.Cast
    -- Lua
    local strsplit = _G['strsplit']
    local UnitInRaid = _G['UnitInRaid']
    local UnitInParty = _G['UnitInParty']
    local UnitGroupRolesAssigned = _G['UnitGroupRolesAssigned']
    local ipairs = ipairs
    local pairs = pairs
    local GetTime = _G['GetTime']
    local tinsert = _G['tinsert']
    local tremove = _G['tremove']
    local EssencePowerType = _G['Enum'].PowerType.Essence
    local GetPowerRegenForPowerType = _G['GetPowerRegenForPowerType']
    local GetUnitName = _G['GetUnitName']
    local UnitGUID = _G['UnitGUID']
    local max           = math.max
    local min           = math.min
    local wipe          = _G['wipe']
    local UIDropDownMenu_AddButton = _G['UIDropDownMenu_AddButton']
    local UIDropDownMenu_CreateInfo = _G['UIDropDownMenu_CreateInfo']
    local IsInGroup = _G['IsInGroup']
    local UnitCanAttack = _G['UnitCanAttack']
    local UnitPower = _G['UnitPower']

    local S = Spell.Evoker.Augmentation
    local I = Item.Evoker.Augmentation
    
    ---GUI SETTINGS
    local GetSetting = MainAddon.Config.GetClassSetting
    local Config_Key = MainAddon.GetClassVariableName()
    local Config_Color = '33937F'
    local Config_Table = {
        key = Config_Key,
        title = 'Evoker - Augmentation',
        subtitle = '?? ' .. MainAddon.Version,
        width = 600,
        height = 700,
        profiles = true,
        config = {
            { type = 'header', text = '', size = 24, align = 'Center', color = Config_Color },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
            { type = 'header', text = 'Info', color = Config_Color },
            { type = 'text', text ="This spec has special features tied to right-clicking. \nRight-click a party member under your party frame to show the options."},
            { type = 'texture', align = 'CENTER', height = 128, width = 128, texture = MainAddon.mediaDir .. "dropdown_example.blp", x = 0, y = -65, },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
            { type = 'header', text = 'DPS', color = Config_Color },
            { type = 'dropdown', text = ' Tip the Scales', key = 'TS_spell', icon = S.TipTheScales:ID(),
                list = {
                    { text = 'Fire Breath', key = 1 },
                    { text = 'Upheaval', key = 2 },
                },
                default = 1,          
            },
            { type = 'spacer' },
            { type = 'header', text = 'Defensives', color = Config_Color },
            { type = 'checkspin', text = ' Obsidian Scales', icon = S.ObsidianScales:ID(), key = 'obscales', min = 1, max = 100, default_spin = 35, default_check = true },
            { type = 'spinner', text = ' Smart Zephyr above key level', key = 'smart_feint_above_key_level', icon = S.Zephyr:ID(), min = 1, max = 40, default = 2},
            { type = 'spacer' },
            { type = 'checkbox', text = ' Renewing Blaze', icon = S.RenewingBlaze:ID(), key = 'renewblazesmart', default = true },
            { type = 'spacer' },
            { type = 'checkspin', text = " Emerald Blossom", icon = S.EmeraldBlossom:ID(), key = 'eblossom', min = 1, max = 100, default_spin = 45, default_check = true },
            { type = 'checkbox', text = " Emerald Blossom: Also check party's allies health", icon = S.EmeraldBlossom:ID(), key = 'eblossom_allies', default = true },
            { type = 'spacer' },
            { type = 'checkbox', text = ' Only Breath of Eons when Power Infusion has been used', icon = S.BreathofEons:ID(), key = 'boe_pi', default = false },
            -- { type = 'dropdown',
            --   text = ' Living Flame', key = 'lflame_heal',
            --   icon = S.LivingFlame:ID(),
            --   multiselect = true,
            --   list = {
            --       { text = 'In Combat', key = 'lflame_heal_combat' },
            --       { text = 'Out of Combat', key = 'lflame_heal_ooc' },
            --   },
            --   default = {
            --       "lflame_heal_ooc",
            --   },
            -- },
            -- { type = 'spinner', text = " Living Flame's health threshold", icon = S.LivingFlame:ID(), key = 'lflame_threshold', min = 0, max = 100, default = 50, step = 1 },
            -- { type = 'spacer' },
            { type = 'header', text = 'Healing - Unit Health threshold', color = Config_Color },
            { type = 'spinner', text = ' Verdant Embrace', key = 'VEHP', icon = S.VerdantEmbrace:ID(), min = 1, max = 100, default = 60 },
            { type = 'dropdown', text = ' Verdant Embrace - Units', key = 'VE_Unit', icon = S.VerdantEmbrace:ID(),
                  list = {
                      { text = 'Tanks', key = 1 },
                      { text = 'Self', key = 2 },
                      { text = 'Everyone', key = 3 }
                  },
                  default = 3,          
            },
            { type = 'spacer' },
            { type = 'header', text = 'Utilities', color = Config_Color },
            { type = 'dropdown',
                text = ' Prescience', key = 'pres',
                icon = S.Prescience:ID(),
                multiselect = true,
                list = {
                    { text = 'In Combat', key = 'pres_combat' },
                    { text = 'Out of Combat', key = 'pres_ooc' },
                },
                default = {
                    "pres_combat"
                },
            },
            { type = 'checkbox', text = ' Use Details! for Prescience priority', icon = S.Prescience:ID(), key = 'PrescienceUsingDetails', default = true },
            { type = 'spinner', text = " Ebon Might - Number of Prescience's buff detected (Raid)", key = 'ebon_m_presc', icon = S.EbonMight:ID(), min = 0, max = 3, default = 2 },
            { type = 'spinner', text = ' Prescience - Number of Units', key = 'pres_number', icon = S.Prescience:ID(), min = 1, max = 3, default = 2 },
            { type = 'checkspin', text = ' Hover movement threshold', icon = S.Hover:ID(), key = 'hover', min = 0, max = 15, default_spin = 2, default_check = false },
            { type = 'spacer' },
            { type = 'header', text = 'Empower Spells', color = Config_Color },
            { type = 'checkbox', text = " Fire Breath - Empower Level - Custom logic (enables settings below)", icon = S.FireBreath:ID(), key = 'fb_emp_level_custom', default = false },
            { type = 'dropdown',
                text = ' Fire Breath - Empower Level - 1 Target', key = 'fb_emp_level_1T',
                icon = S.FireBreath:ID(),
                list = {
                    { text = '1', key = 1 },
                    { text = '2', key = 2 },
                    { text = '3', key = 3 },
                    { text = '4', key = 4 },
                },
                default = 1,
            },
            { type = 'dropdown',
                text = ' Fire Breath - Empower Level - 2 Targets', key = 'fb_emp_level_2T',
                icon = S.FireBreath:ID(),
                list = {
                    { text = '1', key = 1 },
                    { text = '2', key = 2 },
                    { text = '3', key = 3 },
                    { text = '4', key = 4 },
                },
                default = 2,
            },
            { type = 'dropdown',
                text = ' Fire Breath - Empower Level - 3 Targets', key = 'fb_emp_level_3T',
                icon = S.FireBreath:ID(),
                list = {
                    { text = '1', key = 1 },
                    { text = '2', key = 2 },
                    { text = '3', key = 3 },
                    { text = '4', key = 4 },
                },
                default = 3,
            },
            { type = 'dropdown',
                text = ' Fire Breath - Empower Level - 4 Targets', key = 'fb_emp_level_4T',
                icon = S.FireBreath:ID(),
                list = {
                    { text = '1', key = 1 },
                    { text = '2', key = 2 },
                    { text = '3', key = 3 },
                    { text = '4', key = 4 },
                },
                default = 4,
            },
            { type = 'dropdown',
                text = ' Fire Breath - Empower Level - 5+ Targets', key = 'fb_emp_level_5T',
                icon = S.FireBreath:ID(),
                list = {
                    { text = '1', key = 1 },
                    { text = '2', key = 2 },
                    { text = '3', key = 3 },
                    { text = '4', key = 4 },
                },
                default = 4,
            },
            { type = 'spacer' },
            { type = 'checkbox', text = " Upheaval - Empower Level - Custom logic (enables settings below)", icon = S.Upheaval:ID(), key = 'up_emp_level_custom', default = false },
            { type = 'dropdown',
                text = ' Upheaval - Empower Level - 1 Target', key = 'up_emp_level_1T',
                icon = S.Upheaval:ID(),
                list = {
                    { text = '1', key = 1 },
                    { text = '2', key = 2 },
                    { text = '3', key = 3 },
                    { text = '4', key = 4 },
                },
                default = 1,
            },
            { type = 'dropdown',
                text = ' Upheaval - Empower Level - 2 Targets', key = 'up_emp_level_2T',
                icon = S.Upheaval:ID(),
                list = {
                    { text = '1', key = 1 },
                    { text = '2', key = 2 },
                    { text = '3', key = 3 },
                    { text = '4', key = 4 },
                },
                default = 1,
            },
            { type = 'dropdown',
                text = ' Upheaval - Empower Level - 3 Targets', key = 'up_emp_level_3T',
                icon = S.Upheaval:ID(),
                list = {
                    { text = '1', key = 1 },
                    { text = '2', key = 2 },
                    { text = '3', key = 3 },
                    { text = '4', key = 4 },
                },
                default = 1,
            },
            { type = 'dropdown',
                text = ' Upheaval - Empower Level - 4 Targets', key = 'up_emp_level_4T',
                icon = S.Upheaval:ID(),
                list = {
                    { text = '1', key = 1 },
                    { text = '2', key = 2 },
                    { text = '3', key = 3 },
                    { text = '4', key = 4 },
                },
                default = 1,
            },
            { type = 'dropdown',
                text = ' Upheaval - Empower Level - 5+ Targets', key = 'up_emp_level_5T',
                icon = S.Upheaval:ID(),
                list = {
                    { text = '1', key = 1 },
                    { text = '2', key = 2 },
                    { text = '3', key = 3 },
                    { text = '4', key = 4 },
                },
                default = 1,
            },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
        }
    }
    Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
    Config_Table.config = MainAddon.BuildHealingTrinketUI(Config_Table.config, Config_Color)
    Config_Table.config = MainAddon.BuildCombatPotionUI(Config_Table.config, "Augmentation", Config_Color)
    M.SetConfig(1473, Config_Table)
    
    -- Custom
    local Tanks, Healers, Members, Damagers, Melees, RawMembers
    local Enemies25y
    local Enemies8ySplash
    local EnemiesCount8ySplash
    local SoMTargetName = ""
    local InDungeon
    local PriestInGroup = false
    local PriestsTable = {}
    local LastPowerInfusionCastTime = 0
    local fb_emp_level_custom = GetSetting('fb_emp_level_custom', false)
    local up_emp_level_custom = GetSetting('up_emp_level_custom', false)

    local FireBreathFoM = CreateSpell(382266)
    local UpheavelFoM = CreateSpell(408092)
    local FireBreath = CreateSpell(357208)
    local Upheavel = CreateSpell(396286)
    local MassEruption = CreateSpell(438587)
    local MassEruptionStacks = CreateSpell(438588)
    local ImminentDestruction = CreateSpell(459574)
    local EchoingStrike = CreateSpell(410784)

    S.FireBreath = S.FontofMagic:IsAvailable() and FireBreathFoM or FireBreath
    S.Upheaval = S.FontofMagic:IsAvailable() and UpheavelFoM or Upheavel

    HL:RegisterForEvent(function()
        S.FireBreath = S.FontofMagic:IsAvailable() and FireBreathFoM or FireBreath
        S.Upheaval = S.FontofMagic:IsAvailable() and UpheavelFoM or Upheavel
      end, "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")

    local PresciencePriority1 = {}
    local PresciencePriority2 = {}
    local PresciencePriority3 = {}
    local PrescienceMembers = {}
    
    local function Get_Top3_DPS_From_Details()
        if not _G['Details'] then
            return nil, nil, nil
        end

        if not GetSetting('PrescienceUsingDetails', true) then
            return nil, nil, nil
        end

        local combat = _G.Details:GetCurrentCombat()

        if not combat then
            return nil, nil, nil
        end

        local damage_container = combat:GetContainer(_G['DETAILS_ATTRIBUTE_DAMAGE'])

        if not damage_container or not damage_container._ActorTable then
            return nil, nil, nil
        end

        local top3_dps = {}
        local combatTime = combat:GetCombatTime()
        
        -- Prevent division by zero
        if combatTime <= 0 then
            return nil, nil, nil
        end

        for _, actor in ipairs(damage_container._ActorTable) do
            if actor:IsPlayer() then
                local dps = actor.total / combatTime
                table.insert(top3_dps, {name = actor.nome, guid = actor.serial, dps = dps})
            end
        end

        table.sort(top3_dps, function(a, b) return a.dps > b.dps end)

        return top3_dps[1], top3_dps[2], top3_dps[3]
    end

    local function Sort_PrescienceList()
        local tempMembers = {}
        local maxUnits = GetSetting('pres_number', 2)  -- Get max units from settings
        
        -- Get top 3 DPS from Details!
        local top1, top2, top3 = Get_Top3_DPS_From_Details()

        if top1 and top2 and top3 then
            -- Match Details! top DPS players with PrescienceMembers units
            for _, unit in ipairs(Members) do
                -- Match by GUID or name and prevent duplicates
                if (unit:GUID() == top1.guid or unit:GUID() == top2.guid or unit:GUID() == top3.guid) 
                   and not unit:IsDeadOrGhost() 
                   and not tContains(tempMembers, unit) then
                    table.insert(tempMembers, unit)
                    if #tempMembers >= maxUnits then
                        break
                    end
                end
            end
        end

        -- Fallback logic if no valid DPS players were added
        if #tempMembers < maxUnits then
            for _, TargetedUnit in ipairs(Members) do
                if not TargetedUnit:IsDeadOrGhost() and not tContains(tempMembers, TargetedUnit) then
                    if PresciencePriority1[TargetedUnit:GUID()] then
                        table.insert(tempMembers, 1, TargetedUnit)
                    elseif PresciencePriority2[TargetedUnit:GUID()] and #tempMembers < maxUnits then
                        table.insert(tempMembers, math.min(2, #tempMembers + 1), TargetedUnit)
                    elseif PresciencePriority3[TargetedUnit:GUID()] and #tempMembers < maxUnits then
                        table.insert(tempMembers, math.min(3, #tempMembers + 1), TargetedUnit)
                    end
                end
            end

            -- If still not enough members, add based on role priority
            if #tempMembers < maxUnits then
                local dps = {}
                local tanks = {}
                local healers = {}
                local playerIncluded = false

                for _, TargetedUnit in ipairs(Members) do
                    if not tContains(tempMembers, TargetedUnit) and not TargetedUnit:IsDeadOrGhost() then
                        if TargetedUnit:IsADamager() and not TargetedUnit:IsUnit(Player) then
                            table.insert(dps, TargetedUnit)
                        elseif TargetedUnit:IsATank() then
                            table.insert(tanks, TargetedUnit)
                        elseif TargetedUnit:IsAHealer() then
                            table.insert(healers, TargetedUnit)
                        elseif TargetedUnit:IsUnit(Player) then
                            playerIncluded = true
                        end
                    end
                end

                -- Insert DPS that isn't the player
                for _, ThisUnit in ipairs(dps) do
                    if #tempMembers < maxUnits and not tContains(tempMembers, ThisUnit) then
                        table.insert(tempMembers, ThisUnit)
                    end
                end

                -- Insert Tanks
                for _, ThisUnit in ipairs(tanks) do
                    if #tempMembers < maxUnits and not tContains(tempMembers, ThisUnit) then
                        table.insert(tempMembers, ThisUnit)
                    end
                end

                -- Insert the Player
                if playerIncluded and #tempMembers < maxUnits and not tContains(tempMembers, Player) then
                    table.insert(tempMembers, Player)
                end

                -- Insert Healers
                for _, ThisUnit in ipairs(healers) do
                    if #tempMembers < maxUnits and not tContains(tempMembers, ThisUnit) then
                        table.insert(tempMembers, ThisUnit)
                    end
                end
            end
        end
        -- Finally, update PrescienceMembers with our sorted list
        wipe(PrescienceMembers)
        for i=1, #tempMembers do
            PrescienceMembers[i] = tempMembers[i]
        end
    end

    -- Add initialization of PrescienceMembers when group changes
    HL:RegisterForEvent(function()
        wipe(PrescienceMembers)
    end, "GROUP_ROSTER_UPDATE", "PLAYER_ENTERING_WORLD")

    local BlisteringPriority = {}
    local BlisteringMembers = {}
    local BlisteringMembers1 = {}
	local function Sort_BlisteringList()
        BlisteringMembers = Members

        if not Player:IsInPvEActivity() then
            wipe(BlisteringMembers1)

            tinsert(BlisteringMembers1, Player)
            return
        end

        wipe(BlisteringMembers1)
        ---@param TargetedUnit Unit
        for i, TargetedUnit in ipairs(BlisteringMembers) do
            if BlisteringPriority[TargetedUnit:GUID()] and not TargetedUnit:IsDeadOrGhost() then
                tinsert(BlisteringMembers1, TargetedUnit)
            else
                if Utils.tableCount(BlisteringPriority) == 0 or BlisteringPriority[TargetedUnit:GUID()] and TargetedUnit:IsDeadOrGhost() then
                    if #Tanks > 0 then
                        if TargetedUnit:IsATank() then
                            tinsert(BlisteringMembers1, TargetedUnit)
                        end
                    elseif #Melees > 0 then
                        if TargetedUnit:IsAMelee() then
                            tinsert(BlisteringMembers1, TargetedUnit)
                        end
                    else
                        tinsert(BlisteringMembers1, TargetedUnit)
                    end
                end
            end
        end
	end

    local CastOnSpell = Spell(1)
    CastOnSpell.EmpowerLevel = 0
    local function CastEmpower()
        if Player:IsEmpowering() then
            if CastOnSpell.EmpowerLevel and CastOnSpell.EmpowerLevel > 0 then
                if Player:EmpoweredLevel() >= CastOnSpell.EmpowerLevel then
                    if MainAddon.ForceCastDisplay(CastOnSpell) then
                        return "Casting Empower " .. CastOnSpell:Name()
                    end
                else
                    return "Waiting Empower"
                end
            else
                return "Waiting Empower"
            end
        end
    end

  ---@param TargetedUnit Unit
  local function SourceOfMagicMissing(TargetedUnit)
      return TargetedUnit:BuffRemains(S.SourceofMagic) <= 10
  end
  ---@param TargetedUnit Unit
  local function EvaluateSourceOfMagic(TargetedUnit)
      return TargetedUnit:BuffDown(S.SourceofMagic) and SoMTargetName == TargetedUnit:UnfilterName() and not TargetedUnit:IsUnit(Player)
  end
    ---@param TargetedUnit Unit
    local function EvaluateSourceOfMagic2(TargetedUnit)
        return TargetedUnit:BuffDown(S.SourceofMagic) and not TargetedUnit:IsUnit(Player)
    end

    local function RaidBuff()
      if Player:AffectingCombat() then
          if Player:BuffDown(S.BlackAttunementBuff, true) then
              if S.BlackAttunement:IsReady(Player) then
                  if Cast(S.BlackAttunement) then
                      return "Black Attunement"
                  end
              end
          end
      end

      if (not Player:AffectingCombat() and Player:IsMovingFor() >= 1 or (not Player:BuffUp(S.BlackAttunement) and Player:BuffUp(S.BlackAttunementBuff, true))) then
          if Player:BuffDown(S.BronzeAttunementBuff, true) then
              if S.BronzeAttunement:IsReady(Player) then
                  if Cast(S.BronzeAttunement) then
                      return "Bronze Attunement"
                  end
              end
          end
      end

      local mthrdm = MainAddon.GenerateRandom('Blessing_Raid', 30, 60, 60)
      if S.BlessingoftheBronze:IsReady(Player) then
          if (Player:BuffRemains(S.BlessingoftheBronzeBuff, true) < 1800 + mthrdm) then
              if Cast(S.BlessingoftheBronze) then
                  return "BlessingOfTheBronze buff 1";
              end
          end
      end

      if S.SourceofMagic:IsReady(Player) then
            if SoMTargetName == "None" or SoMTargetName == "" then
                if HealingEngine:BuffTotal(S.SourceofMagic) == 0 then
                    if CastCycleAlly(S.SourceofMagic, Healers, EvaluateSourceOfMagic2) then
                        return "Source Of Magic (Not Set)";
                    end
                end
            else
                if Player:IsInParty() then
                    if CastCycleAlly(S.SourceofMagic, Healers, EvaluateSourceOfMagic) then
                        return "Source Of Magic (Set)";
                    end
                end
            end
        end
    end

    ---@param TargetedUnit Unit
    local function EvaluateVerdant(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= GetSetting("VEHP")
    end

    local function Defensives()
        if Player:AffectingCombat() then
            if GetSetting('obscales_check') then
                if S.ObsidianScales:IsReady() and Player:BuffDown(S.ObsidianScales) and Player:HealthPercentage() <= GetSetting('obscales_spin') then
                    if Cast(S.ObsidianScales, true) then
                        return "Defensives: Obsidian Scales";
                    end
                end
            end
        end

        if Player:MythicDifficulty() >= GetSetting('smart_feint_above_key_level', 2) or not Player:IsInDungeonArea() then
            if Player:ShouldFeint() and Player:BuffDown(S.Zephyr) and S.Zephyr:IsReady(Player) then
                if Cast(S.Zephyr) then
                    MainAddon.UI:ShowToast("Zephyr", "Dangerous situation detected !", MainAddon.GetTexture(S.Zephyr))
                    return "Zephyr"
                end
            end
        end
        
        if GetSetting('eblossom_check') then
            if S.EmeraldBlossom:IsReady(Player) then
                if Player:HealthPercentage() <= GetSetting('eblossom_spin') then
                    if Cast(S.EmeraldBlossom) then
                        return "Defensives: Emerald Blossom Player"
                    end
                end

                if GetSetting('eblossom_allies') and Player:IsInParty() then
                    if MainAddon.AllyLowestHP(true, 25) <= GetSetting('eblossom_spin') then
                        if Cast(S.EmeraldBlossom) then
                            return "Defensives: Emerald Blossom Ally"
                        end
                    end
                end
            end
        end

        if S.VerdantEmbrace:IsReady(Player) then
            if GetSetting('VE_Unit') == 1 then
                if CastCycleAlly(S.VerdantEmbrace, Tanks, EvaluateVerdant) then
                    return 'VE - Tanks'
                end
            end
            if GetSetting('VE_Unit') == 2 then
              if Player:HealthPercentage() <= GetSetting("VEHP") then
                    if CastAlly(S.VerdantEmbrace, Player) then
                        return 'VE - Self'
                    end
                end
            end
            if GetSetting('VE_Unit') == 3 then
                if CastCycleAlly(S.VerdantEmbrace, Members, EvaluateVerdant) then
                    return 'VE - Everyone'
                end
            end
        end
    end

    local function Utilities()
        if GetSetting('hover_check', false) then
            if S.Hover:IsReady(Player) and Player:IsMovingFor() > GetSetting('hover_spin', 30) then
                if Cast(S.Hover, true) then
                    return "hover utilities";
                end
            end
        end
    end

    local function FB_Custom()
        if not fb_emp_level_custom then return nil end

        if S.FireBreath:CooldownDown() then return nil end
        if CastOnSpell:ID() ~= 1 and CastOnSpell ~= S.FireBreath then return nil end

        local FoM = S.FontofMagic:IsAvailable()
        local FB_1T = GetSetting('fb_emp_level_1T', false)
        local FB_2T = GetSetting('fb_emp_level_2T', false)
        local FB_3T = GetSetting('fb_emp_level_3T', false)
        local FB_4T = GetSetting('fb_emp_level_4T', false)
        local FB_5T = GetSetting('fb_emp_level_5T', false)

        -- Fall back to 3 if we don't have FoM and setting is 4.
        if not FoM then
            if FB_1T == 4 then
                FB_1T = 3
            elseif FB_2T == 4 then
                FB_2T = 3   
            elseif FB_3T == 4 then
                FB_3T = 3
            elseif FB_4T == 4 then
                FB_4T = 3
            elseif FB_5T == 4 then
                FB_5T = 3
            end
        end

        local FBEmpower = 0

        if EnemiesCount8ySplash <= 1 then
            FBEmpower = FB_1T
            CastOnSpell = S.FireBreath;
            S.FireBreath.EmpowerLevel = FB_1T;
        elseif EnemiesCount8ySplash == 2 then
            FBEmpower = FB_2T
            CastOnSpell = S.FireBreath;
            S.FireBreath.EmpowerLevel = FB_2T;
        elseif EnemiesCount8ySplash == 3 then
            FBEmpower = FB_3T
            CastOnSpell = S.FireBreath;
            S.FireBreath.EmpowerLevel = FB_3T;
        elseif EnemiesCount8ySplash == 4 then
            FBEmpower = FB_4T
            CastOnSpell = S.FireBreath;
            S.FireBreath.EmpowerLevel = FB_4T;
        elseif EnemiesCount8ySplash >= 5 then
            FBEmpower = FB_5T
            CastOnSpell = S.FireBreath;
            S.FireBreath.EmpowerLevel = FB_5T;
        end

        if FBEmpower == 0 then
            return
        end

        if GetSetting('TS_spell', 1) == 1 then
            -- tip_the_scales,if=cooldown.fire_breath.ready&buff.ebon_might_self.up
            if S.TipTheScales:IsReady() and (S.FireBreath:CooldownUp() and Player:BuffUp(S.EbonMightSelfBuff)) then
                if Cast(S.TipTheScales) then return "tip_the_scales fb 2"; end
            end
        end

        if S.FireBreath:IsReady(Player) then
            if Cast(S.FireBreath) then return "Fire Breath - Custom Empower: " .. FBEmpower; end
        end
    end

    local function UP_Custom()
        if not up_emp_level_custom then return nil end

        if S.Upheaval:CooldownDown() then return nil end
        if CastOnSpell:ID() ~= 1 and CastOnSpell ~= S.Upheaval then return nil end

        local FoM = S.FontofMagic:IsAvailable()
        local UP_1T = GetSetting('up_emp_level_1T', false)
        local UP_2T = GetSetting('up_emp_level_2T', false)
        local UP_3T = GetSetting('up_emp_level_3T', false)
        local UP_4T = GetSetting('up_emp_level_4T', false)
        local UP_5T = GetSetting('up_emp_level_5T', false)

        if not FoM then
            if UP_1T == 4 then
                UP_1T = 3
            elseif UP_2T == 4 then
                UP_2T = 3
            elseif UP_3T == 4 then
                UP_3T = 3
            elseif UP_4T == 4 then
                UP_4T = 3
            elseif UP_5T == 4 then
                UP_5T = 3
            end
        end

        local UPEmpower = 0

        if EnemiesCount8ySplash <= 1 then
            UPEmpower = UP_1T
            CastOnSpell = S.Upheaval
            S.Upheaval.EmpowerLevel = UP_1T
        elseif EnemiesCount8ySplash == 2 then
            UPEmpower = UP_2T
            CastOnSpell = S.Upheaval
            S.Upheaval.EmpowerLevel = UP_2T
        elseif EnemiesCount8ySplash == 3 then
            UPEmpower = UP_3T
            CastOnSpell = S.Upheaval
            S.Upheaval.EmpowerLevel = UP_3T
        elseif EnemiesCount8ySplash == 4 then
            UPEmpower = UP_4T
            CastOnSpell = S.Upheaval
            S.Upheaval.EmpowerLevel = UP_4T
        elseif EnemiesCount8ySplash >= 5 then
            UPEmpower = UP_5T
            CastOnSpell = S.Upheaval
            S.Upheaval.EmpowerLevel = UP_5T
        end

        if UPEmpower == 0 then
            return
        end

        if GetSetting('TS_spell', 1) == 2 then
            if S.TipTheScales:IsReady() then
                if Cast(S.TipTheScales) then return "tip_the_scales upheaval"; end
            end
        end

        if S.Upheaval:IsReady(Player) then
            if Cast(S.Upheaval) then return "upheaval empower_to=" .. UPEmpower; end
        end 
    end      

    -- Create table to exclude above trinkets from On Use function
    local OnUseExcludes = {
        I.AshesoftheEmbersoul:ID(),
        I.BalefireBranch:ID(),
        I.BeacontotheBeyond:ID(),
        I.BelorrelostheSuncaller:ID(),
        I.IrideusFragment:ID(),
        I.MirrorofFracturedTomorrows:ID(),
        I.NymuesUnravelingSpindle:ID(),
        I.SpoilsofNeltharus:ID(),

        -- For Healing Trinket feature
        158320, -- Revitalizing Voodoo Totem
        110009, -- Leaf of the Ancient Protectors
        207170, -- Smoldering Seedling
        207581, -- Mirror of Fractured Tomorrows
        207552, -- Echoing Tyrstone
        207167, -- Ashes of the Embersoul
        203714, -- Ward of Faceless Ire
        207174, -- Fyrakk's Tainted Rageheart
      }

    local function CheckOnUseExclude(Item)
        for i=1, #OnUseExcludes do
            if OnUseExcludes[i] == Item then
                return true
            end
        end
        return false
    end
      
      -- Trinket Objects
      local Equip = Player:GetEquipment()
      local Trinket1 = Equip[13] and not CheckOnUseExclude(Equip[13]) and Item(Equip[13]) or Item(0)
      local Trinket2 = Equip[14] and not CheckOnUseExclude(Equip[14]) and Item(Equip[14]) or Item(0)
            
      -- Rotation Variables
      local PrescienceTargets = {}
      local MaxEmpower = (S.FontofMagic:IsAvailable()) and 4 or 3
      local FoMEmpowerMod = (S.FontofMagic:IsAvailable()) and 0.8 or 1
      local BossFightRemains = 11111
      local FightRemains = 11111
      local GCDMax = Player:GCD() + 0.25
      local VarEssenceBurstMaxStacks = 2
      local VarTrinket1Exclude, VarTrinket2Exclude
      local VarTrinket1Manual, VarTrinket2Manual
      local VarSpamHeal = true
      local VarMinOpenerDelay = 0
      local VarOpenerDelay = 0
      local VarOpenerCDs = false
      local VarTempWound = 0
    
    -- Reset variables after fights
    HL:RegisterForEvent(function()
        BossFightRemains = 11111
        FightRemains = 11111
    end, "PLAYER_REGEN_ENABLED")
      
    HL:RegisterForEvent(function()
        Equip = Player:GetEquipment()
        Trinket1 = Equip[13] and Item(Equip[13]) or Item(0)
        Trinket2 = Equip[14] and Item(Equip[14]) or Item(0)
    end, "PLAYER_EQUIPMENT_CHANGED")
      
    HL:RegisterForEvent(function()
        MaxEmpower = (S.FontofMagic:IsAvailable()) and 4 or 3
        FoMEmpowerMod = (S.FontofMagic:IsAvailable()) and 0.8 or 1
    end, "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")
      
    local function PrescienceCheck()
    end
      
    local function SoMCheck()
        local Group
        if UnitInRaid("player") then
          Group = Unit.Raid
        elseif UnitInParty("player") then
          Group = Unit.Party
        else
          return false
        end
      
        local SoMTarget = nil
        for _, Char in pairs(Group) do
          if Char:Exists() and Char:BuffUp(S.SourceofMagic) then
            SoMTarget = Char
          end
        end
      
        if SoMTarget == nil then return true end
        return false
    end
      
    local function BlisteringScalesCheck()
        -- if Blistering Scales option is disabled, return 99 (always higher than required stacks, which should result in no suggestion)
        local Group
        if UnitInRaid("player") then
          Group = Unit.Raid
        elseif UnitInParty("player") then
          Group = Unit.Party
        else
          -- If solo, just return our own stacks
          return Player:BuffStack(S.BlisteringScalesBuff)
        end
      
        if Group == Unit.Party then
          for unitID, Char in pairs(Group) do
            -- Check for the buff on the group tank only
            if Char:Exists() and UnitGroupRolesAssigned(unitID) == "TANK" then
              return Char:BuffStack(S.BlisteringScalesBuff)
            end
          end
        elseif Group == Unit.Raid then
          for unitID, Char in pairs(Group) do
            -- Check for the buff on the raid's ACTIVE tank only
            if Char:Exists() and (Char:IsTankingAoE(8) or Char:IsTanking(Target)) and UnitGroupRolesAssigned(unitID) == "TANK" then
              return Char:BuffStack(S.BlisteringScalesBuff)
            end
          end
        end
      
        return 99
    end
      
    local function TemporalWoundCalc(Enemies)
        -- variable,name=temp_wound,value=debuff.temporal_wound.remains,target_if=max:debuff.temporal_wound.remains
        local HighestTW = 0
        for _, CycleUnit in pairs(Enemies) do
          local Remains = CycleUnit:DebuffRemains(S.TemporalWoundDebuff)
          HighestTW = max(Remains, HighestTW)
        end
        return HighestTW
    end
      
    local function EMSelfBuffDuration()
        return S.EbonMightSelfBuff:BaseDuration() * (1 + (Player:CritChancePct() / 100))
    end
      
    local function AllyCount()
        local Group
        local Count = 0
        if UnitInRaid("player") then
          Group = Unit.Raid
        elseif UnitInParty("player") then
          Group = Unit.Party
        else
          return 0
        end
      
        for _, CycleUnit in pairs(Group) do
          if CycleUnit:Exists() then
            Count = Count + 1
          end
        end
      
        return Count
    end
      
    local function Precombat()
        -- flask
        -- food
        -- augmentation
        -- snapshot_stats
        -- variable,name=spam_heal,default=1,op=reset
        VarSpamHeal = true
        -- variable,name=minimum_opener_delay,op=reset,default=0
        VarMinOpenerDelay = 0
        -- variable,name=opener_delay,value=variable.minimum_opener_delay,if=!talent.interwoven_threads
        -- variable,name=opener_delay,value=variable.minimum_opener_delay+variable.opener_delay,if=talent.interwoven_threads
        if not S.InterwovenThreads:IsAvailable() then
          VarOpenerDelay = VarMinOpenerDelay
        else
          VarOpenerDelay = VarMinOpenerDelay + (VarOpenerDelay or 0)
        end
        -- variable,name=opener_cds_detected,op=reset,default=0
        VarOpenerCDs = false
        -- variable,name=trinket_1_exclude,value=trinket.1.is.irideus_fragment|trinket.1.is.balefire_branch|trinket.1.is.ashes_of_the_embersoul|trinket.1.is.nymues_unraveling_spindle|trinket.1.is.mirror_of_fractured_tomorrows|trinket.1.is.spoils_of_neltharus
        local T1ID = Trinket1:ID()
        VarTrinket1Exclude = T1ID == I.IrideusFragment:ID() or T1ID == I.BalefireBranch:ID() or T1ID == I.AshesoftheEmbersoul:ID() or T1ID == I.NymuesUnravelingSpindle:ID() or T1ID == I.MirrorofFracturedTomorrows:ID() or T1ID == I.SpoilsofNeltharus:ID()
        -- variable,name=trinket_2_exclude,value=trinket.2.is.irideus_fragment|trinket.2.is.balefire_branch|trinket.2.is.ashes_of_the_embersoul|trinket.2.is.nymues_unraveling_spindle|trinket.2.is.mirror_of_fractured_tomorrows|trinket.2.is.spoils_of_neltharus
        local T2ID = Trinket2:ID()
        VarTrinket2Exclude = T2ID == I.IrideusFragment:ID() or T2ID == I.BalefireBranch:ID() or T2ID == I.AshesoftheEmbersoul:ID() or T2ID == I.NymuesUnravelingSpindle:ID() or T2ID == I.MirrorofFracturedTomorrows:ID() or T2ID == I.SpoilsofNeltharus:ID()
        -- variable,name=trinket_1_manual,value=trinket.1.is.irideus_fragment|trinket.1.is.balefire_branch|trinket.1.is.ashes_of_the_embersoul|trinket.1.is.nymues_unraveling_spindle|trinket.1.is.mirror_of_fractured_tomorrows|trinket.1.is.spoils_of_neltharus|trinket.1.is.beacon_to_the_beyond|trinket.1.is.belorrelos_the_suncaller
        VarTrinket1Manual = VarTrinket1Exclude or T1ID == I.BeacontotheBeyond:ID() or T1ID == I.BelorrelostheSuncaller:ID()
        -- variable,name=trinket_2_manual,value=trinket.2.is.irideus_fragment|trinket.2.is.balefire_branch|trinket.2.is.ashes_of_the_embersoul|trinket.2.is.nymues_unraveling_spindle|trinket.2.is.mirror_of_fractured_tomorrows|trinket.2.is.spoils_of_neltharus|trinket.2.is.beacon_to_the_beyond|trinket.2.is.belorrelos_the_suncaller
        VarTrinket2Manual = VarTrinket2Exclude or T2ID == I.BeacontotheBeyond:ID() or T2ID == I.BelorrelostheSuncaller:ID()
        -- Manually added: Group buff check
        if S.BlessingoftheBronze:IsReady(Player) and M.GroupBuffMissing(S.BlessingoftheBronzeBuff) then
          if Cast(S.BlessingoftheBronze) then return "blessing_of_the_bronze precombat"; end
        end
        -- living_flame
        if S.LivingFlame:IsReady() or S.ChronoFlames:IsReady() then
          if Cast(S.LivingFlame) then return "living_flame precombat 10"; end
        end
    end
      
    local function EbonLogic()
        -- ebon_might
        if S.EbonMight:IsReady(Player) then
          if Cast(S.EbonMight) then return "ebon_might ebon_logic 2"; end
        end
    end
      
    local function OpenerFiller()
        -- variable,name=opener_delay,value=variable.opener_delay>?variable.minimum_opener_delay,if=!variable.opener_cds_detected&evoker.allied_cds_up>0
        -- Note: Can't track others' CDs.
        if not VarOpenerCDs then
          VarOpenerDelay = min(VarOpenerDelay, VarMinOpenerDelay)
        end
        -- variable,name=opener_delay,value=variable.opener_delay-1
        VarOpenerDelay = VarOpenerDelay - 1
        -- variable,name=opener_cds_detected,value=1,if=!variable.opener_cds_detected&evoker.allied_cds_up>0
        if not VarOpenerCDs then
          VarOpenerCDs = true
        end
        -- variable,name=opener_delay,value=variable.opener_delay-2,if=equipped.nymues_unraveling_spindle&trinket.nymues_unraveling_spindle.cooldown.up
        if I.NymuesUnravelingSpindle:IsEquippedAndReady() then
          VarOpenerDelay = VarOpenerDelay - 2
        end
        -- living_flame,if=active_enemies=1|talent.pupil_of_alexstrasza
        if (S.LivingFlame:IsReady() or S.ChronoFlames:IsReady()) and (EnemiesCount8ySplash == 1 or S.PupilofAlexstrasza:IsAvailable()) then
          if Cast(S.LivingFlame) then return "living_flame opener_filler 4"; end
        end
        -- azure_strike
        if S.AzureStrike:IsReady() then
          if Cast(S.AzureStrike) then return "azure_strike opener_filler 6"; end
        end
    end
      
    local function Items()
        if I.Iridal:IsEquippedAndReady() and Target:HealthPercentage() < 35 then
            if Cast(I.Iridal) then return "iridal_the_earths_master"; end
        end
        -- use_item,name=nymues_unraveling_spindle,if=cooldown.breath_of_eons.remains<=3
        if I.NymuesUnravelingSpindle:IsEquippedAndReady() and (S.BreathofEons:CooldownRemains() <= 3) then
            if Cast(I.NymuesUnravelingSpindle) then return "nymues_unraveling_spindle items 2"; end
        end
        if Target:DebuffUp(S.TemporalWoundDebuff) or FightRemains <= 30 and Player:BuffUp(S.EbonMightSelfBuff) then
            -- use_item,name=irideus_fragment,if=debuff.temporal_wound.up|fight_remains<=30&buff.ebon_might_self.up
            if I.IrideusFragment:IsEquippedAndReady() then
                if Cast(I.IrideusFragment) then return "irideus_fragment items 4"; end
            end
            -- use_item,name=ashes_of_the_embersoul,if=debuff.temporal_wound.up|fight_remains<=30&buff.ebon_might_self.up
            if I.AshesoftheEmbersoul:IsEquippedAndReady() then
                if Cast(I.AshesoftheEmbersoul) then return "ashes_of_the_embersoul items 6"; end
            end
            -- use_item,name=mirror_of_fractured_tomorrows,if=debuff.temporal_wound.up|fight_remains<=30&buff.ebon_might_self.up
            if I.MirrorofFracturedTomorrows:IsEquippedAndReady() then
                if Cast(I.MirrorofFracturedTomorrows) then return "mirror_of_fractured_tomorrows items 8"; end
            end
            -- use_item,name=balefire_branch,if=debuff.temporal_wound.up|fight_remains<=30&buff.ebon_might_self.up
            if I.BalefireBranch:IsEquippedAndReady() then
                if Cast(I.BalefireBranch) then return "balefire_branch items 10"; end
            end
        end
        -- use_item,name=spoils_of_neltharus,if=buff.spoils_of_neltharus_mastery.up&(!((trinket.1.is.irideus_fragment|trinket.1.is.mirror_of_fractured_tomorrows)&trinket.1.cooldown.up|(trinket.is.2.irideus_fragment|trinket.2.is.mirror_of_fractured_tomorrows)&trinket.2.cooldown.up)|!(time%%120<=20|fight_remains>=190&fight_remains<=250&&time%%60<=25|fight_remains<=25))
        if I.SpoilsofNeltharus:IsEquippedAndReady() and (Player:BuffUp(S.SpoilsofNeltharusMastery) and (not ((Trinket1:ID() == I.IrideusFragment:ID() or Trinket1:ID() == I.MirrorofFracturedTomorrows:ID()) and Trinket1:CooldownUp() or (Trinket2:ID() == I.IrideusFragment:ID() or Trinket2:ID() == I.MirrorofFracturedTomorrows:ID()) and Trinket2:CooldownUp()) or not (HL.CombatTime() % 120 <= 20 or FightRemains >= 190 and FightRemains <= 250 and HL.CombatTime() % 60 <= 25 or FightRemains <= 25))) then
            if Cast(I.SpoilsofNeltharus) then return "spoils_of_neltharus items 12"; end
        end
        -- use_item,name=beacon_to_the_beyond,use_off_gcd=1,if=gcd.remains>0.1&((!debuff.temporal_wound.up&((trinket.1.cooldown.remains>=20|!variable.trinket_1_exclude)&(trinket.2.cooldown.remains>=20|!variable.trinket_2_exclude))|variable.trinket_1_exclude&variable.trinket_2_exclude))&(!raid_event.adds.exists|raid_event.adds.up|spell_targets.beacon_to_the_beyond>=5|raid_event.adds.in>60)|fight_remains<20
        if I.BeacontotheBeyond:IsEquippedAndReady() and ((Target:DebuffDown(S.TemporalWoundDebuff) and ((Trinket1:CooldownRemains() >= 20 or not VarTrinket1Exclude) and (Trinket2:CooldownRemains() >= 20 or not VarTrinket2Exclude)) or VarTrinket1Exclude and VarTrinket2Exclude) or FightRemains < 20) then
            if Cast(I.BeacontotheBeyond) then return "beacon_to_the_beyond items 14"; end
        end
        -- use_item,name=belorrelos_the_suncaller,use_off_gcd=1,if=gcd.remains>0.1&((!debuff.temporal_wound.up&((trinket.1.cooldown.remains>=20|!variable.trinket_1_exclude)&(trinket.2.cooldown.remains>=20|!variable.trinket_2_exclude))|variable.trinket_1_exclude&variable.trinket_2_exclude))&(!raid_event.adds.exists|raid_event.adds.up|spell_targets.beacon_to_the_beyond>=5|raid_event.adds.in>60)|fight_remains<20
        if I.BelorrelostheSuncaller:IsEquippedAndReady() and ((Target:DebuffDown(S.TemporalWoundDebuff) and ((Trinket1:CooldownRemains() >= 20 or not VarTrinket1Exclude) and (Trinket2:CooldownRemains() >= 20 or not VarTrinket2Exclude)) or VarTrinket1Exclude and VarTrinket2Exclude) or FightRemains < 20) then
            if Cast(I.BelorrelostheSuncaller) then return "belorrelos_the_suncaller items 16"; end
        end
        -- use_item,slot=trinket1,if=!debuff.temporal_wound.up&(cooldown.breath_of_eons.remains>=30|!variable.trinket_2_exclude)&!variable.trinket_1_manual
        if Trinket1:IsReady() and (Target:DebuffDown(S.TemporalWoundDebuff) and (S.BreathofEons:CooldownRemains() >= 30 or not VarTrinket2Exclude) and not VarTrinket1Manual) then
            if Cast(Trinket1) then return "items 18"; end
        end
        -- use_item,slot=trinket2,if=!debuff.temporal_wound.up&(cooldown.breath_of_eons.remains>=30|!variable.trinket_1_exclude)&!variable.trinket_2_manual
        if Trinket2:IsReady() and (Target:DebuffDown(S.TemporalWoundDebuff) and (S.BreathofEons:CooldownRemains() >= 30 or not VarTrinket1Exclude) and not VarTrinket2Manual) then
            if Cast(Trinket2) then return "items 20"; end
        end
        -- use_item,slot=main_hand,use_off_gcd=1,if=gcd.remains>=gcd.max*0.6
        if true then
          ---@class Item
          local MainHandOnUse = Player:GetUseableItems(OnUseExcludes, 16)
          if MainHandOnUse and MainHandOnUse:IsReady() then
            if Cast(MainHandOnUse) then return "use_item for main_hand (" .. MainHandOnUse:Name() .. ") items 22"; end
          end
        end
    end
      
    local function FB()
        if fb_emp_level_custom then return FB_Custom() end

        if S.FireBreath:CooldownDown() then return nil end
        if CastOnSpell:ID() ~= 1 and CastOnSpell ~= S.FireBreath then return nil end

        -- Note: Using Player:EmpowerCastTime() in place of duration in the below lines. Intention seems to be whether we can get the spell off before Ebom Might ends.
        if S.MoltenEmbers:IsAvailable() then
            if Player:BuffRemains(S.EbonMightSelfBuff) > Player:EmpowerCastTime(1) then
                CastOnSpell = S.FireBreath
                S.FireBreath.EmpowerLevel = 1
            end
        else
            if S.LeapingFlames:IsAvailable() then
                if S.FontofMagic:IsAvailable() then
                    if (Player:BuffRemains(S.EbonMightSelfBuff) > Player:EmpowerCastTime(4)) then
                        CastOnSpell = S.FireBreath
                        S.FireBreath.EmpowerLevel = 4
                    end
                else
                    if (Player:BuffRemains(S.EbonMightSelfBuff) > Player:EmpowerCastTime(3)) then
                        CastOnSpell = S.FireBreath
                        S.FireBreath.EmpowerLevel = 3
                    end
                end
            else
                if Player:BuffRemains(S.EbonMightSelfBuff) > Player:EmpowerCastTime(1) then
                    CastOnSpell = S.FireBreath
                    S.FireBreath.EmpowerLevel = 1
                end
            end
        end

        if S.FireBreath.EmpowerLevel == 0 then
            return
        end

        if GetSetting('TS_spell', 1) == 1 then
            if S.TipTheScales:IsReady() and (Player:BuffUp(S.EbonMightSelfBuff)) then
                if Cast(S.TipTheScales) then return "tip_the_scales fb 2"; end
            end
        end

        if S.FireBreath:IsReady(Player) then
            if Cast(S.FireBreath) then return "fire_breath empower_to=" .. S.FireBreath.EmpowerLevel; end
        end
    end

    local function UP()
        if up_emp_level_custom then return UP_Custom() end

        if S.Upheaval:CooldownDown() then return nil end
        if CastOnSpell:ID() ~= 1 and CastOnSpell ~= S.Upheaval then return nil end

        CastOnSpell = S.Upheaval;
        S.Upheaval.EmpowerLevel = 1;

        if GetSetting('TS_spell', 1) == 2 then
            if S.TipTheScales:IsReady() then
                if Cast(S.TipTheScales) then return "tip_the_scales upheaval"; end
            end
        end

        if S.Upheaval:IsReady(Player) then
            if Cast(S.Upheaval) then return "upheaval empower_to=" .. S.Upheaval.EmpowerLevel; end
        end
    end
      
    local function Filler()
        -- living_flame,if=(buff.ancient_flame.up|mana>=200000|!talent.dream_of_spring|variable.spam_heal=0)&(active_enemies=1|talent.pupil_of_alexstrasza)
        if (S.LivingFlame:IsReady() or S.ChronoFlames:IsReady()) and ((Player:BuffUp(S.AncientFlameBuff) or Player:Mana() >= 200000 or not S.DreamofSpring:IsAvailable() or VarSpamHeal == 0) and (EnemiesCount8ySplash == 1 or S.PupilofAlexstrasza:IsAvailable())) then
          if Cast(S.LivingFlame) then return "living_flame filler 2"; end
        end
        -- azure_strike
        if S.AzureStrike:IsReady() then
          if Cast(S.AzureStrike) then return "azure_strike filler 4"; end
        end
    end

    ---@param TargetedUnit Unit
    local function EvaluatePrescience(TargetedUnit)
        if PresciencePriority1[TargetedUnit:GUID()] then
            return TargetedUnit:BuffDown(S.PrescienceBuff)
        end
        if PresciencePriority2[TargetedUnit:GUID()] then
            return TargetedUnit:BuffDown(S.PrescienceBuff)
        end
        if PresciencePriority3[TargetedUnit:GUID()] then
            return TargetedUnit:BuffDown(S.PrescienceBuff)
        end
        return false
    end

    ---@param TargetedUnit Unit
    local function EvaluateBlistering(TargetedUnit)
        return TargetedUnit:BuffStack(S.BlisteringScales) <= 2
    end

    local function APL()
        fb_emp_level_custom = GetSetting('fb_emp_level_custom', false)
        up_emp_level_custom = GetSetting('up_emp_level_custom', false)

        --Cast Empower
        do
            local ShouldReturn = CastEmpower()
            if ShouldReturn then
                return ShouldReturn
            end
        end

        -- Variables
        Tanks, Healers, Members, Damagers, Melees, RawMembers = HealingEngine:Fetch()

        -- Update Enemies
        Enemies25y = Player:GetEnemiesInRange(25)
        Enemies8ySplash = Target:GetEnemiesInSplashRange(10)
        if (AoEON()) then
            EnemiesCount8ySplash = #Enemies8ySplash
        else
            EnemiesCount8ySplash = 1
        end

        if M.TargetIsValid() or Player:AffectingCombat() then
            -- Calculate fight_remains
            BossFightRemains = HL.BossFightRemains()
            FightRemains = BossFightRemains
            if FightRemains == 11111 then
              FightRemains = HL.FightRemains(Enemies25y, false)
            end

            -- Calculate GCDMax
            GCDMax = Player:GCD() + 0.25

            -- Are we running a dungeon (non-raid)
            InDungeon = Player:IsInDungeonArea()
        end

        -- Override Channel
        MainAddon.IgnoreChannel = false

        -- Raid Buff
        local ShouldReturn = RaidBuff();
        if ShouldReturn then
            return ShouldReturn;
        end

        -- Defensives
        local ShouldReturn = Defensives();
        if ShouldReturn then
            return ShouldReturn;
        end

        if Player:AffectingCombat() then
            -- Utilities
            local ShouldReturn = Utilities();
            if ShouldReturn then
                return ShouldReturn;
            end
        end

        -- Trinkets
        local shouldReturn = MainAddon.TrinketHealing(Members)
        if shouldReturn then
            return shouldReturn
        end  

      Sort_PrescienceList()
      do
        ---@class Unit
        local PrescienceTarget = PrescienceMembers[1]
        ---@class Unit
        local PrescienceTarget2 = PrescienceMembers[2]
        ---@class Unit
        local PrescienceTarget3 = PrescienceMembers[3]

          if PrescienceTarget and S.Prescience:IsReady(PrescienceTarget) and PrescienceTarget:BuffRefreshable(S.PrescienceBuff) then
              if CastAlly(S.Prescience, PrescienceTarget) then
                  return "Prescience 1 ->" .. PrescienceTarget:Name();
              end
          end

          if PrescienceTarget2 and S.Prescience:IsReady(PrescienceTarget2) and PrescienceTarget2:BuffRefreshable(S.PrescienceBuff) then
              if CastAlly(S.Prescience, PrescienceTarget2) then
                  return "Prescience 2 ->" .. PrescienceTarget2:Name();
              end
          end

          if PrescienceTarget3 and S.Prescience:IsReady(PrescienceTarget3) and PrescienceTarget3:BuffRefreshable(S.PrescienceBuff) then
              if CastAlly(S.Prescience, PrescienceTarget3) then
                  return "Prescience 3 ->" .. PrescienceTarget3:Name();
              end
          end
      end

      Sort_BlisteringList()
      do
          ---@class Unit
          local BlisteringTarget = BlisteringMembers1[1]
          if BlisteringTarget then
              if S.BlisteringScales:IsReady(BlisteringTarget) and BlisteringTarget:BuffStack(S.BlisteringScales) <= 2 then
                  if CastAlly(S.BlisteringScales, BlisteringTarget) then
                      return "Blistering Scales ->" .. BlisteringTarget:Name();
                  end
              end
          end
      end
    
        if M.TargetIsValid() then
            -- Precombat
            if not Player:AffectingCombat() and not Player:IsCasting() then
              local ShouldReturn = Precombat(); if ShouldReturn then return ShouldReturn; end
            end

            -- Manually added: unravel
            if S.Unravel:IsReady() and Target:ActiveDamageAbsorb() then
              if Cast(S.Unravel) then return "unravel main 2"; end
            end
            -- variable,name=temp_wound,value=debuff.temporal_wound.remains,target_if=max:debuff.temporal_wound.remains
            --VarTempWound = TemporalWoundCalc(Enemies25y)    
            -- call_action_list,name=ebon_logic,if=(buff.ebon_might_self.remains-cast_time)<=buff.ebon_might_self.duration*0.4&(active_enemies>0|raid_event.adds.in<=3)&(evoker.prescience_buffs>=2&time<=10|evoker.prescience_buffs>=3|!group&!raid|buff.ebon_might_self.remains>=action.ebon_might.cast_time|group_members<=3)
            if (Player:BuffRemains(S.EbonMightSelfBuff) - S.EbonMight:CastTime()) <= EMSelfBuffDuration() * 0.4
            and (S.PrescienceBuff:AuraActiveCount() >= 2 and HL.CombatTime() <= 10
            or S.PrescienceBuff:AuraActiveCount() >= GetSetting('ebon_m_presc', 2)
            or Player:IsInSoloMode()
            or Player:BuffRemains(S.EbonMightSelfBuff) >= S.EbonMight:CastTime()
            or AllyCount() <= 3) then
                local ShouldReturn = EbonLogic(); if ShouldReturn then return ShouldReturn; end
            end
            -- run_action_list,name=opener_filler,if=variable.opener_delay>0&!fight_style.dungeonroute
            if VarOpenerDelay > 0 and HL.CombatTime() < VarOpenerDelay and not InDungeon then
                local ShouldReturn = OpenerFiller(); if ShouldReturn then return ShouldReturn; end
                if Cast(S.Pool) then return "Wait for OpenerFiller()"; end
            end
            -- potion,if=debuff.temporal_wound.up&buff.ebon_might_self.up
            if MainAddon.UsePotion() then
                MainAddon.SetTopColor(1, "Combat Potion")
            end
            -- call_action_list,name=items
            if true then
              local ShouldReturn = Items(); if ShouldReturn then return ShouldReturn; end
            end
            -- deep_breath
            if S.DeepBreath:IsReady() then
              if Cast(S.DeepBreath) then return "deep_breath main 8"; end
            end
            -- call_action_list,name=fb,if=cooldown.time_skip.up&talent.time_skip&!talent.interwoven_threads
            if S.TimeSkip:IsAvailable() and S.TimeSkip:CooldownUp() and not S.InterwovenThreads:IsAvailable() then
              local ShouldReturn = FB(); if ShouldReturn then return ShouldReturn; end
            end

            -- upheaval,target_if=target.time_to_die>duration+0.2,empower_to=1,if=buff.ebon_might_self.remains>duration&cooldown.time_skip.up&talent.time_skip&!talent.interwoven_threads
            if (Player:BuffRemains(S.EbonMightSelfBuff) > Player:EmpowerCastTime(1) and S.TimeSkip:IsAvailable() and S.TimeSkip:CooldownUp() and not S.InterwovenThreads:IsAvailable()) then
                local ShouldReturn = UP(); if ShouldReturn then return ShouldReturn; end
            end
            if GetSetting('boe_pi', false) and PriestInGroup then
                -- Custom BreathofEons
                if S.BreathofEons:IsReady() and LastPowerInfusionCastTime + 15 > GetTime() then
                    if Cast(S.BreathofEons) then return "breath of eons PI detected"; end
                end
            else
                -- breath_of_eons,if=((cooldown.ebon_might.remains<=4|buff.ebon_might_self.up)&target.time_to_die>15&raid_event.adds.in>15&(!equipped.nymues_unraveling_spindle|trinket.nymues_unraveling_spindle.cooldown.remains>=10|fight_remains<30)|fight_remains<30)&!fight_style.dungeonroute,line_cd=117
                -- breath_of_eons,if=evoker.allied_cds_up>0&((cooldown.ebon_might.remains<=4|buff.ebon_might_self.up)&target.time_to_die>15&(!equipped.nymues_unraveling_spindle|trinket.nymues_unraveling_spindle.cooldown.remains>=10|fight_remains<30)|fight_remains<30)&fight_style.dungeonroute
                -- Note: Combined both lines. Only difference seems to be a line_cd if not in a dungeon.
                if S.BreathofEons:IsReady() and (S.BreathofEons:TimeSinceLastCast() >= 117 or InDungeon) and (S.EbonMight:CooldownRemains() <= 4 or Player:BuffUp(S.EbonMightSelfBuff)) then
                    if Cast(S.BreathofEons) then return "breath_of_eons main 12"; end
                end
            end

            -- living_flame,if=buff.leaping_flames.up&cooldown.fire_breath.up&fight_style.dungeonroute
            if (S.LivingFlame:IsReady() or S.ChronoFlames:IsReady()) and (Player:BuffUp(S.LeapingFlamesBuff) and S.FireBreathDebuff:CooldownUp()) then
                if Cast(S.LivingFlame) then return "living_flame main 14"; end
            end
            -- call_action_list,name=fb,if=(raid_event.adds.remains>13|raid_event.adds.in>20|evoker.allied_cds_up>0|!raid_event.adds.exists)
            local ShouldReturn = FB(); if ShouldReturn then return ShouldReturn; end
            -- upheaval,target_if=target.time_to_die>duration+0.2,empower_to=1,if=buff.ebon_might_self.remains>duration&(raid_event.adds.remains>13|!raid_event.adds.exists|raid_event.adds.in>20)
            if (Player:BuffRemains(S.EbonMightSelfBuff) > Player:EmpowerCastTime(1)) then
                local ShouldReturn = UP(); if ShouldReturn then return ShouldReturn; end
            end
            -- time_skip,if=(cooldown.fire_breath.remains+cooldown.upheaval.remains+cooldown.prescience.full_recharge_time)>=35
            if S.TimeSkip:IsReady() and (S.FireBreath:CooldownRemains() + S.Upheaval:CooldownRemains() + S.Prescience:FullRechargeTime() > 35) then
              if Cast(S.TimeSkip) then return "time_skip main 18"; end
            end
            -- emerald_blossom,if=talent.dream_of_spring&buff.essence_burst.up&(variable.spam_heal=2|variable.spam_heal=1&!buff.ancient_flame.up)&(buff.ebon_might_self.up|essence.deficit=0|buff.essence_burst.stack=buff.essence_burst.max_stack&cooldown.ebon_might.remains>4)
            if S.EmeraldBlossom:IsReady() and (S.DreamofSpring:IsAvailable() and Player:BuffUp(S.EssenceBurstBuff) and (VarSpamHeal == 2 or VarSpamHeal == 1 and Player:BuffDown(S.AncientFlameBuff)) and (Player:BuffUp(S.EbonMightSelfBuff) or Player:EssenceDeficit() == 0 or Player:BuffStack(S.EssenceBurstBuff) == VarEssenceBurstMaxStacks and S.EbonMight:CooldownRemains() > 4)) then
                if Cast(S.EmeraldBlossom) then return "emerald_blossom main 20"; end
            end

            -- living_flame,target_if=max:debuff.bombardments.remains,if=talent.mass_eruption&buff.mass_eruption_stacks.up&!buff.imminent_destruction.up&buff.essence_burst.stack<buff.essence_burst.max_stack&essence.deficit>1&(buff.ebon_might_self.remains>=6|cooldown.ebon_might.remains<=6)&debuff.bombardments.remains<action.eruption.execute_time&(talent.pupil_of_alexstrasza|active_enemies=1)
            -- azure_strike,target_if=max:debuff.bombardments.remains,if=talent.mass_eruption&buff.mass_eruption_stacks.up&!buff.imminent_destruction.up&buff.essence_burst.stack<buff.essence_burst.max_stack&essence.deficit>1&(buff.ebon_might_self.remains>=6|cooldown.ebon_might.remains<=6)&debuff.bombardments.remains<action.eruption.execute_time&(talent.echoing_strike&active_enemies>1)
            -- Mass Eruption and Bombardments logic
            if MassEruption:IsAvailable() and Player:BuffUp(MassEruptionStacks) and Player:BuffDown(ImminentDestruction) 
               and Player:BuffStack(S.EssenceBurst) < VarEssenceBurstMaxStacks and Player:EssenceDeficit() > 1 
               and (Player:BuffRemains(S.EbonMightSelfBuff) >= 6 or S.EbonMight:CooldownRemains() <= 6) then
                
                -- Living Flame for single target or with Pupil
                if (S.LivingFlame:IsReady() or S.ChronoFlames:IsReady()) and (EnemiesCount8ySplash == 1 or S.PupilofAlexstrasza:IsAvailable()) then
                    if Cast(S.LivingFlame) then return "living_flame mass_eruption"; end
                end
                
                -- Azure Strike for AoE with Echoing Strike
                if S.AzureStrike:IsReady() and EchoingStrike:IsAvailable() and EnemiesCount8ySplash > 1 then
                    if Cast(S.AzureStrike) then return "azure_strike mass_eruption"; end
                end
            end

            -- eruption,if=buff.ebon_might_self.remains>execute_time|essence.deficit=0|buff.essence_burst.stack=buff.essence_burst.max_stack&cooldown.ebon_might.remains>4
            if S.Eruption:IsReady() and (Player:BuffRemains(S.EbonMightSelfBuff) > S.Eruption:ExecuteTime() or ((Player:EssenceDeficit() == 0 or Player:BuffUp(S.EssenceBurstBuff)) and S.EbonMight:CooldownRemains(nil, true) > 4)) then
                if Cast(S.Eruption) then return "eruption main 22"; end
            end
            -- emerald_blossom,if=!buff.ebon_might_self.up&talent.ancient_flame&talent.scarlet_adaptation&!talent.dream_of_spring&!buff.ancient_flame.up&active_enemies=1
            if S.EmeraldBlossom:IsReady() and (Player:BuffDown(S.EbonMightSelfBuff) and S.AncientFlame:IsAvailable() and S.ScarletAdaptation:IsAvailable() and not S.DreamofSpring:IsAvailable() and Player:BuffDown(S.AncientFlameBuff) and EnemiesCount8ySplash == 1) then
                if Cast(S.EmeraldBlossom) then return "emerald_blossom main 26"; end
            end
            -- run_action_list,name=filler
            local ShouldReturn = Filler(); if ShouldReturn then return ShouldReturn; end
            -- pool if nothing else to do
            if Cast(S.Pool) then return "Wait/Pool"; end
        end
    end

    local function Init()
        S.FireBreathDebuff:RegisterAuraTracking()
        S.TemporalWoundDebuff:RegisterAuraTracking()
        S.PrescienceBuff:RegisterAuraTracking()
        --Setting to TAP to avoid issues.
        _G['SetCVar']("empowerTapControls", 1)

        S.FireBreath.EmpowerLevel = 0
        S.Upheaval.EmpowerLevel = 0
    end
    M.SetAPL(1473, APL, Init);

    HL:RegisterForEvent(
    function(Event, Arg1, Arg2)
        -- Ensure it's the player
        if Arg1 ~= "player" then
            return
        end

        if Arg2 == "ESSENCE" then
            Cache.Persistent.Player.LastPowerUpdate = GetTime()
        end
    end,
    "UNIT_POWER_UPDATE")

     local OldIsCastable
    OldIsCastable = HL.AddCoreOverride("Spell.IsCastable",
            function(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                if MainAddon.PlayerSpecID() == 1473 then
                    if Player:IsMoving() then
                        if (self == S.FireBreath or self == S.Upheaval or self == S.TimeSkip) and Player:BuffDown(S.TipTheScales) then
                            return false, "Override: Player is moving"
                        end
                    end
                    if Player:BuffUp(S.Hover) and not MainAddon.CONST.Empower[self:ID()] then
                        ignoreMovement = true
                    end
                    if self == S.TipTheScales and Player:BuffUp(S.TipTheScales) then
                        return false
                    end

                    if self == S.Prescience then
                        local Pres = GetSetting('pres', {})
                        if Player:AffectingCombat() then
                            if not Pres['pres_combat'] then
                                return false, "Override: Prescience Combat"
                            end
                        else
                            if not Pres['pres_ooc'] then
                                return false, "Override: Prescience OOC"
                            end
                        end
                    end
                end
                local BaseCheck, Reason = OldIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                return BaseCheck, Reason
            end
    , 1473);
    
    local AugOldIsReady
    AugOldIsReady = HL.AddCoreOverride ("Spell.IsReady",
        function (self, TargetedUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
            local BaseCheck = AugOldIsReady(self, TargetedUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
            if MainAddon.PlayerSpecID() == 1473 then
                if self == S.Eruption then
                    return BaseCheck and Player:EssenceP() >= 2
                elseif self == S.EbonMight then
                    return BaseCheck and not Player:IsCasting(self)
                else
                    return BaseCheck
                end
            end
            return BaseCheck
        end
    , 1473)
            
    local AugBuffUp
    AugBuffUp = HL.AddCoreOverride("Player.BuffUp",
        function(self, Spell, AnyCaster, BypassRecovery)
            local BaseCheck = AugBuffUp(self, Spell, AnyCaster, BypassRecovery)
            if MainAddon.PlayerSpecID() == 1473 then
                if Spell == S.LeapingFlamesBuff then
                    if Player:IsCasting(S.LivingFlame) or Player:IsCasting(S.ChronoFlames) then
                        return false
                    end
                end
            end
            return BaseCheck
        end
    , 1473)

    local AugOldBuffRemains
    AugOldBuffRemains = HL.AddCoreOverride ("Player.BuffRemains",
      function(self, Spell, AnyCaster, Offset)
        if MainAddon.PlayerSpecID() == 1473 then
            if Spell == S.EbonMightSelfBuff then
                return self:IsCasting(S.EbonMight) and 10 or AugOldBuffRemains(self, Spell, AnyCaster, Offset)
            else
                return AugOldBuffRemains(self, Spell, AnyCaster, Offset)
            end
        end
        return AugOldBuffRemains(self, Spell, AnyCaster, Offset)
      end
    , 1473)
    
    HL.AddCoreOverride ("Player.EmpowerCastTime",
      function(self, stage)
        local Haste = Player:SpellHaste()
        local FoMEmpowerMod = (S.FontofMagic:IsAvailable()) and 0.8 or 1
        local MaxEmpower = (S.FontofMagic:IsAvailable()) and 4 or 3
        if not stage then stage = MaxEmpower end
        return ((1 + 0.75 * (stage - 1)) * Haste * FoMEmpowerMod)
      end
    , 1473)
        
    HL.AddCoreOverride ("Player.EssenceP",
      function()
        local Essence = Player:Essence()
        if MainAddon.PlayerSpecID() == 1473 then
            if not Player:IsCasting() and not Player:IsChanneling() then
                return Essence
            else
                if Player:IsCasting(S.Eruption) and Player:BuffDown(S.EssenceBurstBuff) then
                    return Essence - 2
                else
                    return Essence
                end
            end
        end
        return Essence
      end
    , 1473)
    
    HL.AddCoreOverride ("Player.EssenceTimeToMax",
      function()
        local Deficit = Player:EssenceDeficit()
        if Deficit == 0 then return 0; end
        local Regen = GetPowerRegenForPowerType(EssencePowerType)
        if not Regen or Regen < 0.2 then Regen = 0.2; end
        local TimeToOneEssence = 1 / Regen
        local LastUpdate = Cache.Persistent.Player.LastPowerUpdate
        return Deficit * TimeToOneEssence - (GetTime() - LastUpdate)
      end
    , 1473)
    
    HL.AddCoreOverride ("Player.EssenceTimeToX",
      function(Amount)
        local Essence = Player:Essence()
        if Essence >= Amount then return 0; end
        local Regen = GetPowerRegenForPowerType(EssencePowerType)
        local TimeToOneEssence = 1 / Regen
        local LastUpdate = Cache.Persistent.Player.LastPowerUpdate
        return ((Amount - Essence) * TimeToOneEssence) - (GetTime() - LastUpdate)
      end
    , 1473)

    HL.AddCoreOverride ("Player.EssenceBurst",
        function()
            return Player:BuffStack(S.EssenceBurstBuff)
        end
    , 1473)

        HL.AddCoreOverride ("Player.MaxEssenceBurst",
        function()
            return 2
        end
    , 1473)

    -- Safety measure just in case UNIT_SPELLCAST_EMPOWER_STOP didn't fire
    HL:RegisterForEvent(
        function(event, unitTarget, castGUID, spellGUID)
            if unitTarget == 'player' and CastOnSpell:ID() ~= 1 then
                local spellID = select(6, strsplit("-", spellGUID))
                spellID = tonumber(spellID)
                if spellID and not MainAddon.CONST.Empower[spellID] then 
                    CastOnSpell = Spell(1)
                    S.FireBreath.EmpowerLevel = 0
                    S.Upheaval.EmpowerLevel = 0
                end
            end
        end,
    "UNIT_SPELLCAST_SENT")

    HL:RegisterForEvent(
        function(event, unitTarget, castGUID, spellID)
            if unitTarget == 'player' then
                CastOnSpell = Spell(1)
                S.FireBreath.EmpowerLevel = 0
                S.Upheaval.EmpowerLevel = 0
            end
        end,
    "UNIT_SPELLCAST_EMPOWER_STOP")

    local function CheckIfPriestIsInGroup()
        PriestInGroup = false
        PriestsTable = {}
        for i = 1, #RawMembers do
            if RawMembers[i]:Class() == "PRIEST" then
                PriestInGroup = true
                PriestsTable[RawMembers[i]:ID():lower()] = RawMembers[i]
            end
        end
    end

    HL:RegisterForEvent(function(self, event, isLogin, isReload)
        if not isLogin and not isReload then
            Tanks, Healers, Members, Damagers, Melees, RawMembers = HealingEngine:Fetch()
            CheckIfPriestIsInGroup()
        end
    end, "PLAYER_ENTERING_WORLD")

    HL:RegisterForEvent(function(arg1, unitID)
        if unitID == "player" or arg1 ~= "UNIT_IN_RANGE_UPDATE" then
            Tanks, Healers, Members, Damagers, Melees, RawMembers = HealingEngine:Fetch()
            CheckIfPriestIsInGroup()
        end
    end, "UNIT_IN_RANGE_UPDATE", "GROUP_ROSTER_UPDATE", "GROUP_JOINED", "GROUP_LEFT", "PLAYER_REGEN_ENABLED", "PLAYER_REGEN_DISABLED")

    HL:RegisterForEvent(function(arg1, unitID, arg3, spellID)
        if PriestsTable then
            if PriestsTable[unitID:lower()] then
                if spellID == 10060 then
                    LastPowerInfusionCastTime = GetTime()
                end
            end
        end
    end, "UNIT_SPELLCAST_SUCCEEDED")

    ---@param rootDescription Menu
    local function RightClick(_, rootDescription, contextData)
        local unit = contextData.unit
        local unitName = contextData.name
        local unitGUID = UnitGUID(unit)
    
        if MainAddon.PlayerSpecID() ~= 1473 then
            return
        end
    
        if not unit or UnitCanAttack(Player:ID(), unit) then
            return
        end
    
        -- Prescience 1 button creation
        local SetUnset_Prescience1 = PresciencePriority1[unitGUID] and "Unset" or "Set"
        local colorCode_Prescience1 = PresciencePriority1[unitGUID] and "|cffff0000" or "|cff00ff00"
        local iconID_Prescience = 5199639
        
        rootDescription:CreateButton("|T" .. iconID_Prescience .. ":24|t" .. " " .. colorCode_Prescience1 .. SetUnset_Prescience1 .. " Prescience 1" .. "|r", function()
            if unitGUID and not PresciencePriority2[unitGUID] and not PresciencePriority3[unitGUID] then
                if not PresciencePriority1[unitGUID] then
                    wipe(PresciencePriority1)
                    PresciencePriority1[unitGUID] = true
                else
                    wipe(PresciencePriority1)
                end
                MainAddon:Print("Prescience 1 on " .. unitName .. ":", PresciencePriority1[unitGUID], 2)
            else
                MainAddon:Print("You can't set Prescience multiple times on the same unit.")
            end
        end)
    
        -- Prescience 2 button creation
        local SetUnset_Prescience2 = PresciencePriority2[unitGUID] and "Unset" or "Set"
        local colorCode_Prescience2 = PresciencePriority2[unitGUID] and "|cffff0000" or "|cff00ff00"
        
        rootDescription:CreateButton("|T" .. iconID_Prescience .. ":24|t" .. " " .. colorCode_Prescience2 .. SetUnset_Prescience2 .. " Prescience 2" .. "|r", function()
            if unitGUID and not PresciencePriority1[unitGUID] and not PresciencePriority3[unitGUID] then
                if not PresciencePriority2[unitGUID] then
                    wipe(PresciencePriority2)
                    PresciencePriority2[unitGUID] = true
                else
                    wipe(PresciencePriority2)
                end
                MainAddon:Print("Prescience 2 on " .. unitName .. ":", PresciencePriority2[unitGUID], 2)
            else
                MainAddon:Print("You can't set Prescience multiple times on the same unit.")
            end
        end)
    
        -- Prescience 3 button creation
        local SetUnset_Prescience3 = PresciencePriority3[unitGUID] and "Unset" or "Set"
        local colorCode_Prescience3 = PresciencePriority3[unitGUID] and "|cffff0000" or "|cff00ff00"
        
        rootDescription:CreateButton("|T" .. iconID_Prescience .. ":24|t" .. " " .. colorCode_Prescience3 .. SetUnset_Prescience3 .. " Prescience 3" .. "|r", function()
            if unitGUID and not PresciencePriority1[unitGUID] and not PresciencePriority2[unitGUID] then
                if not PresciencePriority3[unitGUID] then
                    wipe(PresciencePriority3)
                    PresciencePriority3[unitGUID] = true
                else
                    wipe(PresciencePriority3)
                end
                MainAddon:Print("Prescience 3 on " .. unitName .. ":", PresciencePriority3[unitGUID], 2)
            else
                MainAddon:Print("You can't set Prescience multiple times on the same unit.")
            end
        end)
    
        -- Blistering Scales button creation
        local SetUnset_Blistering = BlisteringPriority[unitGUID] and "Unset" or "Set"
        local colorCode_Blistering = BlisteringPriority[unitGUID] and "|cffff0000" or "|cff00ff00"
        local iconID_Blistering = 5199621
        
        rootDescription:CreateButton("|T" .. iconID_Blistering .. ":24|t" .. " " .. colorCode_Blistering .. SetUnset_Blistering .. " Blistering Scales" .. "|r", function()
            if unitGUID and not BlisteringPriority[unitGUID] then
                wipe(BlisteringPriority)
                BlisteringPriority[unitGUID] = true
            else
                if unitGUID then
                    BlisteringPriority[unitGUID] = false
                end
            end
            MainAddon:Print("Blistering Scales on " .. unitName .. ":", BlisteringPriority[unitGUID], 2)
        end)
    
        -- Source of Magic button creation
        local SetUnset_SoM = unitName == Player:UnfilterName() and "Set" or SoMTargetName == "None" and "Set" or SoMTargetName == unitName and "Unset" or "Set"
        local colorCode_SoM = SoMTargetName ~= "None" and SoMTargetName ~= "" and "|cffff0000" or "|cff00ff00"
        local iconID_SoM = 4630412
    
        rootDescription:CreateButton("|T" .. iconID_SoM .. ":24|t" .. " " .. colorCode_SoM .. SetUnset_SoM .. " Source of Magic" .. "|r", function()
            if unitName == Player:UnfilterName() then
                SoMTargetName = "None"
                MainAddon:Print("You can't select yourself for Source of Magic.")
            else
                if SoMTargetName == "None" then
                    SoMTargetName = unitName
                    MainAddon:Print("Source of Magic on: " .. unitName .. " ", true, 2)
                else
                    if SoMTargetName == unitName then
                        SoMTargetName = "None"
                        MainAddon:Print("Source of Magic removed for: " .. unitName .. " ", false, 2)
                    else
                        MainAddon:Print("Source of Magic removed for: " .. SoMTargetName .. " ", false, 2)
                        SoMTargetName = unitName
                        MainAddon:Print("Source of Magic on: " .. unitName .. " ", true, 2)
                    end
                end
            end
        end)
    end
    Menu.ModifyMenu("MENU_UNIT_SELF", RightClick);
    Menu.ModifyMenu("MENU_UNIT_PLAYER", RightClick);
    Menu.ModifyMenu("MENU_UNIT_TARGET", RightClick);
    Menu.ModifyMenu("MENU_UNIT_FOCUS", RightClick);
    Menu.ModifyMenu("MENU_UNIT_RAID_PLAYER", RightClick);
    Menu.ModifyMenu("MENU_UNIT_PARTY", RightClick);
end