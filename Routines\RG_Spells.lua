---@class MainAddon
local MainAddon = MainAddon
local HL = HeroLibEx
---@class Spell
local Spell = HL.Spell
---@class Item
local Item = HL.Item
local CreateSpell = MainAddon.CreateSpell
local CreateMultiSpell = MainAddon.CreateMultiSpell
local MergeTableByKey = HL.Utils.MergeTableByKey

---@class Spell
-- Spells
if not Spell.Rogue then Spell.Rogue = {} end

---@class RGCustomTable
Spell.Rogue.Custom = {
    -- Racial
    WingBuffet              = CreateSpell(357214),
    BloodFury               = CreateMultiSpell(20572, 33702, 33697),
    -- Outlaw
    GrapplingHook           = CreateSpell(195457),
    -- Poisons
    CripplingPoison         = CreateSpell(3408),
    DeadlyPoison            = CreateSpell(2823),
    InstantPoison           = CreateSpell(315584),
    AmplifyingPoison        = CreateSpell(381664),
    NumbingPoison           = CreateSpell(5761),
    WoundPoison             = CreateSpell(8679),
    AtrophicPoison          = CreateSpell(381637),
    Ambush                  = CreateSpell(8676),
    -- Abilities
    ShroudOfConcealment     = CreateSpell(114018),
    ZoldyckRecipe           = CreateSpell(381798),
    MomentumOfDespairBuff   = CreateSpell(457115),
    SerratedBoneSpikeChargesBuff = CreateSpell(455366),
    VenomousWounds          = CreateSpell(78134),
    FatefulEnding           = CreateSpell(454428),
    ColdBloodIE             = CreateSpell(456330),
    Distract                = CreateSpell(1725),
    SoTricky                = CreateSpell(441403),
    -- PvP
    SmokeBombSub            = CreateSpell(359053),
    SmokeBomb               = CreateSpell(212182),
    Dismantle               = CreateSpell(207777),
    DeathfromAbove          = CreateSpell(269513),
    TricksOfTheTradeBuffShort     = CreateSpell(1224098),
    TricksOfTheTradeBuffLong      = CreateSpell(59628),
}

---@class RGCommonsTable
Spell.Rogue.Commons = {
  -- Racials
  AncestralCall           = CreateSpell(274738),
  ArcanePulse             = CreateSpell(260364),
  ArcaneTorrent           = CreateSpell(25046),
  BagofTricks             = CreateSpell(312411),
  Berserking              = CreateSpell(26297),
  BloodFury               = CreateSpell(20572),
  Fireblood               = CreateSpell(265221),
  LightsJudgment          = CreateSpell(255647),
  Shadowmeld              = CreateSpell(58984),
  -- Defensive
  CloakofShadows          = CreateSpell(31224),
  CrimsonVial             = CreateSpell(185311),
  Evasion                 = CreateSpell(5277),
  Feint                   = CreateSpell(1966),
  -- Utility
  Blind                   = CreateSpell(2094),
  CheapShot               = CreateSpell(1833),
  ColdBlood               = CreateSpell(382245),
  FlayedwingToxin         = CreateSpell(345545),
  FullMomentum            = CreateSpell(459228),
  Kick                    = CreateSpell(1766),
  KidneyShot              = CreateSpell(408),
  PickPocket              = CreateSpell(921),
  Sap                     = CreateSpell(6770),
  Shiv                    = CreateSpell(5938),
  SliceandDice            = CreateSpell(315496),
  Shadowstep              = CreateSpell(36554),
  Sprint                  = CreateSpell(2983),
  TricksoftheTrade        = CreateSpell(57934),
  -- Talents
  AcrobaticStrikes        = CreateSpell(455143),
  Alacrity                = CreateSpell(193539),
  ClearTheWitnessesBuff   = CreateSpell(457178),
  CoupDeGrace             = CreateSpell(441776),
  DarkestNight            = CreateSpell(457058),
  DarkestNightBuff        = CreateSpell(457280),
  DeeperStratagem         = CreateSpell(193531),
  DeathStalkersMark       = CreateSpell(457052),
  DeathStalkersMarkDebuff = CreateSpell(457129),
  DisorientingStrikes     = CreateSpell(441274),
  DoubleJeopardy          = CreateSpell(454430),
  EchoingReprimand        = CreateSpell(385616),
  EchoingReprimand2       = CreateSpell(323558),
  EchoingReprimand3       = CreateSpell(323559),
  EchoingReprimand4       = CreateSpell(323560),
  EchoingReprimand5       = CreateSpell(354838),
  EscalatingBlade         = CreateSpell(441786),
  EdgeCase                = CreateSpell(453457),
  FateboundCoin           = CreateSpell(453012),
  FateboundCoinHeads      = CreateSpell(456479),
  FateboundCoinTails      = CreateSpell(452538),
  FateboundLuckyCoin      = CreateSpell(461818),
  FatefulEnding           = CreateSpell(454428),
  FazedDebuff             = CreateSpell(441224),
  FindWeakness            = CreateSpell(91023),
  FindWeaknessDebuff      = CreateSpell(316220),
  FlawlessForm            = CreateSpell(441321),
  FlawlessFormBuff        = CreateSpell(441326),
  FollowTheBlood          = CreateSpell(457068),
  HandOfFate              = CreateSpell(452536),
  ImprovedAmbush          = CreateSpell(381620),
  FateboundInevitability  = CreateSpell(454434),
  InevitabileEnd          = CreateSpell(454434),
  Inevitability           = CreateSpell(382512),
  LingeringDarkness       = CreateSpell(457056),
  LingeringDarknessBuff   = CreateSpell(457273),
  MarkedforDeath          = CreateSpell(137619),
  MeanStreak              = CreateSpell(453428),
  MomentumOfDespair       = CreateSpell(457067),
  Nightstalker            = CreateSpell(14062),
  NimbleFlurry            = CreateSpell(441367),
  ResoundingClarity       = CreateSpell(381622),
  Reverberation           = CreateSpell(394332),
  SealFate                = CreateSpell(14190),
  ShadowDance             = CreateSpell(185313), -- Base Spell
  ShadowDanceTalent       = CreateSpell(394930),
  ShadowDanceBuff         = CreateSpell(185422),
  Subterfuge              = CreateSpell(108208),
  SubterfugeBuff          = CreateSpell(115192),
  SurprisingStrikes       = CreateSpell(441273),
  ThistleTea              = CreateSpell(381623),
  TricksterDistract       = CreateSpell(441587),
  UnseenBlade             = CreateSpell(441146),
  UnseenBladeBuff         = CreateSpell(459485),
  Vigor                   = CreateSpell(14983),
  -- Stealth
  Stealth                 = CreateSpell(1784),
  Stealth2                = CreateSpell(115191),
  Vanish                  = CreateSpell(1856),
  VanishBuff              = CreateSpell(11327),
  VanishBuff2             = CreateSpell(115193),
  WithoutATrace           = CreateSpell(382513),
  -- Trinkets
  JunkmaestrosBuff        = CreateSpell(1219661), -- Junkmaestro's Mega Magnet buff
  -- Misc
  PoolEnergy              = CreateSpell(999910),
}

---@class AssassinationTable
Spell.Rogue.Assassination = {
  -- Abilities
  Ambush                  = CreateSpell(8676),
  AmbushOverride          = CreateSpell(430023),
  AmplifyingPoison        = CreateSpell(381664),
  AmplifyingPoisonDebuff  = CreateSpell(383414),
  AmplifyingPoisonDebuffDeathmark = CreateSpell(394328),
  CripplingPoisonDebuff   = CreateSpell(3409),
  DeadlyPoison            = CreateSpell(2823),
  DeadlyPoisonDebuff      = CreateSpell(2818),
  DeadlyPoisonDebuffDeathmark = CreateSpell(394324),
  Envenom                 = CreateSpell(32645),
  FanofKnives             = CreateSpell(51723),
  Garrote                 = CreateSpell(703),
  GarroteDeathmark        = CreateSpell(360830),
  Mutilate                = CreateSpell(1329),
  PoisonedKnife           = CreateSpell(185565),
  Rupture                 = CreateSpell(1943),
  RuptureDeathmark        = CreateSpell(360826),
  WoundPoison             = CreateSpell(8679),
  WoundPoisonDebuff       = CreateSpell(8680),
  -- Talents
  ArterialPrecision       = CreateSpell(400783),
  AtrophicPoisonDebuff    = CreateSpell(392388),
  Blindside               = CreateSpell(236274),
  BlindsideBuff           = CreateSpell(121153),
  CausticSpatter          = CreateSpell(421975),
  CausticSpatterDebuff    = CreateSpell(421976),
  CrimsonTempest          = CreateSpell(121411),
  CutToTheChase           = CreateSpell(51667),
  DashingScoundrel        = CreateSpell(381797),
  Deathmark               = CreateSpell(360194),
  Doomblade               = CreateSpell(381673),
  DragonTemperedBlades    = CreateSpell(381801),
  Elusiveness             = CreateSpell(79008),
  ImprovedGarrote         = CreateSpell(381632),
  ImprovedGarroteBuff     = CreateSpell(392401),
  ImprovedGarroteAura     = CreateSpell(392403),
  IndiscriminateCarnage   = CreateSpell(381802),
  IndiscriminateCarnageAura = CreateSpell(385754),
  IndiscriminateCarnageBuff = CreateSpell(385747),
  InternalBleeding        = CreateSpell(154953),
  Kingsbane               = CreateSpell(385627),
  LightweightShiv         = CreateSpell(394983),
  MasterAssassin          = CreateSpell(255989),
  MasterAssassinBuff      = CreateSpell(256735),
  PreyontheWeak           = CreateSpell(131511),
  PreyontheWeakDebuff     = CreateSpell(255909),
  SanguineBlades          = CreateSpell(200806),
  ScentOfBlood            = CreateSpell(381799),
  ScentOfBloodBuff        = CreateSpell(394080),
  SerratedBoneSpike       = CreateSpell(385424),
  SerratedBoneSpikeDebuff = CreateSpell(394036),
  ShivDebuff              = CreateSpell(319504),
  ShroudedSuffocation     = CreateSpell(385478),
  ThrownPrecision         = CreateSpell(381629),
  VenomRush               = CreateSpell(152152),
  ViciousVenoms           = CreateSpell(381634),
  ZoldyckRecipe           = CreateSpell(381798)
  -- PvP
}
---@class RGCustomTable
Spell.Rogue.Assassination = MergeTableByKey(Spell.Rogue.Assassination, Spell.Rogue.Custom)
---@class RGCommonsTable
Spell.Rogue.Assassination = MergeTableByKey(Spell.Rogue.Assassination, Spell.Rogue.Commons, true)

---@class OutlawTable
Spell.Rogue.Outlaw = {
  -- Abilities
  AdrenalineRush          = CreateSpell(13750),
  Ambush                  = CreateSpell(8676),
  SSAudacity              = CreateSpell(197834),
  AmbushOverride          = CreateSpell(430023),
  BetweentheEyes          = CreateSpell(315341),
  BladeFlurry             = CreateSpell(13877),
  Dispatch                = CreateSpell(2098),
  Elusiveness             = CreateSpell(79008),
  Opportunity             = CreateSpell(195627),
  PistolShot              = CreateSpell(185763),
  RolltheBones            = CreateSpell(315508),
  SinisterStrike          = CreateSpell(193315),
  -- Talents
  Audacity                = CreateSpell(381845),
  AudacityBuff            = CreateSpell(386270),
  BladeRush               = CreateSpell(271877),
  CountTheOdds            = CreateSpell(381982),
  Crackshot               = CreateSpell(423703),
  DeftManeuvers           = CreateSpell(381878),
  Dreadblades             = CreateSpell(343142),
  FanTheHammer            = CreateSpell(381846),
  GhostlyStrike           = CreateSpell(196937),
  GreenskinsWickers       = CreateSpell(386823),
  GreenskinsWickersBuff   = CreateSpell(394131),
  HiddenOpportunity       = CreateSpell(383281),
  ImprovedAdrenalineRush  = CreateSpell(395422),
  ImprovedBetweenTheEyes  = CreateSpell(235484),
  KeepItRolling           = CreateSpell(381989),
  KillingSpree            = CreateSpell(51690),
  LoadedDice              = CreateSpell(256170),
  LoadedDiceBuff          = CreateSpell(256171),
  PreyontheWeak           = CreateSpell(131511),
  PreyontheWeakDebuff     = CreateSpell(255909),
  QuickDraw               = CreateSpell(196938),
  Ruthlessness            = CreateSpell(14161),
  SummarilyDispatched     = CreateSpell(381990),
  Supercharger            = CreateSpell(470347),
  SwiftSlasher            = CreateSpell(381988),
  TakeEmBySurprise        = CreateSpell(382742),
  TakeEmBySurpriseBuff    = CreateSpell(385907),
  UnderhandedUpperhand    = CreateSpell(424044),
  Weaponmaster            = CreateSpell(200733),
  DancingSteel            = CreateSpell(272026),
  -- Utility
  Gouge                   = CreateSpell(1776),
  -- PvP
  -- Roll the Bones
  Broadside               = CreateSpell(193356),
  BuriedTreasure          = CreateSpell(199600),
  GrandMelee              = CreateSpell(193358),
  RuthlessPrecision       = CreateSpell(193357),
  SkullandCrossbones      = CreateSpell(199603),
  TrueBearing             = CreateSpell(193359),
  -- Set Bonuses
  ViciousFollowup         = CreateSpell(394879),
}
---@class RGCustomTable
Spell.Rogue.Outlaw = MergeTableByKey(Spell.Rogue.Outlaw, Spell.Rogue.Custom)
---@class RGCommonsTable
Spell.Rogue.Outlaw = MergeTableByKey(Spell.Rogue.Outlaw, Spell.Rogue.Commons, true)

---@class SubtletyTable
Spell.Rogue.Subtlety = {
  -- Abilities
  Backstab                = CreateSpell(53),
  BlackPowder             = CreateSpell(319175),
  Elusiveness             = CreateSpell(79008),
  Eviscerate              = CreateSpell(196819),
  Rupture                 = CreateSpell(1943),
  ShadowBlades            = CreateSpell(121471),
  Shadowstrike            = CreateSpell(185438),
  ShurikenStorm           = CreateSpell(197835),
  ShadowTechniques        = CreateSpell(196911),
  ShurikenToss            = CreateSpell(114014),
  SymbolsofDeath          = CreateSpell(212283),
  -- Talents
  CloakedinShadows        = CreateSpell(382515),
  CloakedinShadowsBuff    = CreateSpell(386165),
  DanseMacabre            = CreateSpell(382528),
  DanseMacabreBuff        = CreateSpell(393969),
  DeathPerception         = CreateSpell(469642),
  DeeperDaggers           = CreateSpell(382517),
  DeeperDaggersBuff       = CreateSpell(383405),
  DarkBrew                = CreateSpell(382504),
  DarkShadow              = CreateSpell(245687),
  DoubleDance             = CreateSpell(394930),
  EnvelopingShadows       = CreateSpell(238104),
  Finality                = CreateSpell(382525),
  FinalityBlackPowderBuff = CreateSpell(385948),
  FinalityEviscerateBuff  = CreateSpell(385949),
  FinalityRuptureBuff     = CreateSpell(385951),
  Flagellation            = CreateSpell(384631),
  FlagellationBuff        = CreateSpell(384631),
  FlagellationPersistBuff = CreateSpell(394758),
  Gloomblade              = CreateSpell(200758),
  GoremawsBite            = CreateSpell(426591),
  ImprovedBackstab        = CreateSpell(319949),
  ImprovedShadowDance     = CreateSpell(393972),
  ImprovedShurikenStorm   = CreateSpell(319951),
  Inevitability           = CreateSpell(382512),
  InvigoratingShadowdust  = CreateSpell(382523),
  LingeringShadow         = CreateSpell(382524),
  LingeringShadowBuff     = CreateSpell(385960),
  MasterofShadows         = CreateSpell(196976),
  PerforatedVeins         = CreateSpell(382518),
  PerforatedVeinsBuff     = CreateSpell(394254),
  PreyontheWeak           = CreateSpell(131511),
  PreyontheWeakDebuff     = CreateSpell(255909),
  Premeditation           = CreateSpell(343160),
  PremeditationBuff       = CreateSpell(343173),
  ReplicatingShadows      = CreateSpell(382506),
  SecretStratagem         = CreateSpell(394320),
  SecretTechnique         = CreateSpell(280719),
  Shadowcraft             = CreateSpell(426594),
  ShadowFocus             = CreateSpell(108209),
  ShurikenTornado         = CreateSpell(277925),
  SilentStorm             = CreateSpell(385722),
  SilentStormBuff         = CreateSpell(385727),
  SwiftDeath              = CreateSpell(394309),
  TheFirstDance           = CreateSpell(382505),
  TheRotten               = CreateSpell(382015),
  TheRottenBuff           = CreateSpell(394203),
  Weaponmaster            = CreateSpell(193537),
  -- PvP
  -- Set Bonuses
  ShadowRupture           = CreateSpell(424493),
}
---@class RGCustomTable
Spell.Rogue.Subtlety = MergeTableByKey(Spell.Rogue.Subtlety, Spell.Rogue.Custom)
---@class RGCommonsTable
Spell.Rogue.Subtlety = MergeTableByKey(Spell.Rogue.Subtlety, Spell.Rogue.Commons, true)

-- Items
if not Item.Rogue then Item.Rogue = {} end

---@class RGCustomItemTable
Item.Rogue.Custom = {
  -- Trinkets
  ManicGrieftorch                         = Item(194308, {13, 14}),
  WindscarWhetstone                       = Item(137486, {13, 14}),
  StormEatersBoon                         = Item(194302, {13, 14}),
  BeaconToTheBeyond                       = Item(203963, {13, 14}),
  MirrorOfFracturedTomorrows              = Item(207581, {13, 14}),
  DragonfireBombDispenser                 = Item(202610, {13, 14}),
  BandolierOfTwistedBlades                = Item(207165, {13, 14}) 
}

---@class AssassinationItemTable
Item.Rogue.Assassination = {
    -- Trinkets
    BottledFlayedwingToxin     = Item(178742, {13, 14}),
    ImperfectAscendancySerum   = Item(225654, {13, 14}),
    TreacherousTransmitter     = Item(221023, {13, 14}),
    ConcoctionKissOfDeath      = Item(215174, {13, 14}),
    MadQueensMandate           = Item(212454, {13, 14}),
    JunkmaestrosMegaMagnet     = Item(230189, {13, 14}),
}
---@class RGCustomItemTable
Item.Rogue.Assassination = MergeTableByKey(Item.Rogue.Custom, Item.Rogue.Assassination)

---@class OutlawItemTable
Item.Rogue.Outlaw = {
    -- Trinkets
    BottledFlayedwingToxin   = Item(178742, {13, 14}),
    ImperfectAscendancySerum = Item(225654, {13, 14}),
    JunkmaestrosMegaMagnet   = Item(230189, {13, 14}),
    MadQueensMandate         = Item(212454, {13, 14}),
    ScrollOfMomentum         = Item(226539, {13, 14})
}
---@class RGCustomItemTable
Item.Rogue.Outlaw = MergeTableByKey(Item.Rogue.Custom, Item.Rogue.Outlaw)

---@class ItemTable
Item.Rogue.Subtlety = {
  -- Trinkets
  BottledFlayedwingToxin     = Item(178742, {13, 14}),
  ImperfectAscendancySerum   = Item(225654, {13, 14}),
  TreacherousTransmitter     = Item(221023, {13, 14}),
  MadQueensMandate           = Item(212454, {13, 14}),
  SkardynsGrace              = Item(56440,  {13, 14}),
  ConcoctionKissOfDeath      = Item(215174, {13, 14})
}
---@class RGCustomItemTable
Item.Rogue.Subtlety = MergeTableByKey(Item.Rogue.Custom, Item.Rogue.Subtlety)

-- Generic
Spell.Rogue.Assassination.Rupture:SetGeneric(ROGUE_ASSASSINATION_SPECID, 'Generic1')
Spell.Rogue.Assassination.SerratedBoneSpike:SetGeneric(ROGUE_ASSASSINATION_SPECID, 'Generic2')
Spell.Rogue.Assassination.Garrote:SetGeneric(ROGUE_ASSASSINATION_SPECID, 'Generic3')
Spell.Rogue.Assassination.Mutilate:SetGeneric(ROGUE_ASSASSINATION_SPECID, 'Generic4')
Spell.Rogue.Assassination.KidneyShot:SetGeneric(ROGUE_ASSASSINATION_SPECID, 'Generic5')
Spell.Rogue.Assassination.Blind:SetGeneric(ROGUE_ASSASSINATION_SPECID, 'Generic6')
Spell.Rogue.Assassination.Evasion.offGCD = true
Spell.Rogue.Assassination.Feint.offGCD = true


Spell.Rogue.Outlaw.CheapShot:SetGeneric(ROGUE_OUTLAW_SPECID, 'Generic1')
Spell.Rogue.Outlaw.Shiv:SetGeneric(ROGUE_OUTLAW_SPECID, 'Generic2')
Spell.Rogue.Outlaw.PistolShot:SetGeneric(ROGUE_OUTLAW_SPECID, 'Generic3')
Spell.Rogue.Outlaw.KidneyShot:SetGeneric(ROGUE_OUTLAW_SPECID, 'Generic4')
Spell.Rogue.Outlaw.Blind:SetGeneric(ROGUE_OUTLAW_SPECID, 'Generic5')
Spell.Rogue.Outlaw.AdrenalineRush.offGCD = true
Spell.Rogue.Outlaw.Vanish.offGCD = true
Spell.Rogue.Outlaw.Evasion.offGCD = true
Spell.Rogue.Outlaw.Feint.offGCD = true

Spell.Rogue.Subtlety.Rupture:SetGeneric(ROGUE_SUBTLETY_SPECID, 'Generic1')
Spell.Rogue.Subtlety.Shiv:SetGeneric(ROGUE_SUBTLETY_SPECID, 'Generic2')
Spell.Rogue.Subtlety.ShurikenToss:SetGeneric(ROGUE_SUBTLETY_SPECID, 'Generic3')
Spell.Rogue.Subtlety.KidneyShot:SetGeneric(ROGUE_SUBTLETY_SPECID, 'Generic4')
Spell.Rogue.Subtlety.Blind:SetGeneric(ROGUE_SUBTLETY_SPECID, 'Generic5')
Spell.Rogue.Subtlety.Shadowstrike:SetGeneric(ROGUE_SUBTLETY_SPECID, 'Generic6')
Spell.Rogue.Subtlety.SymbolsofDeath.offGCD = true
Spell.Rogue.Subtlety.ShadowBlades.offGCD = true
Spell.Rogue.Subtlety.Vanish.offGCD = true
Spell.Rogue.Subtlety.ShadowDance.offGCD = true
Spell.Rogue.Subtlety.ThistleTea.offGCD = true
Spell.Rogue.Subtlety.ColdBlood.offGCD = true
Spell.Rogue.Subtlety.Evasion.offGCD = true
Spell.Rogue.Subtlety.Feint.offGCD = true