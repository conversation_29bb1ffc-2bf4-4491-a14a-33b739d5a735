---@diagnostic disable: undefined-global

-- Declare global variables
---@class MainAddon
MainAddon = {
    Version = "",
    CurrentEncounter = 0,
    LastDisplayTime = 0,
    CastQueue = {},
    Shared = {
    },
    UI = {
    },
    db = {
    },
    Interface = {
    },
    Nameplate = {
    },
    Timers = {
    },
    GetTexture = function (Object)
    end,
    SetTopColor = function (Place, Color)
    end,
    SetTopTexture = function (TexturePosition, Object)
    end,
    Toggle = {
        Special
    },
    Autobind = {
    },
    ---@class HealingEngine
    HealingEngine = {
        Fetch = function()
        end,
        Update = function()
        end,
        FriendlyInRange = function(self, Range, Units)
        end,
        AverageHP = function(self, CheckRange, Range, Units)
        end,
        MedianHP = function(self, CheckRange, Range, Units)
        end,
        LowestHP = function(self, CheckRange, Range, Units, Role)
        end,
        SecondLowestHP = function(self, CheckRange, Range, Units, Role)
        end,
        BuffTotal = function(self, Buff, Range, Units)
        end,
        BuffTotalNotPandemic = function(self, Buff, Range, Units)
        end,
        DebuffTotal = function(self, Debuff, Range, Units)
        end,
        LowestBuff = function(self, Buff, Units)
        end,
        LowestDebuff = function(self, Debuff, Units)
        end,
        HighestDebuff = function(self, Debuff, Units)
        end,
        MembersUnderPercentage = function(self, Percentage, Units, Range)
        end,
        MembersUnderPercentageWithCondition = function(self, Condition, Percentage, Units, Range)
        end,
        MembersUnderValue = function(self, Value, Units)
        end,
        BuffRemainsAverage = function(self, Buffs, Range, Percentage, Units)
        end,
        BuffTotalByHealthPercentage = function(self, Buffs, Range, Percentage, Units)
        end,
        ShouldHealTargetOrMouseover = function()
        end,
        PrioHealingDebuff = {
        },
        BlockedUnits = {},
        BlacklistedUnits = {},
    },
    Print = function (msg, state, level, mute)
    end,
    incdmgmagic = function (ThisUnit)
    end,
    incdmgswing = function (ThisUnit)
    end,
    SetAPL = function (ID, APL, Init, Position)
    end,
    Config = {
        GetClassSetting
    },
    SetConfig = function (ID, Config)
    end,
    GetClassVariableName = function()
    end,
    BuildCooldownByTimerUI = function(OldUI, Config_Color, Key)
    end,
    BuildDPSTrinketUI = function(OldUI, Config_Color)
    end,
    BuildHealingTrinketUI = function(OldUI, Config_Color)
    end,
    BuildTankingTrinketUI = function(OldUI, Config_Color)
    end,
    BuildCombatPotionUI = function(OldUI, Title, Config_Color, Headlines)
    end,
    BuildCombatManaPotionUI = function(OldUI, Title, Config_Color, Headlines)
    end,
    PlayerSpecID = function()
        return 0
    end,
    CDsON = function ()
        return true
    end,
    AoEON = function ()
        return true
    end,
    Cast = function (Object, OffGCD, DisplayStyle, OutofRange, CustomTime, ignoreChannel, TextureSlot, ignoreSettings)
        return {}, true or {} or "", "", true, 0, false, 0, false
    end,
    CastAlly = function (Object, TargetedUnit, MagicLoaderName, OutofRange, CustomTime, ignoreChannel, TextureSlot, offGCD, delay)
    end,
    CastCycle = function (Object, Enemies, Condition, OutofRange, OffGCD, DisplayStyle, IsMouseOver, ShouldWait, ForceSnipe, Toast, IgnoreChannel)
    end,
    CastCycleAlly = function (Object, Allies, Condition, ForceCast, OffGCD, ignoreChannel, delay, forceRefresh)
    end,
    CastCycleWait = function (Object, Enemies, Condition, ShouldWait, IgnoreChannel)
    end,
    CastTargetIf = function (Object, Enemies, TargetIfMode, TargetIfCondition, Condition, OutofRange, OffGCD, DisplayStyle, ShouldWait, ForceSnipe, IgnoreAoE, IgnoreChannel)
    end,
    CastTargetIfWait = function (Object, Enemies, TargetIfMode, TargetIfCondition, Condition, OutofRange, OffGCD, DisplayStyle, ShouldWait, ForceSnipe, IgnoreAoE, IgnoreChannel)
    end,
    CastTargetIfDK = function (Object, Enemies, TargetIfMode, TargetIfCondition, Condition, OutofRange, OffGCD, DisplayStyle, ShouldWait, ForceSnipe, IgnoreAoE, IgnoreChannel)
    end,
    CastTargetIfAlly = function (Object, Allies, TargetIfMode, TargetIfCondition, Condition, OffGCD, delay)
    end,
    CastMagic = function (Object, ConditionSkip, MagicLoaderName, Setting, Queue)
    end,
    CastMagicAlly = function (Object, ThisUnit, ConditionSkip, MagicLoaderName, Setting, Queue)
    end,
    CastAnnotated = function (Object, OffGCD, Text)
    end,
    CastPooling = function (Object, CustomTime, OutofRange)
    end,
    ForceCastDisplay = function (Object, TextureSlot, TargetedUnit, ForceCast, SpecialName, delay)
    end,
    ForceCastAlly = function (Object, TargetedUnit, DisplayStyle, OutofRange, CustomTime, ignoreChannel, TextureSlot, offGCD, delay)
    end,
    CastLeftNameplate = function (ThisUnit, Object, IsMouseOver, All, Toast)
    end,
    num = function (...)
        return 0
    end,
    bool = function (...)
        return false
    end,
    CONST = {
        Spec = {
        },
        Class = {
        },
        PvE_NPCDR = {
        },
    },
    CreateSpell = function (SpellID, SpellType)
        return Spell
    end,
    CreateMultiSpell = function (...)
        return Spell
    end,
    safeVanish = function ()
    end,
    SpecialCase_FreedomBlacklist = function ()
    end,
    GroupBuffMissing = function (ThisSpell)
    end,
    TargetIsValid = function (BypassCombat, FilterImmunity)
        return true
    end,
    TrinketDPS = function ()
    end,
    TrinketHealing = function (Members, OnUseExcludes)
    end,
    TrinketTanking = function ()
    end,
    UsePotion = function ()
    end,
    UseManaPotion = function ()
    end,
    CombinedPullTimer = function (bypass)
    end,
    GetSpellInfo = function (SpellID)
    end,
    GetSpellHealing = function (SpellID)
    end,
    DamageIncoming = function ()
    end,
    OpenerSequence = function (steps)
    end,
    OpenerDone = true,
    IsCurrentAffix = function (AffixName)
    end,
    GroundSpellHandling = function (spell, forceOOC, customTarget)
    end,
    GroundSpellHandlingSetup = function (state, spellName, forceBinding, macro)
    end,
    GetEnemyCastingSpell = function (ThisSpellID)
    end,
    AllyLowestHP = function (checkRange, Range)
    end,
    GenerateRandom = function (randomName, minNumber, maxNumber, timer)
    end,
    mediaDir = "media directory path",
    FindPotion = function ()
    end,
}
---@type number
UIDROPDOWNMENU_MENU_LEVEL = UIDROPDOWNMENU_MENU_LEVEL

---[Documentation](https://warcraft.wiki.gg/wiki/Creating_a_slash_command)
---@type table<string, function>
SlashCmdList = SlashCmdList or {}

CreateAtlasMarkup = CreateAtlasMarkup or function(arg1, arg2, arg3) end

---@type function
UIDropDownMenu_CreateInfo = UIDropDownMenu_CreateInfo

---@type function
UIDropDownMenu_AddButton = UIDropDownMenu_AddButton

---@type table<string, table>
StaticPopupDialogs = StaticPopupDialogs
---@type table<string, table>
StaticPopup_Show = StaticPopup_Show

---@type function
GetName = GetName

---@type function
_G['WeakAuras'].Import = _G['WeakAuras'].Import
WeakAuras = _G['WeakAuras']

---@class Frame
---@field IsForbidden fun():boolean
---@field GetName fun():string

---@type Frame
GetMouseFocus = GetMouseFocus

---@type Frame
GetMouseFocusCache = GetMouseFocusCache

---@class ScriptRegion : Frame
---@field IsForbidden fun():boolean
---@field GetName fun():string

---@type function
ReloadUI = ReloadUI

---@type function
---@diagnostic disable-next-line: lowercase-global
loadstring = loadstring

---@type function
IsSpellKnown = IsSpellKnown

---@type table
ElvUI = ElvUI

---@type table
ShestakUI = ShestakUI

---@type table
Hekili = Hekili

---@class Unit
HeroLibEx.Unit.Arena = HeroLibEx.Unit.Arena


---@class GeneralSettings
---@field someSetting string
---@field anotherSetting number

---@class GUISettings
---@field General GeneralSettings

---@type GUISettings
HeroLibEx.GUISettings = {
    General = {
        someSetting = "default",
        anotherSetting = 0,
        Blacklist = {}
    }
}

---@class Spell
---@field IsAvailable fun(self, CheckPet?, CheckTalents?):boolean
---@field IsAvailableX fun(self, CheckPet?):boolean
---@field IsCastable fun(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources):boolean
---@field IsReady fun(self, TargetUnit?, Range?, ignoreChannel?, skipCC?, ignoreSettings?, BypassRecovery?, ignoreResources?):boolean
---@field CooldownRemains fun(self, BypassRecovery, BypassCD):number
---@field CooldownUp fun(self, BypassRecovery, BypassCD):boolean
---@field EnergizeAmount fun():number?
---@field ID fun():number
---@field IsViable fun():string?
---@field InFlightRemains fun():boolean?
---@field CastQueue fun(self, ...):string
---@field LastDisplayTime number?
---@field MeleeRange number?
---@field Range number?
---@field EmpowerLevel number?
---@field offGCD boolean?
---@field ForceDisplaySpellList number?
---@field Macro string?
---@field MaximumRange number?
---@field MinimumRange number?
---@field Focus string?
---@field SpellID number?
---@field Opener boolean?
---@field StanceAura number?
---@field Jumps number?
---@field TimeSinceLastFlight number?
---@field PlayerOnly boolean?
---@field SetGeneric fun(self, SpecID, GenericID, SpellID?):any
---@field IsBlocked fun():boolean
---@field IsQueued fun():boolean
---@field BlockedByUserSettings fun():boolean
---@field MorphedSpellGeneric Spell?

---@class Unit
---@field Raid Unit
---@field Party Unit
---@field Arena Unit
---@field Boss Unit
---@field boss1 Unit
---@field boss2 Unit
---@field boss3 Unit
---@field boss4 Unit
---@field boss5 Unit
---@field Nameplate Unit
---@field Focus Unit
---@field Target Unit
---@field MouseOver Unit
---@field Player Unit
---@field BuffDown fun(self, ThisSpell, AnyCaster?, BypassRecovery?):boolean
---@field HealthPercentage fun():number
---@field BuffStack fun(self, ThisSpell, ThisSpell):number
---@field AstralPowerP fun():number
---@field MaelstromP fun():boolean
---@field MotEUp fun():number
---@field StormkeeperUp fun():number
---@field PotMUp fun():number
---@field IcefuryUp fun():number
---@field FocusP fun():number
---@field BuffStackP fun(self, table?, table?):number
---@field BuffUpP fun(self, table?, table?):boolean
---@field ArcaneChargesP fun(self, table?, table?):number
---@field Demonsurge fun(self, string?, string?):number
---@field EmpowerCastTime fun(self, time):number
---@field BonestormTicking fun():boolean
---@field DnDTicking fun():boolean
---@field UndulationCounterP fun():number
---@field EssenceP fun():number
---@field EssenceBurst fun():number
---@field MaxEssenceBurst fun():number
---@field IsStandingStillFor fun():number 
---@field InPvP fun():boolean
---@field IsEnemy fun():boolean
---@field IsFriend fun(self, otherUnit):boolean
---@field IsAHealer fun():boolean
---@field IsADamager fun():boolean
---@field IsATank fun():boolean
---@field IsAMelee fun():boolean
---@field IsBoss fun():boolean
---@field IsInPartyOrRaid fun():boolean
---@field GetEnemiesInRangeFilter fun(self, Radius, NPCID):table
---@field GetEnemiesInPvP fun(self, Radius):table
---@field GetEnemiesInRangeCombat fun(self, Radius):table
---@field GetEnemiesRangeTTD fun(self, Radius):table
---@field IsFeared fun():boolean
---@field IsRooted fun():boolean
---@field IsSleeping fun():boolean
---@field IsCharmed fun():boolean
---@field Stunned fun():boolean
---@field StunnedUI fun():boolean
---@field InCC fun():boolean
---@field CanTaunt fun(self, otherUnit):boolean
---@field CombatTime fun():number
---@field InArena fun():boolean
---@field AbsentImun fun(self, dmgType, ccImun, kickImun, slowImun, stunImun, fearImun, skipKarma, skipBreakable):boolean, string
---@field ShouldSlow fun(self, dmgType):boolean
---@field InBossEncounter fun():boolean
---@field HasPvEImmunityCached fun():boolean
---@field HasPvPImmunity fun(self):boolean, string
---@field IsMovingFor fun():number
---@field ShouldUseDefensive fun():boolean
---@field DruidCanShapeShift boolean
---@field IsOutdoors fun():boolean
---@field AnyStealthUp fun():boolean
---@field MythicDifficulty fun():boolean
---@field IsInPvEActivity fun():boolean
---@field IsInPvEActivityExtended fun():boolean
---@field IsInSoloMode fun():boolean
---@field TotemIsActive fun(self, totem):boolean
---@field RealHealthPercentage fun():number
---@field UnfilterName fun():string
---@field AffectingCombatPvE fun():boolean
---@field NeedsDefensivePvE fun():boolean
---@field BeingTanked fun():boolean
---@field CurrentTarget fun():boolean, Unit|nil, string
---@field Mounted fun():boolean
---@field ShouldUseDefensive fun(self, customCastRemains?):boolean
---@field InstanceID fun():number
---@field Class fun():string
---@field Speed fun():number
---@field IsEmpowering fun():boolean
---@field EmpoweredLevel fun():number
---@field ShouldFeint fun():boolean
---@field GetEnemiesInRangeUnfilter fun(self, Radius, Filter):table

---@class Menu
---@field ModifyMenu fun(string, global):any
---@field CreateButton fun(string, function, table):Button

---@class Menu
Menu = Menu

---@class Button
---@field SetPoint fun():any
---@field SetWidth fun():any
---@field SetHeight fun():any
---@field SetStylesheet fun():any
---@field SetEventListener fun():any


-- Class IDs
WARRIOR_CLASS_ID = 1
PALADIN_CLASS_ID = 2
HUNTER_CLASS_ID = 3
ROGUE_CLASS_ID = 4
PRIEST_CLASS_ID = 5
DEATHKNIGHT_CLASS_ID = 6
SHAMAN_CLASS_ID = 7
MAGE_CLASS_ID = 8
WARLOCK_CLASS_ID = 9
MONK_CLASS_ID = 10
DRUID_CLASS_ID = 11
DEMONHUNTER_CLASS_ID = 12
EVOKER_CLASS_ID = 13

-- Death Knight
DEATHKNIGHT_INIT_SPECID = 1455
DEATHKNIGHT_BLOOD_SPECID = 250
DEATHKNIGHT_FROST_SPECID = 251
DEATHKNIGHT_UNHOLY_SPECID = 252

-- Demon Hunter
DEMONHUNTER_INIT_SPECID = 1456
DEMONHUNTER_HAVOC_SPECID = 577
DEMONHUNTER_VENGEANCE_SPECID = 581

-- Druid
DRUID_INIT_SPECID = 1447
DRUID_BALANCE_SPECID = 102
DRUID_FERAL_SPECID = 103
DRUID_GUARDIAN_SPECID = 104
DRUID_RESTORATION_SPECID = 105

-- Hunter
HUNTER_INIT_SPECID = 1448
HUNTER_BEASTMASTERY_SPECID = 253
HUNTER_MARKSMANSHIP_SPECID = 254
HUNTER_SURVIVAL_SPECID = 255

-- Mage
MAGE_INIT_SPECID = 1449
MAGE_ARCANE_SPECID = 62
MAGE_FIRE_SPECID = 63
MAGE_FROST_SPECID = 64

-- Monk
MONK_INIT_SPECID = 1450
MONK_BREWMASTER_SPECID = 268
MONK_MISTWEAVER_SPECID = 270
MONK_WINDWALKER_SPECID = 269

-- Paladin
PALADIN_INIT_SPECID = 1451
PALADIN_HOLY_SPECID = 65
PALADIN_PROTECTION_SPECID = 66
PALADIN_RETRIBUTION_SPECID = 70

-- Priest
PRIEST_INIT_SPECID = 1452
PRIEST_DISCIPLINE_SPECID = 256
PRIEST_HOLY_SPECID = 257
PRIEST_SHADOW_SPECID = 258

-- Rogue
ROGUE_INIT_SPECID = 1453
ROGUE_ASSASSINATION_SPECID = 259
ROGUE_OUTLAW_SPECID = 260
ROGUE_SUBTLETY_SPECID = 261

-- Shaman
SHAMAN_INIT_SPECID = 1444
SHAMAN_ELEMENTAL_SPECID = 262
SHAMAN_ENHANCEMENT_SPECID = 263
SHAMAN_RESTORATION_SPECID = 264

-- Warlock
WARLOCK_INIT_SPECID = 1454
WARLOCK_AFFLICTION_SPECID = 265
WARLOCK_DEMONOLOGY_SPECID = 266
WARLOCK_DESTRUCTION_SPECID = 267

-- Warrior
WARRIOR_INIT_SPECID = 1446
WARRIOR_ARMS_SPECID = 71
WARRIOR_FURY_SPECID = 72
WARRIOR_PROTECTION_SPECID = 73

-- Evoker
EVOKER_INIT_SPECID = 1465
EVOKER_DEVASTATION_SPECID = 1467
EVOKER_PRESERVATION_SPECID = 1468
EVOKER_AUGMENTATION_SPECID = 1473