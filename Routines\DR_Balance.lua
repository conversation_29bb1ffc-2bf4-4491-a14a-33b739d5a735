function A_102(...)
    -- HR Update: fix(Balance): Fix typo 13-05-25
    -- Addon
    ---@class MainAddon
    local MainAddon = MainAddon
    local M = MainAddon
    -- HeroLib
    local HL = HeroLibEx
    ---@class HealingEngine
    local HealingEngine = MainAddon.HealingEngine
    ---@class Unit
    local Unit = HL.Unit
    ---@class Unit
    local Player = Unit.Player
    ---@class Unit
    local Target = Unit.Target
    ---@class Unit
    local MouseOver = Unit.MouseOver
    ---@class Unit
    local Focus = Unit.Focus
    ---@class Spell
    local Spell = HL.Spell
    ---@class Item
    local Item = HL.Item
    -- WoW API
    local Delay       = C_Timer.After
    -- lua
    local mathceil    = math.ceil
    local mathmax     = math.max
    -- HeroRotation
    local Cast = M.Cast
    local CastAlly = M.CastAlly
    local CastMagic = M.CastMagic
    local CastCycle = M.CastCycle
    local AoEON = M.AoEON
    local GetMouseFoci = _G['GetMouseFoci']
    local IsCurrentSpell = _G['C_Spell']['IsCurrentSpell']
    local C_Timer = _G['C_Timer']
    local num = M.num

    local S = Spell.Druid.Balance
    local I = Item.Druid.Balance

    -- Create table to exclude trinkets from On Use function
    local OnUseExcludes = {
        -- TWW Trinkets
        I.AberrantSpellforge:ID(),
        I.ImperfectAscendancySerum:ID(),
        I.SpymastersWeb:ID(),
        I.TreacherousTransmitter:ID(),
        -- Older Trinkets
        I.SoullettingRuby:ID(),
        -- TWW Other Items
        I.BestinSlotsCaster:ID(),
        -- Older Other Items
        I.NeuralSynapseEnhancer:ID(),
    }

    -- Toggle Setting
    MainAddon.Toggle.Special["BearMode"] = {
        Icon = MainAddon.GetTexture(S.BearForm),
        Name = "Bear Mode",
        Description = "Bear Mode.",
        Spec = 102
    }

    -- Toggle Setting
    MainAddon.Toggle.Special["SpreadMoonfire"] = {
        Icon = MainAddon.GetTexture(S.Moonfire),
        Name = "Spread Moonfire",
        Description = "This toggle will force spread Moonfire.",
        Spec = 102,
    }

    MainAddon.Toggle.Special["SaveAP"] = {
        Icon = MainAddon.GetTexture(S.Solstice),
        Name = "Save Astral Power",
        Description = "This toggle will save Astral Power.",
        Spec = 102,
    }

    local GetSetting = MainAddon.Config.GetClassSetting
    local Config_Key = MainAddon.GetClassVariableName()
    local Config_Color = 'FF7C0A'
    local Config_Table = {
        key = Config_Key,
        title = 'Druid - Balance',
        subtitle = '?? ' .. MainAddon.Version,
        width = 600,
        height = 700,
        profiles = true,
        config = {
            { type = 'header', text = '', size = 24, align = 'Center', color = Config_Color },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
            { type = 'header', text = 'DPS', color = Config_Color },
            { type = 'checkspin', text = ' Require this % of dotted Enemies for CDs', icon = S.CelestialAlignment:ID(), key = 'dotpack', min = 1, max = 100, default_spin = 60, default_check = false },
            { type = 'checkspin', text = ' Stand still threshold (CA, Inc, FoN, FoE, Convoke)', key = 'DPSMovingValue', icon = S.CelestialAlignment:ID(), min = 0, max = 10, default_spin = 1.5, default_check = false },
            { type = 'checkbox', text = ' DoT units during movement', key = 'movementdots', icon = S.Moonfire:ID(), default = true },  
            { type = 'checkbox', text = ' Pool Astral Power for next pull ', icon = S.Solstice:ID(), key = 'appool', default = true },
            { type = 'spinner', text = '         Start at what TTD', key = 'appoolttd', min = 1, max = 15, default = 4 },
            { type = 'spinner', text = '         Pool how much AP', key = 'appoolamount', min = 1, max = 100, default = 85 },
            { type = 'spacer' },
            { type = 'header', text = 'Defensives', color = Config_Color },
            { type = 'checkspin', text = ' Barkskin', icon = S.Barkskin:ID(), key = 'barkskin', min = 1, max = 100, default_spin = 40, default_check = false },
            { type = 'spacer' },
            { type = 'checkspin', text = ' Renewal', icon = S.Renewal:ID(), key = 'renewal', min = 1, max = 100, default_spin = 25, default_check = true },
            { type = 'spacer' },
            { type = 'checkspin', text = ' Regrowth (Raid/Party)', icon = 8936, key = 'regrowth', min = 1, max = 100, default_spin = 40, default_check = true },
            { type = 'checkspin', text = ' Regrowth (Solo)', icon = 8936, key = 'soloregrowth', min = 1, max = 100, default_spin = 70, default_check = true },
            { type = 'spacer' },
            { type = 'checkspin', text = " Nature's Vigil", icon = S.NaturesVigil:ID(), key = 'nvigil', min = 1, max = 100, default_spin = 35, default_check = true },
            { type = 'checkbox', text = " Nature's Vigil: also check party's allies health", icon = S.NaturesVigil:ID(), key = 'nvigil_allies', default = true },
            { type = 'spacer' },
            { type = 'checkbox', text = ' Smart Bear', key = 'smartbear', icon = S.BearForm:ID(), default = true },
            { type = 'spinner', text = ' Smart Bear above key level', key = 'smart_bear_above_key_level', icon = S.BearForm:ID(), min = 1, max = 40, default = 2 },
            { type = 'checkbox', text = ' Combine Bear Form with other defensives', key = 'combine_bearform', icon = S.BearForm:ID(), default = false },
            { type = 'dropdown',
                text = ' Hold Bear Form until', key = 'smartholdbear',
                icon = S.BearForm:ID(),
                width = 205,
                multiselect = true,
                list = {
                    { text = 'Ursine Vigor falls off', key = 'smartholdbear_uv' },
                    { text = 'Frenzied Regeneration falls off', key = 'smartholdbear_fg' },
                    { text = 'Danger is over', key = 'smartholdbear_over' },
                },
                default = {
                    'smartholdbear_uv',
                    'smartholdbear_fg'
                },
            },
            { type = 'spacer' },
            { type = 'header', text = 'Utilities', color = Config_Color },
            { type = 'dropdown',
                text = ' Moonkin Form', key = 'mform',
                icon = S.MoonkinForm:ID(),
                multiselect = true,
                list = {
                    { text = 'Target is Valid', key = 'mform_valid' },
                    { text = 'From Cat Form', key = 'mform_cat' },
                    { text = 'From Bear Form', key = 'mform_bear' },
                },
                default = {
                    'mform_valid'
                },
            },
            { type = 'checkbox', text = 'Auto-Travel Form (Out of Combat)', key = 'autotravel', icon = S.TravelForm:ID(), default = true },
            { type = 'spacer' },
            { type = 'dropdown',
                text = ' Innervate', key = 'innervate',
                icon = S.Innervate:ID(),
                list = {
                    { text = 'Target', key = 'innervate_target' },
                    { text = 'MouseOver', key = 'innervate_mouseover' },
                    { text = 'Focus', key = 'innervate_focus' },
                    { text = 'None', key = 'innervate_none' },
                },
                default = 'innervate_none',
            },
            { type = 'spinner', text = " Innervate: Healer's Mana threshold", icon = S.Innervate:ID(), key = 'innervate_mana', min = 1, max = 100, default = 60, step = 1 },
            { type = 'spacer' },
            { type = 'dropdown',
                text = ' Rebirth', key = 'autorebirth',
                multiselect = true,
                icon = S.Rebirth:ID(),
                list = {
                    { text = 'Target', key = 'autorebirth_target' },
                    { text = 'MouseOver', key = 'autorebirth_mouseover' },
                },
                default = {
                },
            },
            { type = 'spacer' },
            { type = 'dropdown',
                text = ' Mark of the Wild', key = 'motw',
                icon = S.MarkoftheWild:ID(),
                multiselect = true,
                list = {
                    { text = 'Self', key = 'motw_self' },
                    { text = 'Friends', key = 'motw_friends' },
                },
                default = {
                    'motw_self',
                    'motw_friends'
                },
            },
            { type = 'checkbox', text = "Typhoon only when enemy is under Ursol's Vortex", key = 'typhoon_vortex', icon = S.Typhoon:ID(), default = false },
            { type = 'checkbox', text = ' Magic Groundspell - Celestial Alignment', icon = S.CelestialAlignment:ID(), key = 'magicgroundspell_ca', default = false },
            { type = 'checkbox', text = ' Magic Groundspell - Force of Nature', icon = S.ForceofNature:ID(), key = 'magicgroundspell_fon', default = false },
            { type = 'spacer' },
            { type = 'header', text = 'WeakAuras', color = Config_Color },
            { type = 'button', text = 'Import Enemies Count', width = 150, callback = function()
                if not _G.WeakAuras then
                    return false
                end
                _G.WeakAuras.Import("!WA:2!9zrtVnXr7AIquzvHOPqeIsRmbbnrvXkj0IQOKdEnBi0xts0A7gOT01ZUZ4DhY6zgnZSjXCdRk1EQhsp1Rw947j)tGdV3ELwf1FbCO)a4xqFMz3K4a2Ss2ZZmpFmpF)mLwF2EZINf)BpCinKZAYtLHKR)bhHs1XC5wcnLZuLL93QBxfrxAeIfcNVnNY0b1D3SLRNOcMQejO(TihO97YL9qAFHVM2JuSRKefALZD9uAKux2HYO6YbDHfvCzX8tqaRu9SI4n7siIAkbju7HaHflU(e4kCLcihgNrgIjkKovI0KyNUCME0dPjsE)ko8eStchH)FVombPu(ig23i7BhOOSOeY)3RxAIMo3FDPobAucHPNl7kLfZMQi(NLJook6ligShEcYowqgzpIm2cIsO7r6mWawWJ5UYU85b2gAzJ3laP74zzhqi3PMxJTQ)FYUYCbB024L7iFbyrpcRo3rQyeMV)tYJiZmusIaFBR(cIJgCftXBQpJ305fCEVsd18qqhva7xT0VY9qjIy0mRMsXJwvix9P470pz3(d7spGG3HI1X)Yhj(SjkDwby2fL8uDcLrKB1UvJhTP7uy4yqpJwH9c5jC53nd8nmmvP59mulUr3uMnXzHflxb(KeiyYGfusnUBzcdx4lEAUVasBd5qOLVpR5(ubPJ4sNkT2cmKh4bbfM(vhtMloIepqlPrrGF43VTSa8FC0G)miN7r5l(2JGeyDQ64JIPys2kEmupIQC23eSbOA6yNui9oWy0e5aiuNKaXTYfw2MaThu6pFltJ2TcJRR8yeLvdJ5Sk6ycZIzmdF(CugcnjVe88wka)GDnHhIsgteRvPbnOPonyH5RfsSNT0DQU88lEVhs02Tl8d(2vJoT4yYyJgaZBqKCqaUhmgI2GHzq1OQbAmeBBnwaL58Q57gdDlKmIOpgD(ol6XOrb(EweqJMNdUWnZLJrDDzKEuI6rmpelISWxT8IlwPA1kFrLLGFaWj8Kl7ZWstisOITmwNNY0lScW94ju5SAYNkxUCGndrj(KMB72OHF7TFqTwU(TBwZPHB2AIpFY5KAzkXiaVWys4UbP2KTxRsZLwZ0Uqr0i)61A2YVzRAETob12scGYZEzq)QaGstPSCd3gBVE7ghc6BEk5CzRDUrqT0ZT3)EKvrS(IzlYVZjPbpIgkw6enSGZ3srTN9JR(SVTIrHfFmYkUcr8yoM8F)aXNoXw6f14R8gO5prYqjFFEZJ)4qeJ2Z0vMD3SpC9S7NDXxrqkstTKWI0Xx4iSPjmG2wdjveysdwnWqJXyZUOtpiR1Yy2pDHSNL9Zz(ay53(eXxo9rn4(qvin0xhljQyO8UV4wVVoHcjjKA0(zMslQtT3Gycnkw)YrJr383IjUXe4RO50j9dNORuFIOhygl1eMGSwGYo6nG2tWL6jY2PDzN4m3Z2IxCZPtYP2(qip0ZoarDT5YU)rW2au4UrqBCi34s5t7xxc9hS)jU2oe0U1GOP6ElVSFBnnHQ7xXEW0C3eLUhwQJov5V17BE)P6M3(MzoVuEqXBpMy0xpTO)GJ7XhprpX7CBILEFA17k)xdVqIBs6dTJgDQbBDmpHQJ85qrjTB)SZp5yW74qMNIh6uRrTnR76V8kd2NlX7irIb7uamQRX3dvtinAM3mwmXu8e0SUNR7MIlFS9AURhqvOGecoEkw1u9AqJqZlciXhQijDTpZl78JYNYw3oKUuPsWqAO(LM)0WruwUyHD3EeagsCTDq7iUknIXLKIhrAp1vk5svNaqaDPrLNvf81vx5ovxD29(7N8Vp")
            end },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
        }
    }
    Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
    Config_Table.config = MainAddon.BuildDPSTrinketUI(Config_Table.config, Config_Color)
    Config_Table.config = MainAddon.BuildCombatPotionUI(Config_Table.config, "Balance", Config_Color)
    M.SetConfig(102, Config_Table)

    local Settings = {}

    Settings.TempBlackListSymbioticRelationship = {}
    Settings.Tanks, Settings.Healers, Settings.Members, Settings.Damagers, Settings.Melees = HealingEngine:Fetch()

    -- Innervate Variables
    Settings.InnervateName = S.Innervate:Name()
    Settings.MacroFocus = '/cast [@focus, nodead, help] ' .. Settings.InnervateName
    Settings.MacroMouseOver = '/cast [@mouseover, nodead, help] ' .. Settings.InnervateName
    Settings.MacroTarget = '/cast [@target, nodead, help] ' .. Settings.InnervateName
    Settings.innervateMode = GetSetting('innervate', 'innervate_none')
    Settings.InnervateValue = "None"
    local StaticInnervateValue = "None"

    local function UpdateMacroInnervate()    
        if Settings.innervateMode == 'innervate_target' then
            M.Autobind.ClickedUpdateMacro(Settings.InnervateName, Settings.MacroTarget)
        elseif Settings.innervateMode == 'innervate_mouseover' then
            M.Autobind.ClickedUpdateMacro(Settings.InnervateName, Settings.MacroMouseOver)
        elseif Settings.innervateMode == 'innervate_focus' then
            M.Autobind.ClickedUpdateMacro(Settings.InnervateName, Settings.MacroFocus)
        end
    end

    local old_OnValueChanged = MainAddon.Interface.usedGUIs.CORE_DruidBalance_CONFIG.elements.innervate.eventListeners.OnValueChanged
    MainAddon.Interface.usedGUIs.CORE_DruidBalance_CONFIG.elements.innervate.eventListeners.OnValueChanged = function(...)
        old_OnValueChanged(...)
        Settings.innervateMode = GetSetting('innervate', 'innervate_none')

        local function UpdateInnervate()
            if not Player:AffectingCombat() then
                UpdateMacroInnervate()
                Settings.InnervateValue = M.Autobind.MacroValue(Settings.InnervateName)
            else
                C_Timer.After(3, UpdateInnervate)
            end
        end
        UpdateInnervate()
        Settings.InnervateValue = M.Autobind.MacroValue(Settings.InnervateName)
    end

    local function EvaluateGroundSpellMagic()
        if not S.OrbitalStrike:IsAvailable() then
            return true
        end
        return false
    end

    --- ===== Start Custom =====
    Settings.inDungeon, Settings.inRaid, Settings.inCombat, Settings.isValid = Player:IsInDungeonArea(), Player:IsInRaidArea(), Player:AffectingCombat(), M.TargetIsValid()
    Settings.dotpack_check = GetSetting('dotpack_check', false)
    Settings.dotpack_spin = GetSetting('dotpack_spin', 60)
    Settings.magicgroundspell_ca = GetSetting('magicgroundspell_ca', false)
    Settings.magicgroundspell_fon = GetSetting('magicgroundspell_fon', false)
    
    Enemies40y, EnemiesCount40y = {}, 0

    -- DoT before CDs function (yuno)
    local function PercentageWithDots()
        local totalEnemies = 0
        local dottedEnemies = 0
        for _, ThisUnit in ipairs(Enemies40y) do
            totalEnemies = totalEnemies + 1
            if ThisUnit:DebuffUp(S.MoonfireDebuff) and ThisUnit:DebuffUp(S.SunfireDebuff) then
                dottedEnemies = dottedEnemies + 1
            end
        end
        if totalEnemies == 0 then return 0, 0 end
        return (dottedEnemies / totalEnemies) * 100, dottedEnemies
    end
    
    -- Custom: "and (not ExcludeNPCList[TargetUnit:NPCID()] or TargetUnit:IsUnit(Target))" 
    local ExcludeNPCList = {
        -- Spiteful Shades
        [174773] = true
    }

    local function UnitWithMoonfire(enemies)
        local Count = 0
        for k in pairs(enemies) do
            ---@class Unit
            local CycleUnit = enemies[k]
            if not CycleUnit:DebuffRefreshable(S.MoonfireDebuff) then
                Count = Count + 1
            end
        end
        return Count
    end

    ---@param TargetUnit Unit
    local function EvaluateCycleMoonfireToggle(TargetUnit)
        return TargetUnit:DebuffRefreshable(S.MoonfireDebuff) and not ExcludeNPCList[TargetUnit:NPCID()]
    end

    ---@param TargetedUnit Unit
    local function EvaluateSymbioticRelationship(TargetedUnit)
        return TargetedUnit:BuffDown(S.SymbioticRelationship) and not Settings.TempBlackListSymbioticRelationship[TargetedUnit:UnfilterName()]
    end
    --- ===== End Custom =====
    
    -- Events
    HL.Druid = {}
    local Druid = HL.Druid
    Druid.FullMoonLastCast = nil
    Druid.OrbitBreakerStacks = 0

    --- ===== Rotation Variables =====
    local VarPassiveAsp
    local VarCAEffectiveCD
    local VarPreCDCondition
    local VarCDCondition
    local VarNoCDTalent
    local VarEclipse, VarEclipseRemains
    local VarEnterLunar, VarBoatStacks
    local VarConvokeCondition
    local CAIncBuffUp
    local CAIncBuffRemains
    local CAInc = S.IncarnationTalent:IsAvailable() and S.Incarnation or S.CelestialAlignment
    local CAIncCD = S.OrbitalStrike:IsAvailable() and 120 or (S.WhirlingStars:IsAvailable() and 100 or 180)
    local CAIncDuration = S.IncarnationTalent:IsAvailable() and 20 or (S.CelestialAlignment:IsAvailable() and 15 or 0)
    local ConvokeCD = S.ElunesGuidance:IsAvailable() and 60 or 120
    local IsInSpellRange = false
    local Enemies10ySplash, EnemiesCount10ySplash
    local BossFightRemains = 11111
    local FightRemains = 11111

    --- ===== Trinket Variables =====
    local Trinket1, Trinket2
    local VarTrinket1ID, VarTrinket2ID
    local VarTrinket1Level, VarTrinket2Level
    local VarTrinket1Spell, VarTrinket2Spell
    local VarTrinket1Range, VarTrinket2Range
    local VarTrinket1CastTime, VarTrinket2CastTime
    local VarTrinket1CD, VarTrinket2CD
    local VarTrinket1Ex, VarTrinket2Ex
    local VarOnUseTrinket
    local VarTrinketFailures = 0

    local function SetTrinketVariables()
        local T1, T2 = Player:GetTrinketData(OnUseExcludes)

        -- If we don't have trinket items, try again in 5 seconds.
        if VarTrinketFailures < 5 and ((T1.ID == 0 or T2.ID == 0) or (T1.Level == 0 or T2.Level == 0) or (T1.SpellID > 0 and not T1.Usable or T2.SpellID > 0 and not T2.Usable)) then
            VarTrinketFailures = VarTrinketFailures + 1
            Delay(5, function()
                SetTrinketVariables()
            end
            )
            return
        end

        Trinket1 = T1.Object
        Trinket2 = T2.Object

        VarTrinket1ID = T1.ID
        VarTrinket2ID = T2.ID

        VarTrinket1Level = T1.Level
        VarTrinket2Level = T2.Level

        VarTrinket1Spell = T1.Spell
        VarTrinket1Range = T1.Range
        VarTrinket1CastTime = T1.CastTime
        VarTrinket2Spell = T2.Spell
        VarTrinket2Range = T2.Range
        VarTrinket2CastTime = T2.CastTime

        VarTrinket1CD = T1.Cooldown
        VarTrinket2CD = T2.Cooldown

        VarTrinket1Ex = T1.Excluded
        VarTrinket2Ex = T2.Excluded

        local T1Test = num(Trinket1:HasUseBuff() and VarTrinket1ID ~= I.OvinaxsMercurialEgg:ID())
        local T2Test = num(Trinket2:HasUseBuff() and VarTrinket2ID ~= I.OvinaxsMercurialEgg:ID()) * 2
        VarOnUseTrinket = 0 + T1Test + T2Test
    end
    SetTrinketVariables()

    --- ===== Event Registrations =====
    HL:RegisterForEvent(function()
        SetTrinketVariables()
    end, "PLAYER_EQUIPMENT_CHANGED")

    HL:RegisterForEvent(function()
        BossFightRemains = 11111
        FightRemains = 11111
    end, "PLAYER_REGEN_ENABLED")

    HL:RegisterForEvent(function()
        CAInc = S.IncarnationTalent:IsAvailable() and S.Incarnation or S.CelestialAlignment
        CAIncCD = S.OrbitalStrike:IsAvailable() and 120 or (S.WhirlingStars:IsAvailable() and 80 or 180)
        CAIncDuration = S.IncarnationTalent:IsAvailable() and 20 or (S.CelestialAlignment:IsAvailable() and 15 or 0)
        ConvokeCD = S.ElunesGuidance:IsAvailable() and 60 or 120
    end, "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")


    --- ===== Helper Functions =====
    local function EnergizeAmount(Spell)
        local TotalAsp = 0
        if Spell == S.Wrath then
        -- Calculate Wrath AsP
        TotalAsp = 8
        if S.WildSurges:IsAvailable() then
            TotalAsp = TotalAsp + 2
        end
        if S.SouloftheForest:IsAvailable() and Player:BuffUp(S.EclipseSolar) then
            TotalAsp = TotalAsp * 1.6
        end
        elseif Spell == S.Starfire then
        -- Calculate Starfire AsP
        TotalAsp = 10
        if S.WildSurges:IsAvailable() then
            TotalAsp = TotalAsp + 2
        end
        if Player:BuffUp(S.WarriorofEluneBuff) then
            TotalAsp = TotalAsp * 1.4
        end
        if S.SouloftheForest:IsAvailable() and Player:BuffUp(S.EclipseLunar) then
            local SotFBonus = (1 + 0.2 * EnemiesCount10ySplash)
            if SotFBonus > 1.6 then SotFBonus = 1.6 end
            TotalAsp = TotalAsp * SotFBonus
        end
        elseif Spell == S.Moonfire then
        -- Calculate Moonfire AsP
        TotalAsp = 6
        if S.MoonGuardian:IsAvailable() then
            TotalAsp = TotalAsp + 2
        end
        elseif Spell == S.Sunfire then
        -- Calculate Sunfire AsP
        TotalAsp = 6
        elseif Spell == S.NewMoon then
        -- Calculate New Moon AsP
        TotalAsp = 10
        elseif Spell == S.HalfMoon then
        -- Calculate Half Moon AsP
        TotalAsp = 20
        elseif Spell == S.FullMoon then
        -- Calculate Full Moon AsP
        TotalAsp = 40
        elseif Spell == S.ForceofNature then
        -- Calculate Force of Nature AsP
        TotalAsp = 20
        end
        return TotalAsp
    end

    --- ===== CastCycle Functions =====
    local function EvaluateCycleMoonfireAoE(TargetUnit)
    -- target_if=refreshable&(target.time_to_die-remains)>6&(!talent.treants_of_the_moon|spell_targets-active_dot.moonfire_dmg>6|cooldown.force_of_nature.remains>3&!buff.harmony_of_the_grove.up)
        return TargetUnit:DebuffRefreshable(S.MoonfireDebuff) and (TargetUnit:TimeToDie() - TargetUnit:DebuffRemains(S.MoonfireDebuff)) > 6 and (not S.TreantsoftheMoon:IsAvailable() or EnemiesCount10ySplash - S.MoonfireDebuff:AuraActiveCount() > 6 or (S.ForceofNature:CooldownRemains() > 3 or Player:IsMoving()) and Player:BuffDown(S.HarmonyoftheGroveBuff))
        and (not ExcludeNPCList[TargetUnit:NPCID()] or TargetUnit:IsUnit(Target))
    end
    
    local function EvaluateCycleMoonfireST(TargetUnit)
        -- target_if=remains<3&(!talent.treants_of_the_moon|cooldown.force_of_nature.remains>3&!buff.harmony_of_the_grove.up)
        return TargetUnit:DebuffRemains(S.MoonfireDebuff) < 3 and (not S.TreantsoftheMoon:IsAvailable() or S.ForceofNature:CooldownRemains() > 3 and Player:BuffDown(S.HarmonyoftheGroveBuff))
        and (not ExcludeNPCList[TargetUnit:NPCID()] or TargetUnit:IsUnit(Target))
    end
    
    local function EvaluateCycleMoonfireST2(TargetUnit)
        -- target_if=refreshable&(!talent.treants_of_the_moon|cooldown.force_of_nature.remains>3&!buff.harmony_of_the_grove.up)
        return TargetUnit:DebuffRefreshable(S.MoonfireDebuff) and (not S.TreantsoftheMoon:IsAvailable() or S.ForceofNature:CooldownRemains() > 3 and Player:BuffDown(S.HarmonyoftheGroveBuff))
        and (not ExcludeNPCList[TargetUnit:NPCID()] or TargetUnit:IsUnit(Target))
    end
    
    local function EvaluateCycleStellarFlare(TargetUnit)
        -- target_if=refreshable&(target.time_to_die-remains-target>7+spell_targets)
        return TargetUnit:DebuffRefreshable(S.StellarFlareDebuff) and (TargetUnit:TimeToDie() - TargetUnit:DebuffRemains(S.StellarFlareDebuff) > 7 + EnemiesCount10ySplash)
        and (not ExcludeNPCList[TargetUnit:NPCID()] or TargetUnit:IsUnit(Target))
    end
    
    local function EvaluateCycleSunfireAoE(TargetUnit)
        -- target_if=refreshable&(target.time_to_die-remains)>6-(spell_targets%2)
        return TargetUnit:DebuffRefreshable(S.SunfireDebuff) and (TargetUnit:TimeToDie() - TargetUnit:DebuffRemains(S.SunfireDebuff)) > 6 - (EnemiesCount10ySplash / 2)
        and (not ExcludeNPCList[TargetUnit:NPCID()] or TargetUnit:IsUnit(Target))
    end
    
    local function EvaluateCycleSunfireST(TargetUnit)
        -- target_if=remains<3|refreshable&(hero_tree.keeper_of_the_grove&cooldown.force_of_nature.ready|hero_tree.elunes_chosen&variable.cd_condition)
        return TargetUnit:DebuffRemains(S.SunfireDebuff) < 3 or TargetUnit:DebuffRefreshable(S.SunfireDebuff)
        and (not ExcludeNPCList[TargetUnit:NPCID()] or TargetUnit:IsUnit(Target))
    end
    
    local function EvaluateCycleSunfireST2(TargetUnit)
        -- target_if=refreshable
        return TargetUnit:DebuffRefreshable(S.SunfireDebuff)
        and (not ExcludeNPCList[TargetUnit:NPCID()] or TargetUnit:IsUnit(Target))
    end

    -- Astral Power Pooling
    local function ShouldCastAPSpenders()
        if GetSetting('appool', true) then
            if FightRemains <= GetSetting('appoolttd', 6) and (Settings.inDungeon or Settings.inRaid) and not Player:InBossEncounter() then
                if Player:AstralPower() <= GetSetting('appoolamount', 85) then
                    return false
                end
            end
        end
        return true
    end

    --- ===== Rotation Functions =====
    local function Precombat()
        -- variable,name=no_cd_talent,value=!talent.celestial_alignment&!talent.incarnation_chosen_of_elune|druid.no_cds
        -- variable,name=on_use_trinket,value=0
        -- variable,name=on_use_trinket,op=add,value=trinket.1.has_use_buff&!trinket.1.is.ovinaxs_mercurial_egg&!trinket.1.is.spymasters_web
        -- variable,name=on_use_trinket,op=add,value=(trinket.2.has_use_buff&!trinket.2.is.ovinaxs_mercurial_egg&!trinket.2.is.spymasters_web)*2
        -- YUNO: handled elsewhere
        -- -- Manually added: Group buff check
        -- if S.MarkoftheWild:IsReady() and Everyone.GroupBuffMissing(S.MarkoftheWildBuff) then
        --   if Cast(S.MarkoftheWild) then return "mark_of_the_wild precombat"; end
        -- end
        -- -- moonkin_form
        -- if S.MoonkinForm:IsReady() then
        --   if Cast(S.MoonkinForm) then return "moonkin_form precombat"; end
        -- end
        -- wrath
        if S.Wrath:IsReady() and not Player:IsCasting(S.Wrath) then
          if Cast(S.Wrath) then return "wrath precombat 2"; end
        end
        -- wrath
        if S.Wrath:IsReady() and (Player:IsCasting(S.Wrath) and S.Wrath:Count() == 2 or Player:PrevGCD(1, S.Wrath) and S.Wrath:Count() == 1) then
          if Cast(S.Wrath) then return "wrath precombat 4"; end
        end
        -- starfire,if=!talent.stellar_flare
        if S.Starfire:IsReady() and (not S.StellarFlare:IsAvailable()) then
          if Cast(S.Starfire) then return "starfire precombat 6"; end
        end
        -- stellar_flare
        if S.StellarFlare:IsReady() then
          if Cast(S.StellarFlare) then return "stellar_flare precombat 8"; end
        end
      end
      

    local function PreCD()
        -- use_item,name=spymasters_web,if=variable.cd_condition&(buff.spymasters_report.stack>29|fight_remains<cooldown.ca_inc.duration)
        if I.SpymastersWeb:IsEquippedAndReady() and (VarCDCondition and (Player:BuffStack(S.SpymastersReportBuff) > 29 or BossFightRemains < CAIncCD)) then
          if Cast(I.SpymastersWeb) then return "spymasters_web pre_cd 2"; end
        end
        -- do_treacherous_transmitter_task,if=variable.cd_condition|buff.harmony_of_the_grove.up&(buff.spymasters_report.stack>29|!trinket.1.is.spymasters_web|!trinket.2.is.spymasters_web)
        -- TODO
        -- berserking,if=variable.cd_condition
        if S.Berserking:IsReady() and (VarCDCondition) then
          if Cast(S.Berserking) then return "berserking pre_cd 4"; end
        end
    end
      
    local function ST()
        -- warrior_of_elune,if=talent.lunar_calling|!talent.lunar_calling&variable.eclipse_remains<=7
        if S.WarriorofElune:IsReady() and (S.LunarCalling:IsAvailable() or not S.LunarCalling:IsAvailable() and VarEclipseRemains <= 7) then
          if Cast(S.WarriorofElune) then return "warrior_of_elune st 2"; end
        end
        -- wrath,if=variable.enter_lunar&eclipse.in_eclipse&variable.eclipse_remains<cast_time&!variable.cd_condition
        if S.Wrath:IsReady() and (VarEnterLunar and VarEclipse and VarEclipseRemains < S.Wrath:CastTime() and not VarCDCondition) then
          if Cast(S.Wrath) then return "wrath st 4"; end
        end
        -- starfire,if=!variable.enter_lunar&eclipse.in_eclipse&variable.eclipse_remains<cast_time&!variable.cd_condition
        if S.Starfire:IsReady() and (not VarEnterLunar and VarEclipse and VarEclipseRemains < S.Starfire:CastTime() and not VarCDCondition) then
          if Cast(S.Starfire) then return "starfire st 6"; end
        end
        -- sunfire,target_if=remains<3|refreshable&(hero_tree.keeper_of_the_grove&cooldown.force_of_nature.ready|hero_tree.elunes_chosen&variable.cd_condition)
        if S.Sunfire:IsReady() and (Player:HeroTreeID() == 23 and S.ForceofNature:CooldownUp() or Player:HeroTreeID() == 24 and VarCDCondition) then
          if CastCycle(S.Sunfire, Enemies40y, EvaluateCycleSunfireST) then return "sunfire st 8"; end
        end
        -- moonfire,target_if=remains<3&(!talent.treants_of_the_moon|cooldown.force_of_nature.remains>3&!buff.harmony_of_the_grove.up)
        if S.Moonfire:IsReady() then
          if CastCycle(S.Moonfire, Enemies40y, EvaluateCycleMoonfireST) then return "moonfire st 10"; end
        end
        -- call_action_list,name=pre_cd
        local ShouldReturn = PreCD(); if ShouldReturn then return ShouldReturn; end
        if VarCDCondition then
          -- celestial_alignment,if=variable.cd_condition
          if S.CelestialAlignment:IsReady() then
            if CastMagic(S.CelestialAlignment, EvaluateGroundSpellMagic, "194223-Magic", Settings.magicgroundspell_ca) then return "celestial_alignment st 12"; end
          end
          -- incarnation,if=variable.cd_condition
          if S.Incarnation:IsReady() then
            if CastMagic(S.Incarnation, EvaluateGroundSpellMagic, "194223-Magic", Settings.magicgroundspell_ca) then return "celestial_alignment st 14"; end
          end
        end
        -- wrath,if=variable.enter_lunar&(eclipse.in_none|variable.eclipse_remains<cast_time)
        if S.Wrath:IsReady() and (VarEnterLunar and (not VarEclipse or VarEclipseRemains < S.Wrath:CastTime())) then
          if Cast(S.Wrath) then return "wrath st 16"; end
        end
        -- starfire,if=!variable.enter_lunar&(eclipse.in_none|variable.eclipse_remains<cast_time)
        if S.Starfire:IsReady() and (not VarEnterLunar and (not VarEclipse or VarEclipseRemains < S.Starfire:CastTime())) then
          if Cast(S.Starfire) then return "starfire st 18"; end
        end
        -- starsurge,if=variable.cd_condition&astral_power.deficit>variable.passive_asp+action.force_of_nature.energize_amount
        if S.Starsurge:IsReady() and (VarCDCondition and Player:AstralPowerDeficit() > VarPassiveAsp + EnergizeAmount(S.ForceofNature)) then
          if Cast(S.Starsurge) then return "starsurge st 20"; end
        end
        -- force_of_nature,if=variable.pre_cd_condition|cooldown.ca_inc.full_recharge_time+5+15*talent.control_of_the_dream>cooldown&(!talent.convoke_the_spirits|cooldown.convoke_the_spirits.remains+10+15*talent.control_of_the_dream>cooldown|fight_remains<cooldown.convoke_the_spirits.remains+cooldown.convoke_the_spirits.duration+5)&(variable.on_use_trinket=0|cooldown.ca_inc.remains>20|talent.convoke_the_spirits&cooldown.convoke_the_spirits.remains>20|(variable.on_use_trinket=1|variable.on_use_trinket=3)&(trinket.1.cooldown.remains>5+15*talent.control_of_the_dream|trinket.1.cooldown.ready)|variable.on_use_trinket=2&(trinket.2.cooldown.remains>5+15*talent.control_of_the_dream|trinket.2.cooldown.ready))&(fight_remains>cooldown+5|fight_remains<cooldown.ca_inc.remains+7)|talent.whirling_stars&talent.convoke_the_spirits&cooldown.convoke_the_spirits.remains>cooldown.force_of_nature.duration-10&fight_remains>cooldown.convoke_the_spirits.remains+6
        if S.ForceofNature:IsReady() and (VarPreCDCondition or CAInc:FullRechargeTime() + 5 + 15 * num(S.ControloftheDream:IsAvailable()) > 60 and (not S.ConvoketheSpirits:IsAvailable() or S.ConvoketheSpirits:CooldownRemains() + 10 + 15 * num(S.ControloftheDream:IsAvailable()) > 60 or BossFightRemains < S.ConvoketheSpirits:CooldownRemains() + ConvokeCD + 5) and (VarOnUseTrinket == 0 or CAInc:CooldownRemains() > 20 or S.ConvoketheSpirits:IsAvailable() and S.ConvoketheSpirits:CooldownRemains() > 20 or (VarOnUseTrinket == 1 or VarOnUseTrinket == 3) and (Trinket1:CooldownRemains() > 5 + 15 * num(S.ControloftheDream:IsAvailable()) or Trinket1:CooldownUp()) or VarOnUseTrinket == 2 and (Trinket2:CooldownRemains() > 5 + 15 * num(S.ControloftheDream:IsAvailable()) or Trinket2:CooldownUp())) and (FightRemains > 65 or BossFightRemains < CAInc:CooldownRemains() + 7) or S.WhirlingStars:IsAvailable() and S.ConvoketheSpirits:IsAvailable() and S.ConvoketheSpirits:CooldownRemains() > 50 and FightRemains > S.ConvoketheSpirits:CooldownRemains() + 6) then
          if CastMagic(S.ForceofNature, nil, "205636-Magic", Settings.magicgroundspell_fon) then return "force_of_nature st 22"; end
        end
        -- fury_of_elune,if=5+variable.passive_asp<astral_power.deficit
        if S.FuryofElune:IsReady() and (5 + VarPassiveAsp < Player:AstralPowerDeficit()) then
          if Cast(S.FuryofElune) then return "fury_of_elune st 24"; end
        end
        -- starfall,if=buff.starweavers_warp.up
        if S.Starfall:IsReady() and (Player:BuffUp(S.StarweaversWarp)) then
          if Cast(S.Starfall) then return "starfall st 26"; end
        end
        -- starsurge,if=talent.starlord&buff.starlord.stack<3
        if S.Starsurge:IsReady() and (S.Starlord:IsAvailable() and Player:BuffStack(S.StarlordBuff) < 3) then
          if Cast(S.Starsurge) then return "starsurge st 28"; end
        end
        -- sunfire,target_if=refreshable
        if S.Sunfire:IsReady() then
          if CastCycle(S.Sunfire, Enemies40y, EvaluateCycleSunfireST2) then return "sunfire st 30"; end
        end
        -- moonfire,target_if=refreshable&(!talent.treants_of_the_moon|cooldown.force_of_nature.remains>3&!buff.harmony_of_the_grove.up)
        if S.Moonfire:IsReady() then
          if CastCycle(S.Moonfire, Enemies40y, EvaluateCycleMoonfireST2) then return "moonfire st 32"; end
        end
        -- starsurge,if=cooldown.convoke_the_spirits.remains<gcd.max*2&variable.convoke_condition&astral_power.deficit<50
        if S.Starsurge:IsReady() and (S.ConvoketheSpirits:CooldownRemains() < Player:GCD() * 2 and VarConvokeCondition and Player:AstralPowerDeficit() < 50) then
          if Cast(S.Starsurge) then return "starsurge st 34"; end
        end
        -- convoke_the_spirits,if=variable.convoke_condition
        if S.ConvoketheSpirits:IsReady() and (VarConvokeCondition) then
          if Cast(S.ConvoketheSpirits) then return "convoke_the_spirits st 36"; end
        end
        -- stellar_flare,target_if=refreshable&(target.time_to_die-remains-target>7+spell_targets)
        if S.StellarFlare:IsReady() then
          if CastCycle(S.StellarFlare, Enemies40y, EvaluateCycleStellarFlare) then return "stellar_flare st 38"; end
        end
        -- starsurge,if=buff.starlord.remains>4&variable.boat_stacks>=3|fight_remains<4
        if S.Starsurge:IsReady() and (Player:BuffRemains(S.StarlordBuff) > 4 and VarBoatStacks >= 3 or BossFightRemains < 4) then
          if Cast(S.Starsurge) then return "starsurge st 40"; end
        end
        -- new_moon,if=astral_power.deficit>variable.passive_asp+energize_amount|fight_remains<20|cooldown.ca_inc.remains>15
        if S.NewMoon:IsReady() and (Player:AstralPowerDeficit() > VarPassiveAsp + EnergizeAmount(S.NewMoon) or BossFightRemains < 20 or CAInc:CooldownRemains() > 15) then
          if Cast(S.NewMoon) then return "new_moon st 42"; end
        end
        -- half_moon,if=astral_power.deficit>variable.passive_asp+energize_amount&(buff.eclipse_lunar.remains>execute_time|buff.eclipse_solar.remains>execute_time)|fight_remains<20|cooldown.ca_inc.remains>15
        if S.HalfMoon:IsReady() and (Player:AstralPowerDeficit() > VarPassiveAsp + EnergizeAmount(S.HalfMoon) and (Player:BuffRemains(S.EclipseLunar) > S.HalfMoon:ExecuteTime() or Player:BuffRemains(S.EclipseSolar) > S.HalfMoon:ExecuteTime()) or BossFightRemains < 20 or CAInc:CooldownRemains() > 15) then
          if Cast(S.HalfMoon) then return "half_moon st 44"; end
        end
        -- full_moon,if=astral_power.deficit>variable.passive_asp+energize_amount&(buff.eclipse_lunar.remains>execute_time|buff.eclipse_solar.remains>execute_time)|fight_remains<20|cooldown.ca_inc.remains>15
        if S.FullMoon:IsReady() and (Player:AstralPowerDeficit() > VarPassiveAsp + EnergizeAmount(S.FullMoon) and (Player:BuffRemains(S.EclipseLunar) > S.FullMoon:ExecuteTime() or Player:BuffRemains(S.EclipseSolar) > S.FullMoon:ExecuteTime()) or BossFightRemains < 20 or CAInc:CooldownRemains() > 15) then
          if Cast(S.FullMoon) then return "full_moon st 46"; end
        end
        -- starsurge,if=buff.starweavers_weft.up|buff.touch_the_cosmos.up
        if S.Starsurge:IsReady() and (Player:BuffUp(S.StarweaversWeft) or Player:BuffUp(S.TouchtheCosmosBuff)) then
          if Cast(S.Starsurge) then return "starsurge st 48"; end
        end
        -- starsurge,if=astral_power.deficit<variable.passive_asp+action.wrath.energize_amount+(action.starfire.energize_amount+variable.passive_asp)*(buff.eclipse_solar.remains<(gcd.max*3))
        if S.Starsurge:IsReady() and (Player:AstralPowerDeficit() < VarPassiveAsp + EnergizeAmount(S.Wrath) + (EnergizeAmount(S.Starfire) + VarPassiveAsp) * (num(Player:BuffRemains(S.EclipseSolar) < Player:GCD() * 3))) then
          if Cast(S.Starsurge) then return "starsurge st 50"; end
        end
        -- force_of_nature,if=!hero_tree.keeper_of_the_grove
        if S.ForceofNature:IsReady() and (Player:HeroTreeID() ~= 23) then
          if CastMagic(S.ForceofNature, nil, "205636-Magic", Settings.magicgroundspell_fon) then return "force_of_nature st 52"; end
        end
        -- wild_mushroom,if=!prev_gcd.1.wild_mushroom&dot.fungal_growth.remains<2
        if S.WildMushroom:IsReady() and (not Player:PrevGCD(1, S.WildMushroom) and Target:DebuffRemains(S.FungalGrowthDebuff) < 2) then
          if Cast(S.WildMushroom) then return "wild_mushroom st 54"; end
        end
        -- starfire,if=talent.lunar_calling
        if S.Starfire:IsReady() and (S.LunarCalling:IsAvailable()) then
          if Cast(S.Starfire) then return "starfire st 56"; end
        end
        -- wrath
        if S.Wrath:IsReady() then
          if Cast(S.Wrath) then return "wrath st 58"; end
        end
      end                  

    local function AoE()
        local DungeonRoute = Settings.inDungeon
        -- wrath,if=variable.enter_lunar&eclipse.in_eclipse&variable.eclipse_remains<cast_time
        if S.Wrath:IsReady() and (VarEnterLunar and VarEclipse and VarEclipseRemains < S.Wrath:CastTime()) then
          if Cast(S.Wrath) then return "wrath aoe 2"; end
        end
        -- starfire,if=!variable.enter_lunar&eclipse.in_eclipse&variable.eclipse_remains<cast_time
        if S.Starfire:IsReady() and (not VarEnterLunar and VarEclipse and VarEclipseRemains < S.Starfire:CastTime()) then
          if Cast(S.Starfire) then return "starfire aoe 4"; end
        end
        -- starfall,if=astral_power.deficit<=variable.passive_asp+6
        if S.Starfall:IsReady() and (Player:AstralPowerDeficit() <= VarPassiveAsp + 6) then
          if Cast(S.Starfall) then return "starfall aoe 6"; end
        end
        -- moonfire,target_if=refreshable&(target.time_to_die-remains)>6&(!talent.treants_of_the_moon|spell_targets-active_dot.moonfire_dmg>6|cooldown.force_of_nature.remains>3&!buff.harmony_of_the_grove.up),if=fight_style.dungeonroute|fight_style.dungeonslice
        if S.Moonfire:IsReady() and (DungeonRoute) then
          if CastCycle(S.Moonfire, Enemies40y, EvaluateCycleMoonfireAoE) then return "moonfire aoe 8"; end
        end
        -- sunfire,target_if=refreshable&(target.time_to_die-remains)>6-(spell_targets%2)
        if S.Sunfire:IsReady() then
          if CastCycle(S.Sunfire, Enemies40y, EvaluateCycleSunfireAoE) then return "sunfire aoe 10"; end
        end
        -- moonfire,target_if=refreshable&(target.time_to_die-remains)>6&(!talent.treants_of_the_moon|spell_targets-active_dot.moonfire_dmg>6|cooldown.force_of_nature.remains>3&!buff.harmony_of_the_grove.up),if=!fight_style.dungeonroute&!fight_style.dungeonslice
        if S.Moonfire:IsReady() and (not DungeonRoute) then
          if CastCycle(S.Moonfire, Enemies40y, EvaluateCycleMoonfireAoE) then return "moonfire aoe 12"; end
        end
        -- wrath,if=variable.enter_lunar&(eclipse.in_none|variable.eclipse_remains<cast_time)&!variable.pre_cd_condition
        if S.Wrath:IsReady() and (VarEnterLunar and (not VarEclipse or VarEclipseRemains < S.Wrath:CastTime()) and not VarPreCDCondition) then
          if Cast(S.Wrath) then return "wrath aoe 14"; end
        end
        -- starfire,if=!variable.enter_lunar&(eclipse.in_none|variable.eclipse_remains<cast_time)
        if S.Starfire:IsReady() and (not VarEnterLunar and (not VarEclipse or VarEclipseRemains < S.Starfire:CastTime())) then
          if Cast(S.Starfire) then return "starfire aoe 16"; end
        end
        -- stellar_flare,target_if=refreshable&(target.time_to_die-remains-target>7+spell_targets),if=spell_targets<(11-talent.umbral_intensity.rank-(2*talent.astral_smolder)-talent.lunar_calling)
        if S.StellarFlare:IsReady() and (EnemiesCount10ySplash < (11 - S.UmbralIntensity:TalentRank() - (2 * num(S.AstralSmolder:IsAvailable())) - num(S.LunarCalling:IsAvailable()))) then
          if CastCycle(S.StellarFlare, Enemies40y, EvaluateCycleStellarFlare) then return "stellar_flare aoe 18"; end
        end
        -- force_of_nature...
        if S.ForceofNature:IsReady() and (VarPreCDCondition or CAInc:FullRechargeTime() + 5 + 15 * num(S.ControloftheDream:IsAvailable()) > 60 and (not S.ConvoketheSpirits:IsAvailable() or S.ConvoketheSpirits:CooldownRemains() + 10 + 15 * num(S.ControloftheDream:IsAvailable()) > 60 or BossFightRemains < S.ConvoketheSpirits:CooldownRemains() + ConvokeCD + 5) and (VarOnUseTrinket == 0 or (VarOnUseTrinket == 1 or VarOnUseTrinket == 3) and (Trinket1:CooldownRemains() > 5 + 15 * num(S.ControloftheDream:IsAvailable()) or CAInc:CooldownRemains() > 20 or Trinket1:CooldownUp()) or VarOnUseTrinket == 2 and (Trinket2:CooldownRemains() > 5 + 15 * num(S.ControloftheDream:IsAvailable()) or CAInc:CooldownRemains() > 20 or Trinket2:CooldownUp())) and (FightRemains > 65 or BossFightRemains < CAInc:CooldownRemains() + 7) or S.WhirlingStars:IsAvailable() and S.ConvoketheSpirits:IsAvailable() and S.ConvoketheSpirits:CooldownRemains() > 50 and FightRemains > S.ConvoketheSpirits:CooldownRemains() + 6) then
          if CastMagic(S.ForceofNature, nil, "205636-Magic", Settings.magicgroundspell_fon) then return "force_of_nature aoe 20"; end
        end
        -- fury_of_elune,if=eclipse.in_eclipse
        if S.FuryofElune:IsReady() and (VarEclipse) then
          if Cast(S.FuryofElune) then return "fury_of_elune aoe 22"; end
        end
        -- call_action_list,name=pre_cd
        local ShouldReturn = PreCD(); if ShouldReturn then return ShouldReturn; end
        if VarCDCondition then
          -- celestial_alignment,if=variable.cd_condition
          if S.CelestialAlignment:IsReady() then
            if CastMagic(S.CelestialAlignment, EvaluateGroundSpellMagic, "194223-Magic", Settings.magicgroundspell_ca) then return "celestial_alignment aoe 24"; end
          end
          -- incarnation,if=variable.cd_condition
          if S.Incarnation:IsReady() then
            if CastMagic(S.Incarnation, EvaluateGroundSpellMagic, "194223-Magic", Settings.magicgroundspell_ca) then return "celestial_alignment aoe 26"; end
          end
        end
        -- warrior_of_elune,if=!talent.lunar_calling&buff.eclipse_solar.remains<7|talent.lunar_calling&!buff.dreamstate.up
        if S.WarriorofElune:IsReady() and (not S.LunarCalling:IsAvailable() and Player:BuffRemains(S.EclipseSolar) < 7 or S.LunarCalling:IsAvailable() and Player:BuffDown(S.DreamstateBuff)) then
          if Cast(S.WarriorofElune) then return "warrior_of_elune aoe 28"; end
        end
        -- starfire,if=(!talent.lunar_calling&spell_targets.starfire=1)&(buff.eclipse_solar.up&buff.eclipse_solar.remains<action.starfire.cast_time|eclipse.in_none)
        if S.Starfire:IsReady() and ((not S.LunarCalling:IsAvailable() and EnemiesCount10ySplash == 1) and (Player:BuffUp(S.EclipseSolar) and Player:BuffRemains(S.EclipseSolar) < S.Starfire:CastTime() or not VarEclipse)) then
          if Cast(S.Starfire) then return "starfire aoe 30"; end
        end
        -- starfall,if=buff.starweavers_warp.up|buff.touch_the_cosmos.up
        if S.Starfall:IsReady() and (Player:BuffUp(S.StarweaversWarp) or Player:BuffUp(S.TouchtheCosmosBuff)) then
          if Cast(S.Starfall) then return "starfall aoe 32"; end
        end
        -- starsurge,if=buff.starweavers_weft.up
        if S.Starsurge:IsReady() and (Player:BuffUp(S.StarweaversWeft)) then
          if Cast(S.Starsurge) then return "starsurge aoe 34"; end
        end
        -- starfall
        if S.Starfall:IsReady() then
          if Cast(S.Starfall) then return "starfall aoe 36"; end
        end
        -- convoke_the_spirits...
        if S.ConvoketheSpirits:IsReady() and ((Player:BuffDown(S.DreamstateBuff) and Player:BuffDown(S.UmbralEmbraceBuff) and EnemiesCount10ySplash < 7 or EnemiesCount10ySplash == 1) and (BossFightRemains < 5 or (CAIncBuffUp or CAInc:CooldownRemains() > 40) and (Player:HeroTreeID() ~= 23 or Player:BuffUp(S.HarmonyoftheGroveBuff) or S.ForceofNature:CooldownRemains() > 15))) then
          if Cast(S.ConvoketheSpirits) then return "convoke_the_spirits aoe 38"; end
        end
        -- new_moon
        if S.NewMoon:IsReady() then
          if Cast(S.NewMoon) then return "new_moon aoe 40"; end
        end
        -- half_moon
        if S.HalfMoon:IsReady() then
          if Cast(S.HalfMoon) then return "half_moon aoe 42"; end
        end
        -- full_moon
        if S.FullMoon:IsReady() then
          if Cast(S.FullMoon) then return "full_moon aoe 44"; end
        end
        -- wild_mushroom,if=!prev_gcd.1.wild_mushroom&!dot.fungal_growth.ticking
        if S.WildMushroom:IsReady() and (not Player:PrevGCD(1, S.WildMushroom) and Target:DebuffDown(S.FungalGrowthDebuff)) then
          if Cast(S.WildMushroom) then return "wild_mushroom aoe 46"; end
        end
        -- force_of_nature,if=!hero_tree.keeper_of_the_grove
        if S.ForceofNature:IsReady() and (Player:HeroTreeID() ~= 23) then
          if CastMagic(S.ForceofNature, nil, "205636-Magic", Settings.magicgroundspell_fon) then return "force_of_nature aoe 48"; end
        end
        -- starfire,if=talent.lunar_calling|buff.eclipse_lunar.up&spell_targets.starfire>3-(talent.umbral_intensity|talent.soul_of_the_forest)
        if S.Starfire:IsReady() and (S.LunarCalling:IsAvailable() or Player:BuffUp(S.EclipseLunar) and EnemiesCount10ySplash > 3 - num(S.UmbralIntensity:IsAvailable() or S.SouloftheForest:IsAvailable())) then
          if Cast(S.Starfire) then return "starfire aoe 50"; end
        end
        -- wrath
        if S.Wrath:IsReady() then
          if Cast(S.Wrath) then return "wrath aoe 52"; end
        end
      end         

    local function MovementDoTs()
        if not GetSetting('movementdots', false) then
            return
        end
        
        if Player:IsMovingFor() < 1 then
            return
        end
    
        if S.Sunfire:IsReady() and Target:DebuffRemains(S.SunfireDebuff) < 3 then
            if Cast(S.Sunfire) then
                return "Sunfire Movement"
            end
        end
    
        if S.Moonfire:IsReady() then
            if Cast(S.Moonfire) then
                return "Moonfire Movement"
            end
        end
    end 

    --- ===== Start Custom =====
    local function Utilities()
        if M.Toggle:GetToggle('BearMode') then
            if S.BearForm:IsReady(Player) and Player:BuffDown(S.BearForm) then
                if Cast(S.BearForm) then
                    return "Bear Mode: Bear Form"
                end
            end
            return "Holding Bear"
        end

        local smartholdbear = GetSetting('smartholdbear', {})
        if Player:BuffUp(S.BearForm) then
            if smartholdbear['smartholdbear_fg'] then
                if S.FrenziedRegeneration:IsReady(Player) and Player:BuffDown(S.FrenziedRegenerationBuff) and Player:HealthPercentage() <= 80 then
                    if Cast(S.FrenziedRegeneration) then
                        return "Utilities: Frenzied Regeneration";
                    end
                end
                if S.Ironfur:IsReady(Player) and Player:BuffDown(S.IronfurBuff) and Player:HealthPercentage() <= 80 then
                    if Cast(S.Ironfur) then
                        return "Utilities: Ironfur";
                    end
                end
            end
             local Should = Player:ShouldUseDefensive()
            if not Player:PrevGCD(1, S.IncapacitatingRoar) then
                if smartholdbear['smartholdbear_uv'] and Player:BuffUp(S.UrsineVigorBuff) then
                    return
                elseif smartholdbear['smartholdbear_fg'] and Player:BuffUp(S.FrenziedRegenerationBuff) then
                    return
                elseif smartholdbear['smartholdbear_over'] and Should then
                    return
                end
            end
        end

        local mform = GetSetting('mform', {})
        if S.MoonkinForm:IsReady(Player) and Player:BuffDown(S.MoonkinForm) then
            -- With Fluid Form, don't manually cast Moonkin Form if we're in combat and would likely cast Wrath/Starfire next
            local shouldSkipMoonkinForm = false
            if S.FluidForm:IsAvailable() and Settings.inCombat and Settings.isValid and Target:IsSpellInRange(S.Wrath) then
                local enterLunar = S.LunarCalling:IsAvailable() or EnemiesCount10ySplash > 3 - num(S.UmbralIntensity:IsAvailable() or S.SouloftheForest:IsAvailable())
                
                -- Skip if we're about to cast a filler spell that will auto-shift us
                shouldSkipMoonkinForm = (enterLunar and S.Wrath:IsReady()) or 
                                      (not enterLunar and S.Starfire:IsReady()) or
                                      (S.LunarCalling:IsAvailable() and S.Starfire:IsReady())
            end
            
            if not shouldSkipMoonkinForm then
                -- Handle different form states
                if Player.DruidCanShapeShift then
                    if Cast(S.MoonkinForm) then
                        return "Utilities: Moonkin Form - After roots"
                    end
                elseif Player:BuffUp(S.CatForm) and Settings.inCombat then
                    if not mform['mform_cat'] then return end
                    if Cast(S.MoonkinForm) then
                        return "Utilities: Moonkin Form - from cat"
                    end
                elseif Player:BuffUp(S.BearForm) and Settings.inCombat then
                    if not mform['mform_bear'] then return end
                    if Cast(S.MoonkinForm) then
                        return "Utilities: Moonkin Form - from bear"
                    end
                elseif Player:CanAttack(Target) and Target:IsSpellInRange(S.Wrath) and mform['mform_valid'] then
                    -- Only cast if we're not in a form that's disabled by settings
                    local inDisabledForm = (Player:BuffUp(S.BearForm) and not mform['mform_bear']) or
                                         (Player:BuffUp(S.CatForm) and not mform['mform_cat'])
                    if not inDisabledForm then
                        if Cast(S.MoonkinForm) then
                            return "Utilities: Moonkin Form - target valid"
                        end
                    end
                end
            end
        end

        if Settings.inCombat then
            local autorebirth = GetSetting('autorebirth', {})
            if S.Rebirth:IsReady(MouseOver) and autorebirth['autorebirth_mouseover'] then
                local GetMouseFociCache = GetMouseFoci()
                ---@class Frame
                local MouseFocus = GetMouseFociCache[1]

                local FrameName = MouseFocus and not MouseFocus:IsForbidden() and MouseFocus:GetName() or "None"
                if FrameName ~= "WorldFrame" and FrameName ~= "None" then
                    if MouseOver:EvaluateRebirth() then
                        if Cast(S.Rebirth) then 
                            MainAddon.UI:ShowToast("Rebirth", MouseOver:Name(), MainAddon.GetTexture(S.Rebirth))
                            return "Rebirth MouseOver" 
                        end
                    end
                end
            end
            if S.Rebirth:IsReady() and autorebirth['autorebirth_target'] then
                if Target:EvaluateRebirth() then
                    if Cast(S.Rebirth) then 
                        MainAddon.UI:ShowToast("Rebirth", Target:Name(), MainAddon.GetTexture(S.Rebirth))
                        return "Rebirth Target" 
                    end
                end
            end

            if Settings.innervateMode == 'innervate_target' then
                StaticInnervateValue = Settings.MacroTarget
            elseif Settings.innervateMode == 'innervate_mouseover' then
                StaticInnervateValue = Settings.MacroMouseOver
            elseif Settings.innervateMode == 'innervate_focus' then
                StaticInnervateValue = Settings.MacroFocus
            else
                StaticInnervateValue = "None"
            end
            if Settings.InnervateValue == StaticInnervateValue then
                local innervate = GetSetting('innervate', 'innervate_none')
                local innervate_mana = GetSetting('innervate_mana', 50)
                if S.Innervate:IsReady(MouseOver) and innervate == 'innervate_mouseover' then
                    local GetMouseFociCache = GetMouseFoci()
                    ---@class Frame
                    local MouseFocus = GetMouseFociCache[1]

                    local FrameName = MouseFocus and not MouseFocus:IsForbidden() and MouseFocus:GetName() or "None"
                    if FrameName ~= "WorldFrame" and FrameName ~= "None" then
                        if MouseOver:IsAHealer() and not MouseOver:IsDeadOrGhost() and Player:IsFriend(MouseOver) and MouseOver:IsInPartyOrRaid()
                        and MouseOver:ManaPercentage() <= innervate_mana and MouseOver:PowerType() == 0 and MouseOver:BuffDown(S.Innervate, true) 
                        and (MouseOver:IsCasting() or MouseOver:IsChanneling()) then
                            if Cast(S.Innervate) then return "Innervate MouseOver" end
                        end
                    end
                end
                if S.Innervate:IsReady(Focus)  and innervate == 'innervate_focus' then
                    if Focus:IsAHealer() and not Focus:IsDeadOrGhost() and Player:IsFriend(Focus) and Focus:IsInPartyOrRaid()
                    and Focus:ManaPercentage() <= innervate_mana and Focus:PowerType() == 0 and Focus:BuffDown(S.Innervate, true) 
                    and (Focus:IsCasting() or Focus:IsChanneling()) then
                        if Cast(S.Innervate) then return "Innervate Focus" end
                    end
                end
                if S.Innervate:IsReady(Target) and innervate == 'innervate_target' then
                    if Target:IsAHealer() and not Target:IsDeadOrGhost() and Player:IsFriend(Target) and Target:IsInPartyOrRaid()
                    and Target:ManaPercentage() <= innervate_mana and Target:PowerType() == 0 and Target:BuffDown(S.Innervate, true) 
                    and (Target:IsCasting() or Target:IsChanneling()) then
                        if Cast(S.Innervate) then return "Innervate Target" end
                    end
                end
            end
        end

        local motw = GetSetting('motw', {})
        if S.MarkoftheWild:IsReady(Player) 
        and (motw['motw_self'] and Player:BuffDown(S.MarkoftheWild, true) 
        or motw['motw_friends'] and M.GroupBuffMissing(S.MarkoftheWild) and (Settings.inDungeon or Settings.inRaid)) then
            if Cast(S.MarkoftheWild) then return 'Utilities: Mark of the Wild' end
        end

        -- Symbiotic Relationship handling (tank priority)
        if S.SymbioticRelationship:IsReady(Player) and not Settings.inCombat then
            Settings.Tanks, Settings.Healers, Settings.Members, Settings.Damagers, Settings.Melees = HealingEngine:Fetch()
            if Settings.inDungeon and Settings.Tanks and #Settings.Tanks > 0 then
                -- In dungeon pick the tank
                local tank = Settings.Tanks[1]
                if tank
                    and tank:Exists()
                    and not tank:IsDeadOrGhost()
                    and tank:IsInRange(40)
                    and EvaluateSymbioticRelationship(tank)  -- needed to avoid double casts
                then
                    if CastAlly(S.SymbioticRelationship, tank) then
                        return 'Symbiotic Relationship (Dungeon)'
                    end
                end
        
            elseif Settings.inRaid and Settings.Tanks and #Settings.Tanks > 0 then
                -- In raids pick lowest hp tank
                local lowestTankHP, lowestTankUnit = HealingEngine:LowestHP(true, 40, Settings.Tanks, "Tank")
                if lowestTankUnit
                    and lowestTankUnit:Exists()
                    and not lowestTankUnit:IsDeadOrGhost()
                    and lowestTankUnit:IsInRange(40)
                    and EvaluateSymbioticRelationship(lowestTankUnit)  -- needed to avoid double casts
                then
                    if CastAlly(S.SymbioticRelationship, lowestTankUnit) then
                        return 'Symbiotic Relationship (Raid)'
                    end
                end
            end
        end

        if GetSetting('autotravel', false) and not Settings.inCombat and not Settings.isValid then
            if S.TravelForm:IsReady(Player) and Player:IsOutdoors() and not Player:BuffUp(S.TravelForm) and not Player:AnyStealthUp() and not Player:IsInInstancedPvP() and Player:IsMoving() then
                if Cast(S.TravelForm) then
                    return "Travel Form";
                end
            end
        end
    end

    local function Defensives()
        if Settings.inCombat then
            local DefensiveDown = Player:BuffDown(S.Barkskin) and Player:BuffDown(S.UrsineVigorBuff)
            if GetSetting('nvigil_check', false) then
                if S.NaturesVigil:IsReady(Player) and Player:BuffDown(S.NaturesVigil) then
                    if Player:HealthPercentage() <= GetSetting('nvigil_spin', 30) then
                        if Cast(S.NaturesVigil, true) then return "Defensive: Nature's Vigil Player" end
                    end
                    if GetSetting('nvigil_allies', false) and Player:IsInParty() then
                        if MainAddon.AllyLowestHP() <= GetSetting('nvigil_spin', 30) then
                            if Cast(S.NaturesVigil, true) then return "Defensive: Nature's Vigil Ally" end
                        end
                    end
                end
            end
            local Should, CastID = Player:ShouldUseDefensive()
            if Should then
                if Player:MythicDifficulty() >= GetSetting('smart_bear_above_key_level', 2) or not Settings.inDungeon then
                    if GetSetting('smartbear', false) and Player:BuffDown(S.BearForm) and DefensiveDown and S.UrsineVigor:IsAvailable() and S.BearForm:IsReady(Player) then
                        if Cast(S.BearForm) then 
                            MainAddon.UI:ShowToast("Defensive", MainAddon.GetSpellInfo(CastID), MainAddon.GetTexture(S.BearForm))
                            return 'Defensive: Smart Bear' 
                        end
                    end
                end
            end
            if GetSetting('barkskin_check', false) then
                if S.Barkskin:IsReady(Player) and DefensiveDown and Player:HealthPercentage() <= GetSetting('barkskin_spin', 30) then
                    if Cast(S.Barkskin, true) then return 'Defensive: Barkskin' end
                end
            end
            if GetSetting('renewal_check', false) then
                if S.Renewal:IsReady(Player) and Player:HealthPercentage() <= GetSetting('renewal_spin', 30) then
                    if Cast(S.Renewal, true) then return 'Defensive: Renewal' end
                end
            end
        end

        if Player:BuffDown(S.MoonkinForm) then return end

        if Player:IsInSoloMode() then
            if GetSetting('soloregrowth_check', false) then
                if S.Regrowth:IsReady(Player) and Player:HealthPercentage() <= GetSetting('soloregrowth_spin', 30) then
                    if CastAlly(S.Regrowth, Player) then return 'Defensive: regrowth solo' end
                end
            end
        else
            if GetSetting('regrowth_check', false) then
                if S.Regrowth:IsReady(Player) and Player:HealthPercentage() <= GetSetting('regrowth_spin', 30) then
                    if CastAlly(S.Regrowth, Player) then return 'Defensive: regrowth group' end
                end
            end
        end           
    end
    --- ===== End Custom =====

    --- ======= MAIN =======
    local function APL()
        -- Var update
        Settings.inCombat = Player:AffectingCombat()
        Settings.isValid = M.TargetIsValid()
        Settings.inDungeon = Player:IsInDungeonArea()
        Settings.inRaid = Player:IsInRaidArea()
        Settings.dotpack_check = GetSetting('dotpack_check', false)
        Settings.dotpack_spin = GetSetting('dotpack_spin', 60)
        Settings.magicgroundspell_ca = GetSetting('magicgroundspell_ca', false)
        Settings.magicgroundspell_fon = GetSetting('magicgroundspell_fon', false)

        -- Unit Update
        if AoEON() then
            Enemies40y = Player:GetEnemiesInRange(40)
            Enemies10ySplash = Player:GetEnemiesInSplashRange(10)
        else
            Enemies40y = {Target}
            Enemies10ySplash = {Target}
        end
        EnemiesCount10ySplash = #Enemies40y
        EnemiesCount40y = #Enemies40y

        if Settings.isValid or Settings.inCombat then
            -- Calculate fight_remains
            BossFightRemains = HL.BossFightRemains()
            FightRemains = BossFightRemains
            if FightRemains == 11111 then
                FightRemains = HL.FightRemains(Enemies40y, false)
            end

            -- Check CA/Incarnation Buff Status
            CAIncBuffUp = S.IncarnationTalent:IsAvailable() and (Player:BuffUp(S.IncarnationBuff1) or Player:BuffUp(S.IncarnationBuff2)) or (Player:BuffUp(S.CABuff1) or Player:BuffUp(S.CABuff2))
            CAIncBuffRemains = 0
            if CAIncBuffUp then
                CAIncBuffRemains = S.IncarnationTalent:IsAvailable() and mathmax(Player:BuffRemains(S.IncarnationBuff1), Player:BuffRemains(S.IncarnationBuff2)) or mathmax(Player:BuffRemains(S.CABuff1), Player:BuffRemains(S.CABuff2))
            end
        end

        if Player:IsChanneling() then
            return
        end

        -- Utilities
        local ShouldReturn = Utilities();
        if ShouldReturn then return ShouldReturn; end

        -- Defensives
        local ShouldReturn = Defensives();
        if ShouldReturn then return ShouldReturn; end

        -- With Fluid Form talent, Wrath and Starfire automatically shift into Moonkin Form
        -- so we can continue the rotation even if not in Moonkin Form
        if Player:BuffDown(S.MoonkinForm) and not S.FluidForm:IsAvailable() then
            return
        end

        if MainAddon.Toggle:GetToggle('SpreadMoonfire') then
            if Settings.isValid then
                if CastCycle(S.Moonfire, Enemies40y, EvaluateCycleMoonfireToggle) then 
                    return "Spread Moonfire" 
                end
            end

            if UnitWithMoonfire(Enemies40y) < EnemiesCount40y then
                return
            else
                MainAddon.Toggle:SetToggle('SpreadMoonfire', false)
            end
        end

        if Settings.isValid then
            -- Trinkets
            local shouldReturn = MainAddon.TrinketDPS()
            if shouldReturn then
                return shouldReturn
            end  

            -- Precombat
            if not Player:AffectingCombat() then
                local ShouldReturn = Precombat(); if ShouldReturn then return ShouldReturn; end
            end
            -- variable,name=passive_asp,value=6%spell_haste+talent.natures_balance+talent.orbit_breaker*dot.moonfire.ticking*(buff.orbit_breaker.stack>(27-2*buff.solstice.up))*24
            VarPassiveAsp = 6 / Player:SpellHaste() + num(S.NaturesBalance:IsAvailable()) + num(S.OrbitBreaker:IsAvailable()) * num(S.MoonfireDebuff:AuraActiveCount() > 0) * num(Druid.OrbitBreakerStacks > (27 - 2 * num(Player:BuffUp(S.SolsticeBuff)))) * 24
            -- variable,name=ca_effective_cd,value=cooldown.ca_inc.full_recharge_time<?cooldown.force_of_nature.remains
            VarCAEffectiveCD = mathmax(CAInc:FullRechargeTime(), S.ForceofNature:CooldownRemains())
            -- variable,name=pre_cd_condition,value=(!talent.whirling_stars|!talent.convoke_the_spirits|cooldown.convoke_the_spirits.remains<gcd.max*2|fight_remains<cooldown.convoke_the_spirits.remains+3|cooldown.convoke_the_spirits.remains>cooldown.ca_inc.full_recharge_time+15*talent.control_of_the_dream)&(variable.on_use_trinket=0|(variable.on_use_trinket=1|variable.on_use_trinket=3)&(trinket.1.cooldown.remains>cooldown.ca_inc.full_recharge_time+(15*talent.control_of_the_dream)|!talent.convoke_the_spirits&hero_tree.elunes_chosen&trinket.1.cooldown.remains>cooldown.ca_inc.full_recharge_time-cooldown.ca_inc.duration|talent.convoke_the_spirits&(cooldown.convoke_the_spirits.remains<3&(ceil((fight_remains-10)%cooldown.convoke_the_spirits.duration)>ceil((fight_remains-trinket.1.cooldown.remains-10)%cooldown.convoke_the_spirits.duration))|cooldown.convoke_the_spirits.remains>trinket.1.cooldown.remains&cooldown.ca_inc.full_recharge_time-cooldown.ca_inc.duration<trinket.1.cooldown.remains+15)|trinket.1.cooldown.remains+6>fight_remains|trinket.1.cooldown.ready)|variable.on_use_trinket=2&(trinket.2.cooldown.remains>cooldown.ca_inc.full_recharge_time+(15*talent.control_of_the_dream)|!talent.convoke_the_spirits&hero_tree.elunes_chosen&trinket.1.cooldown.remains>cooldown.ca_inc.full_recharge_time-cooldown.ca_inc.duration|talent.convoke_the_spirits&(cooldown.convoke_the_spirits.remains<3&(ceil((fight_remains-10)%cooldown.convoke_the_spirits.duration)>ceil((fight_remains-trinket.2.cooldown.remains-10)%cooldown.convoke_the_spirits.duration))|cooldown.convoke_the_spirits.remains>trinket.2.cooldown.remains&cooldown.ca_inc.full_recharge_time-cooldown.ca_inc.duration<trinket.2.cooldown.remains+15)|trinket.2.cooldown.remains+6>fight_remains|trinket.2.cooldown.ready))&cooldown.ca_inc.remains<gcd.max&!buff.ca_inc.up
            VarPreCDCondition = (not S.WhirlingStars:IsAvailable() or not S.ConvoketheSpirits:IsAvailable() or S.ConvoketheSpirits:CooldownRemains() < Player:GCD() * 2 or BossFightRemains < S.ConvoketheSpirits:CooldownRemains() + 3 or S.ConvoketheSpirits:CooldownRemains() > CAInc:FullRechargeTime() + 15 * num(S.ControloftheDream:IsAvailable())) and (VarOnUseTrinket == 0 or (VarOnUseTrinket == 1 or VarOnUseTrinket == 3) and (Trinket1:CooldownRemains() > CAInc:FullRechargeTime() + (15 * num(S.ControloftheDream:IsAvailable())) or not S.ConvoketheSpirits:IsAvailable() and Player:HeroTreeID() == 24 and Trinket1:CooldownRemains() > CAInc:FullRechargeTime() - CAIncCD or S.ConvoketheSpirits:IsAvailable() and (S.ConvoketheSpirits:CooldownRemains() < 3 and (mathceil((BossFightRemains - 10) / ConvokeCD) > mathceil((BossFightRemains - Trinket1:CooldownRemains() - 10) / ConvokeCD)) or S.ConvoketheSpirits:CooldownRemains() > Trinket1:CooldownRemains() and CAInc:FullRechargeTime() - CAIncCD < Trinket1:CooldownRemains() + 15) or Trinket1:CooldownRemains() + 6 > BossFightRemains or Trinket1:CooldownUp()) or VarOnUseTrinket == 2 and (Trinket2:CooldownRemains() > CAInc:FullRechargeTime() + (15 * num(S.ControloftheDream:IsAvailable())) or not S.ConvoketheSpirits:IsAvailable() and Player:HeroTreeID() == 24 and Trinket1:CooldownRemains() > CAInc:FullRechargeTime() - CAIncCD or S.ConvoketheSpirits:IsAvailable() and (S.ConvoketheSpirits:CooldownRemains() < 3 and (mathceil((BossFightRemains - 10) / ConvokeCD) > mathceil((BossFightRemains - Trinket2:CooldownRemains() - 10) / ConvokeCD)) or S.ConvoketheSpirits:CooldownRemains() > Trinket2:CooldownRemains() and CAInc:FullRechargeTime() - CAIncCD < Trinket2:CooldownRemains() + 15) or Trinket2:CooldownRemains() + 6 > BossFightRemains or Trinket2:CooldownUp())) and CAInc:CooldownRemains() < Player:GCD() and not CAIncBuffUp
            -- variable,name=cd_condition,value=variable.pre_cd_condition&(fight_remains<(15+5*talent.incarnation_chosen_of_elune)*(1-talent.whirling_stars*0.2)|target.time_to_die>10&(!hero_tree.keeper_of_the_grove|buff.harmony_of_the_grove.up))
            local dottedEnemiesPercentage, dottedEnemies = PercentageWithDots()
            VarCDCondition = VarPreCDCondition and (BossFightRemains < (15 + 5 * num(S.Incarnation:IsAvailable())) * (1 - num(S.WhirlingStars:IsAvailable()) * 0.2) or Target:TimeToDie() > 10 and (Player:HeroTreeID() ~= 23 or Player:BuffUp(S.HarmonyoftheGroveBuff)))
            and (dottedEnemies >= 6 or not Settings.dotpack_check or dottedEnemiesPercentage >= Settings.dotpack_spin)
            -- variable,name=convoke_condition,value=fight_remains<5|(buff.ca_inc.up|cooldown.ca_inc.remains>40)&(!hero_tree.keeper_of_the_grove|buff.harmony_of_the_grove.up|cooldown.force_of_nature.remains>15)
            VarConvokeCondition = (BossFightRemains < 5 or (CAIncBuffUp or CAInc:CooldownRemains() > 40) and (Player:HeroTreeID() ~= 23 or Player:BuffUp(S.HarmonyoftheGroveBuff) or S.ForceofNature:CooldownRemains() > 15))
            -- variable,name=eclipse,value=buff.eclipse_lunar.up|buff.eclipse_solar.up
            VarEclipse = Player:BuffUp(S.EclipseLunar) or Player:BuffUp(S.EclipseSolar)
            -- variable,name=eclipse_remains,value=buff.eclipse_lunar.remains<?buff.eclipse_solar.remains
            VarEclipseRemains = mathmax(Player:BuffRemains(S.EclipseLunar), Player:BuffRemains(S.EclipseSolar))
            -- variable,name=enter_lunar,value=talent.lunar_calling|spell_targets.starfire>3-(talent.umbral_intensity|talent.soul_of_the_forest)
            VarEnterLunar = S.LunarCalling:IsAvailable() or EnemiesCount10ySplash > 3 - num(S.UmbralIntensity:IsAvailable() or S.SouloftheForest:IsAvailable())
            -- variable,name=boat_stacks,value=buff.balance_of_all_things_arcane.stack+buff.balance_of_all_things_nature.stack
            VarBoatStacks = Player:BuffStack(S.BOATArcaneBuff) + Player:BuffStack(S.BOATNatureBuff)
            -- variable,name=no_cd_talent,value=!talent.celestial_alignment&!talent.incarnation_chosen_of_elune|druid.no_cds
            VarNoCDTalent = not S.CelestialAlignment:IsAvailable() and not S.Incarnation:IsAvailable()
            -- do_treacherous_transmitter_task,if=cooldown.ca_inc.remains>10|buff.ca_inc.up
            -- TODO
            -- use_item,name=spymasters_web,if=fight_remains<20
            if I.SpymastersWeb:IsEquippedAndReady() and (BossFightRemains < 20) then
                if Cast(I.SpymastersWeb) then return "spymasters_web main 2"; end
            end
            -- use_item,name=imperfect_ascendancy_serum,if=dot.sunfire.remains>4&(dot.moonfire.remains>4|talent.treants_of_the_moon&(cooldown.force_of_nature.remains<3|buff.harmony_of_the_grove.up)&variable.ca_effective_cd<1|fight_remains<20|fight_remains<variable.ca_effective_cd&(buff.harmony_of_the_grove.up|cooldown.convoke_the_spirits.ready))&buff.spymasters_report.stack<=29
            if I.ImperfectAscendancySerum:IsEquippedAndReady() and (Target:DebuffRemains(S.SunfireDebuff) > 4 and (Target:DebuffRemains(S.MoonfireDebuff) > 4 or S.TreantsoftheMoon:IsAvailable() and (S.ForceofNature:CooldownRemains() < 3 or Player:BuffUp(S.HarmonyoftheGroveBuff)) and VarCAEffectiveCD < 1 or BossFightRemains < 20 or BossFightRemains < VarCAEffectiveCD and (Player:BuffUp(S.HarmonyoftheGroveBuff) or S.ConvoketheSpirits:CooldownUp())) and Player:BuffStack(S.SpymastersReportBuff) <= 29) then
                if Cast(I.ImperfectAscendancySerum) then return "imperfect_ascendancy_serum main 4"; end
            end
            -- use_item,name=treacherous_transmitter,if=((cooldown.force_of_nature.remains<3&(trinket.1.is.spymasters_web|trinket.2.is.spymasters_web)&buff.spymasters_report.stack>=29)|(cooldown.convoke_the_spirits.remains<2&cooldown.ca_inc.ready&cooldown.force_of_nature.remains<3&buff.spymasters_report.stack<=29|fight_remains<20|fight_remains<variable.ca_effective_cd&(buff.harmony_of_the_grove.up|cooldown.convoke_the_spirits.ready)))
            if I.TreacherousTransmitter:IsEquippedAndReady() and ((S.ForceofNature:CooldownRemains() < 3 and (VarTrinket1ID == I.SpymastersWeb:ID() or VarTrinket2ID == I.SpymastersWeb:ID()) and Player:BuffStack(S.SpymastersReportBuff) >= 29) or (S.ConvoketheSpirits:CooldownRemains() < 2 and CAInc:CooldownUp() and S.ForceofNature:CooldownRemains() < 3 and Player:BuffStack(S.SpymastersReportBuff) <= 29 or BossFightRemains < 20 or BossFightRemains < VarCAEffectiveCD and (Player:BuffUp(S.HarmonyoftheGroveBuff) or S.ConvoketheSpirits:CooldownUp()))) then
                if Cast(I.TreacherousTransmitter) then return "treacherous_transmitter main 6"; end
            end
            -- use_item,slot=trinket1,if=!trinket.1.is.spymasters_web&!trinket.1.is.imperfect_ascendancy_serum&!trinket.1.is.treacherous_transmitter&variable.on_use_trinket=1&variable.cd_condition
            if Trinket1 and Trinket1:IsReady() and not VarTrinket1Ex and not Player:IsItemBlacklisted(Trinket1) and (VarTrinket1ID ~= I.SpymastersWeb:ID() and VarTrinket1ID ~= I.ImperfectAscendancySerum:ID() and VarTrinket1ID ~= I.TreacherousTransmitter:ID() and VarOnUseTrinket == 1 and VarCDCondition) then
                if Cast(Trinket1) then return "use_items trinket1 ("..Trinket1:Name()..") main 8"; end
            end
            -- use_item,slot=trinket2,if=!trinket.2.is.spymasters_web&!trinket.2.is.imperfect_ascendancy_serum&!trinket.2.is.treacherous_transmitter&variable.on_use_trinket=2&variable.cd_condition
            if Trinket2 and Trinket2:IsReady() and not VarTrinket2Ex and not Player:IsItemBlacklisted(Trinket2) and (VarTrinket2ID ~= I.SpymastersWeb:ID() and VarTrinket2ID ~= I.ImperfectAscendancySerum:ID() and VarTrinket2ID ~= I.TreacherousTransmitter:ID() and VarOnUseTrinket == 2 and VarCDCondition) then
                if Cast(Trinket2) then return "use_items trinket2 ("..Trinket2:Name()..") main 10"; end
            end
            -- use_item,name=bestinslots,if=hero_tree.keeper_of_the_grove&buff.harmony_of_the_grove.up|hero_tree.elunes_chosen&(cooldown.ca_inc.full_recharge_time>20|buff.ca_inc.up)
            if I.BestinSlotsCaster:IsEquippedAndReady() and (Player:HeroTreeID() == 23 and Player:BuffUp(S.HarmonyoftheGroveBuff) or Player:HeroTreeID() == 24 and (CAInc:FullRechargeTime() > 20 or CAIncBuffUp)) then
                if Cast(I.BestinSlotsCaster) then return "bestinslots main 12"; end
            end
            if I.NeuralSynapseEnhancer:IsEquippedAndReady() and (
                -- use_item,name=neural_synapse_enhancer,if=variable.on_use_trinket=1&!trinket.1.cooldown.ready&(buff.harmony_of_the_grove.up|hero_tree.elunes_chosen)|!trinket.1.has_use_buff&(buff.harmony_of_the_grove.up|hero_tree.elunes_chosen)
                (VarOnUseTrinket == 1 and not Trinket1:IsReady() and (Player:BuffUp(S.HarmonyoftheGroveBuff) or Player:HeroTreeID() == 24) or not Trinket1:HasUseBuff() and (Player:BuffUp(S.HarmonyoftheGroveBuff) or Player:HeroTreeID() == 24)) or
                -- use_item,name=neural_synapse_enhancer,if=variable.on_use_trinket=2&!trinket.2.cooldown.ready&(buff.harmony_of_the_grove.up|hero_tree.elunes_chosen)|!trinket.2.has_use_buff&(buff.harmony_of_the_grove.up|hero_tree.elunes_chosen)
                (VarOnUseTrinket == 2 and not Trinket2:IsReady() and (Player:BuffUp(S.HarmonyoftheGroveBuff) or Player:HeroTreeID() == 24) or not Trinket2:HasUseBuff() and (Player:BuffUp(S.HarmonyoftheGroveBuff) or Player:HeroTreeID() == 24))
            ) then
                if Cast(I.NeuralSynapseEnhancer) then return "neural_synapse_enhancer main 14"; end
            end
            -- use_items
            local ItemToUse = Player:GetUseableItems(OnUseExcludes)
            if ItemToUse then
                if Cast(ItemToUse) then return "use_items ("..ItemToUse:Name()..") main 14"; end
            end
            if Target:IsSpellInRange(S.Wrath) then
                -- Burst Potion
                if MainAddon.UsePotion() then
                    MainAddon.SetTopColor(1, "Combat Potion")
                end
            end
            -- berserking,if=variable.no_cd_talent|fight_remains<15
            if S.Berserking:IsReady() and (VarNoCDTalent or BossFightRemains < 15) then
                if Cast(S.Berserking) then return "berserking main 16"; end
            end
            -- call_action_list,name=aoe,if=spell_targets>1
            if AoEON() and EnemiesCount10ySplash > 1 then
                local ShouldReturn = AoE(); if ShouldReturn then return ShouldReturn; end
            end
            -- call_action_list,name=st
            if EnemiesCount10ySplash == 1 then
                local ShouldReturn = ST(); if ShouldReturn then return ShouldReturn; end
            end
            -- Manually added: Pool, if nothing else to do.
            local ShouldReturn = MovementDoTs()
            if ShouldReturn then
                return ShouldReturn
            end
            -- Manually added: Pool, if nothing else to do.
            if Cast(S.Pool) then return "Pool Resources"; end
        end
    end

    local function Init()
        S.MoonfireDebuff:RegisterAuraTracking()
    end
    M.SetAPL(102, APL, Init)

    local BalOldSpellIsReady
    BalOldSpellIsReady = HL.AddCoreOverride ("Spell.IsReady",
        function (self, Range, AoESpell, ThisUnit, BypassRecovery, Offset)
            local BaseCheck, Reason = BalOldSpellIsReady(self, Range, AoESpell, ThisUnit, BypassRecovery, Offset)
            local incCD  = S.Incarnation:CooldownRemains()
            local celCD = S.CelestialAlignment:CooldownRemains()
            local woeCD = S.WarriorofElune:CooldownRemains()
            local fonCD = S.ForceofNature:CooldownRemains()
            local foeCD = S.FuryofElune:CooldownRemains()

            --- Fury of Elune sync start
            if self == S.FuryofElune
            then
                -- hold FOE if Incarnation is ready or comes up in ≤12 s
                if incCD > 0 and incCD <= 12 then
                    return false, "Sync: wait for Incarnation"
                end
                -- hold FOE if Celestial Alignment is ready or comes up in ≤12 s
                if celCD > 0 and celCD <= 12 then
                    return false, "Sync: wait for Celestial Alignment"
                end
                -- hold FOE if Celestial Alignment is ready or comes up in ≤12 s
                if celCD > 0 and celCD <= 12 then
                    return false, "Sync: wait for Celestial Alignment"
                end
                -- hold FOE if Warrior of Elune is ready or comes up in ≤4 s
                if woeCD > 0 and woeCD <= 4  then
                    return false, "Sync: wait for Warrior of Elune"
                end
                -- hold FOE if Force of Nature is ready or comes up in ≤4 s
                if fonCD > 0 and fonCD <= 4 then
                    return false, "Sync: wait for Force of Nature"
                end
            end
            --- Fury of Elune sync end

            --- Celestial Alignment/Incarnation sync start
            if self == S.CelestialAlignment or self == S.Incarnation
            then
                -- hold CA/Inc if FOE is ready or comes up in ≤10 s
                if foeCD > 0 and foeCD <= 10 then
                    return false, "Sync: wait for Fury of Elune"
                end
            end
            --- Celestial Alignment/Incarnation sync end

            return BaseCheck, Reason
        end
    , 102)

    HL.AddCoreOverride ("Player.AstralPowerP",
        function ()
            local AP = Player:AstralPower()
            if not Player:IsCasting() then
                if IsCurrentSpell(93402) then
                    return AP + 8
                elseif IsCurrentSpell(8921) then
                    return AP + 6
                end
                return AP
            else
                if Player:IsCasting(S.Wrath) then
                    if S.SouloftheForest:IsAvailable() and (Player:BuffUp(S.EclipseSolar) and Player:BuffDown(S.EclipseLunar)) then
                        return AP + 12
                    else
                        return AP + 10
                    end
                elseif Player:IsCasting(S.Starfire) then
                    return AP + 12
                elseif Player:IsCasting(S.StellarFlare) then
                    return AP + 12
                elseif Player:IsCasting(S.NewMoon) then
                    return AP + 12
                elseif Player:IsCasting(S.HalfMoon) then
                    return AP + 24
                elseif Player:IsCasting(S.FullMoon) then
                    return AP + 50
                else
                    return AP
                end
            end
        end
    , 102)

    -- AstralPowerDeficit
    HL.AddCoreOverride ("Player.AstralPowerDeficit",
        function ()
            local AP = Player:AstralPowerP()
            if not Player:IsCasting() then
                if IsCurrentSpell(93402) then
                    return 100 - AP
                elseif IsCurrentSpell(8921) then
                    return 100 - AP
                end
                return 100 - AP
            else
                if Player:IsCasting(S.Wrath) then
                    return 100 - AP
                elseif Player:IsCasting(S.Starfire) then
                    return 100 - AP
                elseif Player:IsCasting(S.StellarFlare) then
                    return 100 - AP
                elseif Player:IsCasting(S.NewMoon) then
                    return 100 - AP
                elseif Player:IsCasting(S.HalfMoon) then
                    return 100 - AP
                elseif Player:IsCasting(S.FullMoon) then
                    return 100 - AP
                else
                    return 100 - AP
                end
            end
        end
    , 102)

    HL.AddCoreOverride ("Spell.EnergizeAmount",
  function(self)
        local Amount = 0
        if self == S.StellarFlare then
        Amount = 12
        elseif self == S.AstralCommunion then
        Amount = 60
        elseif self == S.ForceofNature then
        Amount = 20
        elseif self == S.Sunfire then
        Amount = 8
        elseif self == S.Moonfire then
        Amount = 6
        elseif self == S.NewMoon then
        Amount = 12
        elseif self == S.HalfMoon then
        Amount = 24
        elseif self == S.FullMoon then
        Amount = 50
        end
        return Amount
    end
    , 102)

    local OldPlayerAffectingCombat
    OldPlayerAffectingCombat =
    HL.AddCoreOverride("Player.AffectingCombat",
        function(self)
            if MainAddon.PlayerSpecID() == 102 then
                return Player:IsCasting(S.Wrath) or Player:IsCasting(S.Starfire) or Player:IsCasting(S.StellarFlare) 
                or S.Wrath:TimeSinceLastCast() < 1.5 or S.Starfire:TimeSinceLastCast() < 1.5
                or OldPlayerAffectingCombat(self)
            end
            return OldPlayerAffectingCombat(self)
        end,
    102)

    local BalOldIsUsable
    BalOldIsUsable = HL.AddCoreOverride ("Spell.IsUsable",
        function (self)
            if MainAddon.PlayerSpecID() == 102 then
                if self == S.Starfall and Player:AstralPowerP() > 45 then
                    return true, "Prediction"
                end
            end
            return BalOldIsUsable(self)
        end
    , 102)

    local BalOldSpellIsCastable
    BalOldSpellIsCastable = HL.AddCoreOverride("Spell.IsCastable",
        function(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
            local BaseCheck, Reason = BalOldSpellIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
            if MainAddon.PlayerSpecID() == 102 then
                local DPSMovingValue_check = GetSetting('DPSMovingValue_check', false)
                local DPSMovingValue_spin = GetSetting('DPSMovingValue_spin', 1.5) or 1.5
                local standStillThreshold = nil
                if DPSMovingValue_check then
                    standStillThreshold = DPSMovingValue_spin
                end

                if MainAddon.Toggle:GetToggle('SaveAP') then
                    -- Always hold cooldowns when toggle is enabled
                    if self == S.CelestialAlignment then
                        return false, "Toggle Save AP"
                    end
                    if self == S.Incarnation then
                        return false, "Toggle Save AP"
                    end
                    if self == S.ConvoketheSpirits then
                        return false, "Toggle Save AP"
                    end
                    if self == S.Starfall then
                        if Player:BuffDown(S.TouchtheCosmosBuff) then
                            return false, "Toggle Save AP"
                        end
                    end
                    if self == S.Starsurge then
                        if Player:BuffDown(S.TouchtheCosmosBuff) then
                            return false, "Toggle Save AP"
                        end
                    end
                    if self == S.FuryofElune then
                        return false, "Toggle Save AP"
                    end
                end

                -- Don't cast CA or Incarnation if it's already up
                if self == S.CelestialAlignment or self == S.Incarnation then
                    if Player:BuffUp(CAInc) then
                        return false, 'Buff already up'
                    end
                end

                -- Astral Power Pooling
                if self == S.CelestialAlignment or self == S.Incarnation or self == S.ConvoketheSpirits or self == S.FuryofElune then
                    if not ShouldCastAPSpenders() then
                        return false, "Pooling for next pull"
                    end
                end
                if (self == S.Starfall or self == S.Starsurge) and Player:BuffDown(S.TouchtheCosmosBuff) then
                    if not ShouldCastAPSpenders() then
                        return false, "Pooling for next pull"
                    end
                end
    
                if self == S.CelestialAlignment or self == S.Incarnation or self == S.ForceofNature or self == S.FuryofElune or self == S.ConvoketheSpirits then
                    if standStillThreshold and Player:IsStandingStillFor() <= standStillThreshold then
                        return false, 'Is Not Standing Still'
                    end
                end                
    
                if self == S.MarkoftheWild and Player:StealthUp(true, true) then
                    return false, "Stealth"
                end

                if self == S.MoonkinForm or self == S.BearForm then
                    return BaseCheck and Player:BuffDown(self), Reason
                elseif self == S.StellarFlare then
                    return BaseCheck and not Player:IsCasting(self), Reason
                elseif self == S.Wrath or self == S.Starfire then
                    -- With Fluid Form, Wrath and Starfire don't require being in Moonkin Form
                    if S.FluidForm:IsAvailable() then
                        return BaseCheck and not (Player:IsCasting(self) and self:Count() == 1), Reason
                    elseif Player:BuffDown(S.MoonkinForm) then
                        return false, "Requires Moonkin Form"
                    else
                        return BaseCheck and not (Player:IsCasting(self) and self:Count() == 1), Reason
                    end
                elseif self == S.WarriorofElune then
                    return BaseCheck and Player:BuffDown(self), Reason
                elseif self == S.NewMoon or self == S.HalfMoon or self == S.FullMoon then
                    return BaseCheck and not Player:IsCasting(self), Reason
                -- Other spells require Moonkin Form
                elseif Player:BuffDown(S.MoonkinForm) then
                    return false, "Requires Moonkin Form"
                else
                    return BaseCheck, Reason
                end
            end
            return BaseCheck, Reason
        end,
    102)
    
    local OldCooldownRemains
    OldCooldownRemains = HL.AddCoreOverride("Spell.CooldownRemains",
         function(self, BypassRecovery, BypassCD)
              local BaseCheck = OldCooldownRemains(self, BypassRecovery, BypassCD)
              if MainAddon.PlayerSpecID() == 102 then
                   if self == S.CatForm then
                        if not S.CatForm:IsBlocked() then
                             return 0
                        end
                   end

                   if self == S.BearForm then
                        if not S.BearForm:IsBlocked() then
                             return 0
                        end
                   end

                   if self == S.MoonkinForm then
                        if not S.MoonkinForm:IsBlocked() then
                             return 0
                        end
                   end
              end
              return BaseCheck
         end
    , 102)

    -- Orbit Breaker Tracking
    HL:RegisterForSelfCombatEvent(function(dmgTime, _, _, _, _, _, _, _, _, _, _, spellID)
        if spellID == 202497 then
            Druid.OrbitBreakerStacks = Druid.OrbitBreakerStacks + 1
        end
        if spellID == 274283 then
            if (not S.NewMoon:IsAvailable()) or (S.NewMoon:IsAvailable() and (Druid.FullMoonLastCast == nil or dmgTime - Druid.FullMoonLastCast > 1.5)) then
                Druid.OrbitBreakerStacks = 0
            end
        end
    end, "SPELL_DAMAGE")
    
    HL:RegisterForSelfCombatEvent(function(castTime, _, _, _, _, _, _, _, _, _, _, spellID)
        if spellID == 274283 then
            Druid.FullMoonLastCast = castTime
        end
    end, "SPELL_CAST_SUCCESS") 

    HL:RegisterForEvent(function(arg1, arg2, arg3, arg4, arg5)
        if arg2 == "player" then
            -- Symbiotic Relationship blacklist to prevent spam
            if arg5 == 474750 then
                Settings.TempBlackListSymbioticRelationship[arg3] = true
                C_Timer.After(S.SymbioticRelationship:CastTime() + (HL.Latency()*7), function()
                    Settings.TempBlackListSymbioticRelationship[arg3] = nil
                end)
            end
        end
    end, "UNIT_SPELLCAST_SENT")
end