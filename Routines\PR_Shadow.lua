function A_258()
    -- HR: fix(Shadow): Fix Void Blast range check (10/12/24)
    -- REMEMBER: TempBlacklistVT
    -- REMEMBER: GetSetting('dphighprio', true)
    -- HEAD
    ---@class MainAddon
    local MainAddon = MainAddon
    ---@class MainAddon
    local M = MainAddon
    -- HeroLib
    local HL = HeroLibEx
    ---@class Unit
    local Unit = HL.Unit
    ---@class Unit
    local Player = Unit.Player
    ---@class Unit
    local Target = Unit.Target
    ---@class Unit
    local Pet = Unit.Pet
    ---@class Unit
    local MouseOver = Unit.MouseOver
    ---@class Spell
    local Spell = HL.Spell
    ---@class Item
    local Item = HL.Item
    -- HeroRotation
    local AoEON = M.AoEON
    local CacheCast = M.Cast
    local CastCycle = M.CastCycle
    local CastTargetIf = M.CastTargetIf
    local CastCycleAlly = M.CastCycleAlly
    local CastAlly = M.CastAlly
    ---@class HealingEngine
    local HealingEngine = M.HealingEngine
    ---@class Priest
    local Priest = M.Priest
    -- lua
    local GetTime = _G['GetTime']
    local UnitPower = _G['UnitPower']
    local mathmin = math.min
    local mathmax = math.max
    local CombatLogGetCurrentEventInfo = _G['CombatLogGetCurrentEventInfo']
    local wipe = _G['wipe']
    local GetMouseFoci = _G['GetMouseFoci']
    local IsInGroup = _G['IsInGroup']
    local C_Timer = _G['C_Timer']
    local UnitInRaid = _G['UnitInRaid']
    local UnitInParty = _G['UnitInParty']
    local num = M.num

    local S = Spell.Priest.Shadow
    local I = Item.Priest.Shadow

    MainAddon.Toggle.Special["SpreadVT"] = {
        Icon = MainAddon.GetTexture(S.VampiricTouch),
        Name = "Spread Vampiric Touch",
        Description = "This toggle will force spread Vampiric Touch.",
        Spec = 258,
    }

    MainAddon.Toggle.Special["SpreadVTNeverEnding"] = {
        Icon = MainAddon.GetTexture(S.VampiricTouch),
        Name = "Spread Vampiric Touch",
        Description = "This toggle will force spread Vampiric Touch and will not turn off by itself.",
        Spec = 258,
    }

    -- Create table to exclude above trinkets from On Use function
    local OnUseExcludes = {
        -- TWW Trinkets
        I.AberrantSpellforge:ID(),
        I.FlarendosPilotLight:ID(),
        I.GeargrindersSpareKeys:ID(),
        I.NeuralSynapseEnhancer:ID(),
        I.SpymastersWeb:ID(),
        -- TWW Other Items
        I.HyperthreadWristwraps:ID(),
    }

    local GetSetting = MainAddon.Config.GetClassSetting
    local Config_Key = MainAddon.GetClassVariableName()
    local Config_Color = 'FFFFFF'
    local Config_Table = {
        key = Config_Key,
        title = 'Priest - Shadow',
        subtitle = '?? ' .. MainAddon.Version,
        width = 600,
        height = 700,
        profiles = true,
        config = {
            { type = 'header', text = '', size = 24, align = 'Center', color = Config_Color },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
            { type = 'header', text = 'DPS', color = Config_Color },
            { type = 'dropdown',
                text = "Re-Target Devouring Plague unit for Void Torrent ", key = 'retarget_DP',
                list = {
                { text = 'MouseOver', key = 1 },
                { text = 'Auto', key = 2 },
                { text = 'None', key = 3 },
                },
                default = 3,
            },
            { type = 'checkbox', text = ' Auto-Opener sequence', default = true, key = 'opener_seq' },
            { type = 'spinner', text = ' Minimum enemy Health for using Vampiric Touch (million value)', key = 'vt_min_hp', icon = S.VampiricTouch:ID(), min = 0.1, max = 50, default = 1},
            { type = 'spinner', text = ' Minimum enemy TTD for using Vampiric Touch (sec value)', key = 'vt_min_ttd', icon = S.VampiricTouch:ID(), min = 1, max = 30, default = 18},
            { type = 'checkbox', text = " Shadow Crash - Target speed check", icon = S.ShadowCrash:ID(), key = 'sc_speed_check', default = false },
            { type = 'spacer' },
            { type = 'header', text = 'Defensives', color = Config_Color },
            { type = 'checkspin', text = ' Dispersion', icon = S.Dispersion:ID(), key = 'dispersion_solo', min = 1, max = 100, default_spin = 20, default_check = true },
            { type = 'checkspin', text = ' Dispersion (Raid/Party)', icon = S.Dispersion:ID(), key = 'dispersion_grp', min = 1, max = 100, default_spin = 15, default_check = true },
            { type = 'spacer' },
            { type = 'checkspin', text = ' Desperate Prayer', icon = S.DesperatePrayer:ID(), key = 'dprayer_solo', min = 1, max = 100, default_spin = 40, default_check = true },
            { type = 'checkspin', text = ' Desperate Prayer (Raid/Party)', icon = S.DesperatePrayer:ID(), key = 'dprayer_grp', min = 1, max = 100, default_spin = 30, default_check = true },
            { type = 'spacer' },
            { type = 'checkspin', text = ' Power Word: Shield', icon = S.PowerWordShield:ID(), key = 'pws_solo', min = 1, max = 100, default_spin = 70, default_check = true },
            { type = 'checkspin', text = ' Power Word: Shield (Raid/Party)', icon = S.PowerWordShield:ID(), key = 'pws_grp', min = 1, max = 100, default_spin = 35, default_check = true },
            { type = 'spacer' },
            { type = 'checkspin', text = ' Flash Heal', icon = S.FlashHeal:ID(), key = 'flash_solo', min = 1, max = 100, default_spin = 35, default_check = true },
            { type = 'checkspin', text = ' Flash Heal (Raid/Party)', icon = S.FlashHeal:ID(), key = 'flash_grp', min = 1, max = 100, default_spin = 10, default_check = true },
            { type = 'spacer' },
            { type = 'checkspin', text = ' Vampiric Embrace', icon = S.VampiricEmbrace:ID(), key = 'VE', min = 1, max = 100, default_spin = 40, default_check = true },
            { type = 'checkbox', text = " Vampiric Embrace: also check party's allies health", icon = S.VampiricEmbrace:ID(), key = 'VE_allies', default = true },
            { type = 'spacer' },
            { type = 'header', text = 'Utilities', color = Config_Color },
            { type = 'dropdown',
              text = ' Shadowform', key = 'sform',
              icon = S.Shadowform:ID(),
              multiselect = true,
              list = {
                  { text = 'Target is Valid', key = 'sform_valid' },
                  { text = 'In Combat', key = 'sform_combat' },
                  { text = 'Out of Combat', key = 'sform_ooc' },
              },
              default = {
                  'sform_valid',
                  'sform_combat'
              },
            },
            { type = 'spacer' },
            { type = 'checkspin', text = ' Angelic Feather movement threshold', icon = S.AngelicFeather:ID(), key = 'angelic', min = 0, max = 15, default_spin = 2, default_check = true },
            { type = 'checkspin', text = ' Body and Soul movement threshold', icon = S.BodyAndSoul:ID(), key = 'bsoul', min = 0, max = 15, default_spin = 2, default_check = true },
            { type = 'spacer' },
            {
                type = "checkbox",
                text = "Cast Groundspell only when MouseOver enemies or tank.",
                key = "MOOption",
                default = false
            },
            { type = 'spacer' },
            { type = 'header', text = 'WeakAuras', color = Config_Color },
            { type = 'button', text = 'Import Enemies Count', width = 150, callback = function()
                if not _G['WeakAuras'] then
                    return false
                end
                _G['WeakAuras'].Import("!WA:2!9zr3UnXr3AIquzvHstHieLkzClOevrusqLwXh5c7WgcTMKO12nqBPRNDNJ9oK1ZmAMztI5oSQQ6v9I8iy9176v(rGl(UEv03tax0haEc6zM1jXbSXx45mN)2Z)NPWgZ1Do6C0)4rdyrcEDrQkcUXhDmj1eluBlnmbxxu1B72T1GPWqcpcXVJGXnHR7TvdpFzjktlti9AahAcAluDjMazGH1fgDRGIe50Z981gIYuSkJZmfdBJh64IYYtqbRS05vXB3dazfTeIm(euzXYBmbPIwzeuvUGddOGMysvedexTTGBg(iwIs0RuvrcTAIGq)FVjkHO1beonWQ7BhQz8ojWxnRF30edB()7STcnKeGBMp7QfLZLQHGZlsRQA2lbl1JoLylhih2huXoqscBFOvFl4izSFSSRCruSboXeDdjMw(oXrcQDR4xB71)HSRoF4MnTH5wQxIU0JP6lCSoMqfh808uYmduqhm42ONeQAWyXucNMZfoR(sHOBHbgreAJAu8Rv43)lFsImMmZQPm6WvLQvFg9U9s2R3G2Sdb6UmQj(3(e5xmrTZhbMDzLi1KW4GA7MnQ94T8MIaNa6BTkQFKirO((zWFdIs1grxl3YB2oL7QCwyXILWFkaZMC8GKur4ve40rXINLhlW62ibMBfhWRFatcTKZEM2AkPyHGpMu4MxFcBE0oqCFJI1Pdgh(ZBRgb(pvny8mmx6H5hbouyfSjvFcQygfYwXNt6c6IzFx4MOPzIRMI13HwNgu9XuDscM3koYZ2c59WcLFhxJ1Uexyk9ecJxHsf8sMyG7OmMJxoNKLrB1lql74aJdUZerejzmvSwPASW6M0WfkxjcC4UZDxA5YlE)hbg31f(Pa3P1MwCmDSznu4nbLavG3HJrOj6yws1wYcngHgevhWsYIFP8BoYJN4YrB)6ECOld0pMxhJs6yFcVdSUiLBwyLLx0MylwSyOlvPLFw9D8QvlO5opSsdVGM1RuTMx2AYstU4WOsHCf4hfdr7fM6Y7VrNMRV6PTX65HbRxPEJG6nQ434us7OaKKV7ZHZocroTDvQn9QTZgnRDeAE5vhZNT2fgIL1VWzb7dRs49KZnQulNLAIoSi5Do1ghj57yQoC)8Qp))uYAYYpL4u3iv8ebf(7ps(5tC86O2TvEloiguCsYpM3h)QJiCwx7es(9Y(4nYEq2LFnq0qDJc4DmXx6yQDGis2voR0ao1NQ7B5X6SzxUAxSaYjy2VCPSNN9RzbiyX3fJ8RN(yFApSHGffyIvGog706jV1hAOKubrmR1pZuMwCM)ggdSoXMxnCm(kFlU8MtqUrZjoD00edLMtvDF7kI64W81c1U1GHSUsHYmrXoBG3e3)D(PTYVC6SCMVpaRd9DZY1xF(ShCmEnKeTxhCIkwBmB(M3nuyRQ7p513fi7vbZM67V8YbnnSeMPxjhIPfUbTPlvz6CMXFRp0U3ZSn)dSJ)FL6WrVdyIzFZ0Y(9pzCB8eJeV3xtENpKv9(6)n4Rve2I(i3wQQvWRvTpNPL6fytjRDVSlo5CW7fqkZOhvFZkpC7DdwEL(hiu0Dvez)DhbmSTn0JntedzM3owkX27ewFDFpVTKx5e31(PEittctaA8uCQPg0umUD3meFKgsA7EXv2fhMVVBD36YcfkGRlX2xw(R0gY45QfVD7Hiye45gH2sEnwhUqbJEpNdRNsju6wHOcAZ6uCoT)3S0Dx6BNB)))t)3")
            end },
            { type = 'button', text = 'Import Power Infusion Full Tracking', width = 150, callback = function()
                if not _G['WeakAuras'] then
                    return false
                end
                _G.WeakAuras.Import("!WA:2!nE1ZVTrXx8KMsRWGudHkRQsbCDPPTcvtCst5RknhStDAc4MyT2HuGwwp7oJ9onRNz0mZMe3lOAHeCIdH)dY5Vhq(aNr9VGvr8haQh4pG(pa82z344K4TeFi78J37Z7NZ79Y4lnvNPWtH)5hUh1LZQZdKUKRm2(OaThxUMqt5mvgXNIPkHpQBdYoA7wCzhK2wyRPDi24UmuhQRT2tsuECFCxz31A1sr0l)h78d)ZFn2yJ1hXCb0QXPmTZIvwTrflrU0rmE34sKRr631sPrsDMYugvNXPf8r5LrKFeauSWrH41BsiIskbXvBHaW8exzeC5wmzvzgNr2dtuiDGePjELBXz6(pK6l5DZvgSTY(Ce(33nqrSzKTisphnYNW0zT6e4RPzSC9rk10okkRTprUrjRQRT4xfMnRZYRhz2nnSAiQzzf95KSHzZ8kZEBedBhPQthEPZozy27EXjB(jt28St2Sxepr3aSeF)BbCzqc5t3I0um1auhGIN85GXScwDM9vEimF7hhhuMypjPn4wB0vqkRbVqkos9rCKLFoN3z890CxWOva7xA8FIBH8fEOjMnGI734ZxQdUBL6I2T2RfDhcEdkw79JVR4dhj6SKLHxqYd0(ugrU26nQUYQvsHHdwAfPvylxUpx(Lta)2ZnqP5DIOwC1wbmtoZnVvMCWpjbIJm4dYVeVsgcdN4l(MyFbKN7YHOkFBw9TPcWro5HOTUadPawquMPF5bKvb3M41tlPTBd(HFzAzYY)USg8NoXC3p(JT5ii3vhOo4ipkMew0cEXquzc)FoldQM2RCaKz7ez0ePJrGkX7xVwLQvTxV2dk1OI961lvUALeBDvG7DMi4yglTvogxN7rikRegZz50EeM5MHCf5JVkIWOmzcoVHcWZy(6ZDr(dbXc5QsDQRdCUz(sUeZz3EUcZK)w37HeTz7n)wBZ3iD6wdHrnjLO0aadaRq8rdRRjeXLdTRWJiDCaFBTvgLbCeTfq5ANGPfwi3m)NCoKIQGiiRnOO3y1pR0no8wa8JJ93v8Phf5b8MVyHC5ZvOWOy5ErUMKy0qA(jOC20apzbGE(NWMnnbn7PwqZDQe0CPjO5o1c6oNkbDN0e0Do1cA(tLGMpnbn)if0WvqIXjQaspO2QVpuwnt4cIpA01B0YaseTwUEe3nDcmfsELki(PD9GwqbY(2lwQEd76nkz1yWv1Ke4klZlFOnKdqzuzA5YvQwBP1RUBalPCt2WfotFOo5ZmYFlYSiwxXuj1UIjPkVn1vC7bAycNhtrnNb5oFrUifw8EidCjq8ioM8)ht8bJStDs97IVg6PtKmK)xh3y4x3fXODIA2YUB4BVu49dVWljifPUwsyT1ENFFCuVv4At9rPIaJDGv9IOjYydVq5oqjddJHp58Hpn87dTHLVZXprC930aacjXLgPptSh4DTmT8uxoB493h26GC3Sn04HHD8i02E6x4akrlA7m9oOqVx)Hqp)1z9IMeOo0W(JDuMzKCODeCPEKENd7UjU9BQ56jhDkLUFh6Uh5KyPd3KXtETKeYVn)rC5niOnlbHa19Mzg711uFQUBoZbP4r1WRLoyPU9aRQ32CjEdjs0BJKfwBh1U)fYDgm53VDgyWVrN8Op0AU6iUoPD9H(WRL(OIhgMtXtN6qQVcMWLhLN6AMuPCjyB5OrGBkFg8oI2QB45gPKpP7ipfVBnRvQaVNNz2Dve)wMjDdpx)wrUCiZhPrt86Hcfrj6o1x0QsLvfx8GuUiH8aQc54tWEPfloUzd1LIMiJ4nAD94K3pE(NfnJpn(4JdJpbV(OXt53NYI5d2nDFyPlPIzyKMIlrBZ4ssY)pG50ksjxcxLlD5Mm84ukN5luCUcfNAR)8X)l")
            end },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
        }
    }
    Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
    Config_Table.config = MainAddon.BuildDPSTrinketUI(Config_Table.config, Config_Color)
    Config_Table.config = MainAddon.BuildCombatPotionUI(Config_Table.config, "Shadow", Config_Color)
    M.SetConfig(258, Config_Table)

    -- Custom Var
    local ShouldReturn
    local Enemies40y = {} 
    local Enemies10ySplash
    local EnemiesCount40y, EnemiesCount10ySplash
    local InDungeon, InRaid, InCombat, TargetIsValid
    local TempBlacklistVT = {}
    local MOCheck = false
    local FlayReason = ""
    
    local function ShadowCast(ThisSpell, OffGCD)
        if Player:IsChanneling() and ThisSpell ~= S.MindFlay and ThisSpell ~= S.MindFlayInsanity then
            if Player:IsChanneling(S.MindFlay) or Player:IsChanneling(S.MindFlayInsanity) then
                CacheCast(ThisSpell, OffGCD, nil, true)
            end
        end
        return CacheCast(ThisSpell, OffGCD)
    end
    local Cast = ShadowCast

    local ExcludeNPCList = {
      -- Spiteful Shades
      [174773] = true
    }
    ---@param TargetUnit Unit
    local function EvaluateSpiteful(TargetUnit)
        return TargetUnit:HealthPercentage()
    end
    ---@param TargetUnit Unit
    local function IsSpiteful(TargetUnit)
        return TargetUnit:NPCID() == 174773 and TargetUnit:TimeToDie() > 2 and Player:IsTanking(TargetUnit)
    end

    local function Defensives()
        if InCombat then
            if GetSetting('VE_check') then
                if S.VampiricEmbrace:IsReady() and Player:BuffDown(S.VampiricEmbrace, true) then
                    if Player:HealthPercentage() <= GetSetting('VE_spin') then
                        if Cast(S.VampiricEmbrace, true) then
                            return "Defensive: Vampiric Embrace Player"
                        end
                    end
                    if GetSetting('VE_allies') and Player:IsInParty() then
                        if MainAddon.AllyLowestHP() <= GetSetting('VE_spin') then
                            if Cast(S.VampiricEmbrace, true) then
                                return "Defensive: Vampiric Embrace Ally"
                            end
                        end
                    end
                end
            end
        end

        if InDungeon or InRaid then
            if GetSetting('dispersion_grp_check') and InCombat then
                if S.Dispersion:IsReady(Player) and Player:BuffDown(S.Dispersion) and Player:HealthPercentage() <= GetSetting('dispersion_grp_spin') then
                    if Cast(S.Dispersion) then
                        return 'Defensive: Dispersion'
                    end
                end
            end
            if GetSetting('dispersion_grp_check') then
                if S.DesperatePrayer:IsReady(Player) and Player:HealthPercentage() <= GetSetting('dprayer_grp_spin') then
                    if Cast(S.DesperatePrayer, true) then
                        return 'Defensive: Desperate Prayer'
                    end
                end
            end
            if GetSetting('pws_grp_check') then
                if S.PowerWordShield:IsReady(Player) and Player:BuffDown(S.PowerWordShield, true) and Player:HealthPercentage() <= GetSetting('pws_grp_spin') then
                    if CastAlly(S.PowerWordShield, Player) then
                        return 'Defensive: Power Word Shield'
                    end
                end
            end
            if GetSetting('flash_grp_check') then
                if S.FlashHeal:IsReady(Player) and Player:HealthPercentage() <= GetSetting('flash_grp_spin') then
                    if Cast(S.FlashHeal) then
                        return 'Defensive: Flash Heal'
                    end
                end
            end
        else
            if GetSetting('dispersion_solo_check') and InCombat then
                if S.Dispersion:IsReady(Player) and Player:BuffDown(S.Dispersion) and Player:HealthPercentage() <= GetSetting('dispersion_solo_spin') then
                    if Cast(S.Dispersion) then
                        return 'Defensive: Dispersion'
                    end
                end
            end
            if GetSetting('dispersion_solo_check') then
                if S.DesperatePrayer:IsReady(Player) and Player:HealthPercentage() <= GetSetting('dprayer_solo_spin') then
                    if Cast(S.DesperatePrayer, true) then
                        return 'Defensive: Desperate Prayer'
                    end
                end
            end
            if GetSetting('pws_solo_check') then
                if S.PowerWordShield:IsReady(Player) and Player:BuffDown(S.PowerWordShield, true) and Player:HealthPercentage() <= GetSetting('pws_solo_spin') then
                    if CastAlly(S.PowerWordShield, Player) then
                        return 'Defensive: Power Word Shield'
                    end
                end
            end
            if GetSetting('flash_solo_check') then
                if S.FlashHeal:IsReady(Player) and Player:HealthPercentage() <= GetSetting('flash_solo_spin') then
                    if Cast(S.FlashHeal) then
                        return 'Defensive: Flash Heal'
                    end
                end
            end
        end
    end

    local function Utilities()
        local sform = GetSetting('sform')
        if S.Shadowform:IsReady(Player) and Player:BuffDown(S.ShadowformBuff) then
            if InCombat then
                if sform['sform_combat'] then
                    if Cast(S.Shadowform) then
                        return 'Utilities: Shadowform - In Combat';
                    end
                end
            else
                if sform['sform_ooc'] then
                    if Cast(S.Shadowform) then
                        return 'Utilities: Shadowform - OOC';
                    end
                end
                if sform['sform_valid'] and TargetIsValid then
                    if Cast(S.Shadowform) then
                        return 'Utilities: Shadowform - Target is Valid';
                    end
                end
            end
        end

        if S.PowerWordFortitude:IsReady(Player) and (Player:BuffDown(S.PowerWordFortitude, true) or M.GroupBuffMissing(S.PowerWordFortitude)) then
            if Cast(S.PowerWordFortitude) then
                return 'Utilities: Power Word: Fortitude'
            end
        end
        if InCombat then
            if Player:BuffDown(S.Fade) and not Player:BuffDown(S.Shadowmeld) then
                if Player:IsInPartyOrRaid() then
                    if Player:IsTankingAoE(40) then
                        if S.Fade:IsReady(Player) then 
                            if Cast(S.Fade) then
                                return 'Fade'
                            end
                        end
                    end
                end
            end
        else
            if Target:IsDeadOrGhost() and Target:IsInParty() and Target:IsAPlayer() and not Target:IsEnemy() then
                if S.Resurrection:IsReady(Player) then
                    if Cast(S.Resurrection) then
                        return 'Resurrection';
                    end
                end
            end
        end

          if Player:BuffDown(S.BodyAndSoulBuff) and Player:BuffDown(S.AngelicFeatherBuff) then
            if GetSetting('bsoul_check') and S.BodyAndSoul:IsAvailable() and S.PowerWordShield:IsReady(Player) then
                if Player:IsMovingFor() > GetSetting('bsoul_spin') then
                    if CastAlly(S.PowerWordShield, Player) then
                        return "Power Word: Shield - Body and Soul";
                    end
                end
            elseif GetSetting('angelic_check') and S.AngelicFeather:IsReady(Player) then
                if Player:IsMovingFor() > GetSetting('angelic_spin') then
                    if Cast(S.AngelicFeather) then
                        return "Angelic Feather";
                    end
                end
            end
        end
    end

    ---@param TargetUnit Unit
    local function EvaluateUnitSpeed(TargetUnit)
        return TargetUnit:Speed() < 115
    end

    ---@param TargetUnit Unit
    local function EvaluatePI(TargetUnit)
        return TargetUnit:BuffDown(S.PowerInfusionBuff, true) and (not TargetUnit:IsUnit(Player) or not IsInGroup()) and TargetUnit:CurrentTarget()
    end
    ---@param TargetUnit Unit
    local function EvaluatePIToggle(TargetUnit)
        return TargetUnit:BuffDown(S.PowerInfusionBuff, true) and TargetUnit:Name() == Priest.PiTargetName and TargetUnit:CurrentTarget()
    end

    local Tanks, Healers, Members, Damagers, Melees
    Priest.MembersPI = {}

    local function UnitWithVT(enemies)
        local Count = 0
        for k in pairs(enemies) do
            ---@class Unit
            local CycleUnit = enemies[k]
            if not CycleUnit:DebuffRefreshable(S.VampiricTouchDebuff) then
                Count = Count + 1
            end
        end
        return Count
    end

    local function UnitWithDP(enemies)
        if Target:DebuffUp(S.DevouringPlagueDebuff) then
            return 0
        end
        if S.VoidTorrent:CooldownDown() then
            return 0
        end
        local Count = 0
        for k in pairs(enemies) do
            ---@class Unit
            local CycleUnit = enemies[k]
            if not CycleUnit:DebuffRefreshable(S.DevouringPlagueDebuff) then
                Count = Count + 1
            end
        end
        return Count
    end

    local function EvaluateDP(CycleUnit)
        return CycleUnit:DebuffUp(S.DevouringPlagueDebuff) 
    end

    ---@param TargetUnit Unit
    local function EvaluateCycleVTToggle(TargetUnit)
        -- target_if=refreshable&target.time_to_die>=18&(dot.vampiric_touch.ticking|!variable.dots_up)
        return TargetUnit:DebuffRefreshable(S.VampiricTouchDebuff) 
        and not ExcludeNPCList[TargetUnit:NPCID()] and not TempBlacklistVT[TargetUnit:Name()]
    end

    --- ===== Rotation Variables =====
    local VarDRForcePrio, VarMEForcePrio = false, true
    local VarMaxVTs, VarIsVTPossible = 12, false
    local VarPoolingMindblasts, VarPoolForCDs = false, false
    local VarHoldingCrash = false
    local VarDotsUp = false
    local VarManualVTsApplied = false
    local PreferVT = false
    local Crash = S.ShadowCrashTarget:IsAvailable() and S.ShadowCrashTarget or S.ShadowCrash
    local Fiend = (S.VoidWraith:IsAvailable() and S.VoidWraithAbility) or (S.Mindbender:IsAvailable() and S.Mindbender) or S.Shadowfiend
    local FiendUp, FiendRemains = false, 0
    local EntropicRiftUp, EntropicRiftRemains = false, 0
    local PowerSurgeUp, PowerSurgeRemains = false, 0
    ---@type Spell
    local Flay
    local GCDMax
    local BossFightRemains = 11111
    local FightRemains = 11111

    HL:RegisterForEvent(function()
        VarDRForcePrio, VarMEForcePrio = false, true
        VarMaxVTs, VarIsVTPossible = 12, false
        VarPoolingMindblasts, VarPoolForCDs = false, false
        VarHoldingCrash = false
        VarDotsUp = false
        VarManualVTsApplied = false
        PreferVT = false
        FiendUp, FiendRemains = false, 0
        EntropicRiftUp, EntropicRiftRemains = false, 0
        PowerSurgeUp, PowerSurgeRemains = false, 0
        BossFightRemains = 11111
        FightRemains = 11111
    end, "PLAYER_REGEN_ENABLED")

    HL:RegisterForEvent(function()
        Crash = S.ShadowCrashTarget:IsAvailable() and S.ShadowCrashTarget or S.ShadowCrash
        Fiend = (S.VoidWraith:IsAvailable() and S.VoidWraithAbility) or (S.Mindbender:IsAvailable() and S.Mindbender) or S.Shadowfiend
        S.ShadowCrash:RegisterInFlightEffect(205386)
        S.ShadowCrash:RegisterInFlight()
        S.ShadowCrashTarget:RegisterInFlightEffect(205386)
        S.ShadowCrashTarget:RegisterInFlight()
      end, "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")
      S.ShadowCrash:RegisterInFlightEffect(205386)
      S.ShadowCrash:RegisterInFlight()
      S.ShadowCrashTarget:RegisterInFlightEffect(205386)
      S.ShadowCrashTarget:RegisterInFlight()


    --- ===== Helper Functions =====
    local function ComputeDPPmultiplier()
        local Value = 1
        if Player:BuffUp(S.DarkAscensionBuff) then Value = Value * 1.25 end
        if Player:BuffUp(S.DarkEvangelismBuff) then Value = Value * (1 + (0.01 * Player:BuffStack(S.DarkEvangelismBuff))) end
        if Player:BuffUp(S.DevouredFearBuff) or Player:BuffUp(S.DevouredPrideBuff) then Value = Value * 1.05 end
        if S.DistortedReality:IsAvailable() then Value = Value * 1.2 end
        if Player:BuffUp(S.MindDevourerBuff) then Value = Value * 1.2 end
        if S.Voidtouched:IsAvailable() then Value = Value * 1.06 end
        return Value
    end
    S.DevouringPlague:RegisterPMultiplier(S.DevouringPlagueDebuff, ComputeDPPmultiplier)

    ---@param tar Unit
    local function DotsUp(tar, all)
        if all then
            return (tar:DebuffUp(S.ShadowWordPainDebuff) and tar:DebuffUp(S.VampiricTouchDebuff) and tar:DebuffUp(S.DevouringPlagueDebuff))
          else
            return (tar:DebuffUp(S.ShadowWordPainDebuff) and tar:DebuffUp(S.VampiricTouchDebuff))
        end
    end

    local function HighestTTD(enemies, checkVT)
        if not enemies then return nil end
        local HighTTD = 0
        local HighTTDTar = nil
        ---@param enemy Unit
        for _, enemy in pairs(enemies) do
            local TTD = enemy:TimeToDie()
            if checkVT then
            if TTD * num(enemy:DebuffRefreshable(S.VampiricTouchDebuff)) > HighTTD then
                HighTTD = TTD
                HighTTDTar = enemy
            end
            else
            if TTD > HighTTD then
                HighTTD = TTD
                HighTTDTar = enemy
            end
            end
        end
        return HighTTDTar
    end

    local function CanToF()
        -- buff.twist_of_fate_can_trigger_on_ally_heal.up&(talent.rhapsody|talent.divine_star|talent.halo)
        if not S.Rhapsody:IsAvailable() and not S.DivineStar:IsAvailable() and not S.Halo:IsAvailable() then return false end
        -- Are we in a party or raid?
        local Group
        if UnitInRaid("player") then
          Group = Unit.Raid
        elseif UnitInParty("player") then
          Group = Unit.Party
        else
          return false
        end
        -- Check group HP levels for sub-35%
        local Range = (S.DivineStar:IsAvailable() or S.Halo:IsAvailable()) and 30 or 12
        ---@param Char Unit
        for _, Char in pairs(Group) do
          if Char:Exists() and not Char:IsDeadOrGhost() and Char:IsInRange(Range) and Char:HealthPercentage() < 35 then
            return true
          end
        end
        return false
    end

    --- ===== CastTargetIf Filter Functions =====
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterDPPlusHP(TargetUnit)
        -- target_if=max:(target.health.pct<=20)*100+dot.devouring_plague.ticking
        return num(TargetUnit:HealthPercentage() <= 20) * 100 + num(TargetUnit:DebuffUp(S.DevouringPlagueDebuff))
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterDPPlusTTD(TargetUnit)
        -- target_if=max:(dot.devouring_plague.remains*1000+target.time_to_die)
        return TargetUnit:DebuffRemains(S.DevouringPlagueDebuff) * 1000 + TargetUnit:TimeToDie()
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterDPRemains(TargetUnit)
        -- target_if=max:dot.devouring_plague.remains
        return (TargetUnit:DebuffRemains(S.DevouringPlagueDebuff))
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterSWP(TargetUnit)
        -- target_if=min:remains
        return (TargetUnit:DebuffRemains(S.ShadowWordPainDebuff))
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterTTD(TargetUnit)
        -- target_if=min:target.time_to_die
        return (TargetUnit:TimeToDie())
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterTTDTimesDP(TargetUnit)
        -- target_if=max:target.time_to_die*(dot.devouring_plague.remains<=gcd.max|variable.dr_force_prio|!talent.distorted_reality&variable.me_force_prio)
        return TargetUnit:TimeToDie() * num(TargetUnit:DebuffRemains(S.DevouringPlagueDebuff) <= GCDMax or VarDRForcePrio or not S.DistortedReality:IsAvailable() and VarMEForcePrio)
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterVTRefresh(TargetUnit)
        -- target_if=max:(refreshable*10000+target.time_to_die)*(dot.vampiric_touch.ticking|!variable.dots_up)
        return (num(TargetUnit:DebuffRefreshable(S.VampiricTouchDebuff)) * 10000 + TargetUnit:TimeToDie()) * num(TargetUnit:DebuffUp(S.VampiricTouchDebuff) or not VarDotsUp)
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterVTRemains(TargetUnit)
        -- target_if=min:remains
        return (TargetUnit:DebuffRemains(S.VampiricTouchDebuff))
    end
    
    --- ===== CastTargetIf Condition Functions =====
    ---@param TargetUnit Unit
    local function EvaluateTargetIfDPMain(TargetUnit)
        -- if=active_dot.devouring_plague<=1&dot.devouring_plague.remains<=gcd.max&(!talent.void_eruption|cooldown.void_eruption.remains>=gcd.max*3)|insanity.deficit<=16
        return S.DevouringPlagueDebuff:AuraActiveCount() <= 1 and Target:DebuffRemains(S.DevouringPlagueDebuff) <= GCDMax and (not S.VoidEruption:IsAvailable() or S.VoidEruption:CooldownRemains() >= GCDMax * 3) or Player:InsanityDeficit() <= 16
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfDPMain2(TargetUnit)
        -- if=dot.devouring_plague.remains>=2.5
        return TargetUnit:DebuffRemains(S.DevouringPlagueDebuff) >= 2.5
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfMindBlastMain(TargetUnit)
        -- if=(cooldown.mind_blast.full_recharge_time<=gcd.max+execute_time|pet.fiend.remains<=execute_time+gcd.max)&pet.fiend.active&talent.inescapable_torment&pet.fiend.remains>=execute_time&active_enemies<=7&(!buff.mind_devourer.up|!talent.mind_devourer)&dot.devouring_plague.remains>execute_time&!variable.pooling_mindblasts
        -- Note: All but DP debuff timing handled before CastTargetIf.
        return TargetUnit:DebuffRemains(S.DevouringPlagueDebuff) > S.MindBlast:ExecuteTime()
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfSWD(TargetUnit)
        -- if=talent.depth_of_shadows&(target.health.pct<=20|buff.deathspeaker.up&talent.deathspeaker)
        -- Note: Talent checked before CastTargetIf.
        return TargetUnit:HealthPercentage() <= 20 or Player:BuffUp(S.DeathspeakerBuff) and S.Deathspeaker:IsAvailable()
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfVoidBlastMain(TargetUnit)
        -- if=(dot.devouring_plague.remains>=execute_time|buff.entropic_rift.remains<=gcd.max|action.void_torrent.channeling&talent.void_empowerment)&(insanity.deficit>=16|cooldown.mind_blast.full_recharge_time<=gcd.max)&(!talent.mind_devourer|!buff.mind_devourer.up|buff.entropic_rift.remains<=gcd.max)
        -- Note: 2nd and 3rd parts handled before CastTargetIf.
        return TargetUnit:DebuffRemains(S.DevouringPlagueDebuff) >= S.VoidBlast:ExecuteTime() or EntropicRiftRemains <= GCDMax or (Player:IsChanneling(S.VoidTorrent) or Player:PrevGCDP(1, S.VoidTorrent)) and S.VoidEmpowerment:IsAvailable()

    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfVTMain(TargetUnit)
        -- if=(dot.devouring_plague.ticking|talent.void_eruption&cooldown.void_eruption.up)&talent.entropic_rift&!variable.holding_crash
        -- Note: 2nd and 3rd parts handled before CastTargetIf.
        return TargetUnit:DebuffUp(S.DevouringPlagueDebuff) or S.VoidEruption:IsAvailable() and S.VoidEruption:CooldownUp()
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfVTMain2(TargetUnit)
        -- if=refreshable&target.time_to_die>12&(dot.vampiric_touch.ticking|!variable.dots_up)&(variable.max_vts>0|active_enemies=1)&(cooldown.shadow_crash.remains>=dot.vampiric_touch.remains|variable.holding_crash|!talent.whispering_shadows)&(!action.shadow_crash.in_flight|!talent.whispering_shadows)
        -- Note: Some parts handled before CastTargetIf.
        return TargetUnit:DebuffRefreshable(S.VampiricTouchDebuff) and TargetUnit:TimeToDie() > 12 and (TargetUnit:DebuffUp(S.VampiricTouchDebuff) or not VarDotsUp) and (Crash:CooldownRemains() >= TargetUnit:DebuffRemains(S.VampiricTouchDebuff) or VarHoldingCrash or not S.WhisperingShadows:IsAvailable())
        and not ExcludeNPCList[TargetUnit:NPCID()] and not TempBlacklistVT[TargetUnit:Name()]
        and TargetUnit:MaxHealth() > GetSetting('vt_min_hp') * 1000000 and TargetUnit:MaxHealth() > GetSetting('vt_min_ttd')
    end
    
    -- CastCycle Functions
    ---@param TargetUnit Unit
    local function EvaluateCycleSWDFiller(TargetUnit)
    -- target_if=target.health.pct<20|buff.deathspeaker.up&dot.devouring_plague.ticking
     return TargetUnit:HealthPercentage() < 20 or (Player:BuffUp(S.DeathspeakerBuff) and TargetUnit:DebuffUp(S.DevouringPlagueDebuff))
    end
    ---@param TargetUnit Unit
    local function EvaluateCycleSWDFiller2(TargetUnit)
        -- if=target.health.pct<20
        return (TargetUnit:HealthPercentage() < 20)
    end
    ---@param TargetUnit Unit
    local function EvaluateCycleShadowCrashAoE(TargetUnit)
        -- target_if=dot.vampiric_touch.refreshable|dot.vampiric_touch.remains<=target.time_to_die&!buff.voidform.up&(raid_event.adds.in-dot.vampiric_touch.remains)<15
        return (TargetUnit:DebuffRefreshable(S.VampiricTouchDebuff) or TargetUnit:DebuffRemains(S.VampiricTouchDebuff) <= TargetUnit:TimeToDie() and Player:BuffDown(S.VoidformBuff))
    end
    ---@param TargetUnit Unit
    local function EvaluateCycleVTAoE(TargetUnit)
        if ExcludeNPCList[TargetUnit:NPCID()] or TempBlacklistVT[TargetUnit:Name()] then
            return false
        end
        if TargetUnit:MaxHealth() < GetSetting('vt_min_hp') * 1000000 or TargetUnit:MaxHealth() < GetSetting('vt_min_ttd') then
            return false
        end
        -- target_if=refreshable&target.time_to_die>=18&(dot.vampiric_touch.ticking|!variable.dots_up)
        -- Note: Manually added variable check to avoid cycling on low hp adds.
        return (TargetUnit:DebuffRefreshable(S.VampiricTouchDebuff) and TargetUnit:TimeToDie() >= 18 and (TargetUnit:DebuffUp(S.VampiricTouchDebuff) or not VarDotsUp))
    end
    ---@param TargetUnit Unit
    local function EvaluateCycleVTRefreshable(TargetUnit)
        -- target_if=dot.vampiric_touch.refreshable
        return TargetUnit:DebuffRefreshable(S.VampiricTouchDebuff)
    end

    ---@param TargetUnit Unit
    local function EvaluateTargetIfVTCustom(TargetUnit)
        return not ExcludeNPCList[TargetUnit:NPCID()] and not TempBlacklistVT[TargetUnit:Name()]
        and TargetUnit:MaxHealth() > GetSetting('vt_min_hp') * 1000000 and TargetUnit:MaxHealth() > GetSetting('vt_min_ttd')
    end

    local function Precombat()
        -- use_item,name=ingenious_mana_battery
        if I.IngeniousManaBattery:IsEquippedAndReady() then
            if Cast(I.IngeniousManaBattery) then return "ingenious_mana_battery precombat 6"; end
        end
        -- arcane_torrent
        if S.ArcaneTorrent:IsReady() then
            if Cast(S.ArcaneTorrent) then return "arcane_torrent precombat 6"; end
        end
        -- use_item,name=aberrant_spellforge
        if I.AberrantSpellforge:IsEquippedAndReady() then
            if Cast(I.AberrantSpellforge) then return "aberrant_spellforge precombat 8"; end
        end
        local DungeonSlice = Player:IsInDungeonArea()
        -- halo,if=!fight_style.dungeonroute&!fight_style.dungeonslice&active_enemies<=4&(fight_remains>=120|active_enemies<=2)
        if S.Halo:IsReady() and (not DungeonSlice) then
          if Cast(S.Halo) then return "halo precombat 10"; end
        end
        -- shadow_crash,if=raid_event.adds.in>=25&spell_targets.shadow_crash<=8&!fight_style.dungeonslice
        -- Note: Can't do target counts in Precombat
        local DungeonSlice = Player:IsInDungeonArea()
        if Crash:IsReady() and (not DungeonSlice) then
            if Cast(Crash) then return "shadow_crash precombat 12"; end
        end
        -- vampiric_touch,if=!talent.shadow_crash.enabled|raid_event.adds.in<25|spell_targets.shadow_crash>8|fight_style.dungeonslice
        -- Note: Manually added VT suggestion if Shadow Crash is on CD and wasn't just used.
        if S.VampiricTouch:IsReady() and (not Crash:IsAvailable() or (Crash:CooldownDown() and not Crash:InFlight()) or DungeonSlice) 
        and not TempBlacklistVT[Target:Name()] then
            if Cast(S.VampiricTouch) then return "vampiric_touch precombat 10"; end
        end
        -- Manually added: shadow_word_pain,if=!talent.misery.enabled
        if S.ShadowWordPain:IsReady() and (not S.Misery:IsAvailable()) then
            if Cast(S.ShadowWordPain) then return "shadow_word_pain precombat 16"; end
        end
    end

    local function AoEVariables()
        -- variable,name=max_vts,op=set,default=12,value=spell_targets.vampiric_touch>?12
        VarMaxVTs = mathmin(EnemiesCount10ySplash, 12)
        -- variable,name=is_vt_possible,op=set,value=0,default=1
        VarIsVTPossible = false
        -- variable,name=is_vt_possible,op=set,value=1,target_if=max:(target.time_to_die*dot.vampiric_touch.refreshable),if=target.time_to_die>=18
        ---@type Unit?
        local HighTTDTar = HighestTTD(Enemies10ySplash, true)
        if HighTTDTar and HighTTDTar:TimeToDie() >= 18 then
        VarIsVTPossible = true
        end
        -- variable,name=dots_up,op=set,value=(active_dot.vampiric_touch+8*(action.shadow_crash.in_flight&talent.whispering_shadows))>=variable.max_vts|!variable.is_vt_possible
        VarDotsUp = ((S.VampiricTouchDebuff:AuraActiveCount() + 8 * num(Crash:InFlight() and S.WhisperingShadows:IsAvailable())) >= VarMaxVTs or not VarIsVTPossible)
        -- variable,name=holding_crash,op=set,value=(variable.max_vts-active_dot.vampiric_touch)<4&raid_event.adds.in>15|raid_event.adds.in<10&raid_event.adds.count>(variable.max_vts-active_dot.vampiric_touch),if=variable.holding_crash&talent.whispering_shadows&raid_event.adds.exists
        if VarHoldingCrash and S.WhisperingShadows:IsAvailable() then
            VarHoldingCrash = (VarMaxVTs - S.VampiricTouchDebuff:AuraActiveCount()) < 4
        end
        -- variable,name=manual_vts_applied,op=set,value=(active_dot.vampiric_touch+8*!variable.holding_crash)>=variable.max_vts|!variable.is_vt_possible
        VarManualVTsApplied = ((S.VampiricTouchDebuff:AuraActiveCount() + 8 * num(not VarHoldingCrash)) >= VarMaxVTs or not VarIsVTPossible)
    end

    local function AoE()
        -- call_action_list,name=aoe_variables
        AoEVariables()
        -- vampiric_touch,target_if=refreshable&target.time_to_die>=18&(dot.vampiric_touch.ticking|!variable.dots_up),if=(variable.max_vts>0&!variable.manual_vts_applied&!action.shadow_crash.in_flight|!talent.whispering_shadows)&!buff.entropic_rift.up
        if S.VampiricTouch:IsReady() and ((VarMaxVTs > 0 and not VarManualVTsApplied and not Crash:InFlight() or not S.WhisperingShadows:IsAvailable()) and not EntropicRiftUp) then
          if CastCycle(S.VampiricTouch, Enemies40y, EvaluateCycleVTAoE) then return "vampiric_touch aoe 2"; end
        end
        -- shadow_crash,if=!variable.holding_crash,target_if=dot.vampiric_touch.refreshable|dot.vampiric_touch.remains<=target.time_to_die&!buff.voidform.up&(raid_event.adds.in-dot.vampiric_touch.remains)<15
        if Crash:IsReady() and (not VarHoldingCrash) 
        and EvaluateCycleShadowCrashAoE(Target) then
          if Cast(Crash) then return "shadow_crash aoe 4"; end
        end
    end

    local function Trinkets()
        -- use_item,use_off_gcd=1,name=hyperthread_wristwraps,if=talent.void_blast&hyperthread_wristwraps.void_blast.count>=2&!cooldown.mind_blast.up|!talent.void_blast&((hyperthread_wristwraps.void_bolt.count>=1|!talent.void_eruption)&hyperthread_wristwraps.void_torrent.count>=1)
        if I.HyperthreadWristwraps:IsEquippedAndReady() then
            local HTWWVBlastCount = 0
            local HTWWVBoltCount = 0
            local HTWWVTCount = 0
            for i=1, 3 do
            local Spell = Player:PrevGCD(i)
            if Spell == S.VoidBlast:ID() then
                HTWWVBlastCount = HTWWVBlastCount + 1
            elseif Spell == S.VoidBolt:ID() then
                HTWWVBoltCount = HTWWVBoltCount + 1
            elseif Spell == S.VoidTorrent:ID() then
                HTWWVTCount = HTWWVTCount + 1
            end
            end
            if S.VoidBlast:IsAvailable() and HTWWVBlastCount >= 2 and S.MindBlast:CooldownDown() or not S.VoidBlast:IsAvailable() and ((HTWWVBoltCount >= 1 or not S.VoidEruption:IsAvailable()) and HTWWVTCount >= 1) then
            if Cast(I.HyperthreadWristwraps) then return "hyperthread_wristwraps trinkets 2"; end
            end
        end
        -- use_item,use_off_gcd=1,name=aberrant_spellforge,if=gcd.remains>0&buff.aberrant_spellforge.stack<=4
        if I.AberrantSpellforge:IsEquippedAndReady() and (Player:BuffStack(S.AberrantSpellforgeBuff) <= 4) then
            if Cast(I.AberrantSpellforge) then return "aberrant_spellforge trinkets 4"; end
        end
        -- use_item,use_off_gcd=1,name=neural_synapse_enhancer,if=(buff.power_surge.up|buff.entropic_rift.up)&(buff.voidform.up|cooldown.void_eruption.remains>=40|buff.dark_ascension.up)
        if I.NeuralSynapseEnhancer:IsEquippedAndReady() and ((PowerSurgeUp or EntropicRiftUp) and (Player:BuffUp(S.VoidformBuff) or S.VoidEruption:CooldownRemains() >= 40 or Player:BuffUp(S.DarkAscensionBuff))) then
            if Cast(I.NeuralSynapseEnhancer) then return "neural_synapse_enhancer trinkets 6"; end
        end
        -- use_item,use_off_gcd=1,name=flarendos_pilot_light,if=gcd.remains>0&(buff.voidform.up|buff.power_infusion.remains>=10|buff.dark_ascension.up)|fight_remains<20
        if I.FlarendosPilotLight:IsEquippedAndReady() and ((Player:BuffUp(S.VoidformBuff) or Player:PowerInfusionRemains() >= 10 or Player:BuffUp(S.DarkAscensionBuff)) or BossFightRemains < 20) then
            if Cast(I.FlarendosPilotLight) then return "flarendos_pilot_light trinkets 8"; end
        end
        -- use_item,use_off_gcd=1,name=geargrinders_spare_keys,if=gcd.remains>0
        if I.GeargrindersSpareKeys:IsEquippedAndReady() then
            if Cast(I.GeargrindersSpareKeys) then return "geargrinders_spare_keys trinkets 10"; end
        end
        -- use_item,name=spymasters_web,if=(buff.power_infusion.remains>=10&buff.spymasters_report.stack>=36&fight_remains>240)&(buff.voidform.up|buff.dark_ascension.up|!talent.dark_ascension&!talent.void_eruption)|((buff.power_infusion.remains>=10&buff.bloodlust.up&buff.spymasters_report.stack>=10)|buff.power_infusion.remains>=10&(fight_remains<120))&(buff.voidform.up|buff.dark_ascension.up|!talent.dark_ascension&!talent.void_eruption)|(fight_remains<=20|buff.dark_ascension.up&fight_remains<=60|buff.entropic_rift.up&talent.entropic_rift&fight_remains<=30)&!buff.spymasters_web.up
        if I.SpymastersWeb:IsEquippedAndReady() and ((Player:PowerInfusionRemains() >= 10 and Player:BuffStack(S.SpymastersReportBuff) >= 36 and FightRemains > 240) and (Player:BuffUp(S.VoidformBuff) or Player:BuffUp(S.DarkAscensionBuff) or not S.DarkAscension:IsAvailable() and not S.VoidEruption:IsAvailable()) or ((Player:PowerInfusionRemains() >= 10 and Player:BloodlustUp() and Player:BuffStack(S.SpymastersReportBuff) >= 10) or Player:PowerInfusionRemains() >= 10 and (BossFightRemains < 120)) and (Player:BuffUp(S.VoidformBuff) or Player:BuffUp(S.DarkAscensionBuff) or not S.DarkAscension:IsAvailable() and not S.VoidEruption:IsAvailable()) or (BossFightRemains <= 20 or Player:BuffUp(S.DarkAscensionBuff) and BossFightRemains <= 60 or EntropicRiftUp and S.EntropicRift:IsAvailable() and BossFightRemains <= 30) and Player:BuffDown(S.SpymastersWebBuff)) then
            if Cast(I.SpymastersWeb) then return "spymasters_web trinkets 12"; end
        end
        -- use_items,if=(buff.voidform.up|buff.power_infusion.remains>=10|buff.dark_ascension.up|(cooldown.void_eruption.remains>10&trinket.cooldown.duration<=60))|fight_remains<20
        local ItemToUse, ItemSlot, ItemRange = Player:GetUseableItems(OnUseExcludes)
        if ItemToUse and ((Player:BuffUp(S.VoidformBuff) or Player:PowerInfusionRemains() >= 10 or Player:BuffUp(S.DarkAscensionBuff) or (S.VoidEruption:CooldownRemains() > 10 and (ItemToUse:Cooldown() <= 60 or ItemSlot ~= 13 and ItemSlot ~= 14))) or BossFightRemains < 20) then
          if ((ItemSlot == 13 or ItemSlot == 14)) or (ItemSlot ~= 13 and ItemSlot ~= 14) then
            if Cast(ItemToUse) then return "Generic use_items for " .. ItemToUse:Name() .. " trinkets 14"; end
          end
        end
    end

    local function CDs()
        -- fireblood,if=buff.power_infusion.up&(buff.voidform.up|buff.dark_ascension.up)|fight_remains<=8
        if S.Fireblood:IsReady() and (Player:PowerInfusionUp() and (Player:BuffUp(S.VoidformBuff) or Player:BuffUp(S.DarkAscensionBuff)) or BossFightRemains <= 8) then
            if Cast(S.Fireblood) then return "fireblood cds 4"; end
        end
        -- berserking,if=buff.power_infusion.up&(buff.voidform.up|buff.dark_ascension.up)|fight_remains<=12
        if S.Berserking:IsReady() and (Player:PowerInfusionUp() and (Player:BuffUp(S.VoidformBuff) or Player:BuffUp(S.DarkAscensionBuff)) or BossFightRemains <= 12) then
            if Cast(S.Berserking) then return "berserking cds 6"; end
        end
        -- blood_fury,if=buff.power_infusion.up&(buff.voidform.up|buff.dark_ascension.up)|fight_remains<=15
        if S.BloodFury:IsReady() and (Player:PowerInfusionUp() and (Player:BuffUp(S.VoidformBuff) or Player:BuffUp(S.DarkAscensionBuff)) or BossFightRemains <= 15) then
            if Cast(S.BloodFury) then return "blood_fury cds 8"; end
        end
        -- ancestral_call,if=buff.power_infusion.up&(buff.voidform.up|buff.dark_ascension.up)|fight_remains<=15
        if S.AncestralCall:IsReady() and (Player:PowerInfusionUp() and (Player:BuffUp(S.VoidformBuff) or Player:BuffUp(S.DarkAscensionBuff)) or BossFightRemains <= 15) then
            if Cast(S.AncestralCall) then return "ancestral_call cds 10"; end
        end
        -- power_infusion,if=(buff.voidform.up|buff.dark_ascension.up&(fight_remains<=80|fight_remains>=140)|active_allied_augmentations)
        if Player:BuffUp(S.VoidformBuff) or Player:BuffUp(S.DarkAscensionBuff) then
            -- Cast Power Infusion.
            if S.PowerInfusion:IsReady(Player) then
                if Priest.PiTargetName == "None" then
                    if CastCycleAlly(S.PowerInfusion, Priest.MembersPI, EvaluatePI) then
                        return "Power Infusion - Auto"
                    end
                else
                    if CastCycleAlly(S.PowerInfusion, Priest.MembersPI, EvaluatePIToggle) then
                        return "Power Infusion - Toggle"
                    end
                end
            end
        end
        -- halo,if=talent.power_surge&(pet.fiend.active&cooldown.fiend.remains>=4&talent.mindbender|!talent.mindbender&!cooldown.fiend.up|active_enemies>2&!talent.inescapable_torment|!talent.dark_ascension)&(cooldown.mind_blast.charges=0|!talent.void_eruption|cooldown.void_eruption.remains>=gcd.max*4|buff.mind_devourer.up&talent.mind_devourer)
        if S.Halo:IsReady() and (S.PowerSurge:IsAvailable() and (FiendUp and Fiend:CooldownRemains() >= 4 and S.Mindbender:IsAvailable() or not S.Mindbender:IsAvailable() and Fiend:CooldownDown() or EnemiesCount10ySplash > 2 and not S.InescapableTorment:IsAvailable() or not S.DarkAscension:IsAvailable()) and (S.MindBlast:Charges() == 0 or not S.VoidEruption:IsAvailable() or S.VoidEruption:CooldownRemains() >= GCDMax * 4 or Player:BuffUp(S.MindDevourerBuff) and S.MindDevourer:IsAvailable())) then
            if Cast(S.Halo) then return "halo cds 14"; end
        end
        -- void_eruption,if=(pet.fiend.active&cooldown.fiend.remains>=4|!talent.mindbender&!cooldown.fiend.up|active_enemies>2&!talent.inescapable_torment)&(cooldown.mind_blast.charges=0|time>15|buff.mind_devourer.up&talent.mind_devourer)
        if S.VoidEruption:IsReady() and ((FiendUp and Fiend:CooldownRemains() >= 4 or not S.Mindbender:IsAvailable() and Fiend:CooldownDown() or EnemiesCount10ySplash > 2 and not S.InescapableTorment:IsAvailable()) and (S.MindBlast:Charges() == 0 or HL.CombatTime() > 15 or Player:BuffUp(S.MindDevourerBuff) and S.MindDevourer:IsAvailable())) then
            if Cast(S.VoidEruption) then return "void_eruption cds 16"; end
        end
        -- dark_ascension,if=(pet.fiend.active&cooldown.fiend.remains>=4|!talent.mindbender&!cooldown.fiend.up|active_enemies>2&!talent.inescapable_torment)&(active_dot.devouring_plague>=1|insanity>=(15+5*!talent.minds_eye+5*talent.distorted_reality-pet.fiend.active*6))
        if S.DarkAscension:IsReady() and ((FiendUp and Fiend:CooldownRemains() >= 4 or not S.Mindbender:IsAvailable() and Fiend:CooldownDown() or EnemiesCount10ySplash > 2 and not S.InescapableTorment:IsAvailable()) and (S.DevouringPlagueDebuff:AuraActiveCount() >= 1 or Player:Insanity() >= (15 + 5 * num(not S.MindsEye:IsAvailable()) + 5 * num(S.DistortedReality:IsAvailable()) - num(FiendUp) * 6))) then
            if Cast(S.DarkAscension) then return "dark_ascension cds 18"; end
        end
        -- call_action_list,name=trinkets
        local ShouldReturn = Trinkets(); if ShouldReturn then return ShouldReturn; end
    end

    local function HealForToF()
        -- halo
        if S.Halo:IsReady() and Target:IsInRange(30) then
          if Cast(S.Halo) then return "halo heal_for_tof 2"; end
        end
        -- divine_star
        if S.DivineStar:IsReady() and Target:IsInRange(30) then
          if Cast(S.DivineStar) then return "divine_star heal_for_tof 4"; end
        end
        -- holy_nova,if=buff.rhapsody.stack=20&talent.rhapsody
        if S.HolyNova:IsReady() then
          if Cast(S.HolyNova) then return "holy_nova heal_for_tof 6"; end
        end
    end
      
    local function EmpoweredFiller()
        -- mind_spike_insanity,target_if=max:dot.devouring_plague.remains
        if S.MindSpikeInsanity:IsReady() then
            if CastTargetIf(S.MindSpikeInsanity, Enemies40y, "max", EvaluateTargetIfFilterDPRemains) then return "mind_spike_insanity empowered_filler 2"; end
        end
        -- mind_flay,target_if=max:dot.devouring_plague.remains,if=buff.mind_flay_insanity.up
        if S.MindFlay:IsReady() and (Player:BuffUp(S.MindFlayInsanityBuff)) then
            if CastTargetIf(S.MindFlayInsanity, Enemies40y, "max", EvaluateTargetIfFilterDPRemains) then
                FlayReason = "mind_flay empowered_filler 4"
                return "mind_flay empowered_filler 4"; 
            end
        end
    end

    local function Filler()
        -- call_action_list,name=heal_for_tof,if=!buff.twist_of_fate.up&buff.twist_of_fate_can_trigger_on_ally_heal.up&(talent.rhapsody|talent.divine_star|talent.halo)
        if S.TwistofFate:IsAvailable() and Player:BuffDown(S.TwistofFateBuff) and CanToF() then
          local ShouldReturn = HealForToF(); if ShouldReturn then return ShouldReturn; end
        end
        -- power_word_shield,if=!buff.twist_of_fate.up&buff.twist_of_fate_can_trigger_on_ally_heal.up&talent.crystalline_reflection
        -- Note: Not handling PW:S.
        -- call_action_list,name=empowered_filler
        local ShouldReturn = EmpoweredFiller(); if ShouldReturn then return ShouldReturn; end
        -- vampiric_touch,target_if=min:remains,if=talent.unfurling_darkness&buff.unfurling_darkness_cd.remains<execute_time&talent.inner_quietus
        if S.VampiricTouch:IsReady() and (S.UnfurlingDarkness:IsAvailable() and (15 - S.UnfurlingDarknessBuff:TimeSinceLastAppliedOnPlayer()) < S.VampiricTouch:ExecuteTime() and S.InnerQuietus:IsAvailable()) then
          if CastTargetIf(S.VampiricTouch, Enemies10ySplash, "min", EvaluateTargetIfFilterVTRemains, nil) then return "vampiric_touch filler 2"; end
        end
        -- shadow_word_death,target_if=target.health.pct<20|buff.deathspeaker.up&dot.devouring_plague.ticking
        if S.ShadowWordDeath:IsReady() then
          if CastCycle(S.ShadowWordDeath, Enemies40y, EvaluateCycleSWDFiller) then return "shadow_word_death filler 4"; end
        end
        -- shadow_word_death,target_if=min:target.time_to_die,if=talent.inescapable_torment&pet.fiend.active
        if S.ShadowWordDeath:IsReady() and (S.InescapableTorment:IsAvailable() and FiendUp) then
          if CastTargetIf(S.ShadowWordDeath, Enemies40y, "min", EvaluateTargetIfFilterTTD, nil) then return "shadow_word_death filler 6"; end
        end
        -- mind_flay,target_if=max:dot.devouring_plague.remains,if=bugs&buff.voidform.up&cooldown.void_bolt.remains<=gcd.max*1.65738,interrupt_immediate=1,interrupt_if=ticks>=2&cooldown.void_bolt.remains>=gcd.max&gcd.remains<=0,interrupt_global=1
        if Flay:IsReady() and (Player:BuffUp(S.VoidformBuff) and S.VoidBolt:CooldownRemains() <= Player:GCD() * 1.65738) then
          if CastTargetIf(Flay, Enemies40y, "max", EvaluateTargetIfFilterDPRemains, nil) then return "mind_flay filler 8"; end
        end
        -- devouring_plague,if=talent.empowered_surges&buff.surge_of_insanity.up|buff.voidform.up&talent.void_eruption
        if S.DevouringPlague:IsReady() and (S.EmpoweredSurges:IsAvailable() and (Player:BuffUp(S.MindFlayInsanityBuff) or Player:BuffUp(S.MindSpikeInsanityBuff)) or Player:BuffUp(S.VoidformBuff) and S.VoidEruption:IsAvailable()) then
          if Cast(S.DevouringPlague) then return "devouring_plague filler 10"; end
        end
        -- vampiric_touch,target_if=min:remains,if=talent.unfurling_darkness&buff.unfurling_darkness_cd.remains<execute_time
        if S.VampiricTouch:IsReady() and (S.UnfurlingDarkness:IsAvailable() and (15 - S.UnfurlingDarknessBuff:TimeSinceLastAppliedOnPlayer()) < S.VampiricTouch:ExecuteTime()) then
          if Cast(S.VampiricTouch) then return "vampiric_touch filler 12"; end
        end
        -- halo,if=spell_targets>1
        if S.Halo:IsReady() and (EnemiesCount10ySplash > 1) then
          if Cast(S.Halo) then return "halo filler 14"; end
        end
        -- power_word_life,if=!buff.twist_of_fate.up&buff.twist_of_fate_can_trigger_on_ally_heal.up
        -- Note: Not handling PW:L.
        -- call_action_list,name=empowered_filler
        -- Note: Handled by the earlier call to EmpoweredFiller.
        -- call_action_list,name=heal_for_tof,if=equipped.rashoks_molten_heart&(active_allies-(10-buff.molten_radiance.value))>=10&buff.molten_radiance.up,line_cd=5
        -- Note: Probably no longer needed, as not many are going to use rashoks_molten_heart in 11.1.
        -- shadow_crash,if=!variable.holding_crash&talent.void_eruption&talent.perfected_form
        if Crash:IsReady() and (not VarHoldingCrash and S.VoidEruption:IsAvailable() and S.PerfectedForm:IsAvailable()) then
          if Cast(Crash) then return "shadow_crash filler 16"; end
        end
        -- mind_spike,target_if=max:dot.devouring_plague.remains
        if S.MindSpike:IsReady() then
          if CastTargetIf(S.MindSpike, Enemies40y, "max", EvaluateTargetIfFilterDPRemains, nil) then return "mind_spike filler 18"; end
        end
        -- mind_flay,target_if=max:dot.devouring_plague.remains,chain=1,interrupt_immediate=1,interrupt_if=ticks>=2,interrupt_global=1
        if Flay:IsReady() then
          if CastTargetIf(Flay, Enemies40y, "max", EvaluateTargetIfFilterDPRemains, nil) then return "mind_flay filler 20"; end
        end
        -- divine_star
        if S.DivineStar:IsReady() then
          if Cast(S.DivineStar) then return "divine_star filler 22"; end
        end
        -- shadow_crash,if=raid_event.adds.in>20
        if Crash:IsReady() then
          if Cast(Crash) then return "shadow_crash filler 24"; end
        end
        -- shadow_word_death,target_if=target.health.pct<20
        if S.ShadowWordDeath:IsReady() then
          if CastCycle(S.ShadowWordDeath, Enemies40y, EvaluateCycleSWDFiller2) then return "shadow_word_death filler 26"; end
        end
        -- shadow_word_death,target_if=max:dot.devouring_plague.remains
        -- Note: Per APL note, intent is to be used as a movement filler.
        if S.ShadowWordDeath:IsReady() and Player:IsMoving() then
          if CastTargetIf(S.ShadowWordDeath, Enemies40y, "max", EvaluateTargetIfFilterDPRemains, nil) then return "shadow_word_death movement filler 28"; end
        end
        -- shadow_word_pain,target_if=min:remains
        -- Note: Per APL note, intent is to be used as a movement filler.
        if S.ShadowWordPain:IsReady() and Player:IsMoving() then
          if CastTargetIf(S.ShadowWordPain, Enemies40y, "max", EvaluateTargetIfFilterDPRemains, nil) then return "shadow_word_pain filler 30"; end
        end
        -- shadow_word_pain,target_if=min:remains,if=!set_bonus.tier31_4pc
        -- Note: Per APL note, intent is to be used as a movement filler.
        if S.ShadowWordPain:IsReady() and Player:IsMoving() then
          if CastTargetIf(S.ShadowWordPain, Enemies40y, "min", EvaluateTargetIfFilterSWP, nil) then return "shadow_word_pain filler 32"; end
        end
    end
      
    local function Main()
        -- Reset variable.holding_crash to false for ST, in case it was set to true during AoE.
        VarHoldingCrash = false
        -- variable,name=dots_up,op=set,value=active_dot.vampiric_touch=active_enemies|action.shadow_crash.in_flight&talent.whispering_shadows,if=active_enemies<3
        if EnemiesCount10ySplash < 3 then
          VarDotsUp = S.VampiricTouchDebuff:AuraActiveCount() == EnemiesCount10ySplash or Crash:InFlight() and S.WhisperingShadows:IsAvailable() or Player:IsCasting(S.VampiricTouch) and S.Misery:IsAvailable()
        end
        -- variable,name=pooling_mindblasts,op=setif,value=1,value_else=0,condition=(cooldown.void_torrent.remains<?(variable.holding_crash*raid_event.adds.in))<=gcd.max*(2+talent.mind_melt*2),if=talent.void_blast
        VarPoolingMindblasts = false
        if S.VoidBlast:IsAvailable() then
          VarPoolingMindblasts = S.VoidTorrent:CooldownRemains() <= GCDMax * (2 + num(S.MindMelt:IsAvailable()) * 2)
        end
        -- vampiric_touch,target_if=min:remains,if=buff.unfurling_darkness.up&talent.unfurling_darkness&talent.mind_melt&talent.void_blast&buff.mind_melt.stack<2&cooldown.mindbender.up&cooldown.dark_ascension.up&time<=4
        if S.VampiricTouch:IsReady() and (Player:BuffUp(S.UnfurlingDarknessBuff) and S.UnfurlingDarkness:IsAvailable() and S.MindMelt:IsAvailable() and S.VoidBlast:IsAvailable() and Player:BuffStack(S.MindMeltBuff) < 2 and S.Mindbender:CooldownUp() and S.DarkAscension:CooldownUp() and HL.CombatTime() <= 4) then
          if CastTargetIf(S.VampiricTouch, Enemies10ySplash, "min", EvaluateTargetIfFilterVTRemains, nil) then return "vampiric_touch main 2"; end
        end
        -- mind_spike,if=talent.mind_melt&talent.void_blast&(buff.mind_melt.stack<(1*talent.distorted_reality+1-talent.unfurling_darkness-talent.minds_eye*1)&talent.halo|!talent.halo&buff.mind_melt.stack<2)&cooldown.mindbender.up&cooldown.dark_ascension.up&time<=4&insanity<=20&!set_bonus.tww2_4pc
        if S.MindSpike:IsReady() and (S.MindMelt:IsAvailable() and S.VoidBlast:IsAvailable() and (Player:BuffStack(S.MindMeltBuff) < (1 * num(S.DistortedReality:IsAvailable()) + 1 - num(S.UnfurlingDarkness:IsAvailable()) - num(S.MindsEye:IsAvailable()) * 1) and S.Halo:IsAvailable() or not S.Halo:IsAvailable() and Player:BuffStack(S.MindMeltBuff) < 2) and S.Mindbender:CooldownUp() and S.DarkAscension:CooldownUp() and HL.CombatTime() <= 4 and Player:InsanityDeficit() <= 20 and not Player:HasTier("TWW2", 4)) then
          if Cast(S.MindSpike) then return "mind_spike main 4"; end
        end
        -- call_action_list,name=cds,if=fight_remains<30|target.time_to_die>15&(!variable.holding_crash|active_enemies>2)
        if BossFightRemains < 30 or Target:TimeToDie() > 15 and (not VarHoldingCrash or EnemiesCount10ySplash > 2) then
          local ShouldReturn = CDs(); if ShouldReturn then return ShouldReturn; end
        end
        -- mindbender,if=(dot.shadow_word_pain.ticking&variable.dots_up|action.shadow_crash.in_flight&talent.whispering_shadows)&(fight_remains<30|target.time_to_die>15)&(!talent.dark_ascension|cooldown.dark_ascension.remains<gcd.max|fight_remains<15)
        if Fiend:IsReady() and ((Target:DebuffUp(S.ShadowWordPainDebuff) and VarDotsUp or Crash:InFlight() and S.WhisperingShadows:IsAvailable()) and (BossFightRemains < 30 or Target:TimeToDie() > 15) and (not S.DarkAscension:IsAvailable() or S.DarkAscension:CooldownRemains() < GCDMax or BossFightRemains < 15)) then
          if Cast(Fiend) then return "mindbender main 6"; end
        end
        -- shadow_word_death,target_if=max:(target.health.pct<=20)*100+dot.devouring_plague.ticking,if=priest.force_devour_matter&talent.devour_matter
        if S.ShadowWordDeath:IsReady() and (S.DevourMatter:IsAvailable()) then
          if CastTargetIf(S.ShadowWordDeath, Enemies10ySplash, "max", EvaluateTargetIfFilterDPPlusHP, EvaluateTargetIfSWD) then return "shadow_word_death main 8"; end
        end
        -- void_blast,target_if=max:(dot.devouring_plague.remains*1000+target.time_to_die),if=(dot.devouring_plague.remains>=execute_time|buff.entropic_rift.remains<=gcd.max|action.void_torrent.channeling&talent.void_empowerment)&(insanity.deficit>=16|cooldown.mind_blast.full_recharge_time<=gcd.max|buff.entropic_rift.remains<=gcd.max)&(!talent.mind_devourer|!buff.mind_devourer.up|buff.entropic_rift.remains<=gcd.max)
        if S.VoidBlastAbility:IsReady() and ((Player:InsanityDeficit() >= 16 or S.MindBlast:FullRechargeTime() <= GCDMax or EntropicRiftRemains <= GCDMax) and (not S.MindDevourer:IsAvailable() or Player:BuffDown(S.MindDevourerBuff) or EntropicRiftRemains <= GCDMax)) then
          if CastTargetIf(S.VoidBlast, Enemies10ySplash, "max", EvaluateTargetIfFilterDPPlusTTD, EvaluateTargetIfVoidBlastMain) then return "void_blast main 10"; end
        end
        -- devouring_plague,target_if=max:target.time_to_die*(dot.devouring_plague.remains<=gcd.max|variable.dr_force_prio|!talent.distorted_reality&variable.me_force_prio),if=buff.voidform.up&talent.perfected_form&buff.voidform.remains<=gcd.max&talent.void_eruption
        if S.DevouringPlague:IsReady() and (Player:BuffUp(S.VoidformBuff) and S.PerfectedForm:IsAvailable() and Player:BuffRemains(S.VoidformBuff) <= GCDMax and S.VoidEruption:IsAvailable()) then
          if CastTargetIf(S.DevouringPlague, Enemies10ySplash, "max", EvaluateTargetIfFilterTTDTimesDP, nil) then return "devouring_plague main 12"; end
        end
        -- wait,sec=cooldown.mind_blast.recharge_time,if=cooldown.mind_blast.recharge_time<buff.entropic_rift.remains&buff.entropic_rift.up&buff.entropic_rift.remains<gcd.max&cooldown.mind_blast.charges<1
        if S.MindBlast:Recharge() < EntropicRiftRemains and EntropicRiftUp and EntropicRiftRemains < GCDMax and S.MindBlast:Charges() < 1 then
          if Cast(S.Pool) then return "Wait for Mind Blast"; end
        end
        -- mind_blast,if=talent.void_eruption&buff.voidform.up&full_recharge_time<=gcd.max&(!talent.insidious_ire|dot.devouring_plague.remains>=execute_time)&(cooldown.void_bolt.remains%gcd.max-cooldown.void_bolt.remains%%gcd.max)*gcd.max<=0.25&(cooldown.void_bolt.remains%gcd.max-cooldown.void_bolt.remains%%gcd.max)>=0.01
        if S.MindBlast:IsReady() and (Player:BuffUp(S.VoidformBuff) and S.MindBlast:FullRechargeTime() <= GCDMax and (not S.InsidiousIre:IsAvailable() or Target:DebuffRemains(S.DevouringPlagueDebuff) >= S.MindBlast:ExecuteTime()) and (S.VoidBolt:CooldownRemains() / GCDMax - S.VoidBolt:CooldownRemains() % GCDMax) * GCDMax <= 0.25 and (S.VoidBolt:CooldownRemains() / GCDMax - S.VoidBolt:CooldownRemains() % GCDMax) >= 0.01) then
          if Cast(S.MindBlast) then return "mind_blast main 14"; end
        end
        -- void_bolt,target_if=max:target.time_to_die,if=insanity.deficit>16&cooldown.void_bolt.remains%gcd.max<=0.1
        if S.VoidBolt:IsReady() and (Player:InsanityDeficit() > 16 and S.VoidBolt:CooldownRemains() / Player:GCD() <= 0.1) then
          if CastTargetIf(S.VoidBolt, Enemies10ySplash, "max", EvaluateTargetIfFilterTTD, nil) then return "void_bolt main 16"; end
        end
        -- devouring_plague,target_if=max:target.time_to_die*(dot.devouring_plague.remains<=gcd.max|variable.dr_force_prio|!talent.distorted_reality&variable.me_force_prio),if=active_dot.devouring_plague<=1&dot.devouring_plague.remains<=gcd.max&(!talent.void_eruption|cooldown.void_eruption.remains>=gcd.max*3)|insanity.deficit<=16
        if S.DevouringPlague:IsReady() then
          if CastTargetIf(S.DevouringPlague, Enemies10ySplash, "max", EvaluateTargetIfFilterTTDTimesDP, EvaluateTargetIfDPMain) then return "devouring_plague main 18"; end
        end
        -- void_torrent,target_if=max:(dot.devouring_plague.remains*1000+target.time_to_die),if=(dot.devouring_plague.ticking|talent.void_eruption&cooldown.void_eruption.up)&talent.entropic_rift&!variable.holding_crash&(cooldown.dark_ascension.remains>=12|!talent.dark_ascension|!talent.void_blast)
        if S.VoidTorrent:IsReady() and (S.EntropicRift:IsAvailable() and not VarHoldingCrash and (S.DarkAscension:CooldownRemains() >= 12 or not S.DarkAscension:IsAvailable() or not S.VoidBlast:IsAvailable())) then
          if CastTargetIf(S.VoidTorrent, Enemies10ySplash, "max", EvaluateTargetIfFilterDPPlusTTD, EvaluateTargetIfVTMain) then return "void_torrent main 20"; end
        end
        -- void_bolt,target_if=max:target.time_to_die,if=cooldown.void_bolt.remains<=0.1
        if S.VoidBolt:IsReady() and (S.VoidBolt:CooldownRemains() <= 0.1) then
          if CastTargetIf(S.VoidBolt, Enemies10ySplash, "max", EvaluateTargetIfFilterTTD, nil) then return "void_bolt main 22"; end
        end
        -- vampiric_touch,target_if=min:remains,if=buff.unfurling_darkness.up&active_dot.vampiric_touch<=5
        if S.VampiricTouch:IsReady() and (Player:BuffUp(S.UnfurlingDarknessBuff) and S.VampiricTouchDebuff:AuraActiveCount() <= 5) then
          if Cast(S.VampiricTouch) then return "vampiric_touch main 24"; end
        end
        -- call_action_list,name=empowered_filler,if=(buff.mind_spike_insanity.stack>2&talent.mind_spike|buff.mind_flay_insanity.stack>2&!talent.mind_spike)&talent.empowered_surges&!cooldown.void_eruption.up
        if (Player:BuffStack(S.MindSpikeInsanityBuff) > 2 and S.MindSpike:IsAvailable() or Player:BuffStack(S.MindFlayInsanityBuff) > 2 and not S.MindSpike:IsAvailable()) and S.EmpoweredSurges:IsAvailable() and S.VoidEruption:CooldownDown() then
          local ShouldReturn = EmpoweredFiller(); if ShouldReturn then return ShouldReturn; end
        end
        -- call_action_list,name=heal_for_tof,if=!buff.twist_of_fate.up&buff.twist_of_fate_can_trigger_on_ally_heal.up&(talent.rhapsody|talent.divine_star|talent.halo)
        if Player:BuffDown(S.TwistofFateBuff) and CanToF() then
          local ShouldReturn = HealForToF(); if ShouldReturn then return ShouldReturn; end
        end
        -- devouring_plague,if=fight_remains<=duration+4
        if S.DevouringPlague:IsReady() and (FightRemains <= S.DevouringPlagueDebuff:BaseDuration() + 4) then
          if Cast(S.DevouringPlague) then return "devouring_plague main 26"; end
        end
        -- devouring_plague,target_if=max:target.time_to_die*(dot.devouring_plague.remains<=gcd.max|variable.dr_force_prio|!talent.distorted_reality&variable.me_force_prio),if=insanity.deficit<=35&talent.distorted_reality|buff.mind_devourer.up&cooldown.mind_blast.up&(cooldown.void_eruption.remains>=3*gcd.max|!talent.void_eruption)&talent.mind_devourer|buff.entropic_rift.up|buff.voidform.up&talent.perfected_form&talent.void_eruption
        if S.DevouringPlague:IsReady() and (Player:InsanityDeficit() <= 35 and S.DistortedReality:IsAvailable() or Player:BuffUp(S.MindDevourerBuff) and S.MindBlast:CooldownUp() and (S.VoidEruption:CooldownRemains() >= 3 * GCDMax or not S.VoidEruption:IsAvailable()) and S.MindDevourer:IsAvailable() or EntropicRiftUp or Player:BuffUp(S.VoidformBuff) and S.PerfectedForm:IsAvailable() and S.VoidEruption:IsAvailable()) then
          if CastTargetIf(S.DevouringPlague, Enemies10ySplash, "max", EvaluateTargetIfFilterTTDTimesDP, nil) then return "devouring_plague main 28"; end
        end
        -- void_torrent,target_if=max:(dot.devouring_plague.remains*1000+target.time_to_die),if=!variable.holding_crash&!talent.entropic_rift&cooldown.mind_blast.full_recharge_time>=2,target_if=dot.devouring_plague.remains>=2.5
        if S.VoidTorrent:IsReady() and (not VarHoldingCrash and not S.EntropicRift:IsAvailable() and S.MindBlast:FullRechargeTime() >= 2) then
          if Target:DebuffRemains(S.DevouringPlagueDebuff) >= 2.5 then
            if Cast(S.VoidTorrent) then return "void_torrent main 30 (primary target)"; end
          else
            if CastTargetIf(S.VoidTorrent, Enemies10ySplash, "max", EvaluateTargetIfFilterDPPlusTTD, EvaluateTargetIfDPMain2) then return "void_torrent main 32 (off-target)"; end
          end
        end
        -- shadow_crash,target_if=dot.vampiric_touch.refreshable,if=!variable.holding_crash&(!talent.unfurling_darkness|spell_targets.shadow_crash>1)
        if Crash:IsCastable() and (not VarHoldingCrash and (not S.UnfurlingDarkness:IsAvailable() or EnemiesCount10ySplash > 1)) then
            if Cast(Crash, EvaluateCycleVTRefreshable) then return "shadow_crash main 34"; end
        end
        -- vampiric_touch,target_if=min:remains,if=buff.unfurling_darkness_cd.remains<execute_time&talent.unfurling_darkness&!buff.dark_ascension.up&talent.inner_quietus&active_dot.vampiric_touch<=5
        if S.VampiricTouch:IsReady() and ((15 - S.UnfurlingDarknessBuff:TimeSinceLastAppliedOnPlayer()) < S.VampiricTouch:ExecuteTime() and S.UnfurlingDarkness:IsAvailable() and Player:BuffDown(S.DarkAscensionBuff) and S.InnerQuietus:IsAvailable() and S.VampiricTouchDebuff:AuraActiveCount() <= 5) then
          if Cast(S.VampiricTouch) then return "vampiric_touch main 36"; end
        end
        -- vampiric_touch,target_if=max:(refreshable*10000+target.time_to_die)*(dot.vampiric_touch.ticking|!variable.dots_up),if=refreshable&target.time_to_die>12&(dot.vampiric_touch.ticking|!variable.dots_up)&(variable.max_vts>0|active_enemies=1)&(cooldown.shadow_crash.remains>=dot.vampiric_touch.remains|variable.holding_crash|!talent.whispering_shadows)&(!action.shadow_crash.in_flight|!talent.whispering_shadows)
        if S.VampiricTouch:IsReady() and ((VarMaxVTs > 0 or EnemiesCount10ySplash == 1) and (not Crash:InFlight() or not S.WhisperingShadows:IsAvailable())) then
          if CastTargetIf(S.VampiricTouch, Enemies10ySplash, "max", EvaluateTargetIfFilterVTRefresh, EvaluateTargetIfVTMain2) then return "vampiric_touch main 38"; end
        end
        -- mind_blast,target_if=max:dot.devouring_plague.remains,if=(!buff.mind_devourer.up|!talent.mind_devourer|cooldown.void_eruption.up&talent.void_eruption)&!variable.pooling_mindblasts
        if S.MindBlast:IsReady() and ((Player:BuffDown(S.MindDevourerBuff) or not S.MindDevourer:IsAvailable() or S.VoidEruption:CooldownUp() and S.VoidEruption:IsAvailable()) and not VarPoolingMindblasts) then
          if CastTargetIf(S.MindBlast, Enemies10ySplash, "max", EvaluateTargetIfFilterDPRemains, nil) then return "mind_blast main 40"; end
        end
        -- devouring_plague,target_if=max:target.time_to_die*(dot.devouring_plague.remains<=gcd.max|variable.dr_force_prio|!talent.distorted_reality&variable.me_force_prio),if=buff.voidform.up&talent.perfected_form&talent.void_eruption
        if S.DevouringPlague:IsReady() and (Player:BuffUp(S.VoidformBuff) and S.PerfectedForm:IsAvailable() and S.VoidEruption:IsAvailable()) then
          if CastTargetIf(S.DevouringPlague, Enemies10ySplash, "max", EvaluateTargetIfFilterTTDTimesDP, nil) then return "devouring_plague main 42"; end
        end
        -- call_action_list,name=filler
        local ShouldReturn = Filler(); if ShouldReturn then return ShouldReturn; end
      end

    local function APL()
        Tanks, Healers, Members, Damagers, Melees = HealingEngine:Fetch()

        if Members and #Members > 0 then
            -- Create copies to avoid modifying the original HealingEngine tables
            Priest.MembersPI = {}
            Priest.MembersPIAuto = {}
            
            -- Copy Members table
            for i = 1, #Members do
                Priest.MembersPI[i] = Members[i]
            end
            
            -- Copy Damagers table
            if Damagers then
                for i = 1, #Damagers do
                    Priest.MembersPIAuto[i] = Damagers[i]
                end
            end
            
            Priest.SortMembersPI(Priest.MembersPI, Enemies40y and #Enemies40y or 0)
        end

        InCombat = Player:AffectingCombat()
        TargetIsValid = M.TargetIsValid()
        InDungeon = Player:IsInDungeonArea()
        InRaid = Player:IsInRaidArea()

        if AoEON() then
            Enemies40y = Player:GetEnemiesInRangeCombat(40)
            Enemies10ySplash = Target:GetEnemiesInSplashRange(10)
        else
            Enemies40y = {Target}
            Enemies10ySplash = {Target}
        end
        EnemiesCount10ySplash = #Enemies10ySplash
        EnemiesCount40y = #Enemies40y

        if AoEON() and EnemiesCount10ySplash < 3 then
            EnemiesCount10ySplash = EnemiesCount40y
        end
        
        -- Custom: Must Removed InFlight manually in case ShadowCrash didn't hit any target. 
        -- Else it will return InFlight for ever since there is no event triggered when groundspell is missed.
        -- if S.ShadowCrash:InFlight() and S.ShadowCrash:TimeSinceLastCast() > 2 then
        --     S.ShadowCrash:RemoveInFlight()
        -- end
    
        local SpreadVT = MainAddon.Toggle:GetToggle('SpreadVT')
        local SpreadVTNeverEnding = MainAddon.Toggle:GetToggle('SpreadVTNeverEnding')
        if SpreadVT or SpreadVTNeverEnding then
            if TargetIsValid then
                if CastCycle(S.VampiricTouch, Enemies40y, EvaluateCycleVTToggle) then 
                    return "Spread Vampiric Toggle" 
                end
            end

            if SpreadVTNeverEnding then
                return
            end

            if SpreadVT then
                if UnitWithVT(Enemies40y) < EnemiesCount40y then
                    return
                else
                    MainAddon.Toggle:SetToggle('SpreadVT', false)
                end
            end
        end

        local ReTarget = nil
        -- Re-Target DP
        if GetSetting('retarget_DP', 3) ~= 3 then
            if S.VoidTorrent:IsAvailable() and UnitWithDP(Enemies40y) > 0 then
                for i, ThisUnit in pairs(Enemies40y) do
                    if EvaluateDP(ThisUnit) then
                        MainAddon.Nameplate.AddIcon(ThisUnit, S.VoidTorrent, true)
                        if not ReTarget then
                            ReTarget = GetTime() - 0.15
                        end
                    end
                end
                if GetSetting('retarget_DP', 3) == 1 then
                    if MouseOver:Exists() and MouseOver:DebuffUp(S.DevouringPlagueDebuff) then
                        MainAddon.SetTopColor(1, "Target Mouseover")
                    end
                end
                if GetSetting('retarget_DP', 3) == 2 then
                    if ReTarget and ReTarget < GetTime() - 0.15 then
                        MainAddon.SetTopColor(1, "Target Enemy")
                        ReTarget = GetTime()
                    end  
                end
            else
                ReTarget = nil
            end
        end

        MOCheck = (MouseOver:IsEnemy() or MouseOver:IsATank() or MouseOver:IsAMelee())

        if TargetIsValid or InCombat then
            -- Calculate fight_remains
            BossFightRemains = HL.BossFightRemains()
            FightRemains = BossFightRemains
            if FightRemains == 11111 then
                FightRemains = HL.FightRemains(Enemies10ySplash, false)
            end

            -- Check our fiend status
            FiendUp = Fiend:TimeSinceLastCast() <= 15
            FiendRemains = mathmax(15 - Fiend:TimeSinceLastCast(), 0)

            -- Check out Entropic Rift status
            if S.EntropicRift:IsAvailable() then
                EntropicRiftUp = S.VoidTorrent:TimeSinceLastCast() <= 8
                EntropicRiftRemains = mathmax(8 - S.VoidTorrent:TimeSinceLastCast(), 0)
                PowerSurgeUp = false
                PowerSurgeRemains = 0
            else
                EntropicRiftUp = false
                EntropicRiftRemains = 0
                PowerSurgeUp = S.Halo:TimeSinceLastCast() <= 10
                PowerSurgeRemains = mathmax(10 - S.Halo:TimeSinceLastCast(), 0)
            end

            -- If MF:Insanity buff is up, change which flay we use
            Flay = (Player:BuffUp(S.MindFlayInsanityBuff)) and S.MindFlayInsanity or S.MindFlay

            -- Calculate GCDMax for gcd.max
            GCDMax = Player:GCD() + 0.25

            -- MainAddon.UpdateVariable("DebuffRemains", Target:DebuffRemains(S.DevouringPlagueDebuff))
            -- MainAddon.UpdateVariable("GCDMax", GCDMax)
            -- MainAddon.UpdateVariable("Condition result", Target:DebuffRemains(S.DevouringPlagueDebuff) <= GCDMax)

            -- Override Channel
            --MainAddon.IgnoreChannel = false
            if Player:IsChanneling(S.MindFlayInsanity) then
                return
            end

            if Player:IsChanneling(Flay) then
                if MainAddon.IgnoreChannel then
                    -- Mind Flay
                    if (GetTime() - Player:ChannelStart()) < S.MindFlay:TickTime() then
                        return
                    end
                end
            else
                if Flay:TimeSinceLastDisplay() > GCDMax then
                    MainAddon.IgnoreChannel = false
                end
            end
        end

        -- Burst Potion
        if Target:IsSpellInRange(S.VampiricTouch) then
            if MainAddon.UsePotion() then
                MainAddon.SetTopColor(1, "Combat Potion")
            end
        end

        -- Defensive
        ShouldReturn = Defensives();
        if ShouldReturn then
            return ShouldReturn;
        end
        -- Utilities
        ShouldReturn = Utilities();
        if ShouldReturn then
            return ShouldReturn;
        end

        if InDungeon then
            -- Spiteful Apparitions
            if MainAddon.IsCurrentAffix("Spiteful") then
                if S.DominateMind:IsReady() and not Pet:IsActive() then
                    if CastTargetIf(S.DominateMind, Enemies40y, "max", EvaluateSpiteful, IsSpiteful) then
                        return "Dominate Mind - Spiteful"
                    end
                end
            end
        end

        if TargetIsValid then
            -- call precombat
            if not Player:AffectingCombat() then
              ShouldReturn = Precombat(); if ShouldReturn then return ShouldReturn; end
            end

            -- Trinkets
            local shouldReturn = MainAddon.TrinketDPS()
            if shouldReturn then
                return shouldReturn
            end  

            -- variable,name=holding_crash,op=set,value=raid_event.adds.in<15
            -- Note: We have no way of knowing if adds are coming, so don't ever purposely hold crash
            VarHoldingCrash = false
            PreferVT = EnemiesCount10ySplash == 1 and Player:IsInDungeonArea() and Player:IsInParty() and not Player:IsInRaidArea()
            -- variable,name=pool_for_cds,op=set,value=(cooldown.void_eruption.remains<=gcd.max*3&talent.void_eruption|cooldown.dark_ascension.up&talent.dark_ascension)|talent.void_torrent&talent.psychic_link&cooldown.void_torrent.remains<=4&(!raid_event.adds.exists&spell_targets.vampiric_touch>1|raid_event.adds.in<=5|raid_event.adds.remains>=6&!variable.holding_crash)&!buff.voidform.up
            VarPoolForCDs = ((S.VoidEruption:CooldownRemains() <= Player:GCD() * 3 and S.VoidEruption:IsAvailable() or S.DarkAscension:CooldownUp() and S.DarkAscension:IsAvailable()) or S.VoidTorrent:IsAvailable() and S.PsychicLink:IsAvailable() and S.VoidTorrent:CooldownRemains() <= 4 and Player:BuffDown(S.VoidformBuff))
            -- call_action_list,name=aoe,if=active_enemies>2
            if EnemiesCount10ySplash > 2 then
                ShouldReturn = AoE(); if ShouldReturn then return ShouldReturn; end
            end
            -- run_action_list,name=main
            ShouldReturn = Main(); if ShouldReturn then return ShouldReturn; end
            if Cast(S.Pool) then return "Pool for Main()"; end
        end
    end

    local function Init()
        S.VampiricTouchDebuff:RegisterAuraTracking()
        S.DevouringPlagueDebuff:RegisterAuraTracking()
        HL.SplashEnemies.SplashCleanerFrameUpdateFrequency = 50
    end
    M.SetAPL(258, APL, Init)
  
    local InsanityPowerType = _G['Enum'].PowerType.Insanity
    HL.AddCoreOverride ("Player.Insanity",
    function ()
    ---@diagnostic disable-next-line: param-type-mismatch
      local Insanity = UnitPower("Player", InsanityPowerType)
      if MainAddon.PlayerSpecID() == 258 then
            if not Player:IsCasting() then
                return Insanity
            else
                if Player:IsCasting(S.MindBlast) then
                    return Insanity + 6
                elseif Player:IsCasting(S.VampiricTouch) or Player:IsCasting(S.MindSpike) then
                    return Insanity + 4
                elseif Player:IsCasting(S.MindFlay) then
                    return Insanity + (12 / S.MindFlay:BaseDuration())
                elseif Player:IsCasting(S.DarkAscension) then
                    return Insanity + 30
                elseif Player:IsCasting(S.VoidTorrent) then
                    return Insanity + (60 / S.VoidTorrent:BaseDuration())
                else
                    return Insanity
                end
            end 
        end
        return Insanity
    end
  , 258)

    local OldShadowIsCastable
    OldShadowIsCastable = HL.AddCoreOverride("Spell.IsCastable",
            function(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                local BaseCheck, Reason = OldShadowIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                if MainAddon.PlayerSpecID() == 258 then
                    if self == Fiend then
                        if Player:IsMoving() then
                            if S.VoidEruption:CooldownUp() or S.DarkAscension:CooldownUp() then
                                return false, "Player is moving"
                            end
                        end
                    end

                    if self == S.ShadowCrashTarget then
                        if S.ShadowCrashTarget:IsAvailable() then
                            if GetSetting('sc_speed_check', false) then
                                if not EvaluateUnitSpeed(Target) then
                                    return false, "Target is moving too fast"
                                end
                            end
                        end
                    end

                    if self == S.Fade then
                        if MainAddon.SpecialCase_FreedomBlacklist() then
                            return false, "Freedom Blacklist"
                        end
                    end
                    
                    if self == S.MindFlay then
                        if Player:IsChanneling(S.MindFlay) or Player:IsChanneling(S.MindFlayInsanity) then
                            return false, "Already Channeling Mind Flay"
                        end
                    end

                    if self == S.VampiricTouch then
                        return BaseCheck and (not S.ShadowCrash:InFlight() or S.ShadowCrash:TimeSinceLastCast() > Player:GCD()) and (not S.ShadowCrashTarget:InFlight() or S.ShadowCrashTarget:TimeSinceLastCast() > Player:GCD()) and (S.UnfurlingDarkness:IsAvailable() or not Player:IsCasting(self))
                    elseif self == S.MindBlast then
                        return BaseCheck and (self:Charges() > 0)
                    elseif self == S.VoidBlast then
                        return BaseCheck and (self:Charges() > 0)   
                    elseif self == S.VoidEruption or self == S.DarkAscension then
                        return BaseCheck and not Player:IsCasting(self)
                    elseif self == S.VoidBolt then
                        return BaseCheck or Player:IsCasting(S.VoidEruption)
                    elseif self == S.Halo then
                        return BaseCheck and not Player:IsCasting(self)
                    -- elseif self == S.VoidBlastAbility then
                    --     return BaseCheck and not Player:IsCasting(self)
                    else
                        return BaseCheck, Reason
                    end
                end
                return BaseCheck, Reason
            end
    , 258)

    local OldShadowCharges
    OldShadowCharges = HL.AddCoreOverride("Spell.Charges",
            function(self)
                local BaseCheck = OldShadowCharges(self)
                if MainAddon.PlayerSpecID() == 258 then
                    if self == S.MindBlast then
                        return BaseCheck - num(Player:IsCasting(S.MindBlast))
                    elseif self == S.VoidBlast then
                        return BaseCheck - num(Player:IsCasting(S.VoidBlast))
                    else
                        return BaseCheck
                    end
                end
                return BaseCheck
            end
    , 258)

    local OldShadowUnitBuffUp
    OldShadowUnitBuffUp = HL.AddCoreOverride("Unit.DebuffRemains",
        function (self, ThisSpell, AnyCaster, Offset)
            local BaseCheck = OldShadowUnitBuffUp(self, ThisSpell, AnyCaster, Offset)
            if MainAddon.PlayerSpecID() == 258 then
                if ThisSpell == S.VampiricTouchDebuff or ThisSpell == S.VampiricTouch then
                    if Player:IsCasting(S.VampiricTouch) then
                        if TempBlacklistVT[self:Name()] then
                            return 20
                        end
                    end
                end
            end

            return BaseCheck
        end
    , 258)

    local OldShadowBuffUp
        OldShadowBuffUp = HL.AddCoreOverride("Player.BuffUp",
        function (self, ThisSpell, AnyCaster, Offset)
            local BaseCheck = OldShadowBuffUp(self, ThisSpell, AnyCaster, Offset)
            if MainAddon.PlayerSpecID() == 258 then
                if ThisSpell == S.VoidformBuff then
                    return BaseCheck or Player:IsCasting(S.VoidEruption)
                elseif ThisSpell == S.DarkAscensionBuff then
                    return BaseCheck or Player:IsCasting(S.DarkAscension)
                else
                    return BaseCheck
                end
            end
            return BaseCheck
        end
    , 258)

    local OldIsReady
    OldIsReady = HL.AddCoreOverride("Spell.IsReady",
            function(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
                local BaseCheck, Reason = OldIsReady(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
                if MainAddon.PlayerSpecID() == 258 then
                    if GetSetting('MOOption') then
                        if self == S.ShadowCrash then
                            if not MOCheck or HL.CombatTime() < 1 then
                                return false, "MOCheck is false"
                            end
                        end
                    end

                    local UnitTarget = TargetUnit or Target
                    BypassRecovery = false
                    ignoreChannel = true

                    -- Avoid wrong CooldownRemains value due to cooldown prediction.
                    -- Without this, it returns true even if the spell isn't ready yet, due to prediction.
                    -- And we don't want this because we are going to stop channeling.
                    if Player:IsChanneling() and not Player:IsChanneling(S.VoidTorrent) then
                        BypassRecovery = true
                    end

                    -- Don't interrupt any cast for SWP filler
                    -- if self ~= S.ShadowWordPain or UnitTarget:DebuffRefreshable(S.ShadowWordPainDebuff) then
                    --   -- Any spells besides Mind Flay.
                    --   if self ~= S.MindFlay and self ~= S.MindFlayInsanity then
                    --       -- Mind Flay
                    --       if Player:IsChanneling(S.MindFlay) and (GetTime() - Player:ChannelStart()) > S.MindFlay:TickTime() then
                    --           IgnoreChannel = true
                    --       end
                    --       -- Chain Mind flay near the end of channeling.
                    --   else
                    --       if Player:IsChanneling() and Player:ChannelRemains() < self:TickTime() then
                    --           IgnoreChannel = true
                    --       end
                    --   end
                    -- end

                    if self == S.MindFlay then
                        ignoreChannel = false
                    end
                    -- Don't interrupt Void Torrent
                    if Player:IsChanneling(S.VoidTorrent) then
                        ignoreChannel = false
                    end

                    if self == S.MindSpikeInsanity then
                        return BaseCheck and (Player:BuffStack(S.MindSpikeInsanityBuff) - num(Player:IsCasting(S.MindSpikeInsanity)) > 0)
                    else
                        return BaseCheck, Reason
                    end
                end
                return BaseCheck, Reason
            end
    , 258);

    -- Prevent double Vampiric Touch cast
    HL:RegisterForEvent(function(arg1, arg2, arg3, arg4, arg5)
        if arg2 == "player" then
            if arg5 == 34914 then
                TempBlacklistVT[arg3] = true
            end
        end
    end, "UNIT_SPELLCAST_SENT")

  HL:RegisterForEvent(function(arg1, arg2, arg3, arg4)
        if arg2 == "player" then
            if arg4 == 34914 then
                wipe(TempBlacklistVT)
            end
        end
  end, "UNIT_SPELLCAST_INTERRUPTED", "UNIT_SPELLCAST_FAILED")

  HL:RegisterForEvent(function(...)
      local _, subevent, _, sourceGUID, _, _, _, _, destName, _, _, spellId, spellName = CombatLogGetCurrentEventInfo()
      if sourceGUID == Player:GUID() and (subevent == "SPELL_CAST_SUCCESS" or subevent == "SPELL_CAST_START") 
      and spellId ~= 34914 -- VampiricTouch
      and spellId ~= 19236 -- Desperate Prayer
      and spellId ~= 586 -- Fade
      and spellId ~= 15286 -- Vampiric Embrace 
      and spellId ~= 73325 -- Leap of Faith 
      and spellId ~= 15487 -- Silence 
      and spellId ~= 108968 -- Void Shift
      and spellId ~= 10060 -- Power Infusion
      then
          if TempBlacklistVT[destName] then
            TempBlacklistVT[destName] = nil
          end
      end

      if sourceGUID == Player:GUID() and (subevent == 'SPELL_AURA_APPLIED' or subevent == 'SPELL_AURA_REFRESH') then
          if spellId == 34914 then
              if TempBlacklistVT[destName] then
                TempBlacklistVT[destName] = nil
              end
          end
      end
  end, "COMBAT_LOG_EVENT_UNFILTERED")

  HL:RegisterForEvent(
      function()
          -- Update Evocation
          if S.DarkEnergy:IsAvailable() then
              MainAddon.CONST.SpellIsChannel[S.VoidTorrent:ID()].StandStill = false
          else
              MainAddon.CONST.SpellIsChannel[S.VoidTorrent:ID()].StandStill = true
          end
      end, "PLAYER_ENTERING_WORLD", "UPDATE_CHAT_WINDOWS", "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB"
    )

    local OldShadowInFlight
    OldShadowInFlight = HL.AddCoreOverride("Spell.InFlight",
    function(self)
        local BaseCheck = OldShadowInFlight(self)
        if MainAddon.PlayerSpecID() == 258 then
            if self == S.ShadowCrash then
                return S.ShadowCrash:TimeSinceLastCast() < 2
            else
                return BaseCheck
            end
        end
        return BaseCheck
    end
    , 258)
end