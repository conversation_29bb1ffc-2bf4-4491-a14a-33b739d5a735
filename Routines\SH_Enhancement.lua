  function A_263(...)
    -- HR UPDATE: feat(Enhancement): Update to latest APL for 11.1.5  09.06.2025
    -- YUNO: some custom stuff added, enabled always.
    -- REMEMBER: MainAddon.Toggle:GetToggle('HoldMaelstrom') & MaelstromStacks = 0
    -- REMEMBER: GetSetting('EEDPS', false)
    -- REMEMBER: GetSetting('TempestOnMTOnly', true)
    -- Addon
    ---@class MainAddon
    local MainAddon = MainAddon
    local M = MainAddon
    -- HeroLib
    local HL = HeroLibEx
    ---@class Unit
    local Unit = HL.Unit
    ---@class Unit
    local Player = Unit.Player
    ---@class Unit
    local Target = Unit.Target
    ---@class Unit
    local Party = Unit.Party
    ---@class Spell
    local Spell = HL.Spell
    ---@class Item
    local Item = HL.Item
    local AoEON = M.AoEON
    -- HeroRotation
    local Cast = M.Cast
    local CastTargetIf = MainAddon.CastTargetIf
    local CastCycle = MainAddon.CastCycle
    local CastMagic = MainAddon.CastMagic
    -- Lua
    local GetSpellBonusDamage  = _G['GetSpellBonusDamage']
    local GetWeaponEnchantInfo = _G['GetWeaponEnchantInfo']
    local GetTime = GetTime
    local IsSpellKnown = _G['IsSpellKnown']
    local StaticPopup_Show = _G['StaticPopup_Show']
    local GetTime = _G['GetTime']
    local C_Timer = _G['C_Timer']
    local C_PaperDollInfo = _G['C_PaperDollInfo']
    local GetNumGroupMembers = _G['GetNumGroupMembers']
    local UnitGroupRolesAssigned = _G['UnitGroupRolesAssigned']
    local IsInGroup = _G['IsInGroup']
    local num = M.num
    local mathmin = math.min
    local mathmax = math.max

    ---@class Shaman
    local Shaman = M.Shaman

    local S = Spell.Shaman.Enhancement
    local I = Item.Shaman.Enhancement

    MainAddon.Toggle.Special["Funneling"] = {
        Icon = MainAddon.GetTexture(S.Stormstrike),
        Name = "Funneling",
        Description = "Use the Funnel rotation.",
        Spec = 263
    }

    MainAddon.Toggle.Special["MaelstromHealing"] = {
        Icon = MainAddon.GetTexture(S.HealingSurge),
        Name = "Maelstrom Healing",
        Description = "Spend Maelstrom stacks into Healing Surge.",
        Spec = 263
    }

    MainAddon.Toggle.Special["HoldMaelstrom"] = {
        Icon = MainAddon.GetTexture(S.MaelstromWeaponBuff),
        Name = "Hold Maelstrom",
        Description = "Hold Maelstrom stacks.",
        Spec = 263
    }

    MainAddon.Toggle.Special["PoolTempest"] = {
        Icon = MainAddon.GetTexture(S.TempestAbility),
        Name = "Pool Tempest",
        Description = "Pool Tempest Stacks when enabled.",
        Spec = 263
    }

    local OnUseExcludes = {
    }

    -- GUI Settings
    local GetSetting = MainAddon.Config.GetClassSetting
    local Config_Key = MainAddon.GetClassVariableName()
    local Config_Color = '0070DD'
    local Config_Table = {
        key = Config_Key,
        title = 'Shaman - Enhancement',
        subtitle = '?? ' .. MainAddon.Version,
        width = 600,
        height = 700,
        profiles = true,
        config = {
            { type = 'header', text = '', size = 18, align = 'Center', color = Config_Color },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },    
            { type = "header", text = '\"Spirits watch over you, walk with honor.\"', size = 16, align = "center", color = Config_Color },
            { type = "header", text = "?? x yuno", size = 12, align = "center", color = '4C4C4C' },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },   
            { type = 'spacer' },
            { type = 'header', text = 'APL', color = Config_Color }, 
            {
              type = 'dropdown',
              text = ' Rotation Mode',
              icon = S.Stormstrike:ID(),
              key = 'rotation_mode',
              list = {
                  { text = 'SimC APL', key = 'APLsimc' },
                  { text = 'Custom Rotation', key = 'APLcustom' }
              },
              default = 'APLcustom'
            },    
            { type = 'spacer' },
            { type = 'header', text = 'DPS', color = Config_Color },
            { type = 'checkbox', text = ' Sundering: Use when moving', icon = S.Sundering:ID(), key = 'SunderingMoving', default = true },
            { type = 'checkbox', text = ' Tempest: Use on main target only', icon = S.TempestAbility:ID(), key = 'TempestOnMTOnly', default = true },
            { type = 'checkspin', text = ' Tempest: Build stacks before Ascendance', icon = S.TempestAbility:ID(), key = 'TempestBuildStacks', min = 0, max = 2, default_spin = 1, default_check = true, },
            { type = 'checkspin', text = 'Check for enemies in melee (in %)', key = 'meleeratio', min = 1, max = 100, default_spin = 10, default_check = true, },
            { type = 'dropdown', text = '    Affected Spells:', 
              key = 'meleeratiospells', 
              multiselect = true, 
              list = { 
                { text = 'Ascendance', key = 'ratioAscendance' }, 
                { text = 'Sundering', key = 'ratioSundering' },
                { text = 'Surging Totem', key = 'ratioSurgingTotem' } 
              }, 
              default = 
              { 'ratioAscendance', 'ratioSundering', 'ratioSurgingTotem' } 
            },
            { type = 'dropdown', text = ' Spread Lightning Rod with:', 
              key = 'rodSetting', 
              icon = S.LightningRodDebuff:ID(),
              multiselect = true, 
              list = { 
                { text = 'Chain Lightning, Elemental Blast', key = 'rodCL' }, 
                { text = 'Windstrike (during Ascendance)', key = 'rodWS' },
              }, 
              default = 
              { 'rodWS' } 
            },
            { type = 'checkbox', text = ' Earth Elemental for DPS', icon = S.EarthElemental:ID(), key = 'EEDPS', default = false },
            { type = 'checkbox', text = ' Tempest: Pool for next pull ', icon = S.TempestAbility:ID(), key = 'tempestpool', default = false },
            { type = 'spinner', text = '        Pool TTD', key = 'tempestpoolttd', min = 1, max = 30, default = 5 },
            { type = 'checkbox', text = ' Disable Dispel and Thunderstorm while in Ascendance', icon = S.Thunderstorm:ID(), key = 'cdnodispel', default = true },
            { type = 'spacer' },
            { type = 'header', text = 'Defensives', color = Config_Color },
            { type = 'checkspin', text = ' Astral Shift', icon = 108271, key = 'AstralShift', min = 1, max = 100, default_spin = 30, default_check = true },
            { type = 'checkspin', text = ' Healing Surge (Raid/Party)', icon = 8004, key = 'HealingSurge', min = 1, max = 100, default_spin = 25, default_check = true },
            { type = 'checkspin', text = ' Healing Surge (Solo)', icon = 8004, key = 'HealingSurgeSolo', min = 1, max = 100, default_spin = 55, default_check = true },
            { type = 'checkspin', text = ' Healing Stream Totem (Raid/Party)', icon = 5394, key = 'HSTGroup', min = 1, max = 100, default_spin = 40, default_check = true },
            { type = 'checkspin', text = ' Healing Stream Totem (Solo)', icon = 5394, key = 'HSTSolo', min = 1, max = 100, default_spin = 30, default_check = false },
            { type = 'checkspin', text = ' Stone Bulwark Totem', icon = S.StoneBulwarkTotem:ID(), key = 'Bulwark', min = 1, max = 99, default_spin = 40, default_check = false },
            { type = 'dropdown', text = 'Earth Elemental usage', key = 'EarthElemental', icon = S.EarthElemental:ID(), multiselect = true, list = { { text = 'On Aggro', key = 'EEDEF_aggro' }, { text = 'Tank is dead', key = 'EEDEF_tank_dead' } }, default = { 'EEDEF_tank_dead' } },
            { type = 'spacer' },
            { type = 'header', text = 'Utilities', color = Config_Color },
            { type = 'checkbox', text = ' Stop Rotation when Ghost Wolf ', icon = 2645, key = 'StopWolf', default = false },
            { type = 'dropdown', text = ' Ghost Wolf (Out of Combat)', icon = 2645, key = 'AutoWolfOOC', list = {{ text = 'Everywhere', key = 1 }, { text = 'Dungeon', key = 2 }, { text = 'Raid', key = 3 }, { text = 'Open world only', key = 5 }, { text = 'None', key = 4 }}, default = 5 },
            { type = 'dropdown', text = ' Ghost Wolf (In Combat)', icon = 2645, key = 'AutoWolfCombat', list = {{ text = 'Everywhere', key = 1 }, { text = 'Dungeon', key = 2 }, { text = 'Raid', key = 3 }, { text = 'Open world only', key = 5 }, { text = 'None', key = 4 }}, default = 4 },            
            { type = 'dropdown', text = ' Primary Shield', key = 'shield',
              list = {
                { text = 'Lightning Shield', key = 1 },
                { text = 'Earth Shield', key = 2 },
              },
              default = 1,
            },
            { type = 'dropdown', text = ' Shield Refreshing', key = 'shield_prio',
                list = {
                    { text = 'High priority', key = 1 },
                    { text = 'Low priority', key = 2 },
                },
                default = 2,
            },
            { type = 'dropdown',
              text = ' Skyfury', key = 'sky',
              icon = S.Skyfury:ID(),
              multiselect = true,
              list = {
                  { text = 'Self', key = 'sky_self' },
                  { text = 'Friends', key = 'sky_friends' },
              },
              default = {
                  'sky_self',
                  'sky_friends'
              },
            },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
        }
    }
    Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
    Config_Table.config = MainAddon.BuildDPSTrinketUI(Config_Table.config, Config_Color)
    Config_Table.config = MainAddon.BuildCombatPotionUI(Config_Table.config, "Enhancement", Config_Color)
    MainAddon.SetConfig(263, Config_Table)

    --- ===== Start Custom =====
    local inDungeon, inRaid, inCombat, isMoving
    local LastWolfTimer = 0
    local LastAlphaWolfTimer = 0
    local FSRange = 10
    local EnemiesFlameShock
    ---@type Unit|nil
    local TankUnit = nil

    local function GetTank()
        ---@param v Unit
        for i, v in pairs(Party) do
            if v:Exists() and UnitGroupRolesAssigned(v:ID()) == "TANK" then
                return v
            end
        end
        return nil
    end
    
    HL:RegisterForSelfCombatEvent(function(_, _, _, _, _, _, _, DestGUID, _, _, _, SpellID)
        if SpellID == 51533 then
            LastWolfTimer = GetTime()
        end
        if SpellID == 187874 or SpellID == 188196 then
            local CurrentTimer = LastWolfTimer + 15 - GetTime()
            if CurrentTimer >= 0 then
                LastAlphaWolfTimer = GetTime()
            end
        end
    end, "SPELL_CAST_SUCCESS")

    --- ===== End Custom =====

    --- ===== Rotation Variables =====
    ---@type Item
    local Trinket1, Trinket2
    local VarTrinket1ID, VarTrinket2ID
    local VarTrinket1Range, VarTrinket2Range
    local VarTrinket1CD, VarTrinket2CD
    local VarTrinket1Ex, VarTrinket2Ex
    local VarTrinket1IsWeird, VarTrinket2IsWeird
    local HasMainHandEnchant, HasOffHandEnchant
    local MHEnchantTimeRemains, OHEnchantTimeRemains
    local MHEnchantID, OHEnchantID
    local MaelstromStacks = 0
    local MaxMaelstromStacks = S.RagingMaelstrom:IsAvailable() and 10 or 5
    local MaxAshenCatalystStacks = 8
    local MaxConvergingStormsStacks = 6
    local MaxTempestStacks = 2
    local VarMinTalentedCDRemains = 1000
    local VarTargetNatureMod, VarExpectedLBFunnel, VarExpectedCLFunnel
    local EnemiesMelee, EnemiesMeleeCount, Enemies40yCount
    local MaxEBCharges = S.LavaBurst:IsAvailable() and 2 or 1
    local TIAction = S.ChainLightning
    local BossFightRemains = 11111
    local FightRemains = 11111

    --- ===== Trinket Variables (from Precombat) =====
    local function SetTrinketVariables()
        Trinket1, Trinket2 = Player:GetTrinketItems()
        VarTrinket1ID = Trinket1:ID()
        VarTrinket2ID = Trinket2:ID()

        if not Trinket1:Level() or not Trinket2:Level() then
            C_Timer.After(3, SetTrinketVariables)
            return
        end

        ---@type Spell
        local Trinket1Spell = Trinket1:OnUseSpell()
        VarTrinket1Range = (Trinket1Spell and Trinket1Spell.MaximumRange > 0 and Trinket1Spell.MaximumRange <= 100) and Trinket1Spell.MaximumRange or 100
        ---@type Spell
        local Trinket2Spell = Trinket2:OnUseSpell()
        VarTrinket2Range = (Trinket2Spell and Trinket2Spell.MaximumRange > 0 and Trinket2Spell.MaximumRange <= 100) and Trinket2Spell.MaximumRange or 100

        VarTrinket1CD = Trinket1:Cooldown() or 0
        VarTrinket2CD = Trinket2:Cooldown() or 0

        VarTrinket1Ex = Player:IsItemBlacklisted(Trinket1)
        VarTrinket2Ex = Player:IsItemBlacklisted(Trinket2)

        -- Note: Just setting to false. Variable references all old DF trinkets.
        VarTrinket1IsWeird = false
        VarTrinket2IsWeird = false
    end
    SetTrinketVariables()

    --- ===== Event Registrations =====
    HL:RegisterForEvent(function()
        MaxEBCharges = S.LavaBurst:IsAvailable() and 2 or 1
    end, "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")

    HL:RegisterForEvent(function()
        SetTrinketVariables()
    end, "PLAYER_EQUIPMENT_CHANGED")

    HL:RegisterForEvent(function()
        TIAction = S.ChainLightning
        BossFightRemains = 11111
        FightRemains = 11111
    end, "PLAYER_REGEN_ENABLED")

    --- ===== Helper Functions =====
    local function RangedTargetCount(range)
        local EnemiesTable = Player:GetEnemiesInRange(range)
        local TarCount = 1
        ---@param Enemy Unit
        for _, Enemy in pairs(EnemiesTable) do
            if Enemy:GUID() ~= Target:GUID() and (Enemy:AffectingCombat() or Enemy:IsDummy()) then
                TarCount = TarCount + 1
            end
        end
        return TarCount
    end

    -- Lighting Rod spread
    local rodSetting = GetSetting("rodSetting", {})

    ---@param Totem Spell
    ---@param ReturnTime boolean?
    local function TotemFinder(Totem, ReturnTime)
        for i = 1, 6, 1 do
          local TotemActive, TotemName, StartTime, Duration = Player:GetTotemInfo(i)
          if Totem:Name() == TotemName then
            if ReturnTime then
              return mathmax(Duration - (GetTime() - StartTime), 0)
            else
              return true
            end
          end
        end

        if ReturnTime then
          return 0
        else
          return false
        end
    end

    local function AlphaWolfMinRemains()
        if not S.AlphaWolf:IsAvailable() or Player:BuffDown(S.FeralSpiritBuff) then return 0 end
        local AWStart = mathmin(S.CrashLightning:TimeSinceLastCast(), S.ChainLightning:TimeSinceLastCast())
        if AWStart > 8 or AWStart > S.FeralSpirit:TimeSinceLastCast() then return 0 end
        return 8 - AWStart
    end

    --- ===== Register Damage Formulas =====
    S.LightningBolt:RegisterDamageFormula(
      function()
        return
          -- Spell Power
          GetSpellBonusDamage(4) *
          -- 131.1% modifier
          1.311 *
          -- Mastery bonus
          (1 + Player:MasteryPct() / 100) *
          -- 3% bonus from Amplification Core if Surging Totem is active
          ((S.AmplificationCore:IsAvailable() and TotemFinder(S.SurgingTotem)) and 1.03 or 1)
      end
    )

    S.ChainLightning:RegisterDamageFormula(
      function()
        local MaxTargets = S.CrashingStorms:IsAvailable() and 5 or 3
        return
          -- Spell Power
          GetSpellBonusDamage(4) *
          -- 73.025% modifier
          0.73025 *
          -- Mastery bonus
          (1 + Player:MasteryPct() / 100) *
          -- Crashing Storms bonus
          (S.CrashingStorms:IsAvailable() and 1.4 or 1) *
          -- 3% bonus from Amplification Core if Surging Totem is active
          ((S.AmplificationCore:IsAvailable() and TotemFinder(S.SurgingTotem)) and 1.03 or 1) *
          -- Targets
          mathmin(EnemiesMeleeCount, MaxTargets)
      end
    )

    --- ===== CastTargetIf Filter Functions =====
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterLightningRodRemains(TargetUnit)
      -- target_if=min:debuff.lightning_rod.remains
      return TargetUnit:DebuffRemains(S.LightningRodDebuff)
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterPrimordialWave(TargetUnit)
        return TargetUnit:DebuffRemains(S.FlameShockDebuff)
    end

    --- ===== CastTargetIf Condition Functions =====
    ---@param TargetUnit Unit
    local function EvaluateTargetIfPrimordialWave(TargetUnit)
        return Player:BuffDown(S.PrimordialWaveBuff)
    end

    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterLavaLash(TargetUnit)
        return TargetUnit:DebuffRemains(S.LashingFlamesDebuff)
    end

    --- ===== CastCycle Functions =====
    ---@param TargetUnit Unit
    local function EvaluateCycleFlameShock(TargetUnit)
        return TargetUnit:DebuffRefreshable(S.FlameShockDebuff)
    end
    ---@param TargetUnit Unit
    local function EvaluateCycleLightningRod(TargetUnit)
      return TargetUnit:DebuffRefreshable(S.LightningRodDebuff)
    end
    ---@param TargetUnit Unit
    local function EvaluateCycleLavaLash(TargetUnit)
        return TargetUnit:DebuffRefreshable(S.LashingFlamesDebuff)
    end
    ---@param TargetUnit Unit
    local function EvaluateCycleLavaLashSpread(TargetUnit)
      return TargetUnit:DebuffUp(S.FlameShockDebuff)
    end


    -- Tempest saving
    local function ShouldCastTempest()
      -- Tempest pool
      if GetSetting('tempestpool', false) then
          if FightRemains <= GetSetting('tempestpoolttd', 8) and (Player:IsInDungeonArea() or Player:IsInRaidArea()) and not Player:InBossEncounter() then
              local TempestBuffStacks = Player:BuffStack(S.TempestBuff)
              if TempestBuffStacks >= MaxTempestStacks * 0.5 and TempestBuffStacks < MaxTempestStacks then
                  return false
              end
          end
      end

      -- Tempest Build Stacks
      if GetSetting("rotation_mode", "APLcustom") == "APLcustom"
      and GetSetting("TempestBuildStacks_check", true)
      then
          local wantStacks = GetSetting("TempestBuildStacks_spin", 1)
          local haveStacks = Player:BuffStack(S.TempestBuff)
          if (S.Ascendance:CooldownUp() or S.Ascendance:CooldownRemains() < 12)
          and haveStacks < wantStacks
          then
              return false
          end
      end

      return true
    end

    local function Precombat()
      -- windfury_weapon
      -- flametongue_weapon
      -- lightning_shield
      -- Note: Moved shields and weapon buffs to APL().
      -- windfury_totem
      if S.WindfuryTotem:IsReady() and (Player:BuffDown(S.WindfuryTotemBuff, true) or S.WindfuryTotem:TimeSinceLastCast() > 90) then
        if Cast(S.WindfuryTotem) then return "windfury_totem precombat 2"; end
      end
      -- variable,name=trinket1_is_weird,value=trinket.1.is.algethar_puzzle_box|trinket.1.is.manic_grieftorch|trinket.1.is.elementium_pocket_anvil|trinket.1.is.beacon_to_the_beyond
      -- variable,name=trinket2_is_weird,value=trinket.2.is.algethar_puzzle_box|trinket.2.is.manic_grieftorch|trinket.2.is.elementium_pocket_anvil|trinket.2.is.beacon_to_the_beyond
      -- Note: Handled in trinket definitions.
      -- variable,name=min_talented_cd_remains,value=((cooldown.feral_spirit.remains%(4*talent.witch_doctors_ancestry.enabled))+1000*!talent.feral_spirit.enabled)>?(cooldown.doom_winds.remains+1000*!talent.doom_winds.enabled)>?(cooldown.ascendance.remains+1000*!talent.ascendance.enabled)
      -- variable,name=target_nature_mod,value=(1+debuff.chaos_brand.up*debuff.chaos_brand.value)*(1+(debuff.hunters_mark.up*target.health.pct>=80)*debuff.hunters_mark.value)
      -- variable,name=expected_lb_funnel,value=action.lightning_bolt.damage*(1+debuff.lightning_rod.up*variable.target_nature_mod*(1+buff.primordial_wave.up*active_dot.flame_shock*buff.primordial_wave.value)*debuff.lightning_rod.value)
      -- variable,name=expected_cl_funnel,value=action.chain_lightning.damage*(1+debuff.lightning_rod.up*variable.target_nature_mod*(active_enemies>?(3+2*talent.crashing_storms.enabled))*debuff.lightning_rod.value)
      -- Note: Moved to APL(), as we probably should be checking this during the fight.
      -- snapshot_stats
      -- Manually added openers:
      -- primordial_wave
      if S.PrimordialWave:IsReady() then
        if Cast(S.PrimordialWave) then return "primordial_wave precombat 4"; end
      end
      -- feral_spirit
      if S.FeralSpirit:IsReady() then
        if Cast(S.FeralSpirit) then return "feral_spirit precombat 6"; end
      end
      -- flame_shock
      if S.FlameShock:IsReady() then
        if Cast(S.FlameShock) then return "flame_shock precombat 8"; end
      end
    end

    local function SingleOpen()
      -- flame_shock,if=!ticking
      if S.FlameShock:IsReady() and (Target:DebuffDown(S.FlameShockDebuff)) then
        if Cast(S.FlameShock) then return "flame_shock single_open 2"; end
      end
      -- voltaic_blaze,if=active_dot.flame_shock<3&!buff.ascendance.up
      if S.VoltaicBlazeAbility:IsReady() and (S.FlameShockDebuff:AuraActiveCount() < 3 and Player:BuffDown(S.AscendanceBuff)) then
        if Cast(S.VoltaicBlazeAbility) then return "voltaic_blaze single_open 4"; end
      end
      -- primordial_wave,if=(buff.maelstrom_weapon.stack>=4)&dot.flame_shock.ticking&(active_dot.flame_shock=active_enemies|active_dot.flame_shock=6)
      if S.PrimordialWave:IsReady() and ((MaelstromStacks >= 4) and Target:DebuffUp(S.FlameShockDebuff) and (S.FlameShockDebuff:AuraActiveCount() == mathmin(EnemiesMeleeCount, 6))) then
        if Cast(S.PrimordialWave) then return "primordial_wave single_open 6"; end
      end
      if Player:BuffUp(S.LegacyoftheFrostWitchBuff) then
        -- feral_spirit,if=buff.legacy_of_the_frost_witch.up
        if S.FeralSpirit:IsReady() then
          if Cast(S.FeralSpirit) then return "feral_spirit single_open 8"; end
        end
        -- doom_winds,if=buff.legacy_of_the_frost_witch.up
        if S.DoomWinds:IsReady() then
          if Cast(S.DoomWinds) then return "doom_winds single_open 10"; end
        end
        -- ascendance,if=buff.legacy_of_the_frost_witch.up
        if AoEON() and S.Ascendance:IsReady() then
          if Cast(S.Ascendance) then return "ascendance single_open 12"; end
        end
      end
      -- primordial_storm,if=(buff.maelstrom_weapon.stack>=9)&(buff.legacy_of_the_frost_witch.up|!talent.legacy_of_the_frost_witch.enabled)
      if S.PrimordialStormAbility:IsReady() and ((MaelstromStacks >= 10) and (Player:BuffUp(S.LegacyoftheFrostWitchBuff) or not S.LegacyoftheFrostWitch:IsAvailable())) then
        if Cast(S.PrimordialStormAbility) then return "primordial_storm single_open 14"; end
      end
      -- windstrike
      if S.Windstrike:IsReady() then
        if Cast(S.Windstrike) then return "windstrike single_open 16"; end
      end
      if MaelstromStacks >= 5 then
        -- elemental_blast,if=buff.maelstrom_weapon.stack>=5
        if S.ElementalBlast:IsReady() then
          if Cast(S.ElementalBlast) then return "elemental_blast single_open 18"; end
        end
        -- tempest,if=buff.maelstrom_weapon.stack>=5
        if S.TempestAbility:IsReady() then
          if Cast(S.TempestAbility) then return "tempest single_open 20"; end
        end
        -- lightning_bolt,if=buff.maelstrom_weapon.stack>=5
        if S.LightningBolt:IsReady() then
          if Cast(S.LightningBolt) then return "lightning_bolt single_open 22"; end
        end
      end
      -- stormstrike
      if S.Stormstrike:IsReady() then
        if Cast(S.Stormstrike) then return "stormstrike single_open 24"; end
      end
      -- crash_lightning,if=set_bonus.tww2_4pc
      if S.CrashLightning:IsReady() and (Player:HasTier("TWW2", 4)) then
        if Cast(S.CrashLightning) then return "crash_lightning single_open 26"; end
      end
      -- voltatic_blaze
      if S.VoltaicBlazeAbility:IsReady() then
        if Cast(S.VoltaicBlazeAbility) then return "voltaic_blaze single_open 28"; end
      end
      -- lava_lash
      if S.LavaLash:IsReady() then
        if Cast(S.LavaLash) then return "lava_lash single_open 30"; end
      end
      -- ice_strike
      if S.IceStrike:IsReady() then
        if Cast(S.IceStrike) then return "ice_strike single_open 32"; end
      end
    end

    local function customSingleOpen()
      -- Local variables for readability
      local MWStacks = MaelstromStacks
      local FSDebuff = S.FlameShockDebuff
      local FSCount = FSDebuff:AuraActiveCount()
      local hasLegacyBuff = Player:BuffUp(S.LegacyoftheFrostWitchBuff)
      
      -- Apply Flame Shock if not active on target
      if S.FlameShock:IsReady() and Target:DebuffDown(FSDebuff) then
        if Cast(S.FlameShock) then return "Flame Shock (opener - apply dot)"; end
      end
      
      -- Use Voltaic Blaze for multi-dotting when not in Ascendance
      if S.VoltaicBlazeAbility:IsReady() and (FSCount < 3 and Player:BuffDown(S.AscendanceBuff)) then
        if Cast(S.VoltaicBlazeAbility) then return "Voltaic Blaze (opener - multi-dot setup)"; end
      end
      
      -- Flame Shock spread via Lava Lash
      if S.LavaLash:IsReady() and (Target:DebuffUp(FSDebuff) and FSCount ~= EnemiesMeleeCount) then
        if Cast(S.FlameShock) then return "Flame Shock (spread via Lava Lash)"; end
      end
      
      -- Primordial Wave when we have enough Maelstrom and optimal Flame Shock spread
      if S.PrimordialWave:IsReady() and ((MWStacks >= 4) and Target:DebuffUp(FSDebuff) and 
        (FSCount == EnemiesMeleeCount or (not S.VoltaicBlazeAbility:IsReady() and not S.LavaLash:IsReady()))) then
        if Cast(S.PrimordialWave) then return "Primordial Wave (opener - with dots ready)"; end
      end
      
      -- Special case for APLcustom mode - Doom Winds follow-up after Primordial Wave
      if GetSetting("rotation_mode", "APLcustom") == "APLcustom" and S.DoomWinds:IsReady() and 
        Player:PrevGCD(1, S.PrimordialWave) then
        if Cast(S.DoomWinds) then return "Doom Winds (immediate PW follow-up)"; end
      end
      
      -- Legacy of the Frost Witch buff utilization
      if hasLegacyBuff then
        -- Feral Spirit during Legacy buff window
        if S.FeralSpirit:IsReady() then
          if Cast(S.FeralSpirit) then return "Feral Spirit (Legacy buff window)"; end
        end
        
        -- Doom Winds during Legacy buff window
        if S.DoomWinds:IsReady() then
          if Cast(S.DoomWinds) then return "Doom Winds (Legacy buff window)"; end
        end
        
        -- Ascendance during Legacy buff window
        if S.Ascendance:IsReady() then
          if Cast(S.Ascendance) then return "Ascendance (Legacy buff window)"; end
        end
      end
      
      -- Primordial Storm with maximum Maelstrom stacks
      if S.PrimordialStormAbility:IsReady() and 
        (Player:BuffUp(S.AscendanceBuff) or not S.Ascendance:CooldownUp()) and 
        ((MWStacks >= 9) and (hasLegacyBuff or not S.LegacyoftheFrostWitch:IsAvailable())) then
        if Cast(S.PrimordialStormAbility) then return "Primordial Storm (max Maelstrom)"; end
      end
      
      -- Windstrike during Ascendance
      if S.Windstrike:IsReady() then
        if Cast(S.Windstrike) then return "Windstrike (Ascendance)"; end
      end
      
      -- Maelstrom spenders when we have 5+ stacks
      if MWStacks >= 5 then
        -- Elemental Blast
        if S.ElementalBlast:IsReady() then
          if Cast(S.ElementalBlast) then return "Elemental Blast (5+ MW stacks)"; end
        end
        
        -- Tempest
        if S.TempestAbility:IsReady() then
          if Cast(S.TempestAbility) then return "Tempest (5+ MW stacks)"; end
        end
        
        -- Lightning Bolt
        if S.LightningBolt:IsReady() then
          if Cast(S.LightningBolt) then return "Lightning Bolt (5+ MW stacks)"; end
        end
      end
      
      -- Stormstrike
      if S.Stormstrike:IsReady() then
        if Cast(S.Stormstrike) then return "Stormstrike (core rotational)"; end
      end
      
      -- Crash Lightning with tier bonus
      if S.CrashLightning:IsReady() and Player:HasTier("TWW2", 4) then
        if Cast(S.CrashLightning) then return "Crash Lightning (T30 4pc)"; end
      end
      
      -- Voltaic Blaze fallback
      if S.VoltaicBlazeAbility:IsReady() then
        if Cast(S.VoltaicBlazeAbility) then return "Voltaic Blaze (fallback)"; end
      end
      
      -- Lava Lash with the right talents and DoT
      if S.LavaLash:IsReady() and (S.ElementalAssault:IsAvailable() and 
        S.MoltenAssault:IsAvailable() and Target:DebuffUp(FSDebuff)) then
        if Cast(S.LavaLash) then return "Lava Lash (with Elemental/Molten Assault)"; end
      end
      
      -- Ice Strike
      if S.IceStrike:IsReady() then
        if Cast(S.IceStrike) then return "Ice Strike (filler)"; end
      end
    end    
    
    local function Single()
      -- run_action_list,name=single_open,if=time<15
      if HL.CombatTime() < 15 then
        local ShouldReturn = SingleOpen(); if ShouldReturn then return ShouldReturn; end
        if Cast(S.Pool) then return "Wait for SingleOpen()"; end
      end
      -- primordial_storm,if=(buff.maelstrom_weapon.stack>=10|buff.primordial_storm.remains<=4&buff.maelstrom_weapon.stack>=5)
      if S.PrimordialStormAbility:IsReady() and (MaelstromStacks >= 10 or Player:BuffRemains(S.PrimordialStormBuff) <= 4 and MaelstromStacks >= 5) then
        if Cast(S.PrimordialStormAbility) then return "primordial_storm single 2"; end
      end
      -- flame_shock,if=!ticking&(talent.ashen_catalyst.enabled|talent.primordial_wave.enabled|talent.lashing_flames.enabled)
      if S.FlameShock:IsReady() and (Target:DebuffDown(S.FlameShockDebuff) and (S.AshenCatalyst:IsAvailable() or S.PrimordialWave:IsAvailable() or S.LashingFlames:IsAvailable())) then
        if Cast(S.FlameShock) then return "flame_shock single 4"; end
      end
      -- feral_spirit,if=(cooldown.doom_winds.remains>25|cooldown.doom_winds.remains<=5)
      if S.FeralSpirit:IsReady() and (S.DoomWinds:CooldownRemains() > 25 or S.DoomWinds:CooldownRemains() <= 5) then
        if Cast(S.FeralSpirit) then return "feral_spirit single 6"; end
      end
      -- windstrike,if=talent.thorims_invocation.enabled&buff.maelstrom_weapon.stack>0&ti_lightning_bolt
      if S.Windstrike:IsReady() and (S.ThorimsInvocation:IsAvailable() and MaelstromStacks > 0 and TIAction == S.LightningBolt) then
        if Cast(S.Windstrike) then return "windstrike single 8"; end
      end
      -- doom_winds
      if S.DoomWinds:IsReady() then
        if Cast(S.DoomWinds) then return "doom_winds single 10"; end
      end
      -- primordial_wave,if=dot.flame_shock.ticking&(raid_event.adds.in>action.primordial_wave.cooldown|raid_event.adds.in<6)
      if S.PrimordialWave:IsReady() and (Target:DebuffDown(S.FlameShockDebuff)) then
        if Cast(S.PrimordialWave) then return "primordial_wave single 12"; end
      end
      -- ascendance,if=(dot.flame_shock.ticking|!talent.primordial_wave.enabled|!talent.ashen_catalyst.enabled)
      if S.Ascendance:IsReady() and (Target:DebuffUp(S.FlameShockDebuff) or not S.PrimordialWave:IsAvailable() or not S.AshenCatalyst:IsAvailable()) then
        if Cast(S.Ascendance) then return "ascendance single 14"; end
      end
      -- tempest,if=buff.maelstrom_weapon.stack>=9&(buff.tempest.stack=buff.tempest.max_stack&(tempest_mael_count>30|buff.awakening_storms.stack=3))
      if S.TempestAbility:IsReady() and (MaelstromStacks >= 9 and (Player:BuffStack(S.TempestBuff) == MaxTempestStacks and (Shaman.TempestMaelstrom > 30 or Player:BuffStack(S.AwakeningStormsBuff) == 3))) then
        if Cast(S.TempestAbility) then return "tempest single 16"; end
      end
      -- elemental_blast,if=((!talent.overflowing_maelstrom.enabled&buff.maelstrom_weapon.stack>=5)|(buff.maelstrom_weapon.stack>=9))
      if S.ElementalBlast:IsReady() and ((not S.OverflowingMaelstrom:IsAvailable() and MaelstromStacks >= 5) or (MaelstromStacks >= 9)) then
        if Cast(S.ElementalBlast) then return "elemental_blast single 18"; end
      end
      -- tempest,if=buff.maelstrom_weapon.stack>=9&(cooldown.ascendance.remains>=buff.tempest.remains|cooldown.ascendance.remains<=3*gcd|!talent.tempest_strikes.enabled)
      if S.TempestAbility:IsReady() and (MaelstromStacks >= 9 and (S.Ascendance:CooldownRemains() >= Player:BuffRemains(S.TempestBuff) or S.Ascendance:CooldownRemains() <= 3 * Player:GCD() or not S.TempestStrikes:IsAvailable())) then
        if Cast(S.TempestAbility) then return "tempest single 20"; end
      end
      -- lightning_bolt,if=buff.maelstrom_weapon.stack>=9
      if S.LightningBolt:IsReady() and (MaelstromStacks >= 9) then
        if Cast(S.LightningBolt) then return "lightning_bolt single 22"; end
      end
      -- chain_lightning,if=buff.maelstrom_weapon.stack>=9&!buff.primordial_storm.up&cooldown.ascendance.remains<23&buff.tempest.up&talent.tempest_strikes.enabled
      if S.ChainLightning:IsReady() and (MaelstromStacks >= 9 and not Player:BuffUp(S.PrimordialStormBuff) and S.Ascendance:CooldownRemains() < 23 and Player:BuffUp(S.TempestBuff) and S.TempestStrikes:IsAvailable()) then
        if Cast(S.ChainLightning) then return "chain_lightning single 24"; end
      end
      -- lava_lash,if=(buff.hot_hand.up&(buff.ashen_catalyst.stack=buff.ashen_catalyst.max_stack))|(dot.flame_shock.remains<=2&!talent.voltaic_blaze.enabled&talent.molten_assault.enabled)|(talent.lashing_flames.enabled&(debuff.lashing_flames.down))
      if S.LavaLash:IsReady() and ((Player:BuffUp(S.HotHandBuff) and (Player:BuffStack(S.AshenCatalystBuff) == MaxAshenCatalystStacks)) or (Target:DebuffRemains(S.FlameShockDebuff) <= 2 and not S.VoltaicBlaze:IsAvailable() and S.MoltenAssault:IsAvailable()) or (S.LashingFlames:IsAvailable() and Target:DebuffDown(S.LashingFlamesDebuff))) then
        if Cast(S.LavaLash) then return "lava_lash single 26"; end
      end
      -- crash_lightning,if=(buff.doom_winds.up&buff.electrostatic_wager.stack>1)|buff.electrostatic_wager.stack>8
      if S.CrashLightning:IsReady() and ((Player:BuffUp(S.DoomWindsBuff) and Player:BuffStack(S.ElectrostaticWagerBuff) > 1) or Player:BuffStack(S.ElectrostaticWagerBuff) > 8) then
        if Cast(S.CrashLightning) then return "crash_lightning single 28"; end
      end
      -- stormstrike,if=buff.doom_winds.up|buff.stormblast.stack>0
      if S.Stormstrike:IsReady() and (Player:BuffUp(S.DoomWindsBuff) or Player:BuffUp(S.StormblastBuff)) then
        if Cast(S.Stormstrike) then return "stormstrike single 30"; end
      end
      -- crash_lightning,if=talent.unrelenting_storms.enabled&talent.alpha_wolf.enabled&alpha_wolf_min_remains=0
      if S.CrashLightning:IsReady() and (S.UnrelentingStorms:IsAvailable() and S.AlphaWolf:IsAvailable() and AlphaWolfMinRemains() == 0) then
        if Cast(S.CrashLightning) then return "crash_lightning single 32"; end
      end
      -- lava_lash,if=buff.hot_hand.up
      if S.LavaLash:IsReady() and (Player:BuffUp(S.HotHandBuff)) then
        if Cast(S.LavaLash) then return "lava_lash single 34"; end
      end
      -- crash_lightning,if=set_bonus.tww2_4pc
      if S.CrashLightning:IsReady() and (Player:HasTier("TWW2", 4)) then
        if Cast(S.CrashLightning) then return "crash_lightning single 36"; end
      end
      -- voltaic_blaze
      if S.VoltaicBlazeAbility:IsReady() then
        if Cast(S.VoltaicBlazeAbility) then return "voltaic_blaze single 38"; end
      end
      -- stormstrike
      if S.Stormstrike:IsReady() then
        if Cast(S.Stormstrike) then return "stormstrike single 40"; end
      end
      -- lava_lash,if=talent.elemental_assault.enabled&talent.molten_assault.enabled&dot.flame_shock.ticking
      if S.LavaLash:IsReady() and (S.ElementalAssault:IsAvailable() and S.MoltenAssault:IsAvailable() and Target:DebuffUp(S.FlameShockDebuff)) then
        if Cast(S.LavaLash) then return "lava_lash single 42"; end
      end
      -- ice_strike
      if S.IceStrike:IsReady() then
        if Cast(S.IceStrike) then return "ice_strike single 44"; end
      end
      -- frost_shock,if=buff.hailstorm.stack=10&buff.ice_strike.up
      if S.FrostShock:IsReady() and (Player:BuffStack(S.HailstormBuff) == 10 and Player:BuffUp(S.IceStrikeBuff)) then
        if Cast(S.FrostShock) then return "frost_shock single 46"; end
      end
      -- lava_lash
      if S.LavaLash:IsReady() then
        if Cast(S.LavaLash) then return "lava_lash single 48"; end
      end
      if MaelstromStacks >= 5 and Player:BuffDown(S.PrimordialStormBuff) then
        -- elemental_blast,if=buff.maelstrom_weapon.stack>=5&!buff.primordial_storm.up
        if S.ElementalBlast:IsReady() then
          if Cast(S.ElementalBlast) then return "elemental_blast single 50"; end
        end
        -- lightning_bolt,if=buff.maelstrom_weapon.stack>=5&!buff.primordial_storm.up
        if S.LightningBolt:IsReady() then
          if Cast(S.LightningBolt) then return "lightning_bolt single 52"; end
        end
      end
      -- frost_shock,if=buff.hailstorm.up
      if S.FrostShock:IsReady() and (Player:BuffUp(S.HailstormBuff)) then
        if Cast(S.FrostShock) then return "frost_shock single 54"; end
      end
      -- flame_shock,if=!ticking
      if S.FlameShock:IsReady() and (Target:DebuffDown(S.FlameShockDebuff)) then
        if Cast(S.FlameShock) then return "flame_shock single 56"; end
      end
      -- sundering,if=raid_event.adds.in>=action.sundering.cooldown
      if S.Sundering:IsReady() then
        if Cast(S.Sundering) then return "sundering single 58"; end
      end
      -- crash_lightning
      if S.CrashLightning:IsReady() then
        if Cast(S.CrashLightning) then return "crash_lightning single 60"; end
      end
      -- frost_shock
      if S.FrostShock:IsReady() then
        if Cast(S.FrostShock) then return "frost_shock single 62"; end
      end
      -- fire_nova,if=active_dot.flame_shock
      if S.FireNova:IsReady() and (Target:DebuffUp(S.FlameShockDebuff)) then
        if Cast(S.FireNova) then return "fire_nova single 64"; end
      end
      -- earth_elemental
      if S.EarthElemental:IsReady() then
        if Cast(S.EarthElemental) then return "earth_elemental single 66"; end
      end
      -- flame_shock
      if S.FlameShock:IsReady() then
        if Cast(S.FlameShock) then return "flame_shock single 68"; end
      end
    end

    local function customSingle()
      -- Local variables for readability
      local MWStacks = MaelstromStacks
      local FSDebuff = S.FlameShockDebuff
      
      -- Execute single opener rotation during the first 15 seconds of combat
      if HL.CombatTime() < 15 then
        local ShouldReturn = customSingleOpen(); 
        if ShouldReturn then return ShouldReturn; end
        if Cast(S.Pool) then return "Wait for SingleOpen() to finish"; end
      end
      
      -- Instant Doom Winds after Primordial Wave for synergy
      if S.DoomWinds:IsReady() and Player:PrevGCD(1, S.PrimordialWave) then
        if Cast(S.DoomWinds) then return "Doom Winds (Primordial Wave follow-up)"; end
      end
      
      -- Primordial Storm at max stacks or to refresh when nearly expired
      if S.PrimordialStormAbility:IsReady() and not Player:PrevGCD(1, S.PrimordialWave) and 
        (Player:BuffUp(S.AscendanceBuff) or not S.Ascendance:CooldownUp()) and 
        (MWStacks == MaxMaelstromStacks or 
          (Player:BuffRemains(S.PrimordialStormBuff) <= 4 and MWStacks >= 5)) then
        if Cast(S.PrimordialStormAbility) then return "Primordial Storm (max stacks/refresh)"; end
      end
      
      -- Apply Flame Shock if missing and we have relevant talents
      if S.FlameShock:IsReady() and Target:DebuffDown(FSDebuff) and 
        (S.AshenCatalyst:IsAvailable() or S.PrimordialWave:IsAvailable() or S.LashingFlames:IsAvailable()) then
        if Cast(S.FlameShock) then return "Flame Shock (maintain dot with talents)"; end
      end
      
      -- Feral Spirit with proper Doom Winds timing
      if S.FeralSpirit:IsReady() and (S.DoomWinds:CooldownRemains() > 27 or S.DoomWinds:CooldownRemains() < 7) then
        if Cast(S.FeralSpirit) then return "Feral Spirit (DW timing window)"; end
      end
      
      -- Windstrike for Thorim's Invocation procs
      if S.Windstrike:IsReady() and S.ThorimsInvocation:IsAvailable() and 
        MWStacks > 0 and TIAction == S.LightningBolt then
        if Cast(S.Windstrike) then return "Windstrike (Thorim's proc)"; end
      end
      
      -- Doom Winds during Legacy of the Frost Witch window
      if S.DoomWinds:IsReady() and Player:BuffUp(S.LegacyoftheFrostWitchBuff) and 
        (S.FeralSpirit:CooldownRemains() > 30 or S.FeralSpirit:CooldownRemains() < 2) then
        if Cast(S.DoomWinds) then return "Doom Winds (Legacy buff window)"; end
      end
      
      -- Primordial Wave with Flame Shock active
      if S.PrimordialWave:IsReady() and MWStacks >= 2 and Target:DebuffUp(FSDebuff) then
        if Cast(S.PrimordialWave) then return "Primordial Wave (with active dots)"; end
      end
      
      -- Primordial Wave recovery if Flame Shock is missing
      if S.PrimordialWave:IsReady() and Target:DebuffDown(FSDebuff) then
        if Cast(S.PrimordialWave) then return "Primordial Wave (reapply dot)"; end
      end
      
      -- Ascendance with Flame Shock active
      if S.Ascendance:IsReady() and 
        (Target:DebuffUp(FSDebuff) or not S.PrimordialWave:IsAvailable() or not S.AshenCatalyst:IsAvailable()) then
        if Cast(S.Ascendance) then return "Ascendance (with active dots)"; end
      end
      
      -- Elemental Blast with high Maelstrom Weapon stacks and almost 2 charges
      if S.ElementalBlast:IsReady() and 
        (((not S.OverflowingMaelstrom:IsAvailable() and MWStacks >= 5) or (MWStacks >= 9)) and 
          S.ElementalBlast:ChargesFractional() >= 1.8) then
        if Cast(S.ElementalBlast) then return "Elemental Blast (high stacks + charges)"; end
      end
      
      -- Tempest with optimal conditions
      if S.TempestAbility:IsReady() and 
        (Player:BuffStack(S.TempestBuff) == MaxTempestStacks and 
          (Shaman.TempestMaelstrom > 30 or Player:BuffStack(S.AwakeningStormsBuff) == 3) and 
          MWStacks >= 9) then
        if Cast(S.TempestAbility) then return "Tempest (max stacks + high resources)"; end
      end
      
      -- Lightning Bolt with Arc Discharge stacks
      if S.LightningBolt:IsReady() and MWStacks >= 9 and 
        Player:BuffDown(S.PrimordialStormBuff) and Player:BuffStack(S.ArcDischargeBuff) > 1 then
        if Cast(S.LightningBolt) then return "Lightning Bolt (Arc Discharge build-up)"; end
      end
      
      -- Standard Elemental Blast usage with high Maelstrom
      if S.ElementalBlast:IsReady() and 
        ((not S.OverflowingMaelstrom:IsAvailable() and MWStacks >= 5) or (MWStacks >= 9)) then
        if Cast(S.ElementalBlast) then return "Elemental Blast (high MW stacks)"; end
      end
      
      -- Tempest with high Maelstrom
      if S.TempestAbility:IsReady() and MWStacks >= 9 then
        if Cast(S.TempestAbility) then return "Tempest (high MW stacks)"; end
      end
      
      -- Lightning Bolt with high Maelstrom
      if S.LightningBolt:IsReady() and MWStacks >= 9 then
        if Cast(S.LightningBolt) then return "Lightning Bolt (high MW stacks)"; end
      end
      
      -- Lava Lash with various optimized conditions
      if S.LavaLash:IsReady() and 
        ((Player:BuffUp(S.HotHandBuff) and (Player:BuffStack(S.AshenCatalystBuff) == MaxAshenCatalystStacks)) or 
          (Target:DebuffRemains(FSDebuff) <= 2 and not S.VoltaicBlaze:IsAvailable()) or 
          (S.LashingFlames:IsAvailable() and Target:DebuffDown(S.LashingFlamesDebuff))) then
        if Cast(S.LavaLash) then return "Lava Lash (Hot Hand/Ashen max/FS refresh)"; end
      end
      
      -- Crash Lightning during Doom Winds or with high Electrostatic stacks
      if S.CrashLightning:IsReady() and 
        ((Player:BuffUp(S.DoomWindsBuff) and Player:BuffStack(S.ElectrostaticWagerBuff) > 1) or 
          Player:BuffStack(S.ElectrostaticWagerBuff) > 8) then
        if Cast(S.CrashLightning) then return "Crash Lightning (DW/high Electrostatic)"; end
      end
      
      -- Stormstrike during Doom Winds or with Stormblast procs
      if S.Stormstrike:IsReady() and 
        (Player:BuffUp(S.DoomWindsBuff) or Player:BuffUp(S.StormblastBuff)) then
        if Cast(S.Stormstrike) then return "Stormstrike (Doom Winds/Stormblast)"; end
      end
      
      -- Crash Lightning with Alpha Wolf management
      if S.CrashLightning:IsReady() and 
        S.UnrelentingStorms:IsAvailable() and S.AlphaWolf:IsAvailable() and AlphaWolfMinRemains() == 0 then
        if Cast(S.CrashLightning) then return "Crash Lightning (Alpha Wolf timing)"; end
      end
      
      -- Lava Lash with Hot Hand proc
      if S.LavaLash:IsReady() and Player:BuffUp(S.HotHandBuff) then
        if Cast(S.LavaLash) then return "Lava Lash (Hot Hand proc)"; end
      end
      
      -- Crash Lightning with 4-piece tier bonus
      if S.CrashLightning:IsReady() and Player:HasTier("TWW2", 4) then
        if Cast(S.CrashLightning) then return "Crash Lightning (T30 4pc)"; end
      end
      
      -- Voltaic Blaze for Flame Shock spread
      if S.VoltaicBlazeAbility:IsReady() then
        if Cast(S.VoltaicBlazeAbility) then return "Voltaic Blaze (rotational)"; end
      end
      
      -- Stormstrike as core rotational ability
      if S.Stormstrike:IsReady() then
        if Cast(S.Stormstrike) then return "Stormstrike (core rotational)"; end
      end
      
      -- Lava Lash with Elemental/Molten Assault talent synergy
      if S.LavaLash:IsReady() and 
        S.ElementalAssault:IsAvailable() and S.MoltenAssault:IsAvailable() and Target:DebuffUp(FSDebuff) then
        if Cast(S.LavaLash) then return "Lava Lash (Elemental/Molten synergy)"; end
      end
      
      -- Ice Strike as a filler
      if S.IceStrike:IsReady() then
        if Cast(S.IceStrike) then return "Ice Strike (filler)"; end
      end
      
      -- Lightning Bolt with moderate Maelstrom stacks
      if S.LightningBolt:IsReady() and MWStacks >= 5 and Player:BuffDown(S.PrimordialStormBuff) then
        if Cast(S.LightningBolt) then return "Lightning Bolt (moderate MW stacks)"; end
      end
      
      -- Frost Shock with Hailstorm buff
      if S.FrostShock:IsReady() and Player:BuffUp(S.HailstormBuff) then
        if Cast(S.FrostShock) then return "Frost Shock (Hailstorm buff)"; end
      end
      
      -- Flame Shock if not active (fallback)
      if S.FlameShock:IsReady() and Target:DebuffDown(FSDebuff) then
        if Cast(S.FlameShock) then return "Flame Shock (fallback)"; end
      end
      
      -- Sundering as a filler
      if S.Sundering:IsReady() then
        if Cast(S.Sundering) then return "Sundering (filler)"; end
      end
      
      -- Crash Lightning as a filler
      if S.CrashLightning:IsReady() then
        if Cast(S.CrashLightning) then return "Crash Lightning (filler)"; end
      end
      
      -- Frost Shock as a filler
      if S.FrostShock:IsReady() then
        if Cast(S.FrostShock) then return "Frost Shock (filler)"; end
      end
      
      -- Fire Nova with active Flame Shock
      if S.FireNova:IsReady() and Target:DebuffUp(FSDebuff) then
        if Cast(S.FireNova) then return "Fire Nova (with active Flame Shock)"; end
      end
      
      -- Earth Elemental for additional DPS if enabled
      if S.EarthElemental:IsReady() and GetSetting('EEDPS', false) then
        if Cast(S.EarthElemental) then return "Earth Elemental (DPS boost)"; end
      end
      
      -- Flame Shock as last resort
      if S.FlameShock:IsReady() then
        if Cast(S.FlameShock) then return "Flame Shock (last resort)"; end
      end
    end    
    
    local function SingleTotemicOpen()
      -- flame_shock,if=!ticking
      if S.FlameShock:IsReady() and (Target:DebuffDown(S.FlameShockDebuff)) then
        if Cast(S.FlameShock) then return "flame_shock single_totemic_open 2"; end
      end
      -- lava_lash,if=!pet.surging_totem.active&talent.lashing_flames.enabled&debuff.lashing_flames.down
      if S.LavaLash:IsReady() and (not TotemFinder(S.SurgingTotem) and S.LashingFlames:IsAvailable() and Target:DebuffDown(S.LashingFlamesDebuff)) then
        if Cast(S.LavaLash) then return "lava_lash single_totemic_open 4"; end
      end
      -- surging_totem
      if S.SurgingTotem:IsReady() then
        if Cast(S.SurgingTotem) then return "surging_totem single_totemic_open 6"; end
      end
      -- primordial_wave
      if S.PrimordialWave:IsReady() then
        if Cast(S.PrimordialWave) then return "primordial_wave single_totemic_open 8"; end
      end
      if Player:BuffUp(S.LegacyoftheFrostWitchBuff) then
        -- feral_spirit,if=buff.legacy_of_the_frost_witch.up
        if S.FeralSpirit:IsReady() then
          if Cast(S.FeralSpirit) then return "feral_spirit single_totemic_open 10"; end
        end
        -- doom_winds,if=buff.legacy_of_the_frost_witch.up
        if S.DoomWinds:IsReady() then
          if Cast(S.DoomWinds) then return "doom_winds single_totemic_open 12"; end
        end
        -- primordial_storm,if=(buff.maelstrom_weapon.stack>=10)&(buff.legacy_of_the_frost_witch.up|!talent.legacy_of_the_frost_witch.enabled)
        if S.PrimordialStormAbility:IsReady() and ((MaelstromStacks >= 10) and (Player:BuffUp(S.LegacyoftheFrostWitchBuff) or not S.LegacyoftheFrostWitch:IsAvailable())) then
          if Cast(S.PrimordialStormAbility) then return "primordial_storm single_totemic_open 14"; end
        end
      end
      -- lava_lash,if=buff.hot_hand.up
      if S.LavaLash:IsReady() and (Player:BuffUp(S.HotHandBuff)) then
        if Cast(S.LavaLash) then return "lava_lash single_totemic_open 16"; end
      end
      if Player:BuffUp(S.LegacyoftheFrostWitchBuff) then
        -- stormstrike,if=buff.doom_winds.up&buff.legacy_of_the_frost_witch.up
        if S.Stormstrike:IsReady() and (Player:BuffUp(S.DoomWindsBuff)) then
          if Cast(S.Stormstrike) then return "stormstrike single_totemic_open 18"; end
        end
        -- sundering,if=buff.legacy_of_the_frost_witch.up
        if S.Sundering:IsReady() then
          if Cast(S.Sundering) then return "sundering single_totemic_open 20"; end
        end
      end
      if MaelstromStacks == 10 then
        -- elemental_blast,if=buff.maelstrom_weapon.stack=10
        if S.ElementalBlast:IsReady() then
          if Cast(S.ElementalBlast) then return "elemental_blast single_totemic_open 22"; end
        end
        -- lightning_bolt,if=buff.maelstrom_weapon.stack=10
        if S.LightningBolt:IsReady() then
          if Cast(S.LightningBolt) then return "lightning_bolt single_totemic_open 24"; end
        end
      end
      -- stormstrike
      if S.Stormstrike:IsReady() then
        if Cast(S.Stormstrike) then return "stormstrike single_totemic_open 26"; end
      end
      -- lava_lash
      if S.LavaLash:IsReady() then
        if Cast(S.LavaLash) then return "lava_lash single_totemic_open 28"; end
      end
    end

    local function SingleTotemic()
      -- run_action_list,name=single_totemic_open,if=time<20
      if HL.CombatTime() < 20 then
        local ShouldReturn = SingleTotemicOpen(); if ShouldReturn then return ShouldReturn; end
        if Cast(S.Pool) then return "Wait for SingleTotemicOpen()"; end
      end
      -- surging_totem
      if S.SurgingTotem:IsReady() then
        if Cast(S.SurgingTotem) then return "surging_totem single_totemic 2"; end
      end
      -- ascendance,if=ti_lightning_bolt&pet.surging_totem.remains>4&(buff.totemic_rebound.stack>=3|buff.maelstrom_weapon.stack>0)
      if AoEON() and S.Ascendance:IsReady() and (TIAction == S.LightningBolt and TotemFinder(S.SurgingTotem, true) > 4 and (Player:BuffStack(S.TotemicReboundBuff) >= 3 or MaelstromStacks > 0)) then
        if Cast(S.Ascendance) then return "ascendance single_totemic 4"; end
      end
      -- flame_shock,if=!ticking&(talent.ashen_catalyst.enabled|talent.primordial_wave.enabled)
      if S.FlameShock:IsReady() and (Target:DebuffDown(S.FlameShockDebuff) and (S.AshenCatalyst:IsAvailable() or S.PrimordialWave:IsAvailable())) then
        if Cast(S.FlameShock) then return "flame_shock single_totemic 6"; end
      end
      -- lava_lash,if=buff.hot_hand.up
      if S.LavaLash:IsReady() and (Player:BuffUp(S.HotHandBuff)) then
        if Cast(S.LavaLash) then return "lava_lash single_totemic 8"; end
      end
      -- feral_spirit,if=((cooldown.doom_winds.remains>23|cooldown.doom_winds.remains<7)&(cooldown.primordial_wave.remains<20|buff.primordial_storm.up|!talent.primordial_storm.enabled))
      if S.FeralSpirit:IsReady() and ((S.DoomWinds:CooldownRemains() > 23 or S.DoomWinds:CooldownRemains() < 7) and (S.PrimordialWave:CooldownRemains() < 20 or Player:BuffUp(S.PrimordialStormBuff) or not S.PrimordialStorm:IsAvailable())) then
        if Cast(S.FeralSpirit) then return "feral_spirit single_totemic 10"; end
      end
      -- primordial_wave,if=dot.flame_shock.ticking&(raid_event.adds.in>action.primordial_wave.cooldown)|raid_event.adds.in<6
      if S.PrimordialWave:IsReady() and (Target:DebuffUp(S.FlameShockDebuff)) then
        if Cast(S.PrimordialWave) then return "primordial_wave single_totemic 12"; end
      end
      -- doom_winds,if=buff.legacy_of_the_frost_witch.up
      if S.DoomWinds:IsReady() and (Player:BuffUp(S.LegacyoftheFrostWitchBuff)) then
        if Cast(S.DoomWinds) then return "doom_winds single_totemic 14"; end
      end
      -- primordial_storm,if=(buff.maelstrom_weapon.stack>=10)&((cooldown.doom_winds.remains>=buff.primordial_storm.remains)|buff.doom_winds.up|!talent.doom_winds.enabled|(buff.primordial_storm.remains<2*gcd))
      if S.PrimordialStormAbility:IsReady() and ((MaelstromStacks >= 10) and ((S.DoomWinds:CooldownRemains() >= Player:BuffRemains(S.PrimordialStormBuff)) or Player:BuffUp(S.DoomWindsBuff) or not S.DoomWinds:IsAvailable() or (Player:BuffRemains(S.PrimordialStormBuff) < 2 * Player:GCD()))) then
        if Cast(S.PrimordialStormAbility) then return "primordial_storm single_totemic 16"; end
      end
      -- sundering,if=buff.ascendance.up&pet.surging_totem.active&talent.earthsurge.enabled&buff.legacy_of_the_frost_witch.up&buff.totemic_rebound.stack>=5&buff.earthen_weapon.stack>=2
      if S.Sundering:IsReady() and (Player:BuffUp(S.AscendanceBuff) and TotemFinder(S.SurgingTotem) and S.Earthsurge:IsAvailable() and Player:BuffUp(S.LegacyoftheFrostWitchBuff) and Player:BuffStack(S.TotemicReboundBuff) >= 5 and Player:BuffStack(S.EarthenWeaponBuff) >= 2) then
        if Cast(S.Sundering) then return "sundering single_totemic 18"; end
      end
      -- windstrike,if=talent.thorims_invocation.enabled&buff.maelstrom_weapon.stack>0&ti_lightning_bolt
      if S.Windstrike:IsReady() and (S.ThorimsInvocation:IsAvailable() and MaelstromStacks > 0 and TIAction == S.LightningBolt) then
        if Cast(S.Windstrike) then return "windstrike single_totemic 20"; end
      end
      -- sundering,if=buff.legacy_of_the_frost_witch.up&((cooldown.ascendance.remains>=10&talent.ascendance.enabled)|!talent.ascendance.enabled)&pet.surging_totem.active&buff.totemic_rebound.stack>=3&!buff.ascendance.up
      if S.Sundering:IsReady() and (Player:BuffUp(S.LegacyoftheFrostWitchBuff) and ((S.Ascendance:CooldownRemains() >= 10 and S.Ascendance:IsAvailable()) or not S.Ascendance:IsAvailable()) and TotemFinder(S.SurgingTotem) and Player:BuffStack(S.TotemicReboundBuff) >= 3 and Player:BuffDown(S.AscendanceBuff)) then
        if Cast(S.Sundering) then return "sundering single_totemic 22"; end
      end
      -- crash_lightning,if=talent.unrelenting_storms.enabled&talent.alpha_wolf.enabled&alpha_wolf_min_remains=0
      if S.CrashLightning:IsReady() and (S.UnrelentingStorms:IsAvailable() and S.AlphaWolf:IsAvailable() and AlphaWolfMinRemains() == 0) then
        if Cast(S.CrashLightning) then return "crash_lightning single_totemic 24"; end
      end
      -- lava_burst,if=!talent.thorims_invocation.enabled&buff.maelstrom_weapon.stack>=10&buff.whirling_air.down
      if S.LavaBurst:IsReady() and (not S.ThorimsInvocation:IsAvailable() and MaelstromStacks >= 10 and Player:BuffDown(S.WhirlingAirBuff)) then
        if Cast(S.LavaBurst) then return "lava_burst single_totemic 28"; end
      end
      -- elemental_blast,if=(buff.maelstrom_weapon.stack>=10)&(buff.primordial_storm.down|buff.primordial_storm.remains>4)
      if S.ElementalBlast:IsReady() and ((MaelstromStacks >= 10) and (Player:BuffDown(S.PrimordialStormBuff) or Player:BuffRemains(S.PrimordialStormBuff) > 4)) then
        if Cast(S.ElementalBlast) then return "elemental_blast single_totemic 30"; end
      end
      -- stormstrike,if=buff.doom_winds.up&buff.legacy_of_the_frost_witch.up
      if S.Stormstrike:IsReady() and (Player:BuffUp(S.DoomWindsBuff) and Player:BuffUp(S.LegacyoftheFrostWitchBuff)) then
        if Cast(S.Stormstrike) then return "stormstrike single_totemic 32"; end
      end
      -- lightning_bolt,if=(buff.maelstrom_weapon.stack>=10)&(buff.primordial_storm.down|buff.primordial_storm.remains>4)
      if S.LightningBolt:IsReady() and ((MaelstromStacks >= 10) and (Player:BuffDown(S.PrimordialStormBuff) or Player:BuffRemains(S.PrimordialStormBuff) > 4)) then
        if Cast(S.LightningBolt) then return "lightning_bolt single_totemic 34"; end
      end
      -- crash_lightning,if=buff.electrostatic_wager.stack>4
      if S.CrashLightning:IsReady() and (Player:BuffStack(S.ElectrostaticWagerBuff) > 4) then
        if Cast(S.CrashLightning) then return "crash_lightning single_totemic 36"; end
      end
      -- stormstrike,if=buff.doom_winds.up|buff.stormblast.stack>1
      if S.Stormstrike:IsReady() and (Player:BuffUp(S.DoomWindsBuff) or Player:BuffStack(S.StormblastBuff) > 1) then
        if Cast(S.Stormstrike) then return "stormstrike single_totemic 38"; end
      end
      -- lava_lash,if=buff.whirling_fire.up|buff.ashen_catalyst.stack>=8
      if S.LavaLash:IsReady() and (Player:BuffUp(S.WhirlingFireBuff) or Player:BuffStack(S.AshenCatalystBuff) >= 8) then
        if Cast(S.LavaLash) then return "lava_lash single_totemic 40"; end
      end
      -- windstrike
      if S.Windstrike:IsReady() then
        if Cast(S.Windstrike) then return "windstrike single_totemic 42"; end
      end
      -- stormstrike
      if S.Stormstrike:IsReady() then
        if Cast(S.Stormstrike) then return "stormstrike single_totemic 44"; end
      end
      -- lava_lash
      if S.LavaLash:IsReady() and (S.MoltenAssault:IsAvailable()) then
        if Cast(S.LavaLash) then return "lava_lash single_totemic 46"; end
      end
      -- crash_lightning,if=set_bonus.tww2_4pc
      if S.CrashLightning:IsReady() and (Player:HasTier("TWW2", 4)) then
        if Cast(S.CrashLightning) then return "crash_lightning single_totemic 48"; end
      end
      -- voltaic_blaze
      if S.VoltaicBlazeAbility:IsReady() then
        if Cast(S.VoltaicBlazeAbility) then return "voltaic_blaze single_totemic 50"; end
      end
      -- crash_lightning,if=talent.unrelenting_storms.enabled
      if S.CrashLightning:IsReady() and (S.UnrelentingStorms:IsAvailable()) then
        if Cast(S.CrashLightning) then return "crash_lightning single_totemic 52"; end
      end
      -- ice_strike,if=!buff.ice_strike.up
      if S.IceStrike:IsReady() and (Player:BuffDown(S.IceStrikeBuff)) then
        if Cast(S.IceStrike) then return "ice_strike single_totemic 54"; end
      end
      -- crash_lightning
      if S.CrashLightning:IsReady() then
        if Cast(S.CrashLightning) then return "crash_lightning single_totemic 56"; end
      end
      -- frost_shock
      if S.FrostShock:IsReady() then
        if Cast(S.FrostShock) then return "frost_shock single_totemic 58"; end
      end
      -- fire_nova,if=active_dot.flame_shock
      if S.FireNova:IsReady() and (Target:DebuffUp(S.FlameShockDebuff)) then
        if Cast(S.FireNova) then return "fire_nova single_totemic 60"; end
      end
      -- earth_elemental
      if S.EarthElemental:IsReady() and GetSetting('EEDPS', false) then
        if Cast(S.EarthElemental) then return "earth_elemental single_totemic 62"; end
      end
      -- flame_shock,if=!talent.voltaic_blaze.enabled
      if S.FlameShock:IsReady() and (not S.VoltaicBlaze:IsAvailable()) then
        if Cast(S.FlameShock) then return "flame_shock single_totemic 64"; end
      end
    end

    local function AoeOpen()
      -- flame_shock,if=!ticking
      if S.FlameShock:IsReady() and (Target:DebuffDown(S.FlameShockDebuff)) then
        if Cast(S.FlameShock) then return "flame_shock aoe_open 2"; end
      end
      -- crash_lightning,if=(buff.electrostatic_wager.stack>9&buff.doom_winds.up)|!buff.crash_lightning.up
      if S.CrashLightning:IsReady() and ((Player:BuffStack(S.ElectrostaticWagerBuff) > 9 and Player:BuffUp(S.DoomWindsBuff)) or Player:BuffDown(S.CrashLightningBuff)) then
        if Cast(S.CrashLightning) then return "crash_lightning aoe_open 4"; end
      end
      -- voltaic_blaze,if=active_dot.flame_shock<3
      if S.VoltaicBlazeAbility:IsReady() and (S.FlameShockDebuff:AuraActiveCount() < 3) then
        if Cast(S.VoltaicBlazeAbility) then return "voltaic_blaze aoe_open 6"; end
      end
      -- lava_lash,if=talent.molten_assault.enabled&(talent.primordial_wave.enabled|talent.fire_nova.enabled)&dot.flame_shock.ticking&(active_dot.flame_shock<active_enemies)&active_dot.flame_shock<3
      if S.LavaLash:IsReady() and (S.MoltenAssault:IsAvailable() and (S.PrimordialWave:IsAvailable() or S.FireNova:IsAvailable()) and S.FlameShockDebuff:AuraActiveCount() > 0 and (S.FlameShockDebuff:AuraActiveCount() < EnemiesMeleeCount) and S.FlameShockDebuff:AuraActiveCount() < 3) then
        if Cast(S.LavaLash) then return "lava_lash aoe_open 8"; end
      end
      -- primordial_wave,if=(buff.maelstrom_weapon.stack>=4)&dot.flame_shock.ticking&(active_dot.flame_shock=active_enemies|active_dot.flame_shock=6)
      if S.PrimordialWave:IsReady() and ((MaelstromStacks >= 4) and Target:DebuffUp(S.FlameShockDebuff)) then
        if Cast(S.PrimordialWave) then return "primordial_wave aoe_open 10"; end
      end
      if MaelstromStacks >= 5 then
        -- feral_spirit,if=buff.maelstrom_weapon.stack>=9
        if S.FeralSpirit:IsReady() then
          if Cast(S.FeralSpirit) then return "feral_spirit aoe_open 12"; end
        end
        -- doom_winds,if=buff.maelstrom_weapon.stack>=9
        if S.DoomWinds:IsReady() then
          if Cast(S.DoomWinds) then return "doom_winds aoe_open 14"; end
        end
      end
      -- Custom APL: Ascendance with more controlled conditions
      if S.Ascendance:IsReady() and GetSetting("rotation_mode", "APLcustom") == "APLcustom" and ((S.FlameShockDebuff:AuraActiveCount() > 0 or not S.MoltenAssault:IsAvailable()) and TIAction == S.ChainLightning) then
        if Cast(S.Ascendance) then return "ascendance aoe_open custom 16"; end
      end
      -- ascendance,if=(dot.flame_shock.ticking|!talent.molten_assault.enabled)&ti_chain_lightning&(buff.legacy_of_the_frost_witch.up|!talent.legacy_of_the_frost_witch.enabled)&!buff.doom_winds.up
      if S.Ascendance:IsReady() and GetSetting("rotation_mode", "APLcustom") == "APLsimc" and ((S.FlameShockDebuff:AuraActiveCount() > 0 or not S.MoltenAssault:IsAvailable()) and TIAction == S.ChainLightning and (Player:BuffUp(S.LegacyoftheFrostWitchBuff) or not S.LegacyoftheFrostWitch:IsAvailable()) and Player:BuffDown(S.DoomWindsBuff)) then
        if Cast(S.Ascendance) then return "ascendance aoe_open simc 16"; end
      end
      -- primordial_storm,if=(buff.maelstrom_weapon.stack>=9)&(buff.legacy_of_the_frost_witch.up|!talent.legacy_of_the_frost_witch.enabled)
      if S.PrimordialStormAbility:CooldownUp() and S.PrimordialStormAbility:IsReady() and Player:BuffUp(S.DoomWindsBuff) and Player:BuffUp(S.AscendanceBuff) and ((MaelstromStacks >= 9) and (Player:BuffUp(S.LegacyoftheFrostWitchBuff) or not S.LegacyoftheFrostWitch:IsAvailable())) then
        if Cast(S.PrimordialStormAbility) then return "primordial_storm aoe_open 18"; end
      end
      -- tempest,target_if=min:debuff.lightning_rod.remains,if=buff.maelstrom_weapon.stack>=9&!buff.arc_discharge.stack>0
      if S.TempestAbility:IsReady() and (MaelstromStacks >= 9 and Player:BuffDown(S.ArcDischargeBuff)) then
        if GetSetting('TempestOnMTOnly', true) then
          if Cast(S.TempestAbility) then return "tempest aoe_open 20 (forced MT)"; end
        else
          if CastTargetIf(S.TempestAbility, EnemiesMelee, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then return "tempest aoe_open 20"; end
        end
      end
      -- crash_lightning,if=(buff.electrostatic_wager.stack>4)
      if S.CrashLightning:IsReady() and (Player:BuffStack(S.ElectrostaticWagerBuff) > 4) then
        if Cast(S.CrashLightning) then return "crash_lightning aoe_open 22"; end
      end
      -- windstrike,target_if=min:debuff.lightning_rod.remains,if=talent.thorims_invocation.enabled&ti_chain_lightning
      if S.Windstrike:IsReady() and (S.ThorimsInvocation:IsAvailable() and TIAction == S.ChainLightning) then
        if rodSetting['rodWS'] and not MainAddon.Toggle:GetToggle("DisableLightningRod") then
          if CastTargetIf(S.Windstrike, EnemiesMelee, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then 
            return "windstrike aoe_open 24"; 
          end
        else
          if Cast(S.Windstrike) then 
            return "windstrike aoe_open 24"; 
          end
        end
      end
      -- chain_lightning,target_if=min:debuff.lightning_rod.remains,if=buff.maelstrom_weapon.stack>=5&(!buff.primordial_storm.up|!buff.legacy_of_the_frost_witch.up)&buff.doom_winds.up
      -- chain_lightning,target_if=min:debuff.lightning_rod.remains,if=buff.maelstrom_weapon.stack>=9&(!buff.primordial_storm.up|!buff.legacy_of_the_frost_witch.up)
      if S.ChainLightning:IsReady() and (Player:BuffDown(S.PrimordialStormBuff) and
        (MaelstromStacks >= 5 and Player:BuffUp(S.DoomWindsBuff)) or
        (MaelstromStacks >= 9)
      ) then
        if rodSetting['rodCL'] and not MainAddon.Toggle:GetToggle("DisableLightningRod") then
          if CastTargetIf(S.ChainLightning, EnemiesMelee, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then 
            return "chain_lightning aoe_open 26"; 
          end
        else
          if Cast(S.ChainLightning) then 
            return "chain_lightning aoe_open 26"; 
          end
        end
      end
      -- stormstrike,if=buff.converging_storms.stack=6&buff.stormblast.stack>1
      if S.Stormstrike:IsReady() and (Player:BuffStack(S.ConvergingStormsBuff) == 6 and Player:BuffStack(S.StormblastBuff) > 1) then
        if Cast(S.Stormstrike) then return "stormstrike aoe_open 28"; end
      end
      -- crash_lightning
      if S.CrashLightning:IsReady() then
        if Cast(S.CrashLightning) then return "crash_lightning aoe_open 30"; end
      end
      -- voltaic_blaze
      if S.VoltaicBlazeAbility:IsReady() then
        if Cast(S.VoltaicBlazeAbility) then return "voltaic_blaze aoe_open 32"; end
      end
      -- stormstrike
      if S.Stormstrike:IsReady() then
        if Cast(S.Stormstrike) then return "stormstrike aoe_open 34"; end
      end
    end

    local function customAoeOpen()
      -- local variables
      local MWStacks = MaelstromStacks
      local FSDebuff = S.FlameShockDebuff
      local hasDoomWinds = Player:BuffUp(S.DoomWindsBuff)
      local hasAscendanceBuff = Player:BuffUp(S.AscendanceBuff)

      -- Primordial Storm – combined Ascendance & early low TTD
      if S.PrimordialStormAbility:IsReady() then
        -- 1) During Ascendance
        if MWStacks >= 8
          and hasAscendanceBuff
          and Player:BuffRemains(S.AscendanceBuff) < 4
        then
            if Cast(S.PrimordialStormAbility) then
                return "Primordial Storm (during Ascendance)"
            end

        -- 2) Early if pack’s TTD is low
        elseif MWStacks >= 7
              and HL.CombatTime() < 10
              and FightRemains < 20
              and (
                  S.FlameShockDebuff:AuraActiveCount() >= 5
                  or S.FlameShockDebuff:AuraActiveCount() >= math.ceil(EnemiesMeleeCount * 0.7)
              )
        then
            if Cast(S.PrimordialStormAbility) then
                return "Primordial Storm (early, low TTD)"
            end
        end
      end

      -- 1) Initial AOE opener
      if S.FlameShock:IsReady() and Target:DebuffDown(FSDebuff) then
        if Cast(S.FlameShock) then return "flame_shock aoe_open" end
      end
    
      if S.CrashLightning:IsReady() and not S.Ascendance:CooldownUp() and ((Player:BuffStack(S.ElectrostaticWagerBuff) > 9 and Player:BuffUp(S.DoomWindsBuff)) or Player:BuffDown(S.CrashLightningBuff)) then
        if Cast(S.CrashLightning) then return "crash_lightning aoe_open" end
      end
    
      if S.VoltaicBlazeAbility:IsReady() and (S.FlameShockDebuff:AuraActiveCount() < 3) then
        if Cast(S.VoltaicBlazeAbility) then return "voltaic_blaze aoe_open" end
      end
    
      if S.LavaLash:IsReady() and S.MoltenAssault:IsAvailable() and (S.PrimordialWave:IsAvailable() or S.FireNova:IsAvailable())
        and S.FlameShockDebuff:AuraActiveCount() > 0 and S.FlameShockDebuff:AuraActiveCount() < math.min(EnemiesMeleeCount, 3) then
        if Cast(S.LavaLash) then return "lava_lash aoe_open" end
      end
    
      if S.PrimordialWave:IsReady() and MWStacks >= 2 and Target:DebuffUp(FSDebuff) then
        if Cast(S.PrimordialWave) then return "primordial_wave aoe_open" end
      end

      -- 3) Conditional Ascendance
      if S.Ascendance:IsReady() then
        local mode = GetSetting("rotation_mode", "APLcustom")
        local dot_ok = (S.FlameShockDebuff:AuraActiveCount() >= math.min(EnemiesMeleeCount, 4)) or not S.MoltenAssault:IsAvailable()
        if mode == "APLcustom" and dot_ok and 
        TIAction == S.ChainLightning and
        not S.PrimordialWave:CooldownUp() then
          if Cast(S.Ascendance) then return "ascendance aoe_open custom" end
        end
      end

      -- 2) Major cooldowns
      
      -- Doom Winds - with Ascendance
      if S.DoomWinds:IsReady() and hasAscendanceBuff and (Player:BuffRemains(S.AscendanceBuff) > 6 and Player:BuffRemains(S.AscendanceBuff) < 11) then
        if Cast(S.DoomWinds) then return "Doom Winds (with Ascendance)"; end
      end
      -- Doom Winds - no Ascendance
      if S.DoomWinds:IsReady() and not S.Ascendance:CooldownUp() and not hasAscendanceBuff then
        if Cast(S.DoomWinds) then return "Doom Winds (AoE burst)"; end
      end
    
      -- 2) Major cooldowns at high Maelstrom
      if MWStacks >= 5 then
        if S.FeralSpirit:IsReady() then
          if Cast(S.FeralSpirit) then return "feral_spirit aoe_open" end
        end
      end

      -- Primordial Storm – combined conditions
      if S.PrimordialStormAbility:IsReady() then
        -- 1) During Doom Winds
        if MWStacks == MaxMaelstromStacks
          and not hasAscendanceBuff
          and not (S.DoomWinds:IsReady() or S.DoomWinds:CooldownRemains() < 5)
        then
            if Cast(S.PrimordialStormAbility) then
                return "Primordial Storm (during Doom Winds)"
            end

        -- 2) Early if pack’s TTD is low
        elseif MWStacks >= 7
              and HL.CombatTime() < 10
              and FightRemains < 20
              and (
                  S.FlameShockDebuff:AuraActiveCount() >= 5
                  or S.FlameShockDebuff:AuraActiveCount() >= math.ceil(EnemiesMeleeCount * 0.7)
              )
        then
            if Cast(S.PrimordialStormAbility) then
                return "Primordial Storm (early, low TTD)"
            end
        end
      end
      
      -- 5) Tempest
      if S.TempestAbility:IsReady() and MWStacks >= 10 and Player:BuffDown(S.ArcDischargeBuff) then
        if GetSetting('TempestOnMTOnly', true) then
          if Cast(S.TempestAbility) then return "tempest aoe_open (MT only)" end
        else
          if CastTargetIf(S.TempestAbility, EnemiesMelee, "min", EvaluateTargetIfFilterLightningRodRemains) then
            return "tempest aoe_open"
          end
        end
      end
    
      -- 6) Additional AOE finishers
      if S.CrashLightning:IsReady() and MWStacks <= 6 and Player:BuffStack(S.ElectrostaticWagerBuff) > 4 then
        if Cast(S.CrashLightning) then return "crash_lightning aoe_open" end
      end
    
      -- windstrike,target_if=min:debuff.lightning_rod.remains,if=talent.thorims_invocation.enabled&ti_chain_lightning
      if S.Windstrike:IsReady() and (S.ThorimsInvocation:IsAvailable() and TIAction == S.ChainLightning) then
        if rodSetting['rodWS'] and not MainAddon.Toggle:GetToggle("DisableLightningRod") then
          if CastTargetIf(S.Windstrike, EnemiesMelee, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then 
            return "windstrike aoe_open 24"; 
          end
        else
          if Cast(S.Windstrike) then 
            return "windstrike aoe_open 24"; 
          end
        end
      end
      -- chain_lightning,target_if=min:debuff.lightning_rod.remains,if=buff.maelstrom_weapon.stack>=9&(!buff.primordial_storm.up|!buff.legacy_of_the_frost_witch.up)
      if S.ChainLightning:IsReady() and (Player:BuffDown(S.PrimordialStormBuff) and
        (MaelstromStacks >= 5 and Player:BuffUp(S.DoomWindsBuff)) or
        (MaelstromStacks >= 9)
      ) then
        if rodSetting['rodCL'] and not MainAddon.Toggle:GetToggle("DisableLightningRod") then
          if CastTargetIf(S.ChainLightning, EnemiesMelee, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then 
            return "chain_lightning aoe_open 26"; 
          end
        else
          if Cast(S.ChainLightning) then 
            return "chain_lightning aoe_open 26"; 
          end
        end
      end
    
      -- 7) Core fillers
      if S.Stormstrike:IsReady() and Player:BuffStack(S.ConvergingStormsBuff) == 6 and Player:BuffStack(S.StormblastBuff) > 1 then
        if Cast(S.Stormstrike) then return "stormstrike aoe_open" end
      end
    
      if S.CrashLightning:IsReady() then
        if Cast(S.CrashLightning) then return "crash_lightning aoe_open" end
      end
    
      if S.VoltaicBlazeAbility:IsReady() then
        if Cast(S.VoltaicBlazeAbility) then return "voltaic_blaze aoe_open" end
      end
    
      if S.Stormstrike:IsReady() then
        if Cast(S.Stormstrike) then return "stormstrike aoe_open" end
      end
    end    
    
    local function Aoe()
      -- feral_spirit,if=talent.elemental_spirits.enabled|talent.alpha_wolf.enabled
      if S.FeralSpirit:IsReady() and (S.ElementalSpirits:IsAvailable() or S.AlphaWolf:IsAvailable()) then
        if Cast(S.FeralSpirit) then return "feral_spirit aoe 2"; end
      end
      -- run_action_list,name=aoe_open,if=time<15
      if HL.CombatTime() < 15 then
        local ShouldReturn = AoeOpen(); if ShouldReturn then return ShouldReturn; end
        if Cast(S.Pool) then return "Wait for AoeOpen()"; end
      end
      -- flame_shock,if=talent.molten_assault.enabled&!ticking
      if S.FlameShock:IsReady() and (S.MoltenAssault:IsAvailable() and Target:DebuffDown(S.FlameShockDebuff)) then
        if Cast(S.FlameShock) then return "flame_shock aoe 4"; end
      end
      -- ascendance,if=(dot.flame_shock.ticking|!talent.molten_assault.enabled)&ti_chain_lightning
      if AoEON() and S.Ascendance:IsReady() and ((S.FlameShockDebuff:AuraActiveCount() > 0 or not S.MoltenAssault:IsAvailable()) and TIAction == S.ChainLightning) then
        if Cast(S.Ascendance) then return "ascendance aoe 6"; end
      end
      -- tempest,target_if=min:debuff.lightning_rod.remains,if=!buff.arc_discharge.stack>=1&((buff.maelstrom_weapon.stack=buff.maelstrom_weapon.max_stack&!talent.raging_maelstrom.enabled)|(buff.maelstrom_weapon.stack>=9))|(buff.maelstrom_weapon.stack>=5&(tempest_mael_count>30))
      if S.TempestAbility:IsReady() and (Player:BuffDown(S.ArcDischargeBuff) and ((MaelstromStacks == MaxMaelstromStacks and not S.RagingMaelstrom:IsAvailable()) or (MaelstromStacks >= 9)) or (MaelstromStacks >= 5 and (Shaman.TempestMaelstrom > 30))) then
        if GetSetting('TempestOnMTOnly', true) then
          if Cast(S.TempestAbility) then return "tempest aoe 8 (forced MT)"; end
        else
          if CastTargetIf(S.TempestAbility, EnemiesMelee, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then return "tempest aoe 8"; end
        end
      end
      -- feral_spirit,if=(cooldown.doom_winds.remains>30|cooldown.doom_winds.remains<7)
      if S.FeralSpirit:IsReady() and (S.DoomWinds:CooldownRemains() > 30 or S.DoomWinds:CooldownRemains() < 7) then
        if Cast(S.FeralSpirit) then return "feral_spirit aoe 10"; end
      end
      -- doom_winds
      if S.DoomWinds:IsReady() then
        if Cast(S.DoomWinds) then return "doom_winds aoe 12"; end
      end
      -- primordial_wave,if=dot.flame_shock.ticking&(active_dot.flame_shock=active_enemies|active_dot.flame_shock=6)
      if S.PrimordialWave:IsReady() and (S.FlameShockDebuff:AuraActiveCount() == mathmin(EnemiesMeleeCount, 6)) then
        if Cast(S.PrimordialWave) then return "primordial_wave aoe 14"; end
      end
      -- primordial_storm,if=(buff.maelstrom_weapon.stack>=10)&(buff.doom_winds.up|!talent.doom_winds.enabled|(cooldown.doom_winds.remains>buff.primordial_storm.remains)|(buff.primordial_storm.remains<2*gcd))
      if S.PrimordialStormAbility:IsReady() and ((MaelstromStacks >= 10) and (Player:BuffUp(S.DoomWindsBuff) or not S.DoomWinds:IsAvailable() or (S.DoomWinds:CooldownRemains() > Player:BuffRemains(S.PrimordialStormBuff)) or (Player:BuffRemains(S.PrimordialStormBuff) < 2 * Player:GCD()))) then
        if Cast(S.PrimordialStormAbility) then return "primordial_storm aoe 16"; end
      end
      -- crash_lightning,if=talent.converging_storms.enabled&buff.electrostatic_wager.stack>6|!buff.crash_lightning.up
      if S.CrashLightning:IsReady() and (S.ConvergingStorms:IsAvailable() and Player:BuffStack(S.ElectrostaticWagerBuff) > 6 or Player:BuffDown(S.CrashLightningBuff)) then
        if Cast(S.CrashLightning) then return "crash_lightning aoe 18"; end
      end
      -- windstrike,target_if=min:debuff.lightning_rod.remains,if=talent.thorims_invocation.enabled&buff.maelstrom_weapon.stack>0&ti_chain_lightning
      if S.Windstrike:IsReady() and (S.ThorimsInvocation:IsAvailable() and MaelstromStacks > 0 and TIAction == S.ChainLightning) then
        if rodSetting['rodWS'] and not MainAddon.Toggle:GetToggle("DisableLightningRod") then
          if CastTargetIf(S.Windstrike, EnemiesMelee, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then 
            return "windstrike aoe 20"; 
          end
        else
          if Cast(S.Windstrike) then 
            return "windstrike aoe 20"; 
          end
        end
      end
      -- crash_lightning,if=talent.converging_storms.enabled&talent.alpha_wolf.enabled
      if S.CrashLightning:IsReady() and (S.ConvergingStorms:IsAvailable() and S.AlphaWolf:IsAvailable()) then
        if Cast(S.CrashLightning) then return "crash_lightning aoe 22"; end
      end
      -- stormstrike,if=buff.converging_storms.stack=6&buff.stormblast.stack>0&buff.legacy_of_the_frost_witch.up&buff.maelstrom_weapon.stack<=8
      if S.Stormstrike:IsReady() and (Player:BuffStack(S.ConvergingStormsBuff) == 6 and Player:BuffUp(S.StormblastBuff) and Player:BuffUp(S.LegacyoftheFrostWitchBuff) and MaelstromStacks <= 8) then
        if Cast(S.Stormstrike) then return "stormstrike aoe 24"; end
      end
      -- crash_lightning,if=buff.maelstrom_weapon.stack<=8
      if S.CrashLightning:IsReady() and (MaelstromStacks <= 8) then
        if Cast(S.CrashLightning) then return "crash_lightning aoe 26"; end
      end
      -- voltaic_blaze,if=buff.maelstrom_weapon.stack<=8
      if S.VoltaicBlazeAbility:IsReady() and (MaelstromStacks <= 8) then
        if Cast(S.VoltaicBlazeAbility) then return "voltaic_blaze aoe 28"; end
      end
      -- chain_lightning,target_if=min:debuff.lightning_rod.remains,if=buff.maelstrom_weapon.stack>=5&!buff.primordial_storm.up&(cooldown.crash_lightning.remains>=1|!talent.alpha_wolf.enabled)
      if S.ChainLightning:IsReady() and (MaelstromStacks >= 5 and Player:BuffDown(S.PrimordialStormBuff) and (S.CrashLightning:CooldownRemains() >= 1 or not S.AlphaWolf:IsAvailable())) then
        if rodSetting['rodCL'] and not MainAddon.Toggle:GetToggle("DisableLightningRod") then
          if CastTargetIf(S.ChainLightning, EnemiesMelee, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then 
            return "chain_lightning aoe 30"; 
          end
        else
          if Cast(S.ChainLightning) then 
            return "chain_lightning aoe 30"; 
          end
        end
      end
      -- fire_nova,if=active_dot.flame_shock=6|(active_dot.flame_shock>=4&active_dot.flame_shock=active_enemies)
      if S.FireNova:IsReady() and (S.FlameShockDebuff:AuraActiveCount() == 6 or (S.FlameShockDebuff:AuraActiveCount() >= 4 and S.FlameShockDebuff:AuraActiveCount() >= EnemiesMeleeCount)) then
        if Cast(S.FireNova) then return "fire_nova aoe 32"; end
      end
      -- stormstrike,if=talent.stormblast.enabled&talent.stormflurry.enabled
      if S.Stormstrike:IsReady() and (S.Stormblast:IsAvailable() and S.Stormflurry:IsAvailable()) then
        if Cast(S.Stormstrike) then return "stormstrike aoe 34"; end
      end
      -- voltaic_blaze
      if S.VoltaicBlazeAbility:IsReady() then
        if Cast(S.VoltaicBlazeAbility) then return "voltaic_blaze aoe 36"; end
      end
      -- lava_lash,target_if=min:debuff.lashing_flames.remains,if=talent.lashing_flames.enabled|talent.molten_assault.enabled&dot.flame_shock.ticking
      if S.LavaLash:IsReady() and (S.LashingFlames:IsAvailable() or S.MoltenAssault:IsAvailable() and S.FlameShockDebuff:AuraActiveCount() > 0) then
        if CastTargetIf(S.LavaLash, EnemiesMelee, "min", EvaluateTargetIfFilterLavaLash, nil) then return "lava_lash aoe 38"; end
      end
      -- ice_strike,if=talent.hailstorm.enabled&!buff.ice_strike.up
      if S.IceStrike:IsReady() and (S.Hailstorm:IsAvailable() and Player:BuffDown(S.IceStrikeBuff)) then
        if Cast(S.IceStrike) then return "ice_strike aoe 40"; end
      end
      -- frost_shock,if=talent.hailstorm.enabled&buff.hailstorm.up
      if S.FrostShock:IsReady() and (S.Hailstorm:IsAvailable() and Player:BuffUp(S.HailstormBuff)) then
        if Cast(S.FrostShock) then return "frost_shock aoe 42"; end
      end
      -- sundering
      if S.Sundering:IsReady() then
        if Cast(S.Sundering) then return "sundering aoe 44"; end
      end
      -- flame_shock,if=talent.molten_assault.enabled&!ticking
      -- Note: Duplicate of flame_shock aoe 4.
      -- flame_shock,target_if=min:dot.flame_shock.remains,if=(talent.fire_nova.enabled|talent.primordial_wave.enabled)&(active_dot.flame_shock<active_enemies)&active_dot.flame_shock<6
      if S.FlameShock:IsReady() and ((S.FireNova:IsAvailable() or S.PrimordialWave:IsAvailable()) and (S.FlameShockDebuff:AuraActiveCount() < EnemiesMeleeCount) and S.FlameShockDebuff:AuraActiveCount() < 6) then
        if CastCycle(S.FlameShock, EnemiesMelee, EvaluateCycleFlameShock) then return "flame_shock aoe 46"; end
      end
      -- fire_nova,if=active_dot.flame_shock>=3
      if S.FireNova:IsReady() and (S.FlameShockDebuff:AuraActiveCount() >= 3) then
        if Cast(S.FireNova) then return "fire_nova aoe 48"; end
      end
      -- stormstrike,if=buff.crash_lightning.up&(talent.deeply_rooted_elements.enabled|buff.converging_storms.stack=buff.converging_storms.max_stack)
      if S.Stormstrike:IsReady() and (Player:BuffUp(S.CrashLightningBuff) and (S.DeeplyRootedElements:IsAvailable() or Player:BuffStack(S.ConvergingStormsBuff) == MaxConvergingStormsStacks)) then
        if Cast(S.Stormstrike) then return "stormstrike aoe 50"; end
      end
      -- crash_lightning,if=talent.crashing_storms.enabled&buff.cl_crash_lightning.up
      if S.CrashLightning:IsReady() and (S.CrashingStorms:IsAvailable() and Player:BuffUp(S.CLCrashLightningBuff)) then
        if Cast(S.CrashLightning) then return "crash_lightning aoe 52"; end
      end
      -- windstrike
      if S.Windstrike:IsReady() then
        if Cast(S.Windstrike) then return "windstrike aoe 54"; end
      end
      -- stormstrike
      if S.Stormstrike:IsReady() then
        if Cast(S.Stormstrike) then return "stormstrike aoe 56"; end
      end
      -- ice_strike
      if S.IceStrike:IsReady() then
        if Cast(S.IceStrike) then return "ice_strike aoe 58"; end
      end
      -- lava_lash
      if S.LavaLash:IsReady() then
        if Cast(S.LavaLash) then return "lava_lash aoe 60"; end
      end
      -- crash_lightning
      if S.CrashLightning:IsReady() then
        if Cast(S.CrashLightning) then return "crash_lightning aoe 62"; end
      end
      -- fire_nova,if=active_dot.flame_shock>=2
      if S.FireNova:IsReady() and (S.FlameShockDebuff:AuraActiveCount() >= 2) then
        if Cast(S.FireNova) then return "fire_nova aoe 64"; end
      end
      -- chain_lightning,target_if=min:debuff.lightning_rod.remains,if=buff.maelstrom_weapon.stack>=5&!buff.primordial_storm.up
      if S.ChainLightning:IsReady() and (MaelstromStacks >= 5 and Player:BuffDown(S.PrimordialStormBuff)) then
        if rodSetting['rodCL'] and not MainAddon.Toggle:GetToggle("DisableLightningRod") then
          if CastTargetIf(S.ChainLightning, EnemiesMelee, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then 
            return "chain_lightning aoe 66"; 
          end
        else
          if Cast(S.ChainLightning) then 
            return "chain_lightning aoe 66"; 
          end
        end
      end
      -- flame_shock,if=!ticking
      if S.FlameShock:IsReady() and (Target:DebuffDown(S.FlameShockDebuff)) then
        if Cast(S.FlameShock) then return "flame_shock aoe 68"; end
      end
      -- frost_shock,if=!talent.hailstorm.enabled
      if S.FrostShock:IsReady() and (not S.Hailstorm:IsAvailable()) then
        if Cast(S.FrostShock) then return "frost_shock aoe 70"; end
      end
    end

    local function customAoe()
      -- Local variables for improved readability
      local FSDebuff = S.FlameShockDebuff
      local FSCount = FSDebuff:AuraActiveCount()
      local MWStacks = MaelstromStacks
      local hasDoomWinds = Player:BuffUp(S.DoomWindsBuff)
      local hasAscendanceBuff = Player:BuffUp(S.AscendanceBuff)
      
      -- Feral Spirit - Specialized usage with key talents
      if S.FeralSpirit:IsReady() and (S.ElementalSpirits:IsAvailable() or S.AlphaWolf:IsAvailable()) then
        if Cast(S.FeralSpirit) then return "Feral Spirit (Elemental Spirits/Alpha Wolf)"; end
      end

      -- Ascendance - Optimized usage during key conditions
      if S.Ascendance:IsReady() and 
        ((FSCount == EnemiesMeleeCount or FSCount >= 3 or not S.MoltenAssault:IsAvailable()) and 
          TIAction == S.ChainLightning) then
        if Cast(S.Ascendance) then return "Ascendance (entering Doom Winds window)"; end
      end
    
      -- Primordial Storm – combined Ascendance & early low TTD
      if S.PrimordialStormAbility:IsReady() then
        -- 1) During Ascendance
        if MWStacks >= 8
          and hasAscendanceBuff
          and Player:BuffRemains(S.AscendanceBuff) < 4
        then
            if Cast(S.PrimordialStormAbility) then
                return "Primordial Storm (during Ascendance)"
            end

        -- 2) Early if pack’s TTD is low
        elseif MWStacks >= 7
              and HL.CombatTime() < 10
              and FightRemains < 20
              and (
                  S.FlameShockDebuff:AuraActiveCount() >= 5
                  or S.FlameShockDebuff:AuraActiveCount() >= math.ceil(EnemiesMeleeCount * 0.7)
              )
        then
            if Cast(S.PrimordialStormAbility) then
                return "Primordial Storm (early, low TTD)"
            end
        end
      end

      -- Tempest - High priority resource dump
      if S.TempestAbility:IsReady() and 
        (MWStacks == MaxMaelstromStacks or (MWStacks >= 8 and Shaman.TempestMaelstrom > 30)) then
        if GetSetting('TempestOnMTOnly', true) then
          if Cast(S.TempestAbility) then return "Tempest (main target dump)"; end
        else
          if CastTargetIf(S.TempestAbility, EnemiesMelee, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then 
            return "Tempest (optimal target dump)"; 
          end
        end
      end
      
      -- Early fight special handling
      if HL.CombatTime() < 15 then
        local ShouldReturn = customAoeOpen(); 
        if ShouldReturn then return ShouldReturn; end
        if Cast(S.Pool) then return "Wait for AoE opener to complete"; end
      end
      
      -- Flame Shock - Initial application with Molten Assault
      if S.FlameShock:IsReady() and (S.MoltenAssault:IsAvailable() and Target:DebuffDown(FSDebuff)) then
        if Cast(S.FlameShock) then return "Flame Shock (initial Molten Assault)"; end
      end
  
    
      -- Safety check during Ascendance for Tempest timing
      if Player:BuffUp(S.AscendanceBuff) and S.TempestAbility:IsReady() and 
        MWStacks > 8 and Player:GCDRemains() ~= 0 then
        return "Wait for GCD in Ascendance"
      end
      
      -- Tempest - Optimized usage with resource management
      if S.TempestAbility:IsReady() and 
        (Player:BuffDown(S.ArcDischargeBuff) and 
          ((MWStacks == MaxMaelstromStacks and not S.RagingMaelstrom:IsAvailable()) or (MWStacks == 9)) or 
          (MWStacks >= 8 and Shaman.TempestMaelstrom > 30)) then
        if GetSetting('TempestOnMTOnly', true) then
          if Cast(S.TempestAbility) then return "Tempest (resources ready)"; end
        else
          if CastTargetIf(S.TempestAbility, EnemiesMelee, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then 
            return "Tempest (optimal target)"; 
          end
        end
      end
      
      -- Feral Spirit - Timed with Doom Winds cooldown
      if S.FeralSpirit:IsReady() and 
        (S.DoomWinds:CooldownRemains() >= 25 or S.DoomWinds:CooldownRemains() < 3) then
        if Cast(S.FeralSpirit) then return "Feral Spirit (DW timing)"; end
      end
      
      -- Doom Winds - with Ascendance
      if S.DoomWinds:IsReady() and hasAscendanceBuff and (Player:BuffRemains(S.AscendanceBuff) > 6 and Player:BuffRemains(S.AscendanceBuff) < 11) then
        if Cast(S.DoomWinds) then return "Doom Winds (with Ascendance)"; end
      end
      
      -- Doom Winds - no Ascendance
      if S.DoomWinds:IsReady() and not S.Ascendance:CooldownUp() and not hasAscendanceBuff then
        if Cast(S.DoomWinds) then return "Doom Winds (AoE burst)"; end
      end
      
      -- Primordial Wave - With optimal DoT spread
      if S.PrimordialWave:IsReady() and 
        ((FSCount == EnemiesMeleeCount) or (FSCount >= 3 and TIAction == S.ChainLightning)) then
        if Cast(S.PrimordialWave) then return "Primordial Wave (optimal DoT spread)"; end
      end

      -- Primordial Storm – combined conditions
      if S.PrimordialStormAbility:IsReady() then
        -- 1) During Doom Winds
        if MWStacks == MaxMaelstromStacks
          and not hasAscendanceBuff
          and not (S.DoomWinds:IsReady() or S.DoomWinds:CooldownRemains() < 5)
        then
            if Cast(S.PrimordialStormAbility) then
                return "Primordial Storm (during Doom Winds)"
            end

        -- 2) Early if pack’s TTD is low
        elseif MWStacks >= 7
              and HL.CombatTime() < 10
              and FightRemains < 20
              and (
                  S.FlameShockDebuff:AuraActiveCount() >= 5
                  or S.FlameShockDebuff:AuraActiveCount() >= math.ceil(EnemiesMeleeCount * 0.7)
              )
        then
            if Cast(S.PrimordialStormAbility) then
                return "Primordial Storm (early, low TTD)"
            end
        end
      end
      
      -- Crash Lightning - With stacks or buff maintenance
      if S.CrashLightning:IsReady() and 
        (S.ConvergingStorms:IsAvailable() and Player:BuffStack(S.ElectrostaticWagerBuff) > 6 or 
          Player:BuffDown(S.CrashLightningBuff)) then
        if Cast(S.CrashLightning) then return "Crash Lightning (stack/buff maintenance)"; end
      end
      
      -- Windstrike - With Thorim's procs
      if S.Windstrike:IsReady() and 
        (S.ThorimsInvocation:IsAvailable() and MWStacks > 0 and TIAction == S.ChainLightning) then
        if rodSetting['rodWS'] and not MainAddon.Toggle:GetToggle("DisableLightningRod") then
          if CastTargetIf(S.Windstrike, EnemiesMelee, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then 
            return "Windstrike (Thorim's - optimal target)"; 
          end
        else
          if Cast(S.Windstrike) then 
            return "Windstrike (Thorim's proc)"; 
          end
        end
      end
      
      -- Crash Lightning - Talent synergy optimization
      if S.CrashLightning:IsReady() and (S.ConvergingStorms:IsAvailable() and S.AlphaWolf:IsAvailable()) then
        if Cast(S.CrashLightning) then return "Crash Lightning (talent synergy)"; end
      end
      
      -- Stormstrike - During maximum buff accumulation
      if S.Stormstrike:IsReady() and 
        (Player:BuffStack(S.ConvergingStormsBuff) == 6 and 
          Player:BuffUp(S.StormblastBuff) and 
          Player:BuffUp(S.LegacyoftheFrostWitchBuff) and 
          MWStacks <= 8) then
        if Cast(S.Stormstrike) then return "Stormstrike (max buff window)"; end
      end
      
      -- Crash Lightning - Resource building
      if S.CrashLightning:IsReady() and (MWStacks <= 8) then
        if Cast(S.CrashLightning) then return "Crash Lightning (MW building)"; end
      end
      
      -- Voltaic Blaze - Resource building
      if S.VoltaicBlazeAbility:IsReady() and (MWStacks <= 8) then
        if Cast(S.VoltaicBlazeAbility) then return "Voltaic Blaze (MW building)"; end
      end
      
      -- Chain Lightning - Resource dump
      if S.ChainLightning:IsReady() and 
        Player:BuffDown(S.PrimordialStormBuff) and 
        not S.TempestAbility:IsReady() and 
        (MWStacks >= 9 and (S.CrashLightning:CooldownRemains() >= 1 or not S.AlphaWolf:IsAvailable())) then
        if rodSetting['rodCL'] and not MainAddon.Toggle:GetToggle("DisableLightningRod") then
          if CastTargetIf(S.ChainLightning, EnemiesMelee, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then 
            return "Chain Lightning (optimal target)"; 
          end
        else
          if Cast(S.ChainLightning) then 
            return "Chain Lightning (MW dump)"; 
          end
        end
      end
      
      -- Fire Nova - With maximum DoT spread
      if S.FireNova:IsReady() and 
        (FSCount == 6 or (FSCount >= 4 and FSCount >= EnemiesMeleeCount)) then
        if Cast(S.FireNova) then return "Fire Nova (optimal spread)"; end
      end
      
      -- Stormstrike - With key talents for enhanced value
      if S.Stormstrike:IsReady() and 
        MWStacks ~= MaxMaelstromStacks and 
        (S.Stormblast:IsAvailable() and S.Stormflurry:IsAvailable()) then
        if Cast(S.Stormstrike) then return "Stormstrike (talent synergy)"; end
      end
      
      -- Voltaic Blaze - General application
      if S.VoltaicBlazeAbility:IsReady() then
        if Cast(S.VoltaicBlazeAbility) then return "Voltaic Blaze (general)"; end
      end
      
      -- Lava Lash - With DoT spread talents
      if S.LavaLash:IsReady() and 
        (S.LashingFlames:IsAvailable() or 
          (S.MoltenAssault:IsAvailable() and FSCount > 0)) then
        if CastTargetIf(S.LavaLash, EnemiesMelee, "min", EvaluateTargetIfFilterLavaLash, nil) then 
          return "Lava Lash (DoT spread)"; 
        end
      end
      
      -- Ice Strike - To maintain Hailstorm buff
      if S.IceStrike:IsReady() and (S.Hailstorm:IsAvailable() and Player:BuffDown(S.IceStrikeBuff)) then
        if Cast(S.IceStrike) then return "Ice Strike (Hailstorm refresh)"; end
      end
      
      -- Frost Shock - To utilize Hailstorm buff
      if S.FrostShock:IsReady() and (S.Hailstorm:IsAvailable() and Player:BuffUp(S.HailstormBuff)) then
        if Cast(S.FrostShock) then return "Frost Shock (Hailstorm consumer)"; end
      end
      
      -- Sundering - AoE stun and damage
      if S.Sundering:IsReady() then
        if Cast(S.Sundering) then return "Sundering (AoE damage)"; end
      end
      
      -- Flame Shock - Cycling to spread DoTs
      if S.FlameShock:IsReady() and 
        ((S.FireNova:IsAvailable() or S.PrimordialWave:IsAvailable()) and 
          (FSCount < EnemiesMeleeCount) and FSCount < 6) then
        if CastCycle(S.FlameShock, EnemiesMelee, EvaluateCycleFlameShock) then 
          return "Flame Shock (DoT spread cycling)"; 
        end
      end
      
      -- Fire Nova - With sufficient DoTs active
      if S.FireNova:IsReady() and (FSCount >= 3) then
        if Cast(S.FireNova) then return "Fire Nova (3+ DoTs)"; end
      end
      
      -- Stormstrike - During buff windows
      if S.Stormstrike:IsReady() and 
        (Player:BuffUp(S.CrashLightningBuff) and 
          (S.DeeplyRootedElements:IsAvailable() or 
          Player:BuffStack(S.ConvergingStormsBuff) == MaxConvergingStormsStacks)) then
        if Cast(S.Stormstrike) then return "Stormstrike (buff window)"; end
      end
      
      -- Crash Lightning - For AoE buff extension
      if S.CrashLightning:IsReady() and 
        (S.CrashingStorms:IsAvailable() and Player:BuffUp(S.CLCrashLightningBuff)) then
        if Cast(S.CrashLightning) then return "Crash Lightning (buff extension)"; end
      end
      
      -- Windstrike - During Ascendance
      if S.Windstrike:IsReady() then
        if Cast(S.Windstrike) then return "Windstrike (Ascendance)"; end
      end
      
      -- Stormstrike - Core rotational ability
      if S.Stormstrike:IsReady() then
        if Cast(S.Stormstrike) then return "Stormstrike (core rotational)"; end
      end
      
      -- Ice Strike - General filler
      if S.IceStrike:IsReady() then
        if Cast(S.IceStrike) then return "Ice Strike (filler)"; end
      end
      
      -- Lava Lash - General filler
      if S.LavaLash:IsReady() then
        if Cast(S.LavaLash) then return "Lava Lash (filler)"; end
      end
      
      -- Crash Lightning - AoE filler
      if S.CrashLightning:IsReady() then
        if Cast(S.CrashLightning) then return "Crash Lightning (filler)"; end
      end
      
      -- Fire Nova - With minimal spread
      if S.FireNova:IsReady() and (FSCount >= 2) then
        if Cast(S.FireNova) then return "Fire Nova (2+ DoTs)"; end
      end
      
      -- Chain Lightning - Resource dump
      if S.ChainLightning:IsReady() and (MWStacks >= 9 and Player:BuffDown(S.PrimordialStormBuff)) then
        if rodSetting['rodCL'] and not MainAddon.Toggle:GetToggle("DisableLightningRod") then
          if CastTargetIf(S.ChainLightning, EnemiesMelee, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then 
            return "Chain Lightning (resource dump)"; 
          end
        else
          if Cast(S.ChainLightning) then 
            return "Chain Lightning (resource dump)"; 
          end
        end
      end
      
      -- Flame Shock - Basic application
      if S.FlameShock:IsReady() and Target:DebuffDown(FSDebuff) then
        if Cast(S.FlameShock) then return "Flame Shock (basic application)"; end
      end
      
      -- Frost Shock - Last resort filler
      if S.FrostShock:IsReady() and not S.Hailstorm:IsAvailable() then
        if Cast(S.FrostShock) then return "Frost Shock (last resort)"; end
      end
    end    

    local function AoeTotemicOpen()
      -- flame_shock,if=!ticking&!(active_dot.flame_shock=active_enemies|active_dot.flame_shock=6)
      if S.FlameShock:IsReady() and (Target:DebuffDown(S.FlameShockDebuff) and not (S.FlameShockDebuff:AuraActiveCount() == mathmin(EnemiesMeleeCount, 6))) then
        if Cast(S.FlameShock) then return "flame_shock aoe_totemic_open 2"; end
      end
      -- lava_lash,if=!pet.surging_totem.active&!(active_dot.flame_shock=active_enemies|active_dot.flame_shock=6)
      if S.LavaLash:IsReady() and (not TotemFinder(S.SurgingTotem) and not (S.FlameShockDebuff:AuraActiveCount() == mathmin(EnemiesMeleeCount, 6))) then
        if Cast(S.LavaLash) then return "lava_lash aoe_totemic_open 4"; end
      end
      -- surging_totem
      if S.SurgingTotem:IsReady() then
        if Cast(S.SurgingTotem) then return "surging_totem aoe_totemic_open 6"; end
      end
      -- flame_shock,if=!ticking
      if S.FlameShock:IsReady() and (Target:DebuffDown(S.FlameShockDebuff)) then
        if Cast(S.FlameShock) then return "flame_shock aoe_totemic_open 8"; end
      end
      -- fire_nova,if=talent.swirling_maelstrom.enabled&dot.flame_shock.ticking&(active_dot.flame_shock=active_enemies|active_dot.flame_shock=6)
      if S.FireNova:IsReady() and (S.SwirlingMaelstrom:IsAvailable() and S.FlameShockDebuff:AuraActiveCount() == mathmin(EnemiesMeleeCount, 6)) then
        if Cast(S.FireNova) then return "fire_nova aoe_totemic_open 10"; end
      end
      -- primordial_wave,if=dot.flame_shock.ticking&(active_dot.flame_shock=active_enemies|active_dot.flame_shock=6)
      if S.PrimordialWave:IsReady() and (S.FlameShockDebuff:AuraActiveCount() == mathmin(EnemiesMeleeCount, 6)) then
        if Cast(S.PrimordialWave) then return "primordial_wave aoe_totemic_open 12"; end
      end
      -- elemental_blast,if=buff.maelstrom_weapon.stack>=10&!buff.legacy_of_the_frost_witch.up&cooldown.doom_winds.remains=0
      if S.ElementalBlast:IsReady() and (MaelstromStacks >= 10 and Player:BuffDown(S.LegacyoftheFrostWitchBuff) and S.DoomWinds:CooldownUp()) then
        if Cast(S.ElementalBlast) then return "elemental_blast aoe_totemic_open 14"; end
      end
      -- doom_winds,if=buff.legacy_of_the_frost_witch.up
      if S.DoomWinds:IsReady() and (Player:BuffUp(S.LegacyoftheFrostWitchBuff)) then
        if Cast(S.DoomWinds) then return "doom_winds aoe_totemic_open 16"; end
      end
      -- crash_lightning,if=(buff.electrostatic_wager.stack>9&buff.doom_winds.up)|!buff.crash_lightning.up
      if S.CrashLightning:IsReady() and ((Player:BuffStack(S.ElectrostaticWagerBuff) > 9 and Player:BuffUp(S.DoomWindsBuff)) or Player:BuffDown(S.CrashLightningBuff)) then
        if Cast(S.CrashLightning) then return "crash_lightning aoe_totemic_open 18"; end
      end
      -- primordial_storm,if=(buff.maelstrom_weapon.stack>=10)&(buff.doom_winds.remains<=gcd.max|!buff.doom_winds.up&cooldown.doom_winds.remains>buff.primordial_storm.remains)
      if S.PrimordialStormAbility:IsReady() and ((MaelstromStacks >= 10) and (Player:BuffRemains(S.DoomWindsBuff) <= Player:GCD() or Player:BuffDown(S.DoomWindsBuff) and S.DoomWinds:CooldownRemains() > Player:BuffRemains(S.PrimordialStormBuff))) then
        if Cast(S.PrimordialStormAbility) then return "primordial_storm aoe_totemic_open 20"; end
      end
      -- lava_lash,if=buff.hot_hand.up
      if S.LavaLash:IsReady() and (Player:BuffUp(S.HotHandBuff)) then
        if Cast(S.LavaLash) then return "lava_lash aoe_totemic_open 22"; end
      end
      -- sundering,if=buff.legacy_of_the_frost_witch.up|(buff.earthen_weapon.stack>=2&buff.primordial_storm.up)
      if S.Sundering:IsReady() and (Player:BuffUp(S.LegacyoftheFrostWitchBuff) or (Player:BuffStack(S.EarthenWeaponBuff) >= 2 and Player:BuffUp(S.PrimordialStormBuff))) then
        if Cast(S.Sundering) then return "sundering aoe_totemic_open 24"; end
      end
      -- lava_lash,if=(buff.legacy_of_the_frost_witch.up&buff.whirling_fire.up)
      if S.LavaLash:IsReady() and (Player:BuffUp(S.LegacyoftheFrostWitchBuff) and Player:BuffUp(S.WhirlingFireBuff)) then
        if Cast(S.LavaLash) then return "lava_lash aoe_totemic_open 26"; end
      end
      -- crash_lightning,if=(buff.earthen_weapon.stack>=2&buff.primordial_storm.up&buff.doom_winds.up)
      if S.CrashLightning:IsReady() and (Player:BuffStack(S.EarthenWeaponBuff) >= 2 and Player:BuffUp(S.PrimordialStormBuff) and Player:BuffUp(S.DoomWindsBuff)) then
        if Cast(S.CrashLightning) then return "crash_lightning aoe_totemic_open 28"; end
      end
      if MaelstromStacks >= 10 then
        -- elemental_blast,if=buff.maelstrom_weapon.stack>=10
        if S.ElementalBlast:IsReady() then
          if Cast(S.ElementalBlast) then return "elemental_blast aoe_totemic_open 30"; end
        end
        -- chain_lightning,if=buff.maelstrom_weapon.stack>=10
        if S.ChainLightning:IsReady() then
          if Cast(S.ChainLightning) then return "chain_lightning aoe_totemic_open 32"; end
        end
      end
      -- frost_shock,if=talent.hailstorm.enabled&buff.hailstorm.up&pet.searing_totem.active
      if S.FrostShock:IsReady() and (S.Hailstorm:IsAvailable() and Player:BuffUp(S.HailstormBuff) and Shaman.SearingTotemActive) then
        if Cast(S.FrostShock) then return "frost_shock aoe_totemic_open 34"; end
      end
      -- fire_nova,if=pet.searing_totem.active&dot.flame_shock.ticking&(active_dot.flame_shock=active_enemies|active_dot.flame_shock=6)
      if S.FireNova:IsReady() and (Shaman.SearingTotemActive and S.FlameShockDebuff:AuraActiveCount() == mathmin(EnemiesMeleeCount, 6)) then
        if Cast(S.FireNova) then return "fire_nova aoe_totemic_open 36"; end
      end
      -- ice_strike
      if S.IceStrike:IsReady() then
        if Cast(S.IceStrike) then return "ice_strike aoe_totemic_open 38"; end
      end
      -- stormstrike,if=buff.maelstrom_weapon.stack<10&!buff.legacy_of_the_frost_witch.up
      if S.Stormstrike:IsReady() and (MaelstromStacks < 10 and Player:BuffDown(S.LegacyoftheFrostWitchBuff)) then
        if Cast(S.Stormstrike) then return "stormstrike aoe_totemic_open 40"; end
      end
      -- lava_lash
      if S.LavaLash:IsReady() then
        if Cast(S.LavaLash) then return "lava_lash aoe_totemic_open 42"; end
      end
      -- crash_lightning,if=talent.crashing_storms.enabled
      if S.CrashLightning:IsReady() and (S.CrashingStorms:IsAvailable()) then
        if Cast(S.CrashLightning) then return "crash_lightning aoe_totemic_open 44"; end
      end
      -- fire_nova,if=dot.flame_shock.ticking&(active_dot.flame_shock=active_enemies|active_dot.flame_shock=6)
      if S.FireNova:IsReady() and (S.FlameShockDebuff:AuraActiveCount() == mathmin(EnemiesMeleeCount, 6)) then
        if Cast(S.FireNova) then return "fire_nova aoe_totemic_open 46"; end
      end
      -- frost_shock,if=talent.hailstorm.enabled&buff.hailstorm.up
      if S.FrostShock:IsReady() and (S.Hailstorm:IsAvailable() and Player:BuffUp(S.HailstormBuff)) then
        if Cast(S.FrostShock) then return "frost_shock aoe_totemic_open 48"; end
      end
      -- crash_lightning
      if S.CrashLightning:IsReady() then
        if Cast(S.CrashLightning) then return "crash_lightning aoe_totemic_open 50"; end
      end
      -- ice_strike,if=talent.hailstorm.enabled&!buff.ice_strike.up
      if S.IceStrike:IsReady() and (S.Hailstorm:IsAvailable() and Player:BuffDown(S.IceStrikeBuff)) then
        if Cast(S.IceStrike) then return "ice_strike aoe_totemic_open 52"; end
      end
      if MaelstromStacks >= 5 and Player:BuffDown(S.PrimordialStormBuff) then
        -- elemental_blast,if=buff.maelstrom_weapon.stack>=5&!buff.primordial_storm.up
        if S.ElementalBlast:IsReady() then
          if Cast(S.ElementalBlast) then return "elemental_blast aoe_totemic_open 54"; end
        end
        -- chain_lightning,if=buff.maelstrom_weapon.stack>=5&!buff.primordial_storm.up
        if S.ChainLightning:IsReady() then
          if Cast(S.ChainLightning) then return "chain_lightning aoe_totemic_open 56"; end
        end
      end
      -- stormstrike
      if S.Stormstrike:IsReady() then
        if Cast(S.Stormstrike) then return "stormstrike aoe_totemic_open 58"; end
      end
    end    
    
    local function AoeTotemic()
      -- run_action_list,name=aoe_totemic_open,if=(time<=16)
      if HL.CombatTime() <= 16 then
        local ShouldReturn = AoeTotemicOpen(); if ShouldReturn then return ShouldReturn; end
        if Cast(S.Pool) then return "Wait for AoeTotemicOpen()"; end
      end
      -- surging_totem
      if S.SurgingTotem:IsReady() then
        if Cast(S.SurgingTotem) then return "surging_totem aoe_totemic 2"; end
      end
      -- ascendance,if=ti_chain_lightning
      if S.Ascendance:IsReady() and (TIAction == S.ChainLightning) then
        if Cast(S.Ascendance) then return "ascendance aoe_totemic 4"; end
      end
      -- crash_lightning,if=talent.crashing_storms.enabled&(active_enemies>=15-5*talent.unruly_winds.enabled)
      if S.CrashLightning:IsReady() and (S.CrashingStorms:IsAvailable() and (EnemiesMeleeCount >= 15 - 5 * num(S.UnrulyWinds:IsAvailable()))) then
        if Cast(S.CrashLightning) then return "crash_lightning aoe_totemic 6"; end
      end
      -- feral_spirit,if=(cooldown.doom_winds.remains>15|cooldown.doom_winds.remains<=7)|buff.earthen_weapon.stack>=2
      if S.FeralSpirit:IsReady() and ((S.DoomWinds:CooldownRemains() > 15 or S.DoomWinds:CooldownRemains() < 7) or Player:BuffStack(S.EarthenWeaponBuff) >= 2) then
        if Cast(S.FeralSpirit) then return "feral_spirit aoe_totemic 8"; end
      end
      -- primordial_storm,if=(buff.maelstrom_weapon.stack>=10)&(buff.doom_winds.remains<=gcd*3|!buff.doom_winds.up&cooldown.doom_winds.remains>buff.primordial_storm.remains|buff.earthen_weapon.stack>=4|buff.earthen_weapon.remains<=gcd*3)
      if S.PrimordialStormAbility:IsReady() and ((MaelstromStacks >= 10) and (Player:BuffRemains(S.DoomWindsBuff) <= Player:GCD() * 3 or Player:BuffDown(S.DoomWindsBuff) and S.DoomWinds:CooldownRemains() > Player:BuffRemains(S.PrimordialStormBuff) or Player:BuffStack(S.EarthenWeaponBuff) >= 4 or Player:BuffRemains(S.EarthenWeaponBuff) <= Player:GCD() * 3)) then
        if Cast(S.PrimordialStormAbility) then return "primordial_storm aoe_totemic 10"; end
      end
      -- fflame_shock,if=!ticking&(talent.ashen_catalyst.enabled|talent.primordial_wave.enabled)&(active_dot.flame_shock<active_enemies|active_dot.flame_shock<6)
      if S.FlameShock:IsReady() and (Target:DebuffDown(S.FlameShockDebuff) and (S.AshenCatalyst:IsAvailable() or S.PrimordialWave:IsAvailable()) and (S.FlameShockDebuff:AuraActiveCount() < EnemiesMeleeCount or S.FlameShockDebuff:AuraActiveCount() < 6)) then
        if Cast(S.FlameShock) then return "flame_shock aoe_totemic 12"; end
      end
      -- doom_winds
      if S.DoomWinds:IsReady() then
        if Cast(S.DoomWinds) then return "doom_winds aoe_totemic 14"; end
      end
      -- primordial_wave,if=dot.flame_shock.ticking&(active_dot.flame_shock=active_enemies|active_dot.flame_shock=6)
      if S.PrimordialWave:IsReady() and (S.FlameShockDebuff:AuraActiveCount() == mathmin(EnemiesMeleeCount, 6)) then
        if Cast(S.PrimordialWave) then return "primordial_wave aoe_totemic 16"; end
      end
      -- windstrike
      if S.Windstrike:IsReady() then
        if Cast(S.Windstrike) then return "windstrike aoe_totemic 18"; end
      end
      -- lava_lash,if=buff.hot_hand.up
      if S.LavaLash:IsReady() and (Player:BuffUp(S.HotHandBuff)) then
        if Cast(S.LavaLash) then return "lava_lash aoe_totemic 20"; end
      end
      -- crash_lightning,if=buff.electrostatic_wager.stack>8
      if S.CrashLightning:IsReady() and (Player:BuffStack(S.ElectrostaticWagerBuff) > 8) then
        if Cast(S.CrashLightning) then return "crash_lightning aoe_totemic 22"; end
      end
      -- sundering,if=buff.doom_winds.up|talent.earthsurge.enabled&(buff.legacy_of_the_frost_witch.up|!talent.legacy_of_the_frost_witch.enabled)&pet.surging_totem.active
      if S.Sundering:IsReady() and (Player:BuffUp(S.DoomWindsBuff) or S.Earthsurge:IsAvailable() and (Player:BuffUp(S.LegacyoftheFrostWitchBuff) or not S.LegacyoftheFrostWitch:IsAvailable()) and TotemFinder(S.SurgingTotem)) then
        if Cast(S.Sundering) then return "sundering aoe_totemic 24"; end
      end
      -- chain_lightning,if=buff.maelstrom_weapon.stack>=10&buff.electrostatic_wager.stack>4&!buff.cl_crash_lightning.up&buff.doom_winds.up
      if S.ChainLightning:IsReady() and (MaelstromStacks >= 10 and Player:BuffStack(S.ElectrostaticWagerBuff) > 4 and Player:BuffDown(S.CLCrashLightningBuff) and Player:BuffUp(S.DoomWindsBuff)) then
        if Cast(S.ChainLightning) then return "chain_lightning aoe_totemic 26"; end
      end
      -- elemental_blast,if=buff.maelstrom_weapon.stack>=10
      if S.ElementalBlast:IsReady() and (MaelstromStacks >= 10) then
        if Cast(S.ElementalBlast) then return "elemental_blast aoe_totemic 28"; end
      end
      -- chain_lightning,if=buff.maelstrom_weapon.stack>=10&((buff.doom_winds.remains>=gcd*3&buff.primordial_storm.up)|!buff.primordial_storm.up)
      if S.ChainLightning:IsReady() and (MaelstromStacks >= 10 and ((Player:BuffRemains(S.DoomWindsBuff) >= Player:GCD() * 3 and Player:BuffUp(S.PrimordialStormBuff)) or Player:BuffDown(S.PrimordialStormBuff))) then
        if Cast(S.ChainLightning) then return "chain_lightning aoe_totemic 30"; end
      end
      -- crash_lightning,if=buff.doom_winds.up|!buff.crash_lightning.up|(talent.alpha_wolf.enabled&feral_spirit.active&alpha_wolf_min_remains=0)
      if S.CrashLightning:IsReady() and (Player:BuffUp(S.DoomWindsBuff) or Player:BuffDown(S.CrashLightningBuff) or (S.AlphaWolf:IsAvailable() and Player:BuffUp(S.FeralSpiritBuff) and AlphaWolfMinRemains() == 0)) then
        if Cast(S.CrashLightning) then return "crash_lightning aoe_totemic 32"; end
      end
      -- voltaic_blaze
      if S.VoltaicBlazeAbility:IsReady() then
        if Cast(S.VoltaicBlazeAbility) then return "voltaic_blaze aoe_totemic 34"; end
      end
      -- fire_nova,if=(dot.flame_shock.ticking&(active_dot.flame_shock=active_enemies|active_dot.flame_shock=6))&pet.searing_totem.active
      if S.FireNova:IsReady() and (S.FlameShockDebuff:AuraActiveCount() == mathmin(EnemiesMeleeCount, 6) and Shaman.SearingTotemActive) then
        if Cast(S.FireNova) then return "fire_nova aoe_totemic 36"; end
      end
      -- lava_lash,if=talent.molten_assault.enabled&dot.flame_shock.ticking
      if S.LavaLash:IsReady() and (S.MoltenAssault:IsAvailable() and Target:DebuffUp(S.FlameShockDebuff)) then
        if Cast(S.LavaLash) then return "lava_lash aoe_totemic 38"; end
      end
      -- frost_shock,if=talent.hailstorm.enabled&buff.hailstorm.up&pet.searing_totem.active
      if S.FrostShock:IsReady() and (S.Hailstorm:IsAvailable() and Player:BuffUp(S.HailstormBuff) and Shaman.SearingTotemActive) then
        if Cast(S.FrostShock) then return "frost_shock aoe_totemic 40"; end
      end
      -- crash_lightning,if=talent.crashing_storms.enabled
      if S.CrashLightning:IsReady() and (S.CrashingStorms:IsAvailable()) then
        if Cast(S.CrashLightning) then return "crash_lightning aoe_totemic 42"; end
      end
      -- fire_nova,if=dot.flame_shock.ticking&(active_dot.flame_shock=active_enemies|active_dot.flame_shock=6)
      if S.FireNova:IsReady() and (S.FlameShockDebuff:AuraActiveCount() == mathmin(EnemiesMeleeCount, 6)) then
        if Cast(S.FireNova) then return "fire_nova aoe_totemic 44"; end
      end
      -- frost_shock,if=talent.hailstorm.enabled&buff.hailstorm.up
      if S.FrostShock:IsReady() and (S.Hailstorm:IsAvailable() and Player:BuffUp(S.HailstormBuff)) then
        if Cast(S.FrostShock) then return "frost_shock aoe_totemic 46"; end
      end
      -- crash_lightning
      if S.CrashLightning:IsReady() then
        if Cast(S.CrashLightning) then return "crash_lightning aoe_totemic 48"; end
      end
      -- ice_strike,if=talent.hailstorm.enabled&!buff.ice_strike.up
      if S.IceStrike:IsReady() and (S.Hailstorm:IsAvailable() and Player:BuffDown(S.IceStrikeBuff)) then
        if Cast(S.IceStrike) then return "ice_strike aoe_totemic 50"; end
      end
      if MaelstromStacks >= 5 and Player:BuffDown(S.PrimordialStormBuff) and ((Player:BuffRemains(S.DoomWindsBuff) >= Player:GCD() * 3 and Player:BuffUp(S.PrimordialStormBuff)) or Player:BuffDown(S.PrimordialStormBuff)) then
        -- elemental_blast,if=buff.maelstrom_weapon.stack>=5&!buff.primordial_storm.up&((buff.doom_winds.remains>=gcd*3&buff.primordial_storm.up)|!buff.primordial_storm.up)
        if S.ElementalBlast:IsReady() then
          if Cast(S.ElementalBlast) then return "elemental_blast aoe_totemic 52"; end
        end
        -- chain_lightning,if=buff.maelstrom_weapon.stack>=5&!buff.primordial_storm.up&((buff.doom_winds.remains>=gcd*3&buff.primordial_storm.up)|!buff.primordial_storm.up)
        if S.ChainLightning:IsReady() then
          if Cast(S.ChainLightning) then return "chain_lightning aoe_totemic 54"; end
        end
      end
      -- stormstrike
      if S.Stormstrike:IsReady() then
        if Cast(S.Stormstrike) then return "stormstrike aoe_totemic 56"; end
      end
      -- sundering,if=buff.doom_winds.up|talent.earthsurge.enabled&(buff.legacy_of_the_frost_witch.up|!talent.legacy_of_the_frost_witch.enabled)&pet.surging_totem.active
      if S.Sundering:IsReady() and (Player:BuffUp(S.DoomWindsBuff) or S.Earthsurge:IsAvailable() and (Player:BuffUp(S.LegacyoftheFrostWitchBuff) or not S.LegacyoftheFrostWitch:IsAvailable()) and TotemFinder(S.SurgingTotem)) then
        if Cast(S.Sundering) then return "sundering aoe_totemic 58"; end
      end
      -- fire_nova,if=active_dot.flame_shock=6|(active_dot.flame_shock>=4&active_dot.flame_shock=active_enemies)
      if S.FireNova:IsReady() and (S.FlameShockDebuff:AuraActiveCount() == 6 or (S.FlameShockDebuff:AuraActiveCount() >= 4 and S.FlameShockDebuff:AuraActiveCount() >= EnemiesMeleeCount)) then
        if Cast(S.FireNova) then return "fire_nova aoe_totemic 60"; end
      end
      -- voltaic_blaze
      if S.VoltaicBlazeAbility:IsReady() then
        if Cast(S.VoltaicBlazeAbility) then return "voltaic_blaze aoe_totemic 62"; end
      end
      -- ice_strike,if=talent.hailstorm.enabled&!buff.ice_strike.up
      if S.IceStrike:IsReady() and (S.Hailstorm:IsAvailable() and Player:BuffDown(S.IceStrikeBuff)) then
        if Cast(S.IceStrike) then return "ice_strike aoe_totemic 64"; end
      end
      -- frost_shock,if=talent.hailstorm.enabled&buff.hailstorm.up
      if S.FrostShock:IsReady() and (S.Hailstorm:IsAvailable() and Player:BuffUp(S.HailstormBuff)) then
        if Cast(S.FrostShock) then return "frost_shock aoe_totemic 66"; end
      end
      -- sundering,if=(buff.legacy_of_the_frost_witch.up|!talent.legacy_of_the_frost_witch.enabled)&pet.surging_totem.active
      if S.Sundering:IsReady() and ((Player:BuffUp(S.LegacyoftheFrostWitchBuff) or not S.LegacyoftheFrostWitch:IsAvailable()) and TotemFinder(S.SurgingTotem)) then
        if Cast(S.Sundering) then return "sundering aoe_totemic 68"; end
      end
      -- flame_shock,if=talent.molten_assault.enabled&!ticking
      if S.FlameShock:IsReady() and (S.MoltenAssault:IsAvailable() and Target:DebuffDown(S.FlameShockDebuff)) then
        if Cast(S.FlameShock) then return "flame_shock aoe_totemic 70"; end
      end
      -- fire_nova,if=active_dot.flame_shock>=3
      if S.FireNova:IsReady() and (S.FlameShockDebuff:AuraActiveCount() >= 3) then
        if Cast(S.FireNova) then return "fire_nova aoe_totemic 72"; end
      end
      -- ice_strike
      if S.IceStrike:IsReady() then
        if Cast(S.IceStrike) then return "ice_strike aoe_totemic 74"; end
      end
      -- lava_lash
      if S.LavaLash:IsReady() then
        if Cast(S.LavaLash) then return "lava_lash aoe_totemic 76"; end
      end
      -- crash_lightning
      if S.CrashLightning:IsReady() then
        if Cast(S.CrashLightning) then return "crash_lightning aoe_totemic 78"; end
      end
      -- flame_shock,if=!ticking
      if S.FlameShock:IsReady() and (Target:DebuffDown(S.FlameShockDebuff)) then
        if Cast(S.FlameShock) then return "flame_shock aoe_totemic 80"; end
      end
    end    

    local function Funnel()
      -- feral_spirit,if=talent.elemental_spirits.enabled
      if S.FeralSpirit:IsReady() and S.ElementalSpirits:IsAvailable() then
        if Cast(S.FeralSpirit) then return "feral_spirit funnel 2"; end
      end
      -- surging_totem
      if S.SurgingTotem:IsReady() then
        if Cast(S.SurgingTotem) then return "surging_totem funnel 4"; end
      end
      -- ascendance
      if S.Ascendance:IsReady() then
        if Cast(S.Ascendance) then return "ascendance funnel 6"; end
      end
      -- windstrike,if=(talent.thorims_invocation.enabled&buff.maelstrom_weapon.stack>0)|buff.converging_storms.stack=buff.converging_storms.max_stack
      if S.Windstrike:IsReady() and ((S.ThorimsInvocation:IsAvailable() and MaelstromStacks > 0) or Player:BuffStack(S.ConvergingStormsBuff) == MaxConvergingStormsStacks) then
        if Cast(S.Windstrike) then return "windstrike funnel 8"; end
      end
      -- tempest,if=buff.maelstrom_weapon.stack=buff.maelstrom_weapon.max_stack|(buff.maelstrom_weapon.stack>=5&(tempest_mael_count>30|buff.awakening_storms.stack=2))
      if S.TempestAbility:IsReady() and (MaelstromStacks == MaxMaelstromStacks or (MaelstromStacks >= 5 and (Shaman.TempestMaelstrom > 30 or Player:BuffStack(S.AwakeningStormsBuff) == 2))) then
        if Cast(S.TempestAbility) then return "tempest funnel 10"; end
      end
      -- lightning_bolt,if=(active_dot.flame_shock=active_enemies|active_dot.flame_shock=6)&buff.primordial_wave.up&buff.maelstrom_weapon.stack=buff.maelstrom_weapon.max_stack&(!buff.splintered_elements.up|fight_remains<=12)
      if S.LightningBolt:IsReady() and ((S.FlameShockDebuff:AuraActiveCount() >= EnemiesMeleeCount or S.FlameShockDebuff:AuraActiveCount() >= 6) and Player:BuffUp(S.PrimordialWaveBuff) and MaelstromStacks == MaxMaelstromStacks and (Player:BuffDown(S.SplinteredElementsBuff) or FightRemains <= 12)) then
        if Cast(S.LightningBolt) then return "lightning_bolt funnel 12"; end
      end
      -- elemental_blast,if=buff.maelstrom_weapon.stack>=5&talent.elemental_spirits.enabled&feral_spirit.active>=4
      if S.ElementalBlast:IsReady() and (MaelstromStacks >= 5 and S.ElementalSpirits:IsAvailable() and Shaman.FeralSpiritCount >= 4) then
        if Cast(S.ElementalBlast) then return "elemental_blast funnel 14"; end
      end
      -- lightning_bolt,if=talent.supercharge.enabled&buff.maelstrom_weapon.stack=buff.maelstrom_weapon.max_stack&(variable.expected_lb_funnel>variable.expected_cl_funnel)
      if S.LightningBolt:IsReady() and S.Supercharge:IsAvailable() and MaelstromStacks == MaxMaelstromStacks and (VarExpectedLBFunnel > VarExpectedCLFunnel) then
        if Cast(S.LightningBolt) then return "lightning_bolt funnel 16"; end
      end
      -- chain_lightning,if=(talent.supercharge.enabled&buff.maelstrom_weapon.stack=buff.maelstrom_weapon.max_stack)|buff.arc_discharge.up&buff.maelstrom_weapon.stack>=5
      if S.ChainLightning:IsReady() and ((S.Supercharge:IsAvailable() and MaelstromStacks == MaxMaelstromStacks) or (Player:BuffUp(S.ArcDischargeBuff) and MaelstromStacks >= 5)) then
        if Cast(S.ChainLightning) then return "chain_lightning funnel 18"; end
      end
      -- lava_lash,if=(talent.molten_assault.enabled&dot.flame_shock.ticking&(active_dot.flame_shock<active_enemies)&active_dot.flame_shock<6)|(talent.ashen_catalyst.enabled&buff.ashen_catalyst.stack=buff.ashen_catalyst.max_stack)
      if S.LavaLash:IsReady() and ((S.MoltenAssault:IsAvailable() and Target:DebuffUp(S.FlameShockDebuff) and S.FlameShockDebuff:AuraActiveCount() < EnemiesMeleeCount and S.FlameShockDebuff:AuraActiveCount() < 6) or (S.AshenCatalyst:IsAvailable() and Player:BuffStack(S.AshenCatalystBuff) == MaxAshenCatalystStacks)) then
        if Cast(S.LavaLash) then return "lava_lash funnel 20"; end
      end
      -- primordial_wave,target_if=min:dot.flame_shock.remains,if=!buff.primordial_wave.up
      if S.PrimordialWave:IsReady() and Player:BuffDown(S.PrimordialWaveBuff) then
        if Cast(S.PrimordialWave) then return "primordial_wave funnel 22"; end
      end
      -- elemental_blast,if=(!talent.elemental_spirits.enabled|(talent.elemental_spirits.enabled&(charges=max_charges|buff.feral_spirit.up)))&buff.maelstrom_weapon.stack=buff.maelstrom_weapon.max_stack
      if S.ElementalBlast:IsReady() and ((not S.ElementalSpirits:IsAvailable() or (S.ElementalSpirits:IsAvailable() and (S.ElementalBlast:Charges() == S.ElementalBlast:MaxCharges() or Player:BuffUp(S.FeralSpiritBuff)))) and MaelstromStacks == MaxMaelstromStacks) then
        if Cast(S.ElementalBlast) then return "elemental_blast funnel 24"; end
      end
      -- feral_spirit
      if S.FeralSpirit:IsReady() then
        if Cast(S.FeralSpirit) then return "feral_spirit funnel 26"; end
      end
      -- doom_winds
      if S.DoomWinds:IsReady() then
        if Cast(S.DoomWinds) then return "doom_winds funnel 28"; end
      end
      -- stormstrike,if=buff.converging_storms.stack=buff.converging_storms.max_stack
      if S.Stormstrike:IsReady() and Player:BuffStack(S.ConvergingStormsBuff) == MaxConvergingStormsStacks then
        if Cast(S.Stormstrike) then return "stormstrike funnel 30"; end
      end
      -- lava_burst,if=(buff.molten_weapon.stack>buff.crackling_surge.stack)&buff.maelstrom_weapon.stack=buff.maelstrom_weapon.max_stack
      if S.LavaBurst:IsReady() and (Shaman.MoltenWeaponStacks > Shaman.CracklingSurgeStacks) and MaelstromStacks == MaxMaelstromStacks then
        if Cast(S.LavaBurst) then return "lava_burst funnel 32"; end
      end
      -- lightning_bolt,if=buff.maelstrom_weapon.stack=buff.maelstrom_weapon.max_stack&(variable.expected_lb_funnel>variable.expected_cl_funnel)
      if S.LightningBolt:IsReady() and MaelstromStacks == MaxMaelstromStacks and (VarExpectedLBFunnel > VarExpectedCLFunnel) then
        if Cast(S.LightningBolt) then return "lightning_bolt funnel 34"; end
      end
      -- chain_lightning,if=buff.maelstrom_weapon.stack=buff.maelstrom_weapon.max_stack
      if S.ChainLightning:IsReady() and MaelstromStacks == MaxMaelstromStacks then
        if Cast(S.ChainLightning) then return "chain_lightning funnel 36"; end
      end
      -- crash_lightning,if=buff.doom_winds.up|!buff.crash_lightning.up|(talent.alpha_wolf.enabled&feral_spirit.active&alpha_wolf_min_remains=0)|(talent.converging_storms.enabled&buff.converging_storms.stack<buff.converging_storms.max_stack)
      if S.CrashLightning:IsReady() and (Player:BuffUp(S.DoomWindsBuff) or Player:BuffDown(S.CrashLightningBuff) or (S.AlphaWolf:IsAvailable() and Player:BuffUp(S.FeralSpiritBuff) and AlphaWolfMinRemains() == 0) or (S.ConvergingStorms:IsAvailable() and Player:BuffStack(S.ConvergingStormsBuff) < MaxConvergingStormsStacks)) then
        if Cast(S.CrashLightning) then return "crash_lightning funnel 38"; end
      end
      -- sundering,if=buff.doom_winds.up|talent.earthsurge.enabled
      if S.Sundering:IsReady() and (Player:BuffUp(S.DoomWindsBuff) or S.Earthsurge:IsAvailable()) then
        if Cast(S.Sundering) then return "sundering funnel 40"; end
      end
      -- fire_nova,if=active_dot.flame_shock=6|(active_dot.flame_shock>=4&active_dot.flame_shock=active_enemies)
      if S.FireNova:IsReady() and (S.FlameShockDebuff:AuraActiveCount() >= 6 or (S.FlameShockDebuff:AuraActiveCount() >= 4 and S.FlameShockDebuff:AuraActiveCount() >= EnemiesMeleeCount)) then
        if Cast(S.FireNova) then return "fire_nova funnel 42"; end
      end
      -- ice_strike,if=talent.hailstorm.enabled&!buff.ice_strike.up
      if S.IceStrike:IsReady() and S.Hailstorm:IsAvailable() and Player:BuffDown(S.IceStrikeBuff) then
        if Cast(S.IceStrike) then return "ice_strike funnel 44"; end
      end
      -- frost_shock,if=talent.hailstorm.enabled&buff.hailstorm.up
      if S.FrostShock:IsReady() and S.Hailstorm:IsAvailable() and Player:BuffUp(S.HailstormBuff) then
        if Cast(S.FrostShock) then return "frost_shock funnel 46"; end
      end
      -- sundering
      if S.Sundering:IsReady() then
        if Cast(S.Sundering) then return "sundering funnel 48"; end
      end
      -- flame_shock,if=talent.molten_assault.enabled&!ticking
      if S.FlameShock:IsReady() and S.MoltenAssault:IsAvailable() and Target:DebuffDown(S.FlameShockDebuff) then
        if Cast(S.FlameShock) then return "flame_shock funnel 50"; end
      end
      -- flame_shock,target_if=min:dot.flame_shock.remains,if=(talent.fire_nova.enabled|talent.primordial_wave.enabled)&(active_dot.flame_shock<active_enemies)&active_dot.flame_shock<6
      if S.FlameShock:IsReady() and ((S.FireNova:IsAvailable() or S.PrimordialWave:IsAvailable()) and S.FlameShockDebuff:AuraActiveCount() < EnemiesMeleeCount and S.FlameShockDebuff:AuraActiveCount() < 6) then
        if Cast(S.FlameShock) then return "flame_shock funnel 52"; end
      end
      -- fire_nova,if=active_dot.flame_shock>=3
      if S.FireNova:IsReady() and S.FlameShockDebuff:AuraActiveCount() >= 3 then
        if Cast(S.FireNova) then return "fire_nova funnel 54"; end
      end
      -- stormstrike,if=buff.crash_lightning.up&talent.deeply_rooted_elements.enabled
      if S.Stormstrike:IsReady() and Player:BuffUp(S.CrashLightningBuff) and S.DeeplyRootedElements:IsAvailable() then
        if Cast(S.Stormstrike) then return "stormstrike funnel 56"; end
      end
      -- crash_lightning,if=talent.crashing_storms.enabled&buff.cl_crash_lightning.up&active_enemies>=4
      if S.CrashLightning:IsReady() and S.CrashingStorms:IsAvailable() and Player:BuffUp(S.CLCrashLightningBuff) and EnemiesMeleeCount >= 4 then
        if Cast(S.CrashLightning) then return "crash_lightning funnel 58"; end
      end
      -- windstrike
      if S.Windstrike:IsReady() then
        if Cast(S.Windstrike) then return "windstrike funnel 60"; end
      end
      -- stormstrike
      if S.Stormstrike:IsReady() then
        if Cast(S.Stormstrike) then return "stormstrike funnel 62"; end
      end
      -- ice_strike
      if S.IceStrike:IsReady() then
        if Cast(S.IceStrike) then return "ice_strike funnel 64"; end
      end
      -- lava_lash
      if S.LavaLash:IsReady() then
        if Cast(S.LavaLash) then return "lava_lash funnel 66"; end
      end
      -- crash_lightning
      if S.CrashLightning:IsReady() then
        if Cast(S.CrashLightning) then return "crash_lightning funnel 68"; end
      end
      -- fire_nova,if=active_dot.flame_shock>=2
      if S.FireNova:IsReady() and S.FlameShockDebuff:AuraActiveCount() >= 2 then
        if Cast(S.FireNova) then return "fire_nova funnel 70"; end
      end
      -- elemental_blast,if=(!talent.elemental_spirits.enabled|(talent.elemental_spirits.enabled&(charges=max_charges|buff.feral_spirit.up)))&buff.maelstrom_weapon.stack>=5
      if S.ElementalBlast:IsReady() and ((not S.ElementalSpirits:IsAvailable() or (S.ElementalSpirits:IsAvailable() and (S.ElementalBlast:Charges() == S.ElementalBlast:MaxCharges() or Player:BuffUp(S.FeralSpiritBuff)))) and MaelstromStacks >= 5) then
        if Cast(S.ElementalBlast) then return "elemental_blast funnel 72"; end
      end
      -- lava_burst,if=(buff.molten_weapon.stack>buff.crackling_surge.stack)&buff.maelstrom_weapon.stack>=5
      if S.LavaBurst:IsReady() and (Shaman.MoltenWeaponStacks > Shaman.CracklingSurgeStacks) and MaelstromStacks >= 5 then
        if Cast(S.LavaBurst) then return "lava_burst funnel 74"; end
      end
      -- lightning_bolt,if=buff.maelstrom_weapon.stack>=5&(variable.expected_lb_funnel>variable.expected_cl_funnel)
      if S.LightningBolt:IsReady() and MaelstromStacks >= 5 and (VarExpectedLBFunnel > VarExpectedCLFunnel) then
        if Cast(S.LightningBolt) then return "lightning_bolt funnel 76"; end
      end
      -- chain_lightning,if=buff.maelstrom_weapon.stack>=5
      if S.ChainLightning:IsReady() and MaelstromStacks >= 5 then
        if Cast(S.ChainLightning) then return "chain_lightning funnel 78"; end
      end
      -- flame_shock,if=!ticking
      if S.FlameShock:IsReady() and Target:DebuffDown(S.FlameShockDebuff) then
        if Cast(S.FlameShock) then return "flame_shock funnel 80"; end
      end
      -- frost_shock,if=!talent.hailstorm.enabled
      if S.FrostShock:IsReady() and not S.Hailstorm:IsAvailable() then
        if Cast(S.FrostShock) then return "frost_shock funnel 82"; end
      end
    end

    --- ===== Start Custom =====
    local function AutoWolf()
        if inCombat and GetSetting('AutoWolfCombat', 4) ~= 4 and Player:BuffDown(S.GhostWolf) then
            if isMoving and S.GhostWolf:IsReady(Player) and not Target:IsInRange(30) then
                if GetSetting('AutoWolfCombat', 4) == 1 then
                    if Cast(S.GhostWolf) then
                        return "Auto Wolf Combat 1"
                    end
                end
                if GetSetting('AutoWolfCombat', 4) == 2 and inDungeon then
                    if Cast(S.GhostWolf) then
                        return "Auto Wolf Combat 2"
                    end
                end
                if GetSetting('AutoWolfCombat', 4) == 3 and inRaid then
                    if Cast(S.GhostWolf) then
                        return "Auto Wolf Combat 3"
                    end
                end
                if GetSetting('AutoWolfOOC', 4) == 5 and not inDungeon and not inRaid then
                    if Cast(S.GhostWolf) then
                        return "Auto Wolf OOC 5"
                    end
                end
            end
        end

        if not inCombat and GetSetting('AutoWolfOOC', 5) ~= 4 and Player:BuffDown(S.GhostWolf) then
            if Player:IsMovingFor() >= 2 and S.GhostWolf:IsReady(Player) then
                if GetSetting('AutoWolfOOC', 5) == 1 then
                    if Cast(S.GhostWolf) then
                        return "Auto Wolf OOC 1"
                    end
                end
                if GetSetting('AutoWolfOOC', 5) == 2 and inDungeon then
                    if Cast(S.GhostWolf) then
                        return "Auto Wolf OOC 2"
                    end
                end
                if GetSetting('AutoWolfOOC', 5) == 3 and inRaid then
                    if Cast(S.GhostWolf) then
                        return "Auto Wolf OOC 3"
                    end
                end
                if GetSetting('AutoWolfOOC', 5) == 5 and not inDungeon and not inRaid then
                    if Cast(S.GhostWolf) then
                        return "Auto Wolf OOC 5"
                    end
                end
            end
        end        
    end

    local function Defensives()
        local PlayerHealthPercentage = Player:HealthPercentage()
        if GetSetting('AstralShift_check', false) and S.AstralShift:IsReady(Player) and PlayerHealthPercentage <= GetSetting('AstralShift_spin', 30) then
            if Cast(S.AstralShift, true) then
                return "Defensive: Astral Shift";
            end
        end

        if MainAddon.Toggle:GetToggle('MaelstromHealing') then
            if S.HealingSurge:IsReady(Player) and MaelstromStacks >= 5 then
                if Cast(S.HealingSurge) then
                    return "Maelstrom Healing";
                end
            end
        end
        
        if GetSetting('Bulwark_check', false) and S.StoneBulwarkTotem:IsReady(Player) and PlayerHealthPercentage <= GetSetting('Bulwark_spin', 30) then
            if Cast(S.StoneBulwarkTotem) then
                return "Defensive: Buwlark";
            end
        end

        if not Player:IsInSoloMode() then
            if GetSetting('HealingSurge_check', false) and PlayerHealthPercentage <= GetSetting('HealingSurge_spin', 30)
            and S.HealingSurge:CastTime() == 0
            and S.HealingSurge:IsReady(Player) then
                if Cast(S.HealingSurge) then
                    return "Defensive: Healing Surge RAID/PARTY";
                end
            end
            if GetSetting('HSTGroup_check', false) and MainAddon.HealingEngine:MedianHP() <= GetSetting('HSTGroup_spin', 30)
            and S.HealingStreamTotem:IsReady(Player) and not Player:TotemIsActive(S.HealingStreamTotem:Name()) then
                if Cast(S.HealingStreamTotem) then
                    return "Defensive: HST RAID/PARTY";
                end
            end
        else
            if GetSetting('HealingSurgeSolo_check', false) and PlayerHealthPercentage <= GetSetting('HealingSurgeSolo_spin', 30)
            and S.HealingSurge:CastTime() == 0
            and S.HealingSurge:IsReady(Player) then
                if Cast(S.HealingSurge) then
                    return "Defensive: Healing Surge SOLO";
                end
            end
            if GetSetting('HSTSolo_check', false) and PlayerHealthPercentage <= GetSetting('HSTSolo_spin', 30)
            and S.HealingStreamTotem:IsReady(Player) and not Player:TotemIsActive(S.HealingStreamTotem:Name()) then
                if Cast(S.HealingStreamTotem) then
                    return "Defensive: HST SOLO";
                end
            end
        end
        
        -- Earth Elemental
        if inCombat and S.EarthElemental:IsReady(Player) then
            local EEUsage = GetSetting('EarthElemental', {})
            if IsInGroup() and inDungeon then
                -- Use Earth Elemental if 'Tank is dead' option is selected
                if EEUsage['EEDEF_tank_dead'] then
                    if TankUnit and TankUnit:IsDeadOrGhost() then
                        if Cast(S.EarthElemental) then
                            return "Defensive: Earth Elemental - Tank Dead";
                        end
                    end
                end
                -- Use Earth Elemental if 'On Aggro' option is selected and player has aggro
                if EEUsage['EEDEF_aggro'] and Player:IsTankingAoE(40) then
                    if Cast(S.EarthElemental) then
                        return "Defensive: Earth Elemental - Aggro";
                    end
                end
            end
        end
    end

    local function Shields()
        if GetSetting('shield', 1) == 1 then
            if Player:BuffDown(S.LightningShield) and S.LightningShield:IsReady(Player) then
                if Cast(S.LightningShield) then
                    return "Lightning Shield"
                end
            end
        else
            if S.ElementalOrbit:IsAvailable() then
                if S.EarthShield:IsReady(Player) and (Player:BuffDown(S.EarthShieldSelfBuff) or (not inCombat and Player:BuffStack(S.EarthShieldSelfBuff) < 5)) then
                    if Cast(S.EarthShield, Player) then
                        return 'Earth Shield'
                    end
                end
            else
                if S.EarthShield:IsReady(Player) and (Player:BuffDown(S.EarthShieldOtherBuff) or (not inCombat and Player:BuffStack(S.EarthShieldOtherBuff) < 5)) then
                    if Cast(S.EarthShield, Player) then
                        return 'Earth Shield'
                    end
                end
            end
        end

        if S.ElementalOrbit:IsAvailable() then
            if Player:BuffDown(S.LightningShield) and S.LightningShield:IsReady(Player) then
                if Cast(S.LightningShield) then
                    return "Lightning Shield - Elemental Orbit"
                end
            end
            if Player:BuffDown(S.EarthShieldSelfBuff) and Player:BuffDown(S.EarthShieldOtherBuff) and S.EarthShield:IsReady(Player) then
                if Cast(S.EarthShield, Player) then
                    return "Earth Shield - Elemental Orbit"
                end
            end

            if (Player:BuffUp(S.EarthShieldSelfBuff) or Player:BuffUp(S.EarthShieldOtherBuff)) then
                if Target:IsATank() and not Target:IsUnit(Player) then
                    if S.EarthShield:IsReady(Target) and Target:BuffDown(S.EarthShieldSelfBuff, true) and Target:BuffDown(S.EarthShieldOtherBuff, true) then
                        if Cast(S.EarthShield) then
                            return "Earth Shield - Elemental Orbit - Tank"
                        end
                    end
                end
            end
        end
    end

    local function Buff()
        -- windfury_weapon
        if ((not HasMainHandEnchant) or MHEnchantTimeRemains < 600000) and S.WindfuryWeapon:IsReady(Player) then
            if Cast(S.WindfuryWeapon) then
                return "Windfury Weapon"
            end
        end
        -- flametongue_weapon
        if ((not HasOffHandEnchant) or OHEnchantTimeRemains < 600000) and S.FlametongueWeapon:IsReady(Player) and C_PaperDollInfo.OffhandHasWeapon() then
            if Cast(S.FlametongueWeapon) then
                return "Flametongue Weapon"
            end
        end

        local sky = GetSetting('sky', {})
        if S.Skyfury:IsReady(Player) and (sky['sky_self'] and Player:BuffDown(S.Skyfury, true) or sky['sky_friends'] and M.GroupBuffMissing(S.Skyfury)) then
            if Cast(S.Skyfury) then
                return "Skyfury";
            end
        end
    end
    --- ===== End Custom =====

    --- ======= MAIN =======
    local function APL()
        if IsSpellKnown(326059) then
            StaticPopup_Show("ENHCPOPUP")
            return
        end
        -- Custom Variables Update
        inCombat = Player:AffectingCombat()
        inDungeon = Player:IsInDungeonArea()
        inRaid = Player:IsInRaidArea()
        isMoving = Player:IsMoving()
        rodSetting = GetSetting("rodSetting", {})
        
        -- Check weapon enchants
        HasMainHandEnchant, MHEnchantTimeRemains, _, _, HasOffHandEnchant, OHEnchantTimeRemains = GetWeaponEnchantInfo()

        local settingFSRange = GetSetting('FS_Range', 20)
        if settingFSRange <= 5 then
            FSRange = 5
        elseif settingFSRange >= 40 then
            FSRange = 40
        else
            FSRange = settingFSRange
        end

        -- Unit Update
        EnemiesMelee = Player:GetEnemiesInMeleeRange(10)
        if AoEON() then
          EnemiesMeleeCount = #EnemiesMelee
          Enemies40yCount = RangedTargetCount(40)
        else
          EnemiesMeleeCount = 1
          Enemies40yCount = 1
        end

        --MainAddon:Print("FeralSpiritCount: ", Shaman.FeralSpiritCount)
        -- Calculate fight_remains
        if MainAddon.TargetIsValid() or inCombat then
            -- Calculate fight_remains
            BossFightRemains = HL.BossFightRemains()
            FightRemains = BossFightRemains
            if FightRemains == 11111 then
                FightRemains = HL.FightRemains(EnemiesMelee, false)
            end

            -- Check our Maelstrom Weapon buff stacks
            MaelstromStacks = Player:BuffStack(S.MaelstromWeaponBuff)
            if MainAddon.Toggle:GetToggle('HoldMaelstrom') then
                MaelstromStacks = 0
            end
            VarMinTalentedCDRemains = mathmin(((S.FeralSpirit:CooldownRemains() / (4 * num(S.WitchDoctorsAncestry:IsAvailable()))) + 1000 * num(not S.FeralSpirit:IsAvailable())), (S.DoomWinds:CooldownRemains() + 1000 * num(not S.DoomWinds:IsAvailable())), (S.Ascendance:CooldownRemains() + 1000 * num(not S.Ascendance:IsAvailable())))
            -- variable,name=target_nature_mod,value=(1+debuff.chaos_brand.up*debuff.chaos_brand.value)*(1+(debuff.hunters_mark.up*target.health.pct>=80)*debuff.hunters_mark.value)
            VarTargetNatureMod = (1 + num(Target:DebuffUp(S.ChaosBrandDebuff)) * 0.05) * (1 + num(Target:DebuffUp(S.HuntersMarkDebuff) and Target:HealthPercentage() >= 80) * 0.05)
            -- variable,name=expected_lb_funnel,value=action.lightning_bolt.damage*(1+debuff.lightning_rod.up*variable.target_nature_mod*(1+buff.primordial_wave.up*active_dot.flame_shock*buff.primordial_wave.value)*debuff.lightning_rod.value)
            local PWValue = 1.75 * S.LightningBolt:Damage()
            local LRValue = 0.2 * S.LightningBolt:Damage() * (Target:DebuffUp(S.LightningRodDebuff) and 1.75 or 1)
            VarExpectedLBFunnel = S.LightningBolt:Damage() * (1 + num(Target:DebuffUp(S.LightningRodDebuff)) * VarTargetNatureMod * (1 + num(Player:BuffUp(S.PrimordialWaveBuff)) * S.FlameShockDebuff:AuraActiveCount() * PWValue) * LRValue)
            -- variable,name=expected_cl_funnel,value=action.chain_lightning.damage*(1+debuff.lightning_rod.up*variable.target_nature_mod*(active_enemies>?(3+2*talent.crashing_storms.enabled))*debuff.lightning_rod.value)
            VarExpectedCLFunnel = S.ChainLightning:Damage() * (1 + num(Target:DebuffUp(S.LightningRodDebuff)) * VarTargetNatureMod * mathmin(EnemiesMeleeCount, 3 + 2 * num(S.CrashingStorms:IsAvailable())) * LRValue)
          end

        -- Update Thorim's Invocation
        if inCombat then
          if Player:PrevGCD(1, S.ChainLightning) then
            TIAction = S.ChainLightning        
          elseif Player:PrevGCD(1, S.LightningBolt) then
            TIAction = S.LightningBolt
          elseif Player:PrevGCD(1, S.TempestAbility) then
            if EnemiesMeleeCount >= 2 then
              TIAction = S.ChainLightning
            else
              TIAction = S.LightningBolt
            end
          end
        end

        --Stop WOLF
        if Player:BuffUp(S.GhostWolf) and GetSetting('StopWolf', false) then
            return
        end

        -- Auto Tremor Totem
        if S.TremorTotem:IsReady(Player) then
            local ShouldReturn = Shaman.EvaluateTremor(S.TremorTotem)
            if ShouldReturn then
                return ShouldReturn
            end
        end

        -- Burst Potion
        if Target:IsSpellInRange(S.Stormstrike) then
            if MainAddon.UsePotion() then
                MainAddon.SetTopColor(1, "Combat Potion")
            end
        end

        local ShouldReturn = Defensives();
        if ShouldReturn then
            return ShouldReturn;
        end
        
        if not inCombat or HL.CombatTime() < 3 or GetSetting('shield_prio', 1) == 1 then
            local ShouldReturn = Shields()
            if ShouldReturn then
                return ShouldReturn;
            end
        end

        local ShouldReturn = Buff()
        if ShouldReturn then
            return ShouldReturn;
        end

        local ShouldReturn = AutoWolf()
        if ShouldReturn then
            return ShouldReturn;
        end

        if MainAddon.TargetIsValid() then
            -- Trinkets
            local shouldReturn = MainAddon.TrinketDPS()
            if shouldReturn then
                return shouldReturn
            end  

            -- Primordial Storm - fallback cast before running out
            if S.PrimordialStormAbility:IsReady() and MaelstromStacks >= 7 and 
              (Player:BuffUp(S.PrimordialStormBuff) and Player:BuffRemains(S.PrimordialStormBuff) < 4.75) then
              if Cast(S.PrimordialStormAbility) then return "Primordial Storm (fallback, running out)"; end
            end

            -- Moved from Precombat: lightning_shield
            -- Precombat
            if not inCombat then
                local ShouldReturn = Precombat();
                if ShouldReturn then
                    return ShouldReturn;
                end
            end
            -- TreacherousTransmitter
            if I.TreacherousTransmitter:IsEquippedAndReady() and (Player:BuffUp(S.AscendanceBuff) and Player:BuffRemains(S.AscendanceBuff) >= 10) and FightRemains >= 20 then
            if Cast(I.TreacherousTransmitter) then return "treacherous_transmitter main 8"; end
            end
            -- SkardynsGrace
            if I.SkardynsGrace:IsEquippedAndReady() and (Player:BuffUp(S.AscendanceBuff) and Player:BuffRemains(S.AscendanceBuff) >= 10) and FightRemains >= 20 then
            if Cast(I.SkardynsGrace) then return "skardyns_grace main 10"; end
            end
            -- use_item,name=elementium_pocket_anvil,use_off_gcd=1
            if I.ElementiumPocketAnvil:IsEquippedAndReady() then
            if Cast(I.ElementiumPocketAnvil) then return "elementium_pocket_anvil main 4"; end
            end
            -- use_item,name=algethar_puzzle_box,use_off_gcd=1,if=(!buff.ascendance.up&!buff.feral_spirit.up&!buff.doom_winds.up)|(talent.ascendance.enabled&(cooldown.ascendance.remains<2*action.stormstrike.gcd))|(fight_remains%%180<=30)
            if I.AlgetharPuzzleBox:IsEquippedAndReady() and ((Player:BuffDown(S.AscendanceBuff) and Player:BuffDown(S.FeralSpiritBuff) and Player:BuffDown(S.DoomWindsBuff)) or (S.Ascendance:IsAvailable() and (S.Ascendance:CooldownRemains() < 2 * S.Stormstrike:ExecuteTime())) or (FightRemains % 180 <= 30)) then
            if Cast(I.AlgetharPuzzleBox) then return "algethar_puzzle_box main 6"; end
            end
            -- use_item,slot=trinket1,if=!variable.trinket1_is_weird&trinket.1.has_use_buff&fight_remains<=20|(buff.ascendance.up|(!talent.ascendance.enabled&(buff.splintered_elements.up|buff.doom_winds.up|buff.feral_spirit.up|(!talent.splintered_elements.enabled&!talent.doom_winds.enabled&!talent.feral_spirit.enabled))|(fight_remains%%trinket.1.cooldown.duration<=trinket.1.buff.any.duration)|(variable.min_talented_cd_remains>=trinket.1.cooldown.duration)))
            if Trinket1 and Trinket1:IsReady() and not VarTrinket1Ex and not Player:IsItemBlacklisted(Trinket1) and (not VarTrinket1IsWeird and Trinket1:HasUseBuff() and BossFightRemains <= 20 or (Player:BuffUp(S.AscendanceBuff) or (not S.Ascendance:IsAvailable() and (Player:BuffUp(S.SplinteredElementsBuff) or Player:BuffUp(S.DoomWindsBuff) or Player:BuffUp(S.FeralSpiritBuff) or (not S.SplinteredElements:IsAvailable() and not S.DoomWinds:IsAvailable() and not S.FeralSpirit:IsAvailable())) or (FightRemains % VarTrinket1CD <= Trinket1:BuffDuration()) or (VarMinTalentedCDRemains >= VarTrinket1CD)))) then
            if Cast(Trinket1) then return "trinket1 main 8"; end
            end
            -- use_item,slot=trinket2,if=!variable.trinket2_is_weird&trinket.2.has_use_buff&fight_remains<=20|(buff.ascendance.up|(!talent.ascendance.enabled&(buff.splintered_elements.up|buff.doom_winds.up|buff.feral_spirit.up|(!talent.splintered_elements.enabled&!talent.doom_winds.enabled&!talent.feral_spirit.enabled))|(fight_remains%%trinket.2.cooldown.duration<=trinket.2.buff.any.duration)|(variable.min_talented_cd_remains>=trinket.2.cooldown.duration)))
            if Trinket2 and Trinket2:IsReady() and not VarTrinket2Ex and not Player:IsItemBlacklisted(Trinket2) and (not VarTrinket2IsWeird and Trinket2:HasUseBuff() and BossFightRemains <= 20 or (Player:BuffUp(S.AscendanceBuff) or (not S.Ascendance:IsAvailable() and (Player:BuffUp(S.SplinteredElementsBuff) or Player:BuffUp(S.DoomWindsBuff) or Player:BuffUp(S.FeralSpiritBuff) or (not S.SplinteredElements:IsAvailable() and not S.DoomWinds:IsAvailable() and not S.FeralSpirit:IsAvailable())) or (FightRemains % VarTrinket2CD <= Trinket2:BuffDuration()) or (VarMinTalentedCDRemains >= VarTrinket2CD)))) then
            if Cast(Trinket2) then return "trinket2 main 10"; end
            end
            -- use_item,name=beacon_to_the_beyond,use_off_gcd=1,if=(!buff.ascendance.up&!buff.feral_spirit.up&!buff.doom_winds.up)|(fight_remains%%150<=5)
            if I.BeacontotheBeyond:IsEquippedAndReady() and ((Player:BuffDown(S.AscendanceBuff) and Player:BuffDown(S.FeralSpiritBuff) and Player:BuffDown(S.DoomWindsBuff)) or (FightRemains % 150 <= 5)) then
            if Cast(I.BeacontotheBeyond) then return "beacon_to_the_beyond main 12"; end
            end
            -- use_item,name=manic_grieftorch,use_off_gcd=1,if=(!buff.ascendance.up&!buff.feral_spirit.up&!buff.doom_winds.up)|(fight_remains%%120<=5)
            if I.ManicGrieftorch:IsEquippedAndReady() and ((Player:BuffDown(S.AscendanceBuff) and Player:BuffDown(S.FeralSpiritBuff) and Player:BuffDown(S.DoomWindsBuff)) or (FightRemains % 120 <= 5)) then
            if Cast(I.ManicGrieftorch) then return "manic_grieftorch main 14"; end
            end
            -- use_item,slot=trinket1,if=!variable.trinket1_is_weird&!trinket.1.has_use_buff
            if Trinket1:IsReady() and (not VarTrinket1IsWeird and not Trinket1:HasUseBuff()) then
            if Cast(Trinket1) then return "trinket1 main 16"; end
            end
            -- use_item,slot=trinket2,if=!variable.trinket2_is_weird&!trinket.2.has_use_buff
            if Trinket2:IsReady() and (not VarTrinket2IsWeird and not Trinket2:HasUseBuff()) then
            if Cast(Trinket2) then return "trinket2 main 18"; end
            end
            -- blood_fury,if=(buff.ascendance.up|buff.feral_spirit.up|buff.doom_winds.up|(fight_remains%%action.blood_fury.cooldown<=action.blood_fury.duration)|(variable.min_talented_cd_remains>=action.blood_fury.cooldown)|(!talent.ascendance.enabled&!talent.feral_spirit.enabled&!talent.doom_winds.enabled))
            if S.BloodFury:IsReady() and (Player:BuffUp(S.AscendanceBuff) or Player:BuffUp(S.FeralSpiritBuff) or Player:BuffUp(S.DoomWindsBuff) or (FightRemains % 120 <= 15) or (VarMinTalentedCDRemains >= 120) or (not S.Ascendance:IsAvailable() and not S.FeralSpirit:IsAvailable() and not S.DoomWinds:IsAvailable())) then
            if Cast(S.BloodFury) then return "blood_fury racial"; end
            end
            -- berserking,if=(buff.ascendance.up|buff.feral_spirit.up|buff.doom_winds.up|(fight_remains%%action.berserking.cooldown<=action.berserking.duration)|(variable.min_talented_cd_remains>=action.berserking.cooldown)|(!talent.ascendance.enabled&!talent.feral_spirit.enabled&!talent.doom_winds.enabled))
            if S.Berserking:IsReady() and (Player:BuffUp(S.AscendanceBuff) or Player:BuffUp(S.FeralSpiritBuff) or Player:BuffUp(S.DoomWindsBuff) or (FightRemains % 180 <= 12) or (VarMinTalentedCDRemains >= 180) or (not S.Ascendance:IsAvailable() and not S.FeralSpirit:IsAvailable() and not S.DoomWinds:IsAvailable())) then
            if Cast(S.Berserking) then return "berserking racial"; end
            end
            -- fireblood,if=(buff.ascendance.up|buff.feral_spirit.up|buff.doom_winds.up|(fight_remains%%action.fireblood.cooldown<=action.fireblood.duration)|(variable.min_talented_cd_remains>=action.fireblood.cooldown)|(!talent.ascendance.enabled&!talent.feral_spirit.enabled&!talent.doom_winds.enabled))
            if S.Fireblood:IsReady() and (Player:BuffUp(S.AscendanceBuff) or Player:BuffUp(S.FeralSpiritBuff) or Player:BuffUp(S.DoomWindsBuff) or (FightRemains % 120 <= 8) or (VarMinTalentedCDRemains >= 120) or (not S.Ascendance:IsAvailable() and not S.FeralSpirit:IsAvailable() and not S.DoomWinds:IsAvailable())) then
            if Cast(S.Fireblood) then return "fireblood racial"; end
            end
            -- ancestral_call,if=(buff.ascendance.up|buff.feral_spirit.up|buff.doom_winds.up|(fight_remains%%action.ancestral_call.cooldown<=action.ancestral_call.duration)|(variable.min_talented_cd_remains>=action.ancestral_call.cooldown)|(!talent.ascendance.enabled&!talent.feral_spirit.enabled&!talent.doom_winds.enabled))
            if S.AncestralCall:IsReady() and (Player:BuffUp(S.AscendanceBuff) or Player:BuffUp(S.FeralSpiritBuff) or Player:BuffUp(S.DoomWindsBuff) or (FightRemains % 120 <= 15) or (VarMinTalentedCDRemains >= 120) or (not S.Ascendance:IsAvailable() and not S.FeralSpirit:IsAvailable() and not S.DoomWinds:IsAvailable())) then
              if GetSetting("rotation_mode", "APLcustom") == "APLsimc" then
            if Cast(S.AncestralCall) then return "ancestral_call racial"; end
            end
            end
            -- ancestral_call,if=(buff.ascendance.up|buff.feral_spirit.up|buff.doom_winds.up|(fight_remains%%action.ancestral_call.cooldown<=action.ancestral_call.duration)|(variable.min_talented_cd_remains>=action.ancestral_call.cooldown)|(!talent.ascendance.enabled&!talent.feral_spirit.enabled&!talent.doom_winds.enabled))
            if S.AncestralCall:IsReady() and Player:BuffUp(S.AscendanceBuff) and GetSetting("rotation_mode", "APLcustom") == "APLcustom" then
            if Cast(S.AncestralCall) then return "ancestral_call racial"; end
            end
            -- invoke_external_buff,name=power_infusion,if=(buff.ascendance.up|buff.feral_spirit.up|buff.doom_winds.up|(fight_remains%%120<=20)|(variable.min_talented_cd_remains>=120)|(!talent.ascendance.enabled&!talent.feral_spirit.enabled&!talent.doom_winds.enabled))
            -- Note: Not handling external PI.
            -- call_action_list,name=single,if=active_enemies=1&!talent.surging_totem.enabled
            if EnemiesMeleeCount < 2 and not S.SurgingTotem:IsAvailable() then
              if GetSetting("rotation_mode", "APLcustom") == "APLcustom" then
                local ShouldReturn = customSingle(); if ShouldReturn then return ShouldReturn; end
              else
                local ShouldReturn = Single(); if ShouldReturn then return ShouldReturn; end
              end
            end
            -- call_action_list,name=single_totemic,if=active_enemies=1&talent.surging_totem.enabled
            if EnemiesMeleeCount < 2 and S.SurgingTotem:IsAvailable() then
              local ShouldReturn = SingleTotemic(); if ShouldReturn then return ShouldReturn; end
            end
            -- call_action_list,name=aoe,if=active_enemies>1&(rotation.standard|rotation.simple)&!talent.surging_totem.enabled
            -- call_action_list,name=aoe_totemic,if=active_enemies>1&(rotation.standard|rotation.simple)&talent.surging_totem.enabled
            -- call_action_list,name=funnel,if=active_enemies>1&rotation.funnel
            if AoEON() and EnemiesMeleeCount > 1 then
                if not S.SurgingTotem:IsAvailable() then
                  if GetSetting("rotation_mode", "APLcustom") == "APLcustom" then
                    local ShouldReturn = customAoe(); if ShouldReturn then return ShouldReturn; end
                  else
                    local ShouldReturn = Aoe(); if ShouldReturn then return ShouldReturn; end
                  end
                else
                  local ShouldReturn = AoeTotemic(); if ShouldReturn then return ShouldReturn; end
                end
              else
                local ShouldReturn = Funnel(); if ShouldReturn then return ShouldReturn; end
            end
            -- If nothing else to do, show the Pool icon
            if Cast(S.Pool) then return "Wait/Pool Resources"; end
          end
        end

    local function Init()
        S.FlameShockDebuff:RegisterAuraTracking()
    end
    MainAddon.SetAPL(263, APL, Init)

    local OldIsCastable
    OldIsCastable = HL.AddCoreOverride("Spell.IsCastable",
            function(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                if Player:BuffUp(S.SpiritwalkersGrace) then
                    ignoreMovement = true
                end

                -- Pool Tempest toggle
                if MainAddon.Toggle:GetToggle('PoolTempest') then
                    if self == S.TempestAbility then
                        local tempestStacks = Player:BuffStack(S.TempestBuff)
                        if tempestStacks < 2 then
                            return false, "Pool Tempest: Need 2+ Tempest stacks"
                        end
                    end
                    if self == S.Ascendance then
                      return false, "Toggle Pool Tempest"
                    end
                    if self == S.PrimordialWave then
                      return false, "Toggle Pool Tempest"
                    end
                    if self == S.PrimordialStormAbility then
                      return false, "Toggle Pool Tempest"
                    end
                    if self == S.DoomWinds then
                      return false, "Toggle Pool Tempest"
                    end
                end

                -- YUNO: Custom rotation logic: skip spells when Ascendance is active
                if GetSetting("rotation_mode", "APLcustom") == "APLcustom" and Player:BuffUp(S.AscendanceBuff) and S.Tempest:IsAvailable() then
                    if self == S.Sundering 
                    or self == S.VoltaicBlazeAbility 
                    or self == S.FlameShock 
                    or self == S.LavaLash 
                    or self == S.ChainLightning
                    or self == S.LightningBolt  then
                        return false, "Skipping due to custom rota with Ascendance active"
                    end
                end

                -- YUNO: no dispel/thunderstorm while in Ascendance
                if GetSetting("cdnodispel", true) and Player:BuffUp(S.AscendanceBuff) then
                  if self == S.Thunderstorm 
                  or self == S.PoisonCleansingTotem 
                  or self == S.CleanseSpirit then
                      return false, "Skipping due to no dispel setting active"
                  end
                end

                if MainAddon.Toggle:GetToggle('HoldMaelstrom') then
                    if self == S.HealingSurge then
                        return false, "Toggle Hold Maelstrom"
                    end
                    if self == S.ChainLightning then
                      return false, "Toggle Hold Maelstrom"
                    end
                    if self == S.LightningBolt then
                      return false, "Toggle Hold Maelstrom"
                    end
                    if self == S.PrimordialWave then
                      return false, "Toggle Hold Maelstrom"
                    end
                end

                if self == S.LightningBolt or self == S.ChainLightning or self == S.ElementalBlast then
                    if MainAddon.Toggle:GetToggle('MaelstromHealing') then
                        return false, "Toggle Maelstrom Healing"
                    end
                end

                -- Check for TempestAbility
                if self == S.TempestAbility then
                    -- Tempest Pooling via TTD
                    if not ShouldCastTempest() then
                        return false, "Pooling for next pull"
                    end

                    -- Keep for CD burst
                    if MainAddon.CDsON() and (S.Ascendance:CooldownUp(nil, true) or S.Ascendance:CooldownRemains() <= 4) then
                        return false, "Pooling for Ascendance burst"
                    end
                    
                    -- PoolTempest toggle: block TempestAbility when we have less than 2 TempestBuff stacks
                    if MainAddon.Toggle:GetToggle('PoolTempest') then
                        local tempestStacks = Player:BuffStack(S.TempestBuff)
                        if tempestStacks < 2 then
                            return false, "Pool Tempest: Need 2+ Tempest stacks"
                        end
                    end
                end

                if self == S.PrimordialWave then
                  if not ShouldCastTempest() then
                      return false, "Pooling for next pull"
                  end
              end
                local BaseCheck, Reason = OldIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                return BaseCheck, Reason
            end
    , 263);

    local OldIsReady
    OldIsReady = HL.AddCoreOverride("Spell.IsReady",
            function(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
                local BaseCheck, Reason = OldIsReady(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
                if self == S.Sundering then
                    if GetSetting("SunderingMoving", true) then
                        return BaseCheck
                    else
                        if Player:IsStandingStillFor() <= 0.5 then
                            return false, "Moving"
                        end
                    end
                end

                -- YUNO: meleeratio setting
                if GetSetting("rotation_mode", "APLcustom") == "APLcustom" then
                  local meleeratioEnabled = GetSetting('meleeratio_check', false)
                  local meleeratioValue   = GetSetting('meleeratio_spin', 50)
                  local meleeratioSpells  = GetSetting('meleeratiospells', {})
                  
                  if meleeratioEnabled and BossFightRemains == 11111 then
                      local EnemiesAll = Player:GetEnemiesInRange(40)
                      local EnemiesCount = #EnemiesAll
                      local EnemiesMelee8y = Player:GetEnemiesInMeleeRange(8)
                      local EnemiesMelee8yCount = #EnemiesMelee8y
                      
                      local currentRatio = 0
                      if EnemiesCount > 0 then
                          currentRatio = (EnemiesMelee8yCount / EnemiesCount) * 100
                      end
                                            
                      if (self == S.Ascendance and meleeratioSpells['ratioAscendance']) or
                        (self == S.Sundering and meleeratioSpells['ratioSundering']) or 
                        (self == S.SurgingTotem and meleeratioSpells['ratioSurgingTotem']) then
                            
                          -- If ratio not met, don't cast
                          if currentRatio <= meleeratioValue then
                              return false, "Not enough enemies in melee range ("..currentRatio.."%)"
                          end
                      end
                  end
                end

                -- ------------------------------------
                --        Custom Syncs start
                -- ------------------------------------
                --- Doom Winds sync
                if self == S.DoomWinds
                and GetSetting("rotation_mode", "APLcustom") == "APLcustom"
                then
                    local pwCD  = S.PrimordialWave:CooldownRemains()
                    local ascCD = S.Ascendance:CooldownRemains()
                    -- hold DW if PW is ready or comes up in ≤15 s
                    if pwCD > 0 and pwCD <= 15 then
                        return false, "Sync: wait for Primordial Wave"
                    end
                    -- also hold DW if Ascendance will be ready in <50 s
                    if ascCD > 0 and ascCD <= 45 then
                        return false, "Sync: wait for Ascendance"
                    end
                    if S.PrimordialWave:CooldownUp() then
                      return false, "Sync: wait for Primordial Wave"
                    end
                end

                --- Primordial Wave sync
                if self == S.PrimordialWave
                and GetSetting("rotation_mode", "APLcustom") == "APLcustom"
                and S.DoomWinds:IsAvailable()
                then
                    local dwCD  = S.DoomWinds:CooldownRemains()
                    local ascCD = S.Ascendance:CooldownRemains()
                    -- only use PW if DW is up now or will be in <25 s
                    if dwCD > 5 and dwCD < 25 then
                        return false, "Sync: wait for Doom Winds"
                    end
                    -- also block PW if Ascendance will be ready in <25 s
                    if ascCD > 1.75 and ascCD < 25 then
                        return false, "Sync: wait for Ascendance"
                    end
                end

                --- Feral Spirit sync
                if self == S.FeralSpirit
                and GetSetting("rotation_mode", "APLcustom") == "APLcustom"
                and S.DoomWinds:IsAvailable()
                and S.ElementalSpirits:IsAvailable()
                and S.AlphaWolf:IsAvailable()
                then
                    local dwCD = S.DoomWinds:CooldownRemains()
                    -- only allow FS if DW is up now or will be in ≤15 s
                    if dwCD > 0 and dwCD <= 15 then
                        return false, "Sync: wait for Doom Winds"
                    end
                end

                -- prevent Ascendance until we've built enough Tempest stacks (custom only)
                if self == S.Ascendance
                and GetSetting("rotation_mode", "APLcustom") == "APLcustom"
                and GetSetting("TempestBuildStacks_check", false)
                and not ShouldCastTempest()
                then
                    return false, "Waiting for Tempest stacks"
                end

                --- Ascendance sync (custom only)
                if self == S.Ascendance
                and GetSetting("rotation_mode", "APLcustom") == "APLcustom"
                then
                    local pwCD = S.PrimordialWave:CooldownRemains()
                    -- don't use Ascendance if PW is ready or comes up in ≤15 s
                    if pwCD > 0 and pwCD <= 15 then
                        return false, "Sync: wait for Primordial Wave"
                    end

                    if S.DoomWinds:IsAvailable() then
                        local dwCD = S.DoomWinds:CooldownRemains()
                        -- don't use Ascendance if DW is ready or comes up in ≤15 s
                        if dwCD > 5 and dwCD <= 15 then
                            return false, "Sync: wait for Doom Winds"
                        end
                    end
                    if S.PrimordialWave:CooldownUp() then
                      return false, "Sync: let setup go first"
                    end
                end

                --- Primordial Storm sync
                if self == S.PrimordialStormAbility then
                  if S.Ascendance:IsReady() or S.Ascendance:CooldownRemains() <= 8 then
                      return false, "Keep for Ascendance" end
                end
                -- ------------------------------------
                --        Custom Syncs end
                -- ------------------------------------

                if self == S.AstralShift then
                    if not inCombat then
                        return false, "Out of Combat"
                    end
                end
                return BaseCheck, Reason
            end
    , 263);

    local OldEnhBuffUp
    OldEnhBuffUp = HL.AddCoreOverride("Player.BuffUp",
        function (self, Spell, AnyCaster, Offset)
            local BaseCheck = OldEnhBuffUp(self, Spell, AnyCaster, Offset)
            if Spell == S.PrimordialWaveBuff then
                return BaseCheck or Player:PrevGCDP(1, S.PrimordialWave)
            else
                return BaseCheck
            end
        end
    , 263);

    HL:RegisterForEvent(function()
        TankUnit = GetTank()
  end, "GROUP_ROSTER_UPDATE", "PLAYER_ENTERING_WORLD", "ZONE_CHANGED")
end