--- ============================ HEADER ============================
--- ======= LOCALIZE =======
-- Addon
---@class MainAddon
local MainAddon = MainAddon
local M = MainAddon

-- HeroLib
local HL = HeroLibEx
---@class HeroCache
local Cache = HeroCache
local Unit = HL.Unit
local Player = Unit.Player
local Pet = Unit.Pet
local Target = Unit.Target
---@class Spell
local Spell = HL.Spell
local Item = HL.Item
local Mage = M.Mage
-- Lua
local select = select
-- WoW API
local GetTime = _G['GetTime']
local CTimerAfter = _G['C_Timer']['After']

-- Arguments Variables
HL.RoPTime = 0

--------------------------
-------- Arcane ----------
--------------------------

HL:RegisterForSelfCombatEvent(
        function (...)
            local DateEvent,_,_,_,_,_,_,DestGUID,_,_,_, SpellID = select(1,...);
            if SpellID == 116014 and Player:GUID() == DestGUID then --void RuneofPower
                HL.RoPTime = GetTime()
            end

        end
, "SPELL_AURA_APPLIED"
)

HL:RegisterForSelfCombatEvent(
        function (...)
            local DateEvent,_,_,_,_,_,_,DestGUID,_,_,_, SpellID = select(1,...);
            if SpellID == 116014 and Player:GUID() == DestGUID then --void erruption
                HL.RoPTime = 0
            end
        end
, "SPELL_AURA_REMOVED"
)

--------------------------
-------- Frost -----------
--------------------------
---
local FrozenOrbFirstHit = true
local FrozenOrbHitTime = 0

HL:RegisterForSelfCombatEvent(function(...)
    local spellID = select(12, ...)
    if spellID == 84721 and FrozenOrbFirstHit then
        FrozenOrbFirstHit = false
        FrozenOrbHitTime = GetTime()
        CTimerAfter(10, function()
            FrozenOrbFirstHit = true
            FrozenOrbHitTime = 0
        end)
    end
end, "SPELL_DAMAGE")

function Player:FrozenOrbGroundAoeRemains()
    return math.max((FrozenOrbHitTime - (GetTime() - 10) - HL.RecoveryTimer()), 0)
end

local brain_freeze_active = false

HL:RegisterForSelfCombatEvent(function(...)
    local spellID = select(12, ...)
    if spellID == Spell.Mage.Frost.Flurry:ID() then
        brain_freeze_active =     Player:BuffUp(Spell.Mage.Frost.BrainFreezeBuff)
                or  Spell.Mage.Frost.BrainFreezeBuff:TimeSinceLastRemovedOnPlayer() < 0.1
    end
end, "SPELL_CAST_SUCCESS")

function Player:BrainFreezeActive()
    if self:IsCasting(Spell.Mage.Frost.Flurry) then
        return false
    else
        return brain_freeze_active
    end
end