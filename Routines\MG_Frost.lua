function A_64(...)
    -- HR UPDATE: fix(Frost): Frost Mage APL Alignment Fixes (#1183) (11/03/25)
    -- REMEMBER: CastMagic(S.Blizzard, nil, "190356-Magic", magicgroundspell_blizzard) 
    ---@class MainAddon
    local MainAddon = MainAddon
    local M = MainAddon
    -- HeroLib
    local HL = HeroLibEx
    ---@class Unit
    local Unit = HL.Unit
    ---@class Unit
    local Player = Unit.Player
    ---@class Unit
    local Target = Unit.Target
    ---@class Unit
    local MouseOver = Unit.MouseOver
    ---@class Unit
    local Pet = Unit.Pet
    ---@class Spell
    local Spell = HL.Spell
    ---@class Item
    local Item = HL.Item
    -- HeroRotation
    local Cast = M.Cast
    local CastMagic = M.CastMagic
    local AoEON = M.AoEON
    -- LUA
    local mathmax = math.max
    local GetMouseFoci = _G['GetMouseFoci']
    local C_Timer = _G['C_Timer']
    local GetTime = _G['GetTime']
    local IsInGroup = _G['IsInGroup']
    local IsInRaid = _G['IsInRaid']
    local IsMouseButtonDown = _G['IsMouseButtonDown']
    local IsCurrentSpell = _G['C_Spell']['IsCurrentSpell']
    local num = M.num
    local bool = M.bool
    local CastTargetIf = MainAddon.CastTargetIf
    -- Define S/I for spell and item arrays
    local S = Spell.Mage.Frost
    local I = Item.Mage.Frost

    -- Toggle Setting
    MainAddon.Toggle.Special["ForceAoE"] = {
        Icon = MainAddon.GetTexture(S.Blizzard),
        Name = "Force AoE",
        Description = "Force Rotation to AoE.",
        Spec = 64
    }

    local GetSetting = MainAddon.Config.GetClassSetting
    local Config_Key = MainAddon.GetClassVariableName()
    local Config_Color = '3FC7EB'
    local Config_Table = {
        key = Config_Key,
        title = 'Mage - Frost',
        subtitle = '?? ' .. MainAddon.Version,
        width = 600,
        height = 700,
        profiles = true,
        config = {
            { type = 'header', text = '', size = 24, align = 'Center', color = Config_Color },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
            { type = "header", text = '\"The ice does not forgive.\"', size = 16, align = "center", color = Config_Color },
            { type = "header", text = "?? x yuno", size = 12, align = "center", color = '4C4C4C' },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
            { type = 'header', text = 'DPS', color = Config_Color },
            { type = 'checkspin', text = ' Stand still threshold (IV, CS, FO)', key = 'DPSMovingValue', icon = S.Frostbite:ID(), min = 0, max = 10, default_spin = 1.5, default_check = true },
            { type = 'checkbox', text = ' Comet Storm only in melee', icon = S.CometStorm:ID(), key = 'comet_melee_only', default = false },
            { type = 'spacer' },
            { type = 'header', text = 'Defensives', color = Config_Color },
            { type = 'checkspin', text = ' Ice Barrier', key = 'IB', icon = S.IceBarrier:ID(), min = 1, max = 100, default_spin = 55, default_check = true },
            { type = 'checkspin', text = ' Mass Barrier - Average Group health threshold', key = 'MassBB', icon = S.MassBarrier:ID(), min = 1, max = 100, default_spin = 70, default_check = true },
            { type = 'checkspin', text = ' Mirror Image - Defensive', key = 'mi_defensive', icon = S.MirrorImage:ID(), min = 1, max = 100, default_spin = 40, default_check = false },
            { type = 'checkspin', text = ' Ice Cold / Ice Block', key = 'ICIB', icon = S.IceBlock:ID(), min = 1, max = 100, default_spin = 35, default_check = true },
            { type = 'checkbox', text = ' Use Cold Snap to reset Ice Cold / Ice Block', icon = S.ColdSnap:ID(), key = 'snapreset', default = true },
            { type = 'spacer' },
            { type = 'header', text = 'Utilities', color = Config_Color },
            { type = 'checkbox', text = ' Mirror Image - Aggro', icon = S.MirrorImage:ID(), key = 'mi_aggro', default = true },
            { type = 'checkbox', text = ' Greater Invisibility - Aggro', icon = S.GreaterInvisibility:ID(), key = 'gi_aggro', default = true },
            { type = 'checkspin', text = ' Auto turn off Force AoE Toggle after', key = 'forceAoE_off', min = 1, max = 20, default_spin = 6, default_check = true },
            { type = 'dropdown', text = ' Ice Floes', key = 'if', icon = S.IceFloes:ID(), multiselect = true, list = { { text = 'Shifting Power', key = 1 }, { text = 'Glacial Spike', key = 2 }, { text = 'Ray of Frost', key = 3 } }, default = { 1, 2, 3 }, },
            { type = 'dropdown',
              text = ' Arcane Intellect', key = 'int',
              icon = S.ArcaneIntellect:ID(),
              multiselect = true,
              list = {
                  { text = 'Self', key = 'int_self' },
                  { text = 'Friends', key = 'int_friends' },
              },
              default = {
                  'int_self',
                  'int_friends'
              },
            },
            { type = 'spacer' },
            {
                type = "checkbox",
                text = "Cast Groundspell only when MouseOver enemies or tank.",
                key = "MOOption",
                default = false
            },
            { type = 'checkbox', text = ' Magic Groundspell - Blizzard', icon = S.Blizzard:ID(), key = 'magicgroundspell_blizzard', default = false },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
        }
    }
    Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
    Config_Table.config = MainAddon.BuildDPSTrinketUI(Config_Table.config, Config_Color)
    Config_Table.config = MainAddon.BuildCombatPotionUI(Config_Table.config, "Frost", Config_Color)
    M.SetConfig(64, Config_Table)

    -- Custom Var
    local MOCheck = false
    local ForceAoE_timestamp = 0
    local ForceAoE_status = false
    local Enemies40y = {}
    local EnemiesCount40y = 0
    local EnemiesCount10y = 0
    local ShouldReturn
    local ifSetting = {}

    -- Create table to exclude above trinkets from On Use function
    local OnUseExcludes = {
        -- TWW Trinkets
        I.BurstofKnowledge:ID(),
        I.HouseofCards:ID(),
        I.ImperfectAscendancySerum:ID(),
        I.SpymastersWeb:ID(),
        I.TreacherousTransmitter:ID(),
    }

    --- ===== Rotation Variables =====
    local VarBoltSpam = S.Splinterstorm:IsAvailable() and S.ColdFront:IsAvailable() and S.SlickIce:IsAvailable() and S.DeathsChill:IsAvailable() and S.FrozenTouch:IsAvailable() or S.FrostfireBolt:IsAvailable() and S.DeepShatter:IsAvailable() and S.SlickIce:IsAvailable() and S.DeathsChill:IsAvailable()
    local Bolt = S.FrostfireBolt:IsAvailable() and S.FrostfireBolt or S.Frostbolt
    local EnemiesCount8ySplash, EnemiesCount16ySplash --Enemies arround target
    local Enemies16ySplash
    local RemainingWintersChill = 0
    local Icicles = 0
    local PlayerMaxLevel = 80 -- TODO: Pull this value from Enum instead.
    local BossFightRemains = 11111
    local FightRemains = 11111
    local GCDMax
    local magicgroundspell_blizzard = GetSetting('magicgroundspell_blizzard', false)

    --- ===== Event Registrations =====
    HL:RegisterForEvent(function()
        S.Frostbolt:RegisterInFlightEffect(228597)
        S.Frostbolt:RegisterInFlight()
        S.FrostfireBolt:RegisterInFlight()
        S.FrozenOrb:RegisterInFlightEffect(84721)
        S.FrozenOrb:RegisterInFlight()
        S.Flurry:RegisterInFlightEffect(228354)
        S.Flurry:RegisterInFlight()
        S.GlacialSpike:RegisterInFlightEffect(228600)
        S.GlacialSpike:RegisterInFlight()
        S.IceLance:RegisterInFlightEffect(228598)
        S.IceLance:RegisterInFlight()
        VarBoltSpam = S.Splinterstorm:IsAvailable() and S.ColdFront:IsAvailable() and S.SlickIce:IsAvailable() and S.DeathsChill:IsAvailable() and S.FrozenTouch:IsAvailable() or S.FrostfireBolt:IsAvailable() and S.DeepShatter:IsAvailable() and S.SlickIce:IsAvailable() and S.DeathsChill:IsAvailable()
        Bolt = S.FrostfireBolt:IsAvailable() and S.FrostfireBolt or S.Frostbolt
    end, "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")
    S.Frostbolt:RegisterInFlightEffect(228597)
    S.Frostbolt:RegisterInFlight()
    S.FrostfireBolt:RegisterInFlight()
    S.FrozenOrb:RegisterInFlightEffect(84721)
    S.FrozenOrb:RegisterInFlight()
    S.Flurry:RegisterInFlightEffect(228354)
    S.Flurry:RegisterInFlight()
    S.GlacialSpike:RegisterInFlightEffect(228600)
    S.GlacialSpike:RegisterInFlight()
    S.IceLance:RegisterInFlightEffect(228598)
    S.IceLance:RegisterInFlight()

    HL:RegisterForEvent(function()
        BossFightRemains = 11111
        FightRemains = 11111
        RemainingWintersChill = 0
    end, "PLAYER_REGEN_ENABLED")

    --- ===== Start Custom =====
    local function Defensives()
        local DefensiveUp = Player:BuffUp(S.IceBarrier) or Player:BuffUp(S.IceCold) or Player:BuffUp(S.MirrorImageBuff) or Player:BuffUp(S.IceBlock)
        if not DefensiveUp then
            if GetSetting('IB_check', false) then
                if S.IceBarrier:IsReady(Player) and Player:HealthPercentage() <= GetSetting('IB_spin', 10) then
                    if Cast(S.IceBarrier) then
                        return "Ice Barrier";
                    end
                end
            end

            if Player:AffectingCombat() then
                if S.MirrorImage:IsReady(Player) and GetSetting('mi_defensive_check', false) and Player:HealthPercentage() <= GetSetting('mi_defensive_spin', 40) then
                    if Cast(S.MirrorImage) then
                        return "Mirror Image - HP"
                    end
                end

                if S.ColdSnap:IsReady(Player) 
                and GetSetting('snapreset', false) 
                and not Player:DebuffUp(S.HypothermiaDebuff) then
                    if S.IceCold:IsAvailable() and S.IceCold:CooldownRemains() > 20 then
                        if Cast(S.ColdSnap) then
                            return "Cold Snap - reset Ice Cold"
                        end
                    elseif S.IceBlock:IsAvailable() and S.IceBlock:CooldownRemains() > 20 then
                        if Cast(S.ColdSnap) then
                            return "Cold Snap - reset Ice Block"
                        end
                    end
                end              

                if GetSetting('ICIB_check', false) and Player:HealthPercentage() <= GetSetting('ICIB_spin', 35) and Player:DebuffDown(S.HypothermiaDebuff) then
                    if S.IceCold:IsReady(Player) then
                        if Cast(S.IceCold) then
                            return "Ice Cold - HP"
                        end
                    end
    
                    if S.IceBlock:IsReady(Player) then
                        if Cast(S.IceBlock) then
                            return "Ice Block - HP"
                        end
                    end
                end
            end
        end

        if Player:AffectingCombat() then
            if Player:IsInDungeonArea() or Player:IsInRaidArea() then
                if S.MassBarrier:IsReady(Player) and GetSetting('MassBB_check', false) and MainAddon.HealingEngine:MedianHP() <= GetSetting('MassBB_spin', 10) then
                    if Cast(S.MassBarrier) then
                        return "Mass Barrier"
                    end
                end
            end
        end
    end

    local function Utilities()
        local int = GetSetting('int', {})
        if S.ArcaneIntellect:IsReady(Player) and (int['int_self'] and Player:BuffDown(S.ArcaneIntellect, true) or int['int_friends'] and M.GroupBuffMissing(S.ArcaneIntellect)) then
            if Cast(S.ArcaneIntellect) then
                return "arcane_intellect precombat 2";
            end
        end

        if Player:IsTankingAoE(40) then
            if GetSetting('mi_aggro', true) then
                if S.MirrorImage:IsReady(Player) and not Player:PrevGCD(1, S.GreaterInvisibility) and Player:BuffDown(S.GreaterInvisibilityBuff) then
                    if Cast(S.MirrorImage) then
                        return 'Mirror Image'
                    end
                end
            end
            if GetSetting('gi_aggro', true) then
                if S.GreaterInvisibility:IsReady(Player) and not Player:PrevGCD(1, S.MirrorImage) and (IsInGroup() or IsInRaid()) and not Player:BuffUp(S.MirrorImageBuff) then
                    if Cast(S.GreaterInvisibility) then
                        return 'Greater Invisibility'
                    end
                end
            end
        end
    end
    --- ===== End Custom =====

    --- @param Tar Unit|nil @If nil, defaults to Target
    local function Freezable(Tar)
        if Tar == nil then Tar = Target end
        local NPCID = Tar:NPCID()
        if not MainAddon.CONST.PvE_NPCDR[NPCID] then 
            return not Tar:IsInBossList() or Tar:Level() < PlayerMaxLevel + 3
        end
        return MainAddon.CONST.PvE_NPCDR[NPCID].Root
    end
    
    local function FrozenRemains()
        return mathmax(Player:BuffRemains(S.FingersofFrostBuff), Target:DebuffRemains(S.WintersChillDebuff), Target:DebuffRemains(S.Frostbite), Target:DebuffRemains(S.Freeze), Target:DebuffRemains(S.FrostNova))
    end
    ---@return number
    local function CalculateWintersChill(enemies)
        if S.WintersChillDebuff:AuraActiveCount() == 0 then return 0 end
        local WCStacks = 0
        ---@param CycleUnit Unit
        for _, CycleUnit in pairs(enemies) do
          WCStacks = WCStacks + CycleUnit:DebuffStack(S.WintersChillDebuff)
        end
        if IsCurrentSpell(S.Flurry:ID()) or S.Flurry:TimeSinceLastCast() < 0.8 then return WCStacks + 2 end

        return WCStacks
    end

    --- ===== CastTargetIf Filter Functions =====
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterWCStacks(TargetUnit)
        -- target_if=min/max:debuff.winters_chill.stack
        return (TargetUnit:DebuffStack(S.WintersChillDebuff))
    end
    --- ===== CastTargetIf Condition Functions =====
    --- 
    --- ===== Rotation Functions =====
    local function Precombat()
        -- flask
        -- food
        -- augmentation
        -- arcane_intellect
        -- if S.ArcaneIntellect:IsReady() and (Player:BuffDown(S.ArcaneIntellect, true) or M.GroupBuffMissing(S.ArcaneIntellect)) then
        --     if Cast(S.ArcaneIntellect) then
        --         return "arcane_intellect precombat 2";
        --     end
        -- end
        -- snapshot_stats
        -- variable,name=boltspam,value=talent.splinterstorm&talent.cold_front&talent.slick_ice&talent.deaths_chill&talent.frozen_touch|talent.frostfire_bolt&talent.deep_shatter&talent.slick_ice&talent.deaths_chill
        -- Note: Variables moved to declarations and SPELLS_CHANGED/LEARNED_SPELL_IN_TAB Event Registrations.
        -- variable,name=treacherous_transmitter_precombat_cast,value=12*!variable.boltspam
        -- Note: Unused variable.
        -- use_item,name=treacherous_transmitter
        if I.TreacherousTransmitter:IsEquippedAndReady() then
            if Cast(I.TreacherousTransmitter) then return "treacherous_transmitter precombat 2"; end
        end
        -- blizzard,if=active_enemies>=3
        -- blizzard,if=active_enemies>=2&talent.ice_caller&!talent.fractured_frost|active_enemies>=3
        -- Note: Can't check active_enemies in Precombat
        -- frostbolt,if=active_enemies<=2
        if Bolt:IsReady() and not Player:IsCasting(Bolt) and Bolt:CastTime() >= M.CombinedPullTimer() and M.CombinedPullTimer() > 0 then
            if Cast(Bolt) then
                return "frostbolt precombat 10";
            end
        end
    end

    local function CDs()
        -- use_item,name=treacherous_transmitter,if=fight_remains<32+20*equipped.spymasters_web|prev_off_gcd.icy_veins|(cooldown.icy_veins.remains<12|cooldown.icy_veins.remains<22&cooldown.shifting_power.remains<10)
            if I.TreacherousTransmitter:IsEquippedAndReady() and (BossFightRemains < 32 + 20 * num(I.SpymastersWeb:IsEquipped()) or Player:PrevOffGCDP(1, S.IcyVeins) or (S.IcyVeins:CooldownRemains() < 12 or S.IcyVeins:CooldownRemains() < 22 and S.ShiftingPower:CooldownRemains() < 10)) then
        if Cast(I.TreacherousTransmitter) then return "treacherous_transmitter cds 2"; end
        end
        -- do_treacherous_transmitter_task,if=fight_remains<18|(buff.cryptic_instructions.remains<?buff.realigning_nexus_convergence_divergence.remains<?buff.errant_manaforge_emission.remains)<(action.shifting_power.execute_time+1*talent.ray_of_frost)
        -- TODO
        -- use_item,name=spymasters_web,if=fight_remains<20|buff.icy_veins.remains<19&(fight_remains<105|buff.spymasters_report.stack>=32)&(buff.icy_veins.remains>15|trinket.treacherous_transmitter.cooldown.remains>50)
        if I.SpymastersWeb:IsEquippedAndReady() and (BossFightRemains < 20 or Player:BuffRemains(S.IcyVeinsBuff) < 19 and (FightRemains < 105 or Player:BuffStack(S.SpymastersReportBuff) >= 32) and (Player:BuffRemains(S.IcyVeinsBuff) > 15 or I.TreacherousTransmitter:IsEquipped() and I.TreacherousTransmitter:CooldownRemains() > 50)) then
            if Cast(I.SpymastersWeb) then return "spymasters_web cds 4"; end
        end
        -- use_item,name=house_of_cards,if=buff.icy_veins.remains>9|fight_remains<20
        if I.HouseofCards:IsEquippedAndReady() and (Player:BuffRemains(S.IcyVeinsBuff) > 9 or BossFightRemains < 20) then
            if Cast(I.HouseofCards) then return "house_of_cards cds 5"; end
        end
        -- use_item,name=imperfect_ascendancy_serum,if=buff.icy_veins.remains>15|fight_remains<20
        if I.ImperfectAscendancySerum:IsEquippedAndReady() and (Player:BuffRemains(S.IcyVeinsBuff) > 15 or BossFightRemains < 20) then
            if Cast(I.ImperfectAscendancySerum) then return "imperfect_ascendancy_serum cds 6"; end
        end    
        -- use_item,name=burst_of_knowledge,if=buff.icy_veins.remains>15|fight_remains<20
        if I.BurstofKnowledge:IsEquippedAndReady() and (Player:BuffRemains(S.IcyVeinsBuff) > 15 or BossFightRemains < 20) then
            if Cast(I.BurstofKnowledge) then return "burst_of_knowledge cds 8"; end
        end
        -- potion,if=fight_remains<35|buff.icy_veins.remains>15
        if MainAddon.UsePotion() then
            MainAddon.SetTopColor(1, "Combat Potion")
        end
        -- icy_veins,if=buff.icy_veins.remains<1.5&(talent.frostfire_bolt|active_enemies>=3)
        if S.IcyVeins:IsReady() and (Player:BuffRemains(S.IcyVeinsBuff) < 1.5 and (S.FrostfireBolt:IsAvailable() or EnemiesCount16ySplash >= 3)) then
            if Cast(S.IcyVeins) then return "icy_veins cds 12"; end
        end
        -- frozen_orb,if=time=0&active_enemies>=3
        -- Note: Can't get here at time=0.
        -- flurry,if=time=0&active_enemies<=2
        -- Note: Can't get here at time=0.
        -- icy_veins,if=buff.icy_veins.remains<1.5&talent.splinterstorm
        if S.IcyVeins:IsReady() and (Player:BuffRemains(S.IcyVeinsBuff) < 1.5 and S.Splinterstorm:IsAvailable()) then
            if Cast(S.IcyVeins) then return "icy_veins cds 14"; end
        end
        -- use_items,if=!equipped.balfire_branch|time>5
        ---@class Item
        local ItemToUse = Player:GetUseableItems(OnUseExcludes)
        if ItemToUse then
            if Cast(ItemToUse) then return "Generic use_items for " .. ItemToUse:Name(); end
        end
        -- invoke_external_buff,name=power_infusion,if=buff.power_infusion.down
        -- invoke_external_buff,name=blessing_of_summer,if=buff.blessing_of_summer.down
        -- Note: Not handling external buffs.
        -- blood_fury
        if S.BloodFury:IsReady() and (Player:BuffRemains(S.IcyVeinsBuff) > 10 and Player:BuffRemains(S.IcyVeinsBuff) < 15 or BossFightRemains < 15) then
            if Cast(S.BloodFury) then return "blood_fury cds 16"; end
        end
        -- berserking,if=buff.icy_veins.remains>10&buff.icy_veins.remains<15|fight_remains<15
        if S.Berserking:IsReady() and (Player:BuffRemains(S.IcyVeinsBuff) > 10 and Player:BuffRemains(S.IcyVeinsBuff) < 15 or BossFightRemains < 15) then
            if Cast(S.Berserking) then return "berserking cds 18"; end
        end
        -- fireblood
        if S.Fireblood:IsReady() then
            if Cast(S.Fireblood) then return "fireblood cds 20"; end
        end
        -- ancestral_call
        if S.AncestralCall:IsReady() then
            if Cast(S.AncestralCall) then return "ancestral_call cds 22"; end
        end
    end

    local function Movement()
        -- any_blink,if=movement.distance>10
        -- Note: Not handling blink.
        -- ice_floes,if=buff.ice_floes.down
        if S.IceFloes:IsReady() and Player:BuffDown(S.IceFloes) and not ifSetting[1] and not ifSetting[2] and not ifSetting[3] then
          if Cast(S.IceFloes) then return "ice_floes movement 2"; end
        end
        -- ice_nova
        if S.IceNova:IsReady() then
          if Cast(S.IceNova) then return "ice_nova movement 4"; end
        end
        -- cone_of_cold,if=!talent.coldest_snap&active_enemies>=2
        if S.ConeofCold:IsReady() and (not S.ColdestSnap:IsAvailable() and EnemiesCount16ySplash >= 3) then
          if Cast(S.ConeofCold) then return "cone_of_cold movement 6"; end
        end
        -- Yuno: removed, better to IceLance
        -- -- arcane_explosion,if=mana.pct>30&active_enemies>=2
        -- -- Note: If we're not in ArcaneExplosion range, just move to the next suggestion.
        -- if S.ArcaneExplosion:IsReady() and Target:IsInRange(10) and (Player:ManaPercentage() > 30 and EnemiesCount8ySplash >= 2) then
        --   if Cast(S.ArcaneExplosion) then return "arcane_explosion movement 8"; end
        -- end
        -- YUNO: disabled fireblast during movement
        -- -- fire_blast
        -- if S.FireBlast:IsReady() then
        --   if Cast(S.FireBlast) then return "fire_blast movement 10"; end
        -- end
        -- ice_lance
        if S.IceLance:IsReady() then
          if Cast(S.IceLance) then return "ice_lance movement 12"; end
        end
    end

    local function AoEFF()
        -- YUNO: changed conditions to make sure we actually get the CoC reset
        -- cone_of_cold,if=talent.coldest_snap&prev_gcd.1.comet_storm
        if S.ConeofCold:IsReady() and EnemiesCount16ySplash >= 3 and (S.ColdestSnap:IsAvailable() and S.FrozenOrb:CooldownDown() and (S.CometStorm:TimeSinceLastCast() <= 10 or S.FrozenOrb:CooldownRemains() > 10 and S.CometStorm:CooldownRemains() > 10) and (not S.DeathsChill:IsAvailable() or Player:BuffRemains(S.IcyVeinsBuff) < 9 or Player:BuffStack(S.DeathsChillBuff) >= 12)) then
          if Cast(S.ConeofCold) then return "cone_of_cold aoe_ff 2"; end
        end
        -- frostfire_bolt,if=talent.deaths_chill&buff.icy_veins.remains>9&(buff.deaths_chill.stack<9|buff.deaths_chill.stack=9&!action.frostfire_bolt.in_flight)
        if Bolt:IsReady() and (S.DeathsChill:IsAvailable() and Player:BuffRemains(S.IcyVeinsBuff) > 9 and (Player:BuffStack(S.DeathsChillBuff) < 9 or Player:BuffStack(S.DeathsChillBuff) == 9 and not Bolt:InFlight())) then
          if Cast(Bolt) then return "frostfire_bolt aoe_ff 4"; end
        end
        -- freeze,if=freezable&(prev_gcd.1.glacial_spike|prev_gcd.1.comet_storm&cooldown.cone_of_cold.remains&!prev_gcd.2.cone_of_cold)
        if Pet:IsActive() and S.Freeze:IsReady() and (Freezable() and (Player:PrevGCDP(1, S.GlacialSpike) or Player:PrevGCDP(1, S.CometStorm) and S.ConeofCold:CooldownDown() and not Player:PrevGCDP(2, S.ConeofCold))) then
          if Cast(S.Freeze) then return "freeze aoe_ff 6"; end
        end
        -- ice_nova,if=freezable&(prev_gcd.1.glacial_spike&remaining_winters_chill=0&debuff.winters_chill.down|prev_gcd.1.comet_storm&cooldown.cone_of_cold.remains&!prev_gcd.2.cone_of_cold)&!prev_off_gcd.freeze
        if S.IceNova:IsReady() and (Freezable() and (Player:PrevGCDP(1, S.GlacialSpike) and RemainingWintersChill == 0 and Target:DebuffDown(S.WintersChillDebuff) or Player:PrevGCDP(1, S.CometStorm) and S.ConeofCold:CooldownDown() and not Player:PrevGCDP(2, S.ConeofCold)) and not Player:PrevOffGCDP(1, S.Freeze)) then
          if Cast(S.IceNova) then return "ice_nova aoe_ff 8"; end
        end
        -- frozen_orb,if=!prev_gcd.1.cone_of_cold
        if S.FrozenOrb:IsReady() and (not Player:PrevGCDP(1, S.ConeofCold)) then
          if Cast(S.FrozenOrb) then return "frozen_orb aoe_ff 10"; end
        end
        -- comet_storm,if=cooldown.cone_of_cold.remains>6|cooldown.cone_of_cold.ready
        if S.CometStorm:IsReady() and (S.ConeofCold:CooldownRemains() > 6 or S.ConeofCold:CooldownUp()) then
          if Cast(S.CometStorm) then return "comet_storm aoe_ff 12"; end
        end
        -- flurry,if=cooldown_react&remaining_winters_chill=0&(buff.excess_frost.react&cooldown.comet_storm.remains>5|prev_gcd.1.glacial_spike)
        if S.Flurry:IsReady() and (RemainingWintersChill == 0 and (Player:BuffUp(S.ExcessFrostBuff) and S.CometStorm:CooldownRemains() > 5 or Player:PrevGCDP(1, S.GlacialSpike))) then
          if Cast(S.Flurry) then return "flurry aoe_ff 14"; end
        end
        -- blizzard,if=talent.ice_caller
        if S.Blizzard:IsReady() and (S.IceCaller:IsAvailable()) then
          if CastMagic(S.Blizzard, nil, "190356-Magic", magicgroundspell_blizzard) then return "blizzard aoe_ff 16"; end
        end
        -- ray_of_frost,if=talent.splintering_ray&remaining_winters_chill=2
        if S.RayofFrost:IsReady() and (S.SplinteringRay:IsAvailable() and RemainingWintersChill == 2) then
          if Cast(S.RayofFrost) then return "ray_of_frost aoe_ff 18"; end
        end
        -- shifting_power,if=cooldown.icy_veins.remains>10&(fight_remains+10>cooldown.icy_veins.remains)
        if S.ShiftingPower:IsReady() and (S.IcyVeins:CooldownRemains() > 10 and (FightRemains + 10 > S.IcyVeins:CooldownRemains())) then
          if Cast(S.ShiftingPower) then return "shifting_power aoe_ff 20"; end
        end
        -- frostfire_bolt,if=buff.frostfire_empowerment.react&!buff.excess_frost.react&!buff.excess_fire.react
        if Bolt:IsReady() and (Player:BuffUp(S.FrostfireEmpowermentBuff) and Player:BuffDown(S.ExcessFrostBuff) and Player:BuffDown(S.ExcessFireBuff)) then
          if Cast(Bolt) then return "frostfire_bolt aoe_ff 22"; end
        end
        -- glacial_spike,if=(active_enemies<=6|!talent.ice_caller)&buff.icicles.react=5
        if S.GlacialSpike:IsReady() and ((EnemiesCount8ySplash <= 6 or not S.IceCaller:IsAvailable()) and Icicles == 5) then
          if Cast(S.GlacialSpike) then return "glacial_spike aoe_ff 24"; end
        end
        -- ice_lance,if=buff.fingers_of_frost.react|remaining_winters_chill
        if S.IceLance:IsReady() and (Player:BuffUp(S.FingersofFrostBuff) or RemainingWintersChill > 0) then
          if Cast(S.IceLance) then return "ice_lance aoe_ff 26"; end
        end
        -- flurry,if=cooldown_react&remaining_winters_chill=0
        if S.Flurry:IsReady() and (RemainingWintersChill == 0) then
          if Cast(S.Flurry) then return "flurry aoe_ff 28"; end
        end
        -- frostfire_bolt
        if Bolt:IsReady() then
          if Cast(Bolt) then return "frostfire_bolt aoe_ff 30"; end
        end
        -- call_action_list,name=movement
        if Player:IsMoving() then
          local ShouldReturn = Movement(); if ShouldReturn then return ShouldReturn; end
        end
    end      

    local function AoESS()
        -- YUNO: changed conditions to make sure we actually get the CoC reset
        -- cone_of_cold,if=talent.coldest_snap&!action.frozen_orb.cooldown_react&(prev_gcd.1.comet_storm|prev_gcd.1.frozen_orb&cooldown.comet_storm.remains>5)&(!talent.deaths_chill|buff.icy_veins.remains<9|buff.deaths_chill.stack>=15)
        if S.ConeofCold:IsReady() and EnemiesCount16ySplash >= 3 and (S.ColdestSnap:IsAvailable() and S.FrozenOrb:CooldownDown() and (S.CometStorm:TimeSinceLastCast() <= 10 or S.FrozenOrb:CooldownRemains() > 10 and S.CometStorm:CooldownRemains() > 10) and (not S.DeathsChill:IsAvailable() or Player:BuffRemains(S.IcyVeinsBuff) < 9 or Player:BuffStack(S.DeathsChillBuff) >= 12)) then
          if Cast(S.ConeofCold) then return "cone_of_cold aoe_ss 2"; end
        end
        -- freeze,if=freezable&(prev_gcd.1.glacial_spike|!talent.glacial_spike)
        if Pet:IsActive() and S.Freeze:IsReady() and (Freezable() and (Player:PrevGCDP(1, S.GlacialSpike) or not S.GlacialSpike:IsAvailable())) then
          if Cast(S.Freeze) then return "freeze aoe_ss 4"; end
        end
        -- flurry,if=cooldown_react&remaining_winters_chill=0&debuff.winters_chill.down&prev_gcd.1.glacial_spike
        if S.Flurry:IsReady() and (RemainingWintersChill == 0 and Target:DebuffDown(S.WintersChillDebuff) and Player:PrevGCDP(1, S.GlacialSpike)) then
          if Cast(S.Flurry) then return "flurry aoe_ss 6"; end
        end
        -- ice_nova,if=freezable&!prev_off_gcd.freeze&prev_gcd.1.glacial_spike&remaining_winters_chill=0&debuff.winters_chill.down
        if S.IceNova:IsReady() and (Freezable() and not Player:PrevOffGCDP(1, S.Freeze) and Player:PrevGCDP(1, S.GlacialSpike) and RemainingWintersChill == 0 and Target:DebuffDown(S.WintersChillDebuff)) then
          if Cast(S.IceNova) then return "ice_nova aoe_ss 8"; end
        end
        -- ice_nova,if=talent.unerring_proficiency&time-action.cone_of_cold.last_used<10&time-action.cone_of_cold.last_used>7
        if S.IceNova:IsReady() and (S.UnerringProficiency:IsAvailable() and S.ConeofCold:TimeSinceLastCast() < 10 and S.ConeofCold:TimeSinceLastCast() > 7) then
          if Cast(S.IceNova) then return "ice_nova aoe_ss 9"; end
        end
        -- frozen_orb,if=cooldown_react
        if S.FrozenOrb:IsReady() then
          if Cast(S.FrozenOrb) then return "frozen_orb aoe_ss 10"; end
        end
        -- blizzard,if=talent.ice_caller|talent.freezing_rain
        if S.Blizzard:IsReady() and (S.IceCaller:IsAvailable() or S.FreezingRain:IsAvailable()) then
          if CastMagic(S.Blizzard, nil, "190356-Magic", magicgroundspell_blizzard) then return "blizzard aoe_ss 12"; end
        end
        -- frostbolt,if=talent.deaths_chill&buff.icy_veins.remains>9&(buff.deaths_chill.stack<12|buff.deaths_chill.stack=12&!action.frostbolt.in_flight)
        if Bolt:IsReady() and (S.DeathsChill:IsAvailable() and Player:BuffRemains(S.IcyVeinsBuff) > 9 and (Player:BuffStack(S.DeathsChillBuff) < 12 or Player:BuffStack(S.DeathsChillBuff) == 12 and not Bolt:InFlight())) then
          if Cast(Bolt) then return "frostbolt aoe_ss 14"; end
        end
        -- comet_storm
        if S.CometStorm:IsReady() then
          if Cast(S.CometStorm) then return "comet_storm aoe_ss 16"; end
        end
        -- ray_of_frost,if=talent.splintering_ray&remaining_winters_chill&buff.icy_veins.down
        if S.RayofFrost:IsReady() and (S.SplinteringRay:IsAvailable() and RemainingWintersChill > 0 and Player:BuffDown(S.IcyVeinsBuff)) then
          if Cast(S.RayofFrost) then return "ray_of_frost aoe_ss 18"; end
        end
        -- glacial_spike,if=buff.icicles.react=5&(action.flurry.cooldown_react|remaining_winters_chill|freezable&cooldown.ice_nova.ready)
        if S.GlacialSpike:IsReady() and (Icicles == 5 and (S.Flurry:CooldownUp() or RemainingWintersChill > 0 or Freezable() and S.IceNova:CooldownUp())) then
          if Cast(S.GlacialSpike) then return "glacial_spike aoe_ss 20"; end
        end
        -- shifting_power,if=cooldown.icy_veins.remains>10&(fight_remains+15>cooldown.icy_veins.remains)
        if S.ShiftingPower:IsReady() and (S.IcyVeins:CooldownRemains() > 10 and (FightRemains + 15 > S.IcyVeins:CooldownRemains())) then
          if Cast(S.ShiftingPower) then return "shifting_power aoe_ss 22"; end
        end
        -- ice_lance,if=buff.fingers_of_frost.react|remaining_winters_chill
        if S.IceLance:IsReady() and (Player:BuffUp(S.FingersofFrostBuff) or RemainingWintersChill > 0) then
          if Cast(S.IceLance) then return "ice_lance aoe_ss 24"; end
        end
        -- flurry,if=cooldown_react&remaining_winters_chill=0&debuff.winters_chill.down
        if S.Flurry:IsReady() and (RemainingWintersChill == 0 and Target:DebuffDown(S.WintersChillDebuff)) then
          if Cast(S.Flurry) then return "flurry aoe_ss 26"; end
        end
        -- frostbolt
        if Bolt:IsReady() then
          if Cast(Bolt) then return "frostbolt aoe_ss 28"; end
        end
        -- call_action_list,name=movement
        if Player:IsMoving() then
          local ShouldReturn = Movement(); if ShouldReturn then return ShouldReturn; end
        end
    end      

    local function CleaveFF()
        -- frostfire_bolt,if=talent.deaths_chill&buff.icy_veins.remains>9&(buff.deaths_chill.stack<4|buff.deaths_chill.stack=4&!action.frostfire_bolt.in_flight)
        if Bolt:IsReady() and (S.DeathsChill:IsAvailable() and Player:BuffRemains(S.IcyVeinsBuff) > 9 and (Player:BuffStack(S.DeathsChillBuff) < 4 or Player:BuffStack(S.DeathsChillBuff) == 4 and not Bolt:InFlight())) then
          if Cast(Bolt) then return "frostfire_bolt cleave_ff 4"; end
        end
        -- freeze,if=freezable&prev_gcd.1.glacial_spike
        if Pet:IsActive() and S.Freeze:IsReady() and (Freezable() and Player:PrevGCDP(1, S.GlacialSpike)) then
          if Cast(S.Freeze) then return "freeze cleave_ff 6"; end
        end
        -- ice_nova,if=freezable&prev_gcd.1.glacial_spike&remaining_winters_chill=0&debuff.winters_chill.down&!prev_off_gcd.freeze
        if S.IceNova:IsReady() and (Freezable() and Player:PrevGCDP(1, S.GlacialSpike) and RemainingWintersChill == 0 and Target:DebuffDown(S.WintersChillDebuff) and not Player:PrevOffGCDP(1, S.Freeze)) then
          if Cast(S.IceNova) then return "ice_nova cleave_ff 8"; end
        end
        -- flurry,target_if=min:debuff.winters_chill.stack,if=cooldown_react&prev_gcd.1.glacial_spike&!prev_off_gcd.freeze
        if S.Flurry:IsReady() and (Player:PrevGCDP(1, S.GlacialSpike) and not Player:PrevOffGCDP(1, S.Freeze)) then
          if Cast(S.Flurry) then return "flurry cleave_ff 10"; end
        end
        -- flurry,if=cooldown_react&(buff.icicles.react<5|!talent.glacial_spike)&remaining_winters_chill=0&debuff.winters_chill.down&(prev_gcd.1.frostfire_bolt|prev_gcd.1.comet_storm)
        if S.Flurry:IsReady() and ((Icicles < 5 or not S.GlacialSpike:IsAvailable()) and RemainingWintersChill == 0 and Target:DebuffDown(S.WintersChillDebuff) and (Player:PrevGCDP(1, Bolt) or Player:PrevGCDP(1, S.CometStorm))) then
          if Cast(S.Flurry) then return "flurry cleave_ff 12"; end
        end
        -- flurry,if=cooldown_react&(buff.icicles.react<5|!talent.glacial_spike)&buff.excess_fire.up&buff.excess_frost.up
        if S.Flurry:IsReady() and ((Icicles < 5 or not S.GlacialSpike:IsAvailable()) and Player:BuffUp(S.ExcessFireBuff) and Player:BuffUp(S.ExcessFrostBuff)) then
          if Cast(S.Flurry) then return "flurry cleave_ff 14"; end
        end
        -- comet_storm
        if S.CometStorm:IsReady() then
          if Cast(S.CometStorm) then return "comet_storm cleave_ff 16"; end
        end
        -- frozen_orb
        if S.FrozenOrb:IsReady() then
          if Cast(S.FrozenOrb) then return "frozen_orb cleave_ff 18"; end
        end
        -- blizzard,if=buff.freezing_rain.up&talent.ice_caller
        if S.Blizzard:IsReady() and (Player:BuffUp(S.FreezingRainBuff) and S.IceCaller:IsAvailable()) then
          if CastMagic(S.Blizzard, nil, "190356-Magic", magicgroundspell_blizzard) then return "blizzard cleave_ff 20"; end
        end
        -- glacial_spike,if=buff.icicles.react=5
        if S.GlacialSpike:IsReady() and (Icicles == 5) then
          if Cast(S.GlacialSpike) then return "glacial_spike cleave_ff 22"; end
        end
        -- ray_of_frost,target_if=max:debuff.winters_chill.stack,if=remaining_winters_chill=1
        if S.RayofFrost:IsReady() and (RemainingWintersChill == 1) then
          if Cast(S.RayofFrost) then return "ray_of_frost cleave_ff 24"; end
        end
        -- frostfire_bolt,if=buff.frostfire_empowerment.react&!buff.excess_fire.up
        if Bolt:IsReady() and (Player:BuffUp(S.FrostfireEmpowermentBuff) and Player:BuffDown(S.ExcessFireBuff)) then
          if Cast(Bolt) then return "frostfire_bolt cleave_ff 26"; end
        end
        -- ice_lance,target_if=max:debuff.winters_chill.stack,if=buff.fingers_of_frost.react|remaining_winters_chill
        if S.IceLance:IsReady() and (Player:BuffUp(S.FingersofFrostBuff) or RemainingWintersChill > 0) then
          if Cast(S.IceLance) then return "ice_lance cleave_ff 30"; end
        end
        -- frostfire_bolt
        if Bolt:IsReady() then
          if Cast(Bolt) then return "frostfire_bolt cleave_ff 32"; end
        end
        -- call_action_list,name=movement
        if Player:IsMoving() then
          local ShouldReturn = Movement(); if ShouldReturn then return ShouldReturn; end
        end
    end      
      
    local function CleaveSS()
        -- flurry,target_if=min:debuff.winters_chill.stack,if=cooldown_react&prev_gcd.1.glacial_spike&!prev_off_gcd.freeze
        if S.Flurry:IsReady() and (Player:PrevGCDP(1, S.GlacialSpike) and not Player:PrevOffGCDP(1, S.Freeze)) then
          if Cast(S.Flurry) then return "flurry cleave_ss 2"; end
        end
        -- freeze,if=freezable&prev_gcd.1.glacial_spike
        if Pet:IsActive() and S.Freeze:IsReady() and (Freezable() and Player:PrevGCDP(1, S.GlacialSpike)) then
          if Cast(S.Freeze) then return "freeze cleave_ss 4"; end
        end
        -- ice_nova,if=freezable&!prev_off_gcd.freeze&remaining_winters_chill=0&debuff.winters_chill.down&prev_gcd.1.glacial_spike
        if S.IceNova:IsReady() and (Freezable() and not Player:PrevOffGCDP(1, S.Freeze) and RemainingWintersChill == 0 and Target:DebuffDown(S.WintersChillDebuff) and Player:PrevGCDP(1, S.GlacialSpike)) then
          if Cast(S.IceNova) then return "ice_nova cleave_ss 6"; end
        end
        -- flurry,if=cooldown_react&debuff.winters_chill.down&remaining_winters_chill=0&prev_gcd.1.frostbolt
        if S.Flurry:IsReady() and (Target:DebuffDown(S.WintersChillDebuff) and RemainingWintersChill == 0 and Player:PrevGCDP(1, S.Frostbolt)) then
          if Cast(S.Flurry) then return "flurry cleave_ss 8"; end
        end
        -- ice_lance,if=buff.fingers_of_frost.react=2
        if S.IceLance:IsReady() and (Player:BuffStack(S.FingersofFrostBuff) == 2) then
          if Cast(S.IceLance) then return "ice_lance cleave_ss 10"; end
        end
        -- comet_storm,if=remaining_winters_chill&buff.icy_veins.down
        if S.CometStorm:IsReady() and (RemainingWintersChill > 0 and Player:BuffDown(S.IcyVeinsBuff)) then
          if Cast(S.CometStorm) then return "comet_storm cleave_ss 12"; end
        end
        -- frozen_orb,if=cooldown_react&(cooldown.icy_veins.remains>30|buff.icy_veins.react)
        if S.FrozenOrb:IsReady() and (S.IcyVeins:CooldownRemains() > 30 or Player:BuffUp(S.IcyVeinsBuff)) then
           if Cast(S.FrozenOrb) then return "frozen_orb cleave_ss 14"; end
        end
        -- ray_of_frost,target_if=max:debuff.winters_chill.stack,if=prev_gcd.1.flurry&buff.icy_veins.down
        if S.RayofFrost:IsReady() and (Player:PrevGCDP(1, S.Flurry) and Player:BuffDown(S.IcyVeinsBuff)) then
          if Cast(S.RayofFrost) then return "ray_of_frost cleave_ss 16"; end
        end
        -- glacial_spike,if=buff.icicles.react=5&(action.flurry.cooldown_react|remaining_winters_chill|freezable&cooldown.ice_nova.ready)
        if S.GlacialSpike:IsReady() and (Icicles == 5 and (S.Flurry:CooldownUp() or RemainingWintersChill > 0 or Freezable() and S.IceNova:CooldownUp())) then
          if Cast(S.GlacialSpike) then return "glacial_spike cleave_ss 18"; end
        end
        -- shifting_power,if=cooldown.icy_veins.remains>10&!action.flurry.cooldown_react&(fight_remains+15>cooldown.icy_veins.remains)
        if S.ShiftingPower:IsReady() and (S.IcyVeins:CooldownRemains() > 10 and S.Flurry:CooldownDown() and (FightRemains + 15 > S.IcyVeins:CooldownRemains())) then
            if Cast(S.ShiftingPower) then return "shifting_power cleave_ss 20"; end
        end
        -- frostbolt,if=talent.deaths_chill&buff.icy_veins.remains>9&(buff.deaths_chill.stack<6|buff.deaths_chill.stack=6&!action.frostbolt.in_flight)
        if Bolt:IsReady() and (S.DeathsChill:IsAvailable() and Player:BuffRemains(S.IcyVeinsBuff) > 9 and (Player:BuffStack(S.DeathsChillBuff) < 6 or Player:BuffStack(S.DeathsChillBuff) == 6 and not Bolt:InFlight())) then
          if Cast(Bolt) then return "frostbolt cleave_ss 24"; end
        end
        -- blizzard,if=talent.freezing_rain&talent.ice_caller
        if S.Blizzard:IsReady() and (S.FreezingRain:IsAvailable() and S.IceCaller:IsAvailable()) then
          if CastMagic(S.Blizzard, nil, "190356-Magic", magicgroundspell_blizzard) then return "blizzard cleave_ss 26"; end
        end
        -- ice_lance,target_if=max:debuff.winters_chill.stack,if=buff.fingers_of_frost.react|remaining_winters_chill
        if S.IceLance:IsReady() and (Player:BuffUp(S.FingersofFrostBuff) or RemainingWintersChill > 0) then
          if Cast(S.IceLance) then return "ice_lance cleave_ss 28"; end
        end
        -- frostbolt
        if Bolt:IsReady() then
          if Cast(Bolt) then return "frostbolt cleave_ss 30"; end
        end
        -- call_action_list,name=movement
        if Player:IsMoving() then
          local ShouldReturn = Movement(); if ShouldReturn then return ShouldReturn; end
        end
    end      

    local function STFF()
        -- flurry,if=cooldown_react&(buff.icicles.react<5|!talent.glacial_spike)&remaining_winters_chill=0&debuff.winters_chill.down&(prev_gcd.1.glacial_spike|prev_gcd.1.frostfire_bolt|prev_gcd.1.comet_storm)
        if S.Flurry:IsReady() and ((Icicles < 5 or not S.GlacialSpike:IsAvailable()) and RemainingWintersChill == 0 and Target:DebuffDown(S.WintersChillDebuff) and (Player:PrevGCDP(1, S.GlacialSpike) or Player:PrevGCDP(1, Bolt) or Player:PrevGCDP(1, S.CometStorm))) then
          if Cast(S.Flurry) then return "flurry st_ff 2"; end
        end
        -- flurry,if=cooldown_react&(buff.icicles.react<5|!talent.glacial_spike)&buff.excess_fire.up&buff.excess_frost.up
        if S.Flurry:IsReady() and ((Icicles < 5 or not S.GlacialSpike:IsAvailable()) and Player:BuffUp(S.ExcessFireBuff) and Player:BuffUp(S.ExcessFrostBuff)) then
          if Cast(S.Flurry) then return "flurry st_ff 4"; end
        end
        -- comet_storm
        if S.CometStorm:IsReady() then
          if Cast(S.CometStorm) then return "comet_storm st_ff 6"; end
        end
        -- glacial_spike,if=buff.icicles.react=5
        if S.GlacialSpike:IsReady() and (Icicles == 5) then
          if Cast(S.GlacialSpike) then return "glacial_spike st_ff 8"; end
        end
        -- ray_of_frost,if=remaining_winters_chill=1
        if S.RayofFrost:IsReady() and (RemainingWintersChill == 1) then
          if Cast(S.RayofFrost) then return "ray_of_frost st_ff 10"; end
        end
        -- frozen_orb
        if S.FrozenOrb:IsReady() then
          if Cast(S.FrozenOrb) then return "frozen_orb st_ff 12"; end
        end
        -- ice_lance,if=buff.fingers_of_frost.react|remaining_winters_chill
        if S.IceLance:IsReady() and (Player:BuffUp(S.FingersofFrostBuff) or RemainingWintersChill > 0) then
          if Cast(S.IceLance) then return "ice_lance st_ff 16"; end
        end
        -- frostfire_bolt
        if Bolt:IsReady() then
          if Cast(Bolt) then return "frostfire_bolt st_ff 18"; end
        end
        -- call_action_list,name=movement
        if Player:IsMoving() then
          local ShouldReturn = Movement(); if ShouldReturn then return ShouldReturn; end
        end
    end      

    local function STSS()
        -- flurry,if=cooldown_react&debuff.winters_chill.down&remaining_winters_chill=0&(prev_gcd.1.glacial_spike|prev_gcd.1.frostbolt)
        if S.Flurry:IsReady() and (Target:DebuffDown(S.WintersChillDebuff) and RemainingWintersChill == 0 and (Player:PrevGCDP(1, S.GlacialSpike) or Player:PrevGCDP(1, S.Frostbolt))) then
          if Cast(S.Flurry) then return "flurry st_ss 2"; end
        end
        -- comet_storm,if=remaining_winters_chill&buff.icy_veins.down
        if S.CometStorm:IsReady() and (RemainingWintersChill > 0 and Player:BuffDown(S.IcyVeinsBuff)) then
          if Cast(S.CometStorm) then return "comet_storm st_ss 4"; end
        end
        -- frozen_orb,if=cooldown_react&(cooldown.icy_veins.remains>30|buff.icy_veins.react)
        if S.FrozenOrb:IsReady() and (S.IcyVeins:CooldownRemains() > 30 or Player:BuffUp(S.IcyVeinsBuff)) then
          if Cast(S.FrozenOrb) then return "frozen_orb st_ss 6"; end
        end
        -- ray_of_frost,if=prev_gcd.1.flurry
        if S.RayofFrost:IsReady() and (Player:PrevGCDP(1, S.Flurry)) then
          if Cast(S.RayofFrost) then return "ray_of_frost st_ss 8"; end
        end
        -- glacial_spike,if=buff.icicles.react=5&(action.flurry.cooldown_react|remaining_winters_chill)
        if S.GlacialSpike:IsReady() and (Icicles == 5 and (S.Flurry:CooldownUp() or RemainingWintersChill > 0)) then
          if Cast(S.GlacialSpike) then return "glacial_spike st_ss 10"; end
        end
        -- ice_lance,if=buff.fingers_of_frost.react|remaining_winters_chill
        if S.IceLance:IsReady() and (Player:BuffUp(S.FingersofFrostBuff) or RemainingWintersChill > 0) then
          if Cast(S.IceLance) then return "ice_lance st_ss 14"; end
        end
        -- frostbolt
        if Bolt:IsReady() then
          if Cast(Bolt) then return "frostbolt st_ss 16"; end
        end
        -- call_action_list,name=movement
        if Player:IsMoving() then
          local ShouldReturn = Movement(); if ShouldReturn then return ShouldReturn; end
        end
    end     

    --- ======= ACTION LISTS =======
    local function APL()
        
        -- if Target:Exists() then
        --     Target:IsInRange(12)
        -- end

        -- Check our IF status
        -- Note: Not referenced in the current APL, but saving for potential use later
        --Mage.IFTracker()
        
        -- MainAddon.UpdateVariable("Freezable", Freezable())
        -- MainAddon.UpdateVariable("RemainingWintersChill", RemainingWintersChill)
        -- MainAddon.UpdateVariable("Icicles", Icicles)

        if M.TargetIsValid() or Player:AffectingCombat() then
            -- Calculate fight_remains
            BossFightRemains = HL.BossFightRemains()
            FightRemains = BossFightRemains
            if FightRemains == 11111 then
            FightRemains = HL.FightRemains(Enemies40y, false)
            end

            -- Calculate remaining_winters_chill and icicles, as it's used in many lines
            if AoEON() and EnemiesCount16ySplash > 1 then
                RemainingWintersChill = CalculateWintersChill(Enemies40y)
            else
                RemainingWintersChill = Target:DebuffStack(S.WintersChillDebuff)
            end
            Icicles = Player:BuffStackP(S.IciclesBuff)

            -- Calculate GCDMax
            GCDMax = Player:GCD() + 0.25
        end

        -- Ice Floes Handling
        ifSetting = GetSetting('if', {})

        -- Ice Floes for Ray of Frost
        if ifSetting[3] and Player:IsChanneling(S.RayofFrost) and Player:BuffRemains(S.IceFloes) < Player:ChannelRemains() then
            if S.IceFloes:IsReady(Player, nil, true, true) then
                if Cast(S.IceFloes, true, nil, nil, nil, true) then
                    return "Casting Ice Floes during Ray of Frost channel";
                end
            end
        end

        -- Ice Floes for Glacial Spike
        if ifSetting[2] and Player:IsCasting(S.GlacialSpike) and (S.IceFloes:ChargesFractional() >= 1 or (S.ShiftingPower:CooldownRemains() >= 20 and S.RayofFrost:CooldownRemains() >= 20)) and Player:BuffRemains(S.IceFloes) < Player:CastRemains() then
            if S.IceFloes:IsReady(Player, nil, true, true) then
                if Cast(S.IceFloes, true, nil, nil, nil, true) then
                    return "Casting Ice Floes during Glacial Spike cast";
                end
            end
        end

        -- Ice Floes for Shifting Power
        if ifSetting[1] and Player:IsChanneling(S.ShiftingPower) and Player:BuffRemains(S.IceFloes) < Player:ChannelRemains() then
            if S.IceFloes:IsReady(Player, nil, true, true) then
                if Cast(S.IceFloes, true, nil, nil, nil, true) then
                    return "Casting Ice Floes during Shifting Power channel";
                end
            end
        end

        if MainAddon.TargetIsValid() then
            -- Trinkets
            local shouldReturn = MainAddon.TrinketDPS()
            if shouldReturn then
                return shouldReturn
            end  

            -- call precombat
            if not Player:AffectingCombat() then
                ShouldReturn = Precombat();
                if ShouldReturn then
                    return ShouldReturn;
                end
            end
            -- Force Flurry in opener
            if S.Flurry:IsReady() and (HL.CombatTime() < 5 and (Player:IsCasting(Bolt) or Player:PrevGCDP(1, Bolt))) then
                if Cast(S.Flurry) then return "flurry opener"; end
            end
            -- call_action_list,name=cds
            do
                ShouldReturn = CDs(); if ShouldReturn then return ShouldReturn; end
            end
            -- run_action_list,name=aoe_ff,if=talent.frostfire_bolt&active_enemies>=3
            if AoEON() and (S.FrostfireBolt:IsAvailable() and EnemiesCount16ySplash >= 3) then
                ShouldReturn = AoEFF(); if ShouldReturn then return ShouldReturn; end
                if Cast(S.Pool) then return "Pool for AoeFF()"; end
            end
            -- run_action_list,name=aoe_ss,if=active_enemies>=3
            if AoEON() and (EnemiesCount16ySplash >= 3) then
                ShouldReturn = AoESS(); if ShouldReturn then return ShouldReturn; end
                if Cast(S.Pool) then return "Pool for AoeSS()"; end
            end
            -- run_action_list,name=cleave_ff,if=talent.frostfire_bolt&active_enemies=2
            if AoEON() and (S.FrostfireBolt:IsAvailable() and EnemiesCount16ySplash == 2) then
                ShouldReturn = CleaveFF(); if ShouldReturn then return ShouldReturn; end
                if Cast(S.Pool) then return "pool for CleaveFF()"; end
            end
            -- run_action_list,name=cleave_ss,if=active_enemies=2
            if AoEON() and (EnemiesCount16ySplash == 2) then
                ShouldReturn = CleaveSS(); if ShouldReturn then return ShouldReturn; end
                if Cast(S.Pool) then return "pool for CleaveSS()"; end
            end
            -- run_action_list,name=st_ff,if=talent.frostfire_bolt
            if S.FrostfireBolt:IsAvailable() then
                ShouldReturn = STFF(); if ShouldReturn then return ShouldReturn; end
                if Cast(S.Pool) then return "pool for STFF()"; end
            end
            -- run_action_list,name=st_ss
                ShouldReturn = STSS(); if ShouldReturn then return ShouldReturn; end
                if Cast(S.Pool) then return "pool for STSS()"; end
            end
        end

    local function Main()
        magicgroundspell_blizzard = GetSetting('magicgroundspell_blizzard', false)
        -- Update our enemy tables
        if AoEON() then
            EnemiesCount8ySplash = Target:GetEnemiesInSplashRangeCount(8)
            EnemiesCount16ySplash = Target:GetEnemiesInSplashRangeCount(16)
            EnemiesCount10y = #Player:GetEnemiesInRange(10)
            Enemies40y = Player:GetEnemiesInRangeCombat(40)
        else            
            EnemiesCount8ySplash = 1
            EnemiesCount16ySplash = 1
            EnemiesCount10y = 1
            Enemies40y = {Target}
        end
        EnemiesCount40y = #Enemies40y

        if AoEON() and EnemiesCount8ySplash < 3 then
          EnemiesCount8ySplash = EnemiesCount40y
          EnemiesCount16ySplash = EnemiesCount40y
        end

         -- Force AoE toggle off
         if M.Toggle:GetToggle('ForceAoE') then
            if GetSetting('forceAoE_off_check', true) then
                if not ForceAoE_status then
                    ForceAoE_timestamp = GetTime()
                    ForceAoE_status = true
                end

                if GetTime() - ForceAoE_timestamp > GetSetting('forceAoE_off_spin', 6) then
                    MainAddon.Toggle:SetToggle("ForceAoE", false)
                    ForceAoE_status = false
                    ForceAoE_timestamp = GetTime()
                end
            end

            EnemiesCount8ySplash = 3
            EnemiesCount16ySplash = 3
        end

        -- Defensives
        ShouldReturn = Defensives();
        if ShouldReturn then
            return ShouldReturn;
        end

        -- Utilities
        ShouldReturn = Utilities();
        if ShouldReturn then
            return ShouldReturn;
        end


        MOCheck = (MouseOver:IsEnemy() or MouseOver:IsATank() or MouseOver:IsAMelee())
        return APL()
    end

    local function Init()
        S.WintersChillDebuff:RegisterAuraTracking()
    end
    M.SetAPL(64, Main, Init)

    local OldIsMoving
    OldIsMoving = HL.AddCoreOverride("Player.IsMoving",
        function(self)
            if MainAddon.PlayerSpecID() == 64 then
                if _G['IsFalling']() then
                    return true, "Falling/Jumping"
                end
            end
            local BaseCheck, Reason = OldIsMoving(self)
            return BaseCheck, Reason
        end,
    64);

    local FrostOldSpellIsReady
    FrostOldSpellIsReady = HL.AddCoreOverride("Spell.IsReady",
          function(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
            if MainAddon.PlayerSpecID() == 64 then
                if GetSetting('MOOption', false) then
                    if self == S.Blizzard then
                        if not MOCheck or HL.CombatTime() < 1 then
                            return false, "MOCheck is false"
                        end
                    end
                end
            end
            local BaseCheck = FrostOldSpellIsReady(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
            return BaseCheck
        end
  , 64)


    local OldIsCastable
    OldIsCastable = HL.AddCoreOverride("Spell.IsCastable",
            function(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                if MainAddon.PlayerSpecID() == 64 then
                    
                    local DPSMovingValue_check = GetSetting('DPSMovingValue_check', false)
                    local DPSMovingValue_spin = GetSetting('DPSMovingValue_spin', 1.5) or 1.5
                    
                    ignoreMovement = false

                    -- Allow casting while moving if Ice Floes is active
                    if Player:BuffUp(S.IceFloes) then
                        ignoreMovement = true
                    end

                    -- Ice Floes settings
                    if self == S.ShiftingPower and Player:BuffUp(S.IceFloes) and ifSetting[1] then
                        ignoreMovement = true
                    end

                    if self == S.GlacialSpike and Player:BuffUp(S.IceFloes) and ifSetting[2] then
                        ignoreMovement = true
                    end

                    if self == S.RayofFrost and Player:BuffUp(S.IceFloes) and ifSetting[3] then
                        ignoreMovement = true
                    end

                    if Player:BuffUp(S.IceFloes) then
                        ignoreMovement = true
                    end

                    if self == S.FrozenOrb then
                        if DPSMovingValue_check and Player:IsStandingStillFor() <= DPSMovingValue_spin then
                            return false, 'Is Not Standing Still'
                        end                        
                    end

                    if self == S.IcyVeins then
                        if DPSMovingValue_check and Player:IsStandingStillFor() <= DPSMovingValue_spin then
                            return false, 'Is Not Standing Still'
                        end                        
                    end

                    if self == S.ShiftingPower then
                        if DPSMovingValue_check and Player:IsStandingStillFor() <= DPSMovingValue_spin and S.IceFloes:IsAvailable() and Player:BuffRemains(S.IceFloes) < 4 then
                            return false, 'Is Not Standing Still'
                        end    
                    end

                    if self == S.CometStorm then
                        if DPSMovingValue_check and Player:IsStandingStillFor() <= DPSMovingValue_spin then
                            return false, 'Is Not Standing Still'                     
                        end
                    end

                    if self == S.GlacialSpike then
                        if Player:IsMoving() and not ignoreMovement then
                            return false, 'Cannot cast Glacial Spike while moving'
                        end

                        if self:WillBeInterruptByMechanic() then
                            return false, "Interrupted by Mechanic"
                        end
                        
                        return self:IsLearned() and not Player:IsCasting(self) and (Player:BuffUp(S.GlacialSpikeBuff) or (Icicles == 5))
                    end

                    -- if self == S.FrozenOrb then
                    --     if IsMouseButtonDown(LeftButton) then
                    --         return false, 'Button 1 is pressed'
                    --     end
                    --     if IsMouseButtonDown(RightButton) then
                    --         return false, 'Button 2 is pressed'
                    --     end
                    -- end

                    if self:CastTime() > 0 and Player:IsMoving() then
                        if self == S.Blizzard then
                            if Player:BuffUp(S.FreezingRain) then
                                ignoreMovement = true
                            else
                                return false
                            end
                        end
                    end
                    
                    if self == S.FrozenOrb then
                        local config = MainAddon.Config.GetSetting('CoreUI', 'groundspells_pausehotkey', {"b1", "b2"})
                        if config["b1"] and IsMouseButtonDown("LeftButton") then
                            return false, 'Button 1 is pressed'
                        end
                        if config["b2"] and IsMouseButtonDown("RightButton") then
                            return false, 'Button 2 is pressed'
                        end
                        if config["b3"] and IsMouseButtonDown("MiddleButton") then
                            return false, 'Button 2 is pressed'
                        end
                        if config["b4"] and IsMouseButtonDown("Button4") then
                            return false, 'Button 2 is pressed'
                        end
                        if config["b5"] and IsMouseButtonDown("Button5") then
                            return false, 'Button 2 is pressed'
                        end
                    end
                    
                    if self == S.GreaterInvisibility or self == S.Invisibility then
                        if not MainAddon.safeVanish() then
                            return false, "Not safe to vanish"
                        end
                    end
                end
                local BaseCheck, Reason = OldIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                return BaseCheck, Reason
        end,
    64);

    local OldIsCastableQueue
    OldIsCastableQueue = HL.AddCoreOverride("Spell.IsCastableQueue",
            function(self, ignoreMovement)
            if MainAddon.PlayerSpecID() == 64 then
                if Player:BuffUp(S.IceFloes) then
                    ignoreMovement = true
                end
            end
            local BaseCheck, Reason = OldIsCastableQueue(self, ignoreMovement)
            return BaseCheck, Reason
        end,
    64);

    --FROST OVERRIDES
    local FrostOldSpellCooldownRemains
    FrostOldSpellCooldownRemains = HL.AddCoreOverride("Spell.CooldownRemains",
            function(self, BypassRecovery, BypassCD)
                if MainAddon.PlayerSpecID() == 64 then
                    if self == S.Blizzard and Player:IsCasting(self) then
                        return 8
                    else
                        return FrostOldSpellCooldownRemains(self, BypassRecovery, BypassCD)
                    end
                end
                return FrostOldSpellCooldownRemains(self, BypassRecovery, BypassCD)
            end
    , 64)

    local FrostOldPlayerBuffStack
    FrostOldPlayerBuffStack = HL.AddCoreOverride("Player.BuffStackP",
            function(self, ThisSpell, AnyCaster, Offset)
                local BaseCheck = Player:BuffStack(ThisSpell)
                if MainAddon.PlayerSpecID() == 64 then
                    if ThisSpell == S.IciclesBuff then
                        return self:IsCasting(S.GlacialSpike) and 0 or math.min(BaseCheck + (self:IsCasting(Bolt) and 1 or 0), 5)
                    elseif ThisSpell == S.GlacialSpikeBuff then
                        return self:IsCasting(S.GlacialSpike) and 0 or BaseCheck
                    elseif ThisSpell == S.FingersofFrostBuff then
                        if S.IceLance:InFlight() then
                            if BaseCheck == 0 then
                                return 0
                            else
                                return BaseCheck - 1
                            end
                        else
                            return BaseCheck
                        end
                    else
                        return BaseCheck
                    end
                end
                return BaseCheck
            end
    , 64)

    local FrostOldPlayerBuffUp
    FrostOldPlayerBuffUp = HL.AddCoreOverride("Player.BuffUpP",
            function(self, ThisSpell, AnyCaster, Offset)
                local BaseCheck = Player:BuffUp(ThisSpell)
                if MainAddon.PlayerSpecID() == 64 then
                    if ThisSpell == S.FingersofFrostBuff then
                        if S.IceLance:InFlight() then
                            return Player:BuffStack(ThisSpell) >= 1
                        else
                            return BaseCheck
                        end
                    else
                        return BaseCheck
                    end
                end
                return BaseCheck
            end
    , 64)

    local FrostOldPlayerBuffDown
    FrostOldPlayerBuffDown = HL.AddCoreOverride("Player.BuffDownP",
            function(self, ThisSpell, AnyCaster, Offset)
                local BaseCheck = Player:BuffDown(ThisSpell)
                if MainAddon.PlayerSpecID() == 64 then
                    if ThisSpell == S.FingersofFrostBuff then
                        if S.IceLance:InFlight() then
                            return Player:BuffStack(ThisSpell) == 0
                        else
                            return BaseCheck
                        end
                    else
                        return BaseCheck
                    end
                end
                return BaseCheck
            end
    , 64)

    local FrostOldTargetDebuffStack
    FrostOldTargetDebuffStack = HL.AddCoreOverride("Target.DebuffStack",
            function(self, ThisSpell, AnyCaster, Offset)
                local BaseCheck = FrostOldTargetDebuffStack(self, ThisSpell, AnyCaster, Offset)
                if MainAddon.PlayerSpecID() == 64 then
                    if ThisSpell == S.WintersChillDebuff then
                        if S.Flurry:InFlight() then
                            return 2
                        elseif S.IceLance:InFlight() then
                            if BaseCheck == 0 then
                                return 0
                            else
                                return BaseCheck - 1
                            end
                        -- Custom addition
                        elseif S.GlacialSpike:InFlight() or Player:IsCasting(S.GlacialSpike) then
                            if BaseCheck == 0 then
                                return 0
                            else
                                return BaseCheck - 1
                            end
                        -- Custom addition
                        elseif Bolt:InFlight() or Player:IsCasting(Bolt) then
                            if BaseCheck == 0 then
                                return 0
                            else
                                return BaseCheck - 1
                            end
                        else
                            return BaseCheck
                        end
                    else
                        return BaseCheck
                    end
                end
                return BaseCheck
            end
    , 64)

    local FrostOldTargetDebuffRemains
    FrostOldTargetDebuffRemains = HL.AddCoreOverride("Target.DebuffRemains",
            function(self, ThisSpell, AnyCaster, Offset)
                local BaseCheck = FrostOldTargetDebuffRemains(self, ThisSpell, AnyCaster, Offset)
                if MainAddon.PlayerSpecID() == 64 then
                    if ThisSpell == S.WintersChillDebuff then
                        return S.Flurry:InFlight() and 6 or BaseCheck
                    else
                        return BaseCheck
                    end
                end
                return BaseCheck
            end
    , 64)

    local FrostOldPlayerAffectingCombat
    FrostOldPlayerAffectingCombat = HL.AddCoreOverride("Player.AffectingCombat",
        function(self)
            return Bolt:InFlight() or FrostOldPlayerAffectingCombat(self)
        end
    , 64)
end