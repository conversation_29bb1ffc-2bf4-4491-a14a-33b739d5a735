function A_1468(...)    
    ---@class MainAddon
    local MainAddon = MainAddon
    local M = MainAddon -- Short alias for MainAddon
    ---@class HealingEngine
    local HealingEngine = MainAddon.HealingEngine
    ---@class HeroCache
    local Cache = HeroCache
    -- HeroLib
    local HL = HeroLibEx
    ---@class Unit
    local Unit = HL.Unit
    ---@class Unit
    local Player = Unit.Player
    ---@class Unit
    local Target = Unit.Target
    ---@class Unit
    local MouseOver = Unit.MouseOver
    ---@class Unit
    local Focus = Unit.Focus
    ---@class Spell
    local Spell = HL.Spell
    ---@class Item
    local Item = HL.Item
    -- HeroRotation
    local AoEON = M.AoEON
    local Cast = M.Cast
    local CastAlly = M.CastAlly
    local CastCycleAlly = M.CastCycleAlly
    local CastTargetIfAlly = M.CastTargetIfAlly
    -- Lua
    local strsplit = _G['strsplit']
    local SetCVar = _G.SetCVar
    local GetPowerRegenForPowerType = _G.GetPowerRegenForPowerType
    local EssencePowerType = _G.Enum.PowerType.Essence
    local GetTime = _G.GetTime
    local wipe = _G.wipe
    local C_Timer = _G.C_Timer
    
    -- =================================
    -- SECTION: SPELL AND ITEM DEFINITIONS
    -- =================================
    
    -- Define spell (S) and item (I) arrays for Preservation Evoker
    local S = Spell.Evoker.Preservation
    local I = Item.Evoker.Preservation

    -- Table to exclude specific trinkets from On Use function
    local OnUseExcludes = {
        I.Dreambinder:ID()
    }

    -- ==============================
    -- SECTION: TOGGLE CONFIGURATIONS
    -- ==============================
    
    -- Toggle for spreading Echo buff
    MainAddon.Toggle.Special["SpreadEcho"] = {
        Icon = MainAddon.GetTexture(S.Echo),
        Name = "Spread Echo",
        Description = "Spread Echo.",
        Spec = 1468
    }
    
    -- Toggle for forcing DPS mode
    MainAddon.Toggle.Special["ForceDPS"] = {
        Icon = MainAddon.GetTexture(S.Disintegrate),
        Name = "Force DPS",
        Description = "This toggle will force DPS.",
        Spec = 1468
    }

    -- ====================================
    -- SECTION: GUI SETTINGS CONFIGURATION
    -- ====================================
    
    --- GUI SETTINGS
    local GetSetting = MainAddon.Config.GetClassSetting
    local Config_Key = MainAddon.GetClassVariableName()
    local Config_Color = '33937F'
    local Config_Table = {
        key = Config_Key,
        title = 'Evoker - Preservation',
        subtitle = '?? ' .. MainAddon.Version,
        width = 600,
        height = 700,
        profiles = true,
        config = {
            -- UI layout elements
            { type = 'header', text = '', size = 24, align = 'Center', color = Config_Color },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
            
            -- Single Target Healing section
            { type = 'header', text = 'Single Target Healing', color = Config_Color },
            { type = 'spacer' },
            
            -- Living Flame settings
            { type = 'header', text = 'Living Flame', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'LivingFlameHP', icon = S.LivingFlame:ID(), min = 1, max = 100, default = 85 },
            { type = 'spacer' },
            
            -- Reversion settings
            { type = 'header', text = 'Reversion', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'ReversionHP', icon = S.Reversion:ID(), min = 1, max = 100, default = 85 },
            { type = 'spacer' },
            
            -- Engulf settings
            { type = 'header', text = 'Engulf', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = '1st Charge Threshold (%)', key = 'EngulfHP', icon = S.Engulf:ID(), min = 1, max = 100, default = 80 },
            { type = 'spinner', text = '2nd Charge Threshold (%)', key = 'EngulfHP2', icon = S.Engulf:ID(), min = 1, max = 100, default = 70 },
            { type = 'spacer' },
            
            -- Echo settings
            { type = 'header', text = 'Echo', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'EchoHP', icon = S.Echo:ID(), min = 1, max = 100, default = 85},
            { type = 'spacer' },
            
            -- Temporal Anomaly settings
            { type = 'header', text = 'Temporal Anomaly', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'TemporalAnomalyHP', icon = S.TemporalAnomaly:ID(), min = 1, max = 100, default = 85 },
            { type = 'spacer' },
            
            -- Verdant Embrace settings
            { type = 'header', text = 'Verdant Embrace', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'VerdantEmbraceHP', icon = S.VerdantEmbrace:ID(), min = 1, max = 100, default = 75 },
            { type = 'dropdown', text = 'Target Selection', key = 've_targets', icon = S.VerdantEmbrace:ID(),
              multiselect = true,
              list = {
                { text = 'Self', key = 'self' },
                { text = 'Healer', key = 'healer' },
                { text = 'Tank', key = 'tank' },
                { text = 'DPS', key = 'dps' },
                { text = 'NPC', key = 'npc' },
              },
              default = {
                'tank', 
                'dps', 
                'healer',
                'self',
                'npc'
              },
            },
            { type = 'spacer' },
            
            -- Spiritbloom settings
            { type = 'header', text = 'Spiritbloom', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Tank Health Threshold (%)', key = 'SpiritbloomTankHP', icon = S.Spiritbloom:ID(), min = 1, max = 100, default = 75 },
            { type = 'spinner', text = 'Group Member Threshold (%)', key = 'SpiritbloomHP', icon = S.Spiritbloom:ID(), min = 1, max = 100, default = 60 },
            { type = 'spacer' },
            
            -- Emerald Blossom settings
            { type = 'header', text = 'Emerald Blossom', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'EmeraldBlossomHP', icon = S.EmeraldBlossom:ID(), min = 1, max = 100, default = 60 },
            { type = 'dropdown', text = 'Target Selection', key = 'eblossom_targets', icon = S.EmeraldBlossom:ID(),
              list = {
                  { text = 'Tanks', key = 'tanks' },
                  { text = 'Everyone', key = 'everyone' },
              },
              default = 'everyone'
            },
            { type = 'spacer' },
            
            -- Essence Burst + Emerald Blossom settings
            { type = 'header', text = 'Essence Burst + Emerald Blossom', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = '1 Stack Threshold (%)', key = 'burst1_EmeraldBlossomHP', icon = S.EmeraldBlossom:ID(), min = 1, max = 100, default = 80 },
            { type = 'spinner', text = '2 Stacks Threshold (%)', key = 'burst2_EmeraldBlossomHP', icon = S.EmeraldBlossom:ID(), min = 1, max = 100, default = 95 },
            { type = 'dropdown', text = 'Target Selection', key = 'burst_eblossom_targets', icon = S.EmeraldBlossom:ID(),
              list = {
                  { text = 'Tanks', key = 'tanks' },
                  { text = 'Everyone', key = 'everyone' },
              },
              default = 'everyone'
            },
            { type = 'spacer' },
            
            -- Time Dilation settings
            { type = 'header', text = 'Time Dilation', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Tank Health Threshold (%)', key = 'TimeDilationTankHP', icon = S.TimeDilation:ID(), min = 1, max = 100, default = 65 },
            { type = 'spinner', text = 'Group Member Threshold (%)', key = 'TimeDilationHP', icon = S.TimeDilation:ID(), min = 1, max = 100, default = 40 },
            { type = 'spacer' },
            
            -- Group Healing section
            { type = 'header', text = 'Group Healing', color = Config_Color },
            { type = 'spacer' },
            
            -- Dream Breath settings
            { type = 'header', text = 'Dream Breath', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Level 1 Group Health (%)', key = 'DreamBreathHP', icon = S.DreamBreath:ID(), min = 1, max = 100, default = 70 },
            { type = 'spinner', text = 'Level 2 Group Health (%)', key = 'DreamBreathHP2', icon = S.DreamBreath:ID(), min = 1, max = 100, default = 50 },
            { type = 'spinner', text = 'Level 3 Group Health (%)', key = 'DreamBreathHP3', icon = S.DreamBreath:ID(), min = 1, max = 100, default = 40 },
            { type = 'spinner', text = 'Level 4 Group Health (%)', key = 'DreamBreathHP4', icon = S.DreamBreath:ID(), min = 1, max = 100, default = 30 },
            { type = 'spacer' },
            
            -- Rewind settings
            { type = 'header', text = 'Rewind', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Group Health Threshold (%)', key = 'RewindHP', icon = S.Rewind:ID(), min = 1, max = 100, default = 60 },
            { type = 'spacer' },
            
            -- Emerald Communion settings
            { type = 'header', text = 'Emerald Communion', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Group Health Threshold (%)', key = 'EmeraldCommunionHP', icon = S.EmeraldCommunion:ID(), min = 1, max = 100, default = 40 },
            { type = 'spacer' },
            
            -- Stasis settings
            { type = 'header', text = 'Stasis', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Group Health Threshold (%)', key = 'StasisHP', icon = S.Stasis:ID(), min = 1, max = 100, default = 45 },
            { type = 'spacer' },
            
            -- Zephyr settings
            { type = 'header', text = 'Zephyr', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Group Health Threshold (%)', key = 'ZephyrHP', icon = S.Zephyr:ID(), min = 1, max = 100, default = 45 },
            { type = 'spinner', text = 'Smart Use Above Key Level', key = 'smart_feint_above_key_level', icon = S.Zephyr:ID(), min = 1, max = 40, default = 2},
            { type = 'spacer' },
            
            -- Tip The Scales settings
            { type = 'header', text = 'Tip The Scales', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Group Health Threshold (%)', key = 'TipTheScalesHP', icon = S.TipTheScales:ID(), min = 1, max = 100, default = 50 },
            { type = 'spacer' },
            
            -- Defensive Abilities section
            { type = 'header', text = 'Defensive Abilities', color = Config_Color },
            { type = 'spacer' },
            
            -- Obsidian Scales settings
            { type = 'header', text = 'Obsidian Scales', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkspin', text = 'Health Threshold (%)', icon = S.ObsidianScales:ID(), key = 'obscales', min = 1, max = 100, default_spin = 35, default_check = true },
            { type = 'spacer' },
            
            -- Renewing Blaze settings
            { type = 'header', text = 'Renewing Blaze', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkbox', text = 'Smart Usage', icon = S.RenewingBlaze:ID(), key = 'renewblazesmart', default = true },
            { type = 'spacer' },
            
            -- DPS Options section
            { type = 'header', text = 'DPS Options', color = Config_Color },
            { type = 'spacer' },
            
            -- Mana Management settings
            { type = 'header', text = 'Mana Management', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Stop DPS Below Mana (%)', key = 'stopdps', min = 1, max = 100, default = 15 },
            { type = 'spacer' },
            
            -- Disintegrate settings
            { type = 'header', text = 'Disintegrate', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkbox', text = 'Use with Essence Burst', icon = S.Disintegrate:ID(), key = 'disintegrate_essenceburst', default = false },
            { type = 'spacer' },
            
            -- Fire Breath settings
            { type = 'header', text = 'Fire Breath', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkspin', text = 'Force Empower Level', icon = S.FireBreath:ID(), key = 'fb_emp_level', min = 1, max = 4, default_spin = 4, default_check = false },
            { type = 'spacer' },
            
            -- Movement & Utility section
            { type = 'header', text = 'Movement & Utility', color = Config_Color },
            { type = 'spacer' },
            
            -- Hover settings
            { type = 'header', text = 'Hover', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkspin', text = 'Movement Threshold (seconds)', icon = S.Hover:ID(), key = 'hover', min = 0, max = 15, default_spin = 2, default_check = false },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'ruler' },
            { type = 'spacer' },
        }
    }
    
    -- Build additional UI components
    Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
    Config_Table.config = MainAddon.BuildHealingTrinketUI(Config_Table.config, Config_Color)
    Config_Table.config = MainAddon.BuildCombatManaPotionUI(Config_Table.config, "Preservation", Config_Color)
    
    -- Set the configuration for Preservation Evoker (spec ID 1468)
    M.SetConfig(1468, Config_Table)

    -- =================================
    -- SECTION: CUSTOM VARIABLES AND STATE
    -- =================================
    
    -- Custom spell tracking for empower levels
    local CastOnSpell = Spell(1)
    CastOnSpell.EmpowerLevel = 0
    
    -- Settings and variables storage
    local Settings = {}
    local Vars = {}
    local Tanks, Healers, Members, Damagers, Melees, TargetIfAlly
    
    -- Temporary blacklist for Emerald Blossom to prevent duplicate casts
    local TempBlackListEmeraldBlossom = {}
    
    -- Combat monitoring variables
    Vars['CombatMonitor_TimeStamp'] = GetTime()
    Vars['CombatMonitor_State'] = false
    
    -- Spell casting flags
    Vars['ShouldCastDreamBreath'] = true
    Vars['ShouldCastSpiritbloom'] = true
    Vars['ShouldCastFireBreath'] = true
    
    -- Default settings
    Settings['disintegrate_essenceburst'] = false

    -- ==============================
    -- SECTION: UTILITY FUNCTIONS
    -- ==============================
    
    --- Combat monitoring function to detect combat state through tank activity
    local function combatMonitor()
        -- Cache the result for 1 second to avoid frequent checks
        if GetTime() - Vars['CombatMonitor_TimeStamp'] < 1 then
            return Vars['CombatMonitor_State']
        end
        
        -- Check if any tank is in combat
        if Tanks then
            ---@param TankUnit Unit
            for _, TankUnit in pairs(Tanks) do
                if TankUnit:AffectingCombat() then
                    Vars['CombatMonitor_TimeStamp'] = GetTime()
                    Vars['CombatMonitor_State'] = true
                    return true
                end
            end
        end

        -- Update state and return false if no tanks in combat
        Vars['CombatMonitor_TimeStamp'] = GetTime()
        Vars['CombatMonitor_State'] = false
        return false
    end

    --- Updates global variables and settings
    local function UpdateVars()
        -- Fetch group composition from HealingEngine
        Tanks, Healers, Members, Damagers, Melees, _, _, _, TargetIfAlly = HealingEngine:Fetch()

        
        -- Update combat and health metrics
        Vars['AverageHP'] = HealingEngine:MedianHP(true)
        Vars['IsInCombat'] = Player:AffectingCombat()
        
        -- Update spell casting flags based on current empower state
        Vars['ShouldCastDreamBreath'] = CastOnSpell:ID() == 1 or CastOnSpell == S.DreamBreath
        Vars['ShouldCastSpiritbloom'] = CastOnSpell:ID() == 1 or CastOnSpell == S.Spiritbloom
        Vars['ShouldCastFireBreath'] = CastOnSpell:ID() == 1 or CastOnSpell == S.FireBreath

        -- Load settings from configuration
        Settings['ve_targets'] = GetSetting('ve_targets', {})
        Settings['DreamBreathHP'] = GetSetting('DreamBreathHP', 70)
        Settings['DreamBreathHP2'] = GetSetting('DreamBreathHP2', 50)
        Settings['DreamBreathHP3'] = GetSetting('DreamBreathHP3', 40)
        Settings['DreamBreathHP4'] = GetSetting('DreamBreathHP4', 30)

        -- DPS settings
        Settings['stopdps'] = GetSetting('stopdps', 15)
        Settings['disintegrate_essenceburst'] = GetSetting('disintegrate_essenceburst', false)
        Settings['fb_emp_level_check'] = GetSetting('fb_emp_level_check', false)
        Settings['fb_emp_level_spin'] = GetSetting('fb_emp_level_spin', 4)

        -- Update combat state using monitor if not already in combat
        if not Vars['IsInCombat'] then
            Vars['IsInCombat'] = combatMonitor()
        end
    end

    -- =================================
    -- SECTION: EQUIPMENT AND TRINKETS
    -- =================================
    
    -- Initialize trinket items
    local equip = Player:GetEquipment()
    local trinket1 = equip[13] and Item(equip[13]) or Item(0)
    local trinket2 = equip[14] and Item(equip[14]) or Item(0)

    -- =================================
    -- SECTION: ROTATION VARIABLES
    -- =================================
    
    local ShouldReturn -- Used to return early from functions
    local Enemies25y -- Enemy units within 25 yards
    local Enemies8ySplash -- Enemy units within splash range
    local EnemiesCount8ySplash -- Count of enemies in splash range
    
    -- Talent-based variables
    local MaxEssenceBurstStack = (S.EssenceAttunement:IsAvailable()) and 2 or 1
    local BFRank = S.BlastFurnace:TalentRank()
    
    -- Combat timing variables
    local BossFightRemains = 11111
    local FightRemains = 11111

    -- =================================
    -- SECTION: EVENT HANDLERS
    -- =================================
    
    -- Update equipment when it changes
    HL:RegisterForEvent(function()
        equip = Player:GetEquipment()
        trinket1 = equip[13] and Item(equip[13]) or Item(0)
        trinket2 = equip[14] and Item(equip[14]) or Item(0)
    end, "PLAYER_EQUIPMENT_CHANGED")

    -- Update talent-based variables when spells change
    HL:RegisterForEvent(function()
        MaxEssenceBurstStack = (S.EssenceAttunement:IsAvailable()) and 2 or 1
        BFRank = S.BlastFurnace:TalentRank()
    end, "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")

    -- Reset combat timing variables after fights
    HL:RegisterForEvent(function()
        BossFightRemains = 11111
        FightRemains = 11111
    end, "PLAYER_REGEN_ENABLED")

    -- =================================
    -- SECTION: EVALUATION FUNCTIONS
    -- =================================
    
    -- These functions evaluate conditions for spell targeting and usage
    
    --- Always returns true (generic condition)
    local function EvaluateTrue()
        return true
    end
    
    --- Returns health percentage of target unit
    ---@param TargetUnit Unit
    local function EvaluateTargetIfHP(TargetUnit)
        return TargetUnit:HealthPercentage()
    end
    
    --- Calculates channel execution time for a spell
    ---@param Spell Spell
    local function ChannelExecuteTime(Spell)
        return Spell:BaseDuration() / ((Player:HastePct() / 100) + 1) + 0.25 + HL.Latency()
    end
    
    --- Evaluates if tank needs Time Dilation based on health threshold
    ---@param TargetedUnit Unit
    local function EvaluateTimeDilationTank(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= GetSetting("TimeDilationTankHP", 30)
    end
    
    --- Evaluates if group member needs Time Dilation based on health threshold
    ---@param TargetedUnit Unit
    local function EvaluateTimeDilationMembers(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= GetSetting("TimeDilationHP", 30)
    end
    
    --- Evaluates if unit is valid for Emerald Blossom during Stasis
    ---@param TargetedUnit Unit
    local function EvaluateEBxStasis(TargetedUnit)
        return TargetedUnit:HealthPercentage() < 100 and not TempBlackListEmeraldBlossom[TargetedUnit:Name()]
    end
    
    --- Evaluates if tank needs Spiritbloom based on health threshold
    ---@param TargetedUnit Unit
    local function EvaluateSBTanks(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= GetSetting("SpiritbloomTankHP", 30)
    end
    
    --- Evaluates if group member needs Spiritbloom based on health threshold
    ---@param TargetedUnit Unit
    local function EvaluateSBMembers(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= GetSetting("SpiritbloomHP", 30)
    end
    
    --- Evaluates if unit is valid for Emerald Blossom
    ---@param TargetedUnit Unit
    local function EvaluateEB(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= GetSetting("EmeraldBlossomHP", 30) and not TempBlackListEmeraldBlossom[TargetedUnit:Name()]
    end
    
    --- Evaluates if unit needs Emerald Blossom with 1 Essence Burst stack
    ---@param TargetedUnit Unit
    local function EvaluateEB_Burst1(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= GetSetting("burst1_EmeraldBlossomHP", 30) and not TempBlackListEmeraldBlossom[TargetedUnit:Name()]
    end
    
    --- Evaluates if unit needs Emerald Blossom with 2 Essence Burst stacks
    ---@param TargetedUnit Unit
    local function EvaluateEB_Burst2(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= GetSetting("burst2_EmeraldBlossomHP", 30) and not TempBlackListEmeraldBlossom[TargetedUnit:Name()]
    end
    
    --- Evaluates if unit needs Echo based on health threshold
    ---@param TargetedUnit Unit
    local function EvaluateEcho(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= GetSetting("EchoHP", 30) and TargetedUnit:BuffDown(S.Echo)
    end
    
    --- Evaluates if unit needs Verdant Embrace based on health threshold
    ---@param TargetedUnit Unit
    local function EvaluateVE(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= GetSetting("VerdantEmbraceHP", 30)
    end
    
    --- Evaluates if tank needs Reversion refresh
    ---@param TargetedUnit Unit
    local function EvaluateReversionTanks(TargetedUnit)
        return TargetedUnit:BuffRemains(S.Reversion) < 2
    end
    
    --- Evaluates if group member needs Reversion based on health threshold
    ---@param TargetedUnit Unit
    local function EvaluateReversionMembers(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= GetSetting("ReversionHP", 30) and TargetedUnit:BuffDown(S.Reversion)
    end
    
    --- Evaluates if unit needs Living Flame based on health threshold
    ---@param TargetedUnit Unit
    local function EvaluateLivingFlame(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= GetSetting("LivingFlameHP", 30)
    end
    
    --- Evaluates if unit can receive Living Flame with buff
    ---@param TargetedUnit Unit
    local function EvaluateLFBuffed(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= 99
    end
    
    --- Evaluates if unit can receive Echo spread
    ---@param TargetedUnit Unit
    local function EvaluateEchoSpread(TargetedUnit)
        return TargetedUnit:BuffDown(S.Echo)
    end
    
    --- Evaluates if unit needs Engulf (first charge) with Dream Breath buff
    ---@param TargetedUnit Unit
    local function EvaluateEngulf(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= GetSetting("EngulfHP", 30) and TargetedUnit:BuffUp(S.DreamBreathBuff)
    end
    
    --- Evaluates if unit needs Engulf (second charge) with Dream Breath buff
    ---@param TargetedUnit Unit
    local function EvaluateEngulf2(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= GetSetting("EngulfHP2", 30) and TargetedUnit:BuffUp(S.DreamBreathBuff)
    end

    -- =================================
    -- SECTION: CORE ROTATION FUNCTIONS
    -- =================================
    
    --- Handles empower spell casting logic
    local function CastEmpower()
        if Player:IsEmpowering() then
            if CastOnSpell.EmpowerLevel and CastOnSpell.EmpowerLevel > 0 then
                if Player:EmpoweredLevel() >= CastOnSpell.EmpowerLevel then
                    if MainAddon.ForceCastDisplay(CastOnSpell) then
                        return "Casting Empower"
                    end
                else
                    return "Waiting Empower"
                end
            else
                return "Waiting Empower"
            end
        end
    end

    --- Handles defensive spell usage
    local function Defensives()
        -- Only use defensives in combat
        if Vars['IsInCombat'] then
            -- Obsidian Scales when health is low
            if GetSetting('obscales_check', false) then
                if S.ObsidianScales:IsReady() and Player:BuffDown(S.ObsidianScales) and Player:RealHealthPercentage() <= GetSetting('obscales_spin', 30) then
                    if Cast(S.ObsidianScales, true) then
                        return "Defensives: Obsidian Scales";
                    end
                end
            end
        end
        
        -- Zephyr for dangerous situations in high keys or outdoor content
        if Player:MythicDifficulty() >= GetSetting('smart_feint_above_key_level', 2) or not Player:IsInDungeonArea() then
            if Player:ShouldFeint() and Player:BuffDown(S.Zephyr) and S.Zephyr:IsReady(Player) then
                if Cast(S.Zephyr) then
                    MainAddon.UI:ShowToast("Zephyr", "Dangerous situation detected !", MainAddon.GetTexture(S.Zephyr))
                    return "Zephyr"
                end
            end
        end
    end

    --- Handles utility spell usage
    local function Utilities()
        -- Hover for movement
        if GetSetting('hover_check', false) then
            if S.Hover:IsReady(Player) and Player:IsMovingFor() > GetSetting('hover_spin', 30) then
                if Cast(S.Hover, true) then
                    return "hover utilities";
                end
            end
        end
    end

    --- Handles trinket usage
    local function Trinkets()
        -- Dreambinder trinket
        if I.Dreambinder:IsEquippedAndReady() then
            if MainAddon.SetTopTexture(1, "Weapon On-Use") then return "dreambinder_loom_of_the_great_cycle"; end
        end
    end

    --- Handles special healing cases (focus/mouseover targets)
    local function HealingSpecial()
        local shouldHeal, isReadyToBeHealed, type = HealingEngine.ShouldHealTargetOrMouseover()
        if shouldHeal and isReadyToBeHealed then
            if Focus:IsInRange(30) then
                -- Emerald Blossom on focus
                if S.EmeraldBlossom:IsReady(Focus) and not TempBlackListEmeraldBlossom[Focus:Name()] then
                    if CastAlly(S.EmeraldBlossom, Focus) then
                        return 'Special Healing: Emerald Blossom'
                    end
                end
                
                -- Echo on focus if not already applied
                if S.Echo:IsReady(Focus) and Focus:BuffDown(S.Echo) then
                    if CastAlly(S.Echo, Focus) then
                        return "Special Healing: Echo"
                    end
                end
                
                -- Verdant Embrace on focus NPC if enabled
                if S.VerdantEmbrace:IsReady(Focus) and Settings['ve_targets']['npc'] then
                    if CastAlly(S.VerdantEmbrace, Focus) then
                        return "Special Healing: Verdant Embrace"
                    end
                end
                
                -- Living Flame on focus
                if S.LivingFlame:IsReady(Focus) then
                    if CastAlly(S.LivingFlame, Focus) then
                        return 'Special Healing: Living Flame'
                    end
                end
            end
        elseif not isReadyToBeHealed then
            -- Visual feedback for invalid healing targets
            if type == "MouseOver" then
                MainAddon.SetTopColor(1, "Focus Mouseover")
            elseif type == "Target" then
                MainAddon.SetTopColor(1, "Focus Target")
            end
        end
    end
   
    --- Handles preemptive healing for incoming damage
    local function DamageIncoming()
        -- Spread Echo before incoming damage
        if S.Echo:IsReady(Player) then
            if CastCycleAlly(S.Echo, Members, EvaluateEchoSpread) then
                return "Damage Incoming: Echo Spread"
            end
        end

        -- Dream Breath with possible Verdant Embrace combo
        if S.DreamBreath:IsReady(Player) and Vars['ShouldCastDreamBreath'] then
            if S.VerdantEmbrace:IsReady(Player) and S.CallofYsera:IsAvailable() and not Player:BuffUp(S.Stasis) then
                if CastAlly(S.VerdantEmbrace, Player) then
                    return "Damage Incoming: Verdant Embrace x Dream Breath"
                end
            end
            CastOnSpell = S.DreamBreath
            CastOnSpell.EmpowerLevel = 1
            if Cast(S.DreamBreath) then
                return "Damage Incoming: Dream Breath"
            end
        end
    end

    --- Handles raid buff maintenance
    local function RaidBuff()
        local mthrdm = MainAddon.GenerateRandom('Blessing_Raid', 30, 60, 60)
        if S.BlessingoftheBronze:IsReady(Player) then
            if (Player:BuffRemains(S.BlessingoftheBronzeBuff, true) < 1800 + mthrdm) then
                if Cast(S.BlessingoftheBronze) then
                    return "Raid Buff: Blessing of the Bronze";
                end
            end
        end
    end

    --- Handles major cooldown usage
    local function Cooldowns()
        -- Stasis for group healing
        if S.Stasis:IsReady(Player) and Vars['AverageHP'] <= GetSetting("StasisHP", 30) then
            if S.CycleofLife:IsAvailable() and S.EmeraldBlossom:IsReady(Player) 
            or S.DreamBreath:IsCastable() then
                if Cast(S.Stasis, true) then
                    return "CDs: Stasis Charging..."
                end
            end
        end

        -- Release Stasis when ready or about to expire
        if Player:BuffUp(S.StasisFull) and (Vars['AverageHP'] <= GetSetting("StasisHP", 30) or Player:BuffRemains(S.StasisFull) < 3) then
            if Cast(S.Stasis, true) then
                return "CDs: Stasis"
            end
        end

        -- Tip The Scales for emergency healing
        if S.TipTheScales:IsReady() and Player:BuffDown(S.TipTheScales) and Vars['AverageHP'] <= GetSetting("TipTheScalesHP", 30) then
            if Cast(S.TipTheScales, true) then
                return "CDs: Tip The Scales"
            end
        end

        -- Time Dilation for tanks
        if S.TimeDilation:IsReady(Player) then
            if CastCycleAlly(S.TimeDilation, Tanks, EvaluateTimeDilationTank, nil, true) then
                return "CDs: Time Dilation - Tanks"
            end
        end

        -- Time Dilation for group members
        if S.TimeDilation:IsReady(Player) then
            if CastCycleAlly(S.TimeDilation, Members, EvaluateTimeDilationMembers, nil, true) then
                return "CDs: Time Dilation - Members"
            end
        end

        -- Rewind for group healing
        if S.Rewind:IsReady(Player) and Vars['AverageHP'] <= GetSetting("RewindHP", 30) then
            if Cast(S.Rewind) then
                return "CDs: Rewind"
            end
        end

        -- Zephyr for group mitigation
        if S.Zephyr:IsReady(Player) and Vars['AverageHP'] <= GetSetting("ZephyrHP", 30) then
            if Cast(S.Zephyr) then
                return "CDs: Zephyr"
            end
        end

        -- Emerald Communion for emergency group healing
        if S.EmeraldCommunion:IsReady(Player) and Vars['AverageHP'] <= GetSetting("EmeraldCommunionHP", 30) then
            if Cast(S.EmeraldCommunion) then
                return "CDs: Emerald Communion"
            end
        end
    end

    --- Handles general healing rotation
    local function GeneralHealing()
        -- Temporal Anomaly + Reversion combo
        if S.Reversion:IsReady(Player) and S.ChronoFlames:IsAvailable() then
            if (Player:PrevGCD(1, S.TemporalAnomaly) or S.TemporalAnomaly:TimeSinceLastCast() < Player:GCD()) and not Player:PrevGCD(1, S.Reversion) then
                if CastTargetIfAlly(S.Reversion, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                    return "Temporal Anomaly x Reversion"
                end
            end
        end

        -- Emerald Blossom during Stasis with Cycle of Life
        if Player:BuffUp(S.Stasis) and S.CycleofLife:IsAvailable() and S.EmeraldBlossom:IsReady(Player) then
            if CastCycleAlly(S.EmeraldBlossom, Members, EvaluateEBxStasis) then
                return "G: Emerald Blossom x Stasis"
            end
        end

        -- Dream Breath logic with different empower levels based on group health
        if S.DreamBreath:IsReady(Player) and Vars['ShouldCastDreamBreath'] then
            local DreamBreathLogic = false

            -- Max empower during Stasis
            if Player:BuffUp(S.Stasis) then
                DreamBreathLogic = true
                CastOnSpell = S.DreamBreath
                CastOnSpell.EmpowerLevel = 4
            end

            -- Tiered empower levels based on group health thresholds
            if Vars['AverageHP'] <= Settings["DreamBreathHP4"] then
                DreamBreathLogic = true
                CastOnSpell = S.DreamBreath
                CastOnSpell.EmpowerLevel = 4
            end

            if not DreamBreathLogic and Vars['AverageHP'] <= Settings["DreamBreathHP3"] then
                DreamBreathLogic = true
                CastOnSpell = S.DreamBreath
                CastOnSpell.EmpowerLevel = 3
            end

            if not DreamBreathLogic and Vars['AverageHP'] <= Settings["DreamBreathHP2"] then
                DreamBreathLogic = true
                CastOnSpell = S.DreamBreath
                CastOnSpell.EmpowerLevel = 2
            end

            if not DreamBreathLogic and Vars['AverageHP'] <= Settings["DreamBreathHP"] then
                DreamBreathLogic = true
                CastOnSpell = S.DreamBreath
                CastOnSpell.EmpowerLevel = 1
            end

            if DreamBreathLogic then
                -- Cast Echo before high-level Dream Breath
                if (CastOnSpell.EmpowerLevel >= 3) and S.Echo:IsReady(Player) then
                    if CastCycleAlly(S.Echo, Members, EvaluateEchoSpread) then
                        return "G: Echo x Dream Breath"
                    end
                end
                
                -- Verdant Embrace combo if available
                if S.VerdantEmbrace:IsReady(Player) and S.CallofYsera:IsAvailable() and not Player:BuffUp(S.Stasis) then
                    if CastAlly(S.VerdantEmbrace, Player) then
                        return "G: Verdant Embrace x Dream Breath"
                    end
                end
                
                -- Cast Dream Breath
                if Cast(S.DreamBreath) then
                    return "G: Dream Breath"
                end
            end
        end

        -- Spiritbloom logic for tanks and group members
        if S.Spiritbloom:IsReady(Player) and Vars['ShouldCastSpiritbloom'] then
            local SpiritbloomLogic_Tanks = false
            if Tanks then
                ---@param TankUnit Unit
                for _, TankUnit in pairs(Tanks) do
                    if TankUnit:HealthPercentage() <= GetSetting("SpiritbloomTankHP", 30) then
                        SpiritbloomLogic_Tanks = true
                        CastOnSpell = S.Spiritbloom
                        CastOnSpell.EmpowerLevel = 1
                        break;
                    end
                end
            end

            -- Cast on tanks if needed
            if SpiritbloomLogic_Tanks then
                if CastCycleAlly(S.Spiritbloom, Tanks, EvaluateSBTanks) then
                    return "G: Spiritbloom - Tank"
                end
            end

            local SpiritbloomLogic_Members = false
            if Members then
                ---@param MemberUnit Unit
                for _, MemberUnit in pairs(Members) do
                    if MemberUnit:HealthPercentage() <= GetSetting("SpiritbloomHP", 30) then
                        SpiritbloomLogic_Members = true
                        CastOnSpell = S.Spiritbloom
                        if S.FontofMagic:IsAvailable() then
                            CastOnSpell.EmpowerLevel = 4
                        else
                            CastOnSpell.EmpowerLevel = 3
                        end
                        break;
                    end
                end
            end

            -- Cast on group members if needed
            if SpiritbloomLogic_Members then
                if CastCycleAlly(S.Spiritbloom, Members, EvaluateSBMembers) then
                    return "G: Spiritbloom"
                end
            end
        end

        -- Emerald Blossom based on settings
        if S.EmeraldBlossom:IsReady(Player) then
            local eblossom_targets = GetSetting('eblossom_targets', 'everyone')
            local eblossom_unit = Members
            if eblossom_targets == 'tanks' then
                eblossom_unit = Tanks
            end
            if CastCycleAlly(S.EmeraldBlossom, eblossom_unit, EvaluateEB) then
                return "G: Emerald Blossom"
            end
        end

        -- Engulf charges management
        if S.Engulf:IsReady(Player) then
            -- 1st Charge
            if S.Engulf:ChargesFractional() > 1.9 then
                if CastCycleAlly(S.Engulf, Members, EvaluateEngulf) then
                    return "G: Engulf 1st Charge"
                end
            end
            -- 2nd Charge
            if S.Engulf:ChargesFractional() <= 1.9 then
                if CastCycleAlly(S.Engulf, Members, EvaluateEngulf2) then
                    return "G: Engulf 2nd Charge" 
                end
            end
        end

        -- Echo application
        if S.Echo:IsReady(Player) then
            if CastCycleAlly(S.Echo, Members, EvaluateEcho) then
                return "G: Echo"
            end
        end

        -- Verdant Embrace based on target settings
        if S.VerdantEmbrace:IsReady(Player) then
            local VE_Self = Settings['ve_targets']['self']
            local VE_Tanks = Settings['ve_targets']['tank']
            local VE_Healers = Settings['ve_targets']['healer']
            local VE_DPS = Settings['ve_targets']['dps']

            -- Self heal if enabled and needed
            if VE_Self and EvaluateVE(Player) then
                if CastAlly(S.VerdantEmbrace, Player) then
                    return "G: Verdant Embrace x Self"
                end
            end

            -- Tanks if enabled
            if VE_Tanks and Tanks then
                if CastCycleAlly(S.VerdantEmbrace, Tanks, EvaluateVE) then
                    return "G: Verdant Embrace x Tanks"
                end
            end

            -- Healers if enabled
            if VE_Healers and Healers then
                if CastCycleAlly(S.VerdantEmbrace, Healers, EvaluateVE) then
                    return "G: Verdant Embrace x Healers"
                end
            end

            -- DPS if enabled
            if VE_DPS and Damagers then
                if CastCycleAlly(S.VerdantEmbrace, Damagers, EvaluateVE) then
                    return "G: Verdant Embrace x DPS"
                end
            end
        end

        -- Reversion maintenance
        if S.Reversion:IsReady(Player) then
            -- Refresh on tanks before it expires
            if CastCycleAlly(S.Reversion, Tanks, EvaluateReversionTanks) then
                return "G: Reversion on tank"
            end
            -- Apply to group members based on health
            if CastCycleAlly(S.Reversion, Members, EvaluateReversionMembers) then
                return "G: Reversion on members"
            end
        end

        -- Temporal Anomaly for group mitigation
        if S.TemporalAnomaly:IsReady(Player) and Vars['AverageHP'] <= GetSetting("TemporalAnomalyHP", 30) then
            if Cast(S.TemporalAnomaly) then
                return "G: Temporal Anomaly"
            end
        end

        -- Living Flame for spot healing
        if S.LivingFlame:IsReady(Player) then
            if CastCycleAlly(S.LivingFlame, Members, EvaluateLivingFlame) then
                return "G: Living Flame"
            end
        end

        -- Chrono Flames alternative to Living Flame
        if S.ChronoFlames:IsReady(Player) then
            if CastCycleAlly(S.ChronoFlames, Members, EvaluateLivingFlame) then
                return "G: Chrono Flames"
            end
        end
    end

    --- Handles forced DPS rotation when toggle is active
    local function ForceDPS()
        -- Fire Breath with empower logic
        if (M.TargetIsValid() and S.FireBreath:IsReady() 
        or S.FireBreath:IsReady(Player) and #Enemies25y >= 1 and Vars['IsInCombat']) and Target:TimeToDie() > ChannelExecuteTime(S.FireBreath)
        and Vars['ShouldCastFireBreath'] then
            CastOnSpell = S.FireBreath
            
            -- Handle forced empower levels if setting is enabled
            if Settings['fb_emp_level_check'] then
                if Settings['fb_emp_level_spin'] >= 4 then
                    if S.FontofMagic:IsAvailable() then
                        CastOnSpell.EmpowerLevel = 4
                    else
                        CastOnSpell.EmpowerLevel = 3
                    end
                elseif Settings['fb_emp_level_spin'] < 1 then
                    CastOnSpell.EmpowerLevel = 1
                else
                    CastOnSpell.EmpowerLevel = Settings['fb_emp_level_spin']
                end
            else
                -- Default empower logic based on talents and situation
                if S.LeapingFlamesTalent:IsAvailable() then
                    if S.FontofMagic:IsAvailable() then
                        CastOnSpell.EmpowerLevel = 4
                    else
                        CastOnSpell.EmpowerLevel = 3
                    end
                else
                    if #Enemies25y == 1 then
                        CastOnSpell.EmpowerLevel = 1
                    else
                        CastOnSpell.EmpowerLevel = 3
                    end
                end
            end
            
            if Cast(S.FireBreath) then
                return 'Fire Breath'
            end
        end
        
        -- Standard DPS rotation when target is valid
        if M.TargetIsValid() then
            -- Buffed Living Flame/Chrono Flames
            if Player:BuffUp(S.LeapingFlamesBuff) then
                if S.LivingFlame:IsReady() or S.ChronoFlames:IsReady() then
                    if Cast(S.LivingFlame, "Living Flame Enemy") then
                        return 'Buffed: Living Flame/Chrono Flames'
                    end
                end
            end

            -- Unravel to remove enemy absorb shields
            if S.Unravel:IsReady() and Target:ActiveDamageAbsorb() then
                if Cast(S.Unravel) then return "unravel main 4"; end
            end

            -- Disintegrate as main DPS channel
            if S.Disintegrate:IsReady() and Target:TimeToDie() > ChannelExecuteTime(S.Disintegrate) then
                if Cast(S.Disintegrate) then
                    return 'Disintegrate'
                end
            end

            -- Living Flame/Chrono Flames as filler
            if S.LivingFlame:IsReady() or S.ChronoFlames:IsReady() then
                if Cast(S.LivingFlame, "Living Flame Enemy") then
                    return 'Living Flame/Chrono Flames'
                end
            end

            -- Azure Strike as last resort
            if S.AzureStrike:IsReady() then
                if Cast(S.AzureStrike) then
                    return 'Azure Strike'
                end
            end
        end
    end
    
    --- Handles standard DPS rotation when not in forced mode
    local function DamageRotation()
        local stopdpscondition = Player:ManaPercentage() <= Settings['stopdps']
        
        -- Only DPS if mana is sufficient or Leaping Flames is active
        if not stopdpscondition or S.LeapingFlamesTalent:IsAvailable() then
            -- Fire Breath with empower logic
            if (M.TargetIsValid() and S.FireBreath:IsReady() 
            or S.FireBreath:IsReady(Player) and #Enemies25y >= 1 and Vars['IsInCombat']) and Target:TimeToDie() > ChannelExecuteTime(S.FireBreath) 
            and Vars['ShouldCastFireBreath'] then
                CastOnSpell = S.FireBreath
                
                -- Handle forced empower levels if setting is enabled
                if Settings['fb_emp_level_check'] then
                    if Settings['fb_emp_level_spin'] > 4 then
                        if S.FontofMagic:IsAvailable() then
                            CastOnSpell.EmpowerLevel = 4
                        else
                            CastOnSpell.EmpowerLevel = 3
                        end
                    elseif Settings['fb_emp_level_spin'] < 1 then
                        CastOnSpell.EmpowerLevel = 1
                    else
                        CastOnSpell.EmpowerLevel = Settings['fb_emp_level_spin']
                    end
                else
                    -- Default empower logic based on talents and situation
                    if S.LeapingFlamesTalent:IsAvailable() then
                        if S.FontofMagic:IsAvailable() then
                            CastOnSpell.EmpowerLevel = 4
                        else
                            CastOnSpell.EmpowerLevel = 3
                        end
                    else
                        if #Enemies25y == 1 then
                            CastOnSpell.EmpowerLevel = 1
                        else
                            CastOnSpell.EmpowerLevel = 3
                        end
                    end
                end
                
                if Cast(S.FireBreath) then
                    return 'Fire Breath'
                end
            end
        end

        -- Standard DPS rotation when target is valid
        if M.TargetIsValid() then
            -- Buffed Living Flame/Chrono Flames
            if Player:BuffUp(S.LeapingFlamesBuff) then
                if S.LivingFlame:IsReady() or S.ChronoFlames:IsReady() then
                    if Cast(S.LivingFlame, "Living Flame Enemy") then
                        return 'Buffed: Living Flame/Chrono Flames'
                    end
                end
            end

            -- Unravel to remove enemy absorb shields
            if S.Unravel:IsReady() and Target:ActiveDamageAbsorb() then
                if Cast(S.Unravel) then return "unravel main 4"; end
            end

            -- Disintegrate as main DPS channel
            if S.Disintegrate:IsReady() and Target:TimeToDie() > ChannelExecuteTime(S.Disintegrate) then
                if Cast(S.Disintegrate) then
                    return 'Disintegrate'
                end
            end

            -- Only use fillers if we're not conserving mana
            if not stopdpscondition then
                -- Living Flame/Chrono Flames as filler
                if S.LivingFlame:IsReady() or S.ChronoFlames:IsReady() then
                    if Cast(S.LivingFlame, "Living Flame Enemy") then
                        return 'Living Flame/Chrono Flames'
                    end
                end

                -- Azure Strike as last resort
                if S.AzureStrike:IsReady() then
                    if Cast(S.AzureStrike) then
                        return 'Azure Strike'
                    end
                end
            end
        end
    end

    -- =================================
    -- SECTION: MAIN ACTION PRIORITY LIST
    -- =================================
    
    --- Main APL function that orchestrates the rotation
    local function APL()
        -- Don't cast anything while channeling
        if Player:IsChanneling(S.EmeraldCommunion) then
            return
        end

        -- Update all variables and settings
        UpdateVars()

        -- Update enemy unit lists
        Enemies25y = Player:GetEnemiesInRange(25)
        Enemies8ySplash = Target:GetEnemiesInSplashRange(10)
        if AoEON() then
            EnemiesCount8ySplash = #Enemies8ySplash
        else
            EnemiesCount8ySplash = 1
        end
        
        -- Handle trinket usage
        local shouldReturn = MainAddon.TrinketHealing(Members, OnUseExcludes)
        if shouldReturn then
            return shouldReturn
        end

        -- Handle mana potion usage
        if MainAddon.UseManaPotion() then
            MainAddon.SetTopColor(1, "Combat Potion")
        end

        -- Update fight duration tracking
        if M.TargetIsValid() or Vars['IsInCombat'] then
            BossFightRemains = HL.BossFightRemains()
            FightRemains = BossFightRemains
            if FightRemains == 11111 then
                FightRemains = HL.FightRemains(Enemies25y, false)
            end
        end

        -- Handle empower spell casting
        ShouldReturn = CastEmpower()
        if ShouldReturn then
            return ShouldReturn
        end
        if Player:IsEmpowering() then
            return "Holding during Empowering"
        end

        -- Raid Buff maintenance
        ShouldReturn = RaidBuff();
        if ShouldReturn then
            return ShouldReturn;
        end

        -- Defensive cooldowns
        ShouldReturn = Defensives();
        if ShouldReturn then
            return ShouldReturn;
        end

        -- Utility spells in combat
        if Vars['IsInCombat'] then
            ShouldReturn = Utilities();
            if ShouldReturn then
                return ShouldReturn;
            end
        end

        -- Essence Burst + Emerald Blossom logic
        if Player:BuffStack(S.EssenceBurst) == 1 then
            if S.EmeraldBlossom:IsReady(Player) then
                local burst_eblossom_targets = GetSetting('burst_eblossom_targets', 'everyone')
                local burst_eblossom_unit = Members
                if burst_eblossom_targets == 'tanks' then
                    burst_eblossom_unit = Tanks
                end
                if CastCycleAlly(S.EmeraldBlossom, burst_eblossom_unit, EvaluateEB_Burst1) then
                    return "Essence Burst x Emerald Blossom (1 stack)"
                end
            end
        elseif Player:BuffStack(S.EssenceBurst) >= 2 then
            if S.EmeraldBlossom:IsReady(Player) then
                local burst_eblossom_targets = GetSetting('burst_eblossom_targets', 'everyone')
                local burst_eblossom_unit = Members
                if burst_eblossom_targets == 'tanks' then
                    burst_eblossom_unit = Tanks
                end
                if CastCycleAlly(S.EmeraldBlossom, burst_eblossom_unit, EvaluateEB_Burst2) then
                    return "Essence Burst x Emerald Blossom (2 stacks)"
                end
            end
        end

        -- Buffed Living Flame/Chrono Flames
        if Player:BuffUp(S.LeapingFlamesBuff) then
            if S.LivingFlame:IsReady(Player) then
                if CastCycleAlly(S.LivingFlame, Members, EvaluateLFBuffed) then
                    return "Buffed Living Flame"
                end
            end

            if S.ChronoFlames:IsReady(Player) then
                if CastCycleAlly(S.ChronoFlames, Members, EvaluateLFBuffed) then
                    return "Buffed Chrono Flames"
                end
            end
        end

        -- Force DPS mode when toggle is active
        if MainAddon.Toggle:GetToggle('ForceDPS') then
            ShouldReturn = ForceDPS();
            if ShouldReturn then
                return "Damage: " .. ShouldReturn;
            end
        end

        -- Spread Echo when toggle is enabled
        if MainAddon.Toggle:GetToggle("SpreadEcho") then
            if S.Echo:IsReady(Player) then
                 if CastCycleAlly(S.Echo, Members, EvaluateEchoSpread) then
                      return 'Echo Spread - Toggle'
                 end
            end
        else
            -- Normal healing rotation when not in spread mode
            if Vars['IsInCombat'] then
                ShouldReturn = Cooldowns()
                if ShouldReturn then
                    return "Healing: " .. ShouldReturn
                end
            end

            ShouldReturn = GeneralHealing()
            if ShouldReturn then
                return "Healing: " .. ShouldReturn
            end

            -- Special healing for focus/mouseover targets
            local ShouldReturn = HealingSpecial()
            if ShouldReturn then
                return ShouldReturn;
            end

            -- Preemptive healing for incoming damage
            local Reason, SpellID = MainAddon:DamageIncoming()
            if Reason == "SOON" then
                MainAddon.UI:ShowToast(MainAddon.GetSpellInfo(SpellID), "Predicting major incoming damage.", MainAddon.GetTexture(Spell(SpellID)), 10)

                local ShouldReturn = DamageIncoming();
                if ShouldReturn then
                    return ShouldReturn;
                end
            end
        end

        -- Standard DPS rotation when target is valid
        if M.TargetIsValid() then
            ShouldReturn = Trinkets();
            if ShouldReturn then
                return ShouldReturn;
            end
        end

        -- Execute damage rotation
        ShouldReturn = DamageRotation();
        if ShouldReturn then
            return "Damage: " .. ShouldReturn;
        end
    end

    -- =================================
    -- SECTION: INITIALIZATION FUNCTIONS
    -- =================================
    
    --- Initialization function called when the APL is loaded
    local function Init()
        -- Setting to TAP to avoid empower control issues
        SetCVar("empowerTapControls", 1)

        -- Initialize empower levels for spells
        S.DreamBreath.EmpowerLevel = 0
        S.Spiritbloom.EmpowerLevel = 0
        S.FireBreath.EmpowerLevel = 0
    end
    
    -- Register the APL and Init function for Preservation Evoker (spec ID 1468)
    M.SetAPL(1468, APL, Init);

    -- =================================
    -- SECTION: CORE OVERRIDES
    -- =================================
    
    --- Override for Player.BuffUp to handle special cases
    local PresBuffUp
    PresBuffUp = HL.AddCoreOverride("Player.BuffUp",
        function(self, Spell, AnyCaster, BypassRecovery)
            local BaseCheck = PresBuffUp(self, Spell, AnyCaster, BypassRecovery)
            if MainAddon.PlayerSpecID() == 1468 then
                if Spell == S.LeapingFlamesBuff then
                    if Player:IsCasting(S.LivingFlame) or Player:IsCasting(S.ChronoFlames) then
                        return false
                    end
                end
            end
            return BaseCheck
        end
    , 1468)

    --- Override for Spell.IsCastable to handle movement and special cases
    local OldIsCastable
    OldIsCastable = HL.AddCoreOverride("Spell.IsCastable",
            function(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                if MainAddon.PlayerSpecID() == 1468 then
                    -- Prevent empower spells while moving (unless Tip The Scales is active)
                    if Player:IsMoving() then
                        if (self == S.FireBreath or self == S.DreamBreath or self == S.Spiritbloom) and Player:BuffDown(S.TipTheScales) then
                            return false, "Override: Player is moving"
                        end
                    end

                    -- Disintegrate with Essence Burst handling
                    if self == S.Disintegrate then
                        if Player:BuffUp(S.EssenceBurst) and not GetSetting('disintegrate_essenceburst', false) then
                            return false, "Override: Essence Burst"
                        end
                    end

                    -- Ignore movement checks while Hover is active (except for empower spells)
                    if Player:BuffUp(S.Hover) and not MainAddon.CONST.Empower[self:ID()] then
                        ignoreMovement = true
                    end
                end
                local BaseCheck, Reason = OldIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                return BaseCheck, Reason
            end
    , 1468);

    -- =================================
    -- SECTION: ESSENCE TRACKING OVERRIDES
    -- =================================
    
    -- Track when essence power updates for accurate regeneration calculations
    HL:RegisterForEvent(
        function(Event, Arg1, Arg2)
            -- Ensure it's the player
            if Arg1 ~= "player" then
                return
            end

            if Arg2 == "ESSENCE" then
                Cache.Persistent.Player.LastPowerUpdate = GetTime()
            end
        end,
        "UNIT_POWER_UPDATE"
    )

    -- Override essence time to max calculation for more accurate predictions
    HL.AddCoreOverride("Player.EssenceTimeToMax",
            function()
                local Deficit = Player:EssenceDeficit()
                if Deficit == 0 then
                    return 0;
                end
                local Regen = GetPowerRegenForPowerType(EssencePowerType)
                if not Regen or Regen < 0.2 then
                    Regen = 0.2;
                end
                local TimeToOneEssence = 1 / Regen
                local LastUpdate = Cache.Persistent.Player.LastPowerUpdate
                return Deficit * TimeToOneEssence - (GetTime() - LastUpdate)
            end
    , 1468)

    -- Override essence time to X calculation for more accurate predictions
    HL.AddCoreOverride("Player.EssenceTimeToX",
            function(Amount)
                local Essence = Player:Essence()
                if Essence >= Amount then
                    return 0;
                end
                local Regen = GetPowerRegenForPowerType(EssencePowerType)
                local TimeToOneEssence = 1 / Regen
                local LastUpdate = Cache.Persistent.Player.LastPowerUpdate
                return ((Amount - Essence) * TimeToOneEssence) - (GetTime() - LastUpdate)
            end
    , 1468)

    -- =================================
    -- SECTION: SPELLCAST TRACKING
    -- =================================
    
    -- Track Emerald Blossom casts to prevent duplicate applications
    HL:RegisterForEvent(function(_, source, destName, _, spellID)
        if source == "player" then
            if spellID == 355913 then -- Emerald Blossom spell ID
                TempBlackListEmeraldBlossom[destName] = true
                C_Timer.After(3, function()
                    TempBlackListEmeraldBlossom[destName] = nil
                end)
            end
        end
    end, "UNIT_SPELLCAST_SENT")

    -- Reset tracking variables and update spell states on various events
    HL:RegisterForEvent(function()
        wipe(TempBlackListEmeraldBlossom)
        -- Update Emerald Communion channeling behavior based on Dreamwalker talent
        if S.Dreamwalker:IsAvailable() then
            MainAddon.CONST.SpellIsChannel[S.EmeraldCommunion:ID()].StandStill = false
        else
            MainAddon.CONST.SpellIsChannel[S.EmeraldCommunion:ID()].StandStill = true
        end
    end, "PLAYER_REGEN_ENABLED", "PLAYER_ENTERING_WORLD", "UPDATE_CHAT_WINDOWS", "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")

    -- Safety measure to reset empower tracking if UNIT_SPELLCAST_EMPOWER_STOP didn't fire
    HL:RegisterForEvent(
        function(event, unitTarget, castGUID, spellGUID)
            if unitTarget == 'player' and CastOnSpell:ID() ~= 1 then
                local spellID = select(6, strsplit("-", spellGUID))
                spellID = tonumber(spellID)
                if spellID and not MainAddon.CONST.Empower[spellID] then 
                    CastOnSpell = Spell(1)
                    S.DreamBreath.EmpowerLevel = 0
                    S.Spiritbloom.EmpowerLevel = 0
                    S.FireBreath.EmpowerLevel = 0
                end
            end
        end,
    "UNIT_SPELLCAST_SENT")

    -- Reset empower tracking when empower cast stops
    HL:RegisterForEvent(
        function(event, unitTarget, castGUID, spellID)
            if unitTarget == 'player' then
                CastOnSpell = Spell(1)
                S.DreamBreath.EmpowerLevel = 0
                S.Spiritbloom.EmpowerLevel = 0
                S.FireBreath.EmpowerLevel = 0
            end
        end,
    "UNIT_SPELLCAST_EMPOWER_STOP")
    
    -- Reset empower levels after combat
    HL:RegisterForEvent(function()
        S.DreamBreath.EmpowerLevel = 0
        S.Spiritbloom.EmpowerLevel = 0
        S.FireBreath.EmpowerLevel = 0
    end, "PLAYER_REGEN_ENABLED")
end