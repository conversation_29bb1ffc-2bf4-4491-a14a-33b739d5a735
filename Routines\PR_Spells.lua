---@class MainAddon
local MainAddon = MainAddon
---@class MainAddon
local M = MainAddon
-- HeroLib
local HL = HeroLibEx
---@class HeroCache
local Cache = HeroCache;
---@class Unit
local Unit = HL.Unit
---@class Unit
local Player = Unit.Player
---@class Unit
local Party = Unit.Party
---@class Unit
local Raid = Unit.Raid
---@class Spell
local Spell = HL.Spell
---@class Spell
local MultiSpell = HL.MultiSpell
local CreateSpell = MainAddon.CreateSpell
local CreateMultiSpell = MainAddon.CreateMultiSpell
local MergeTableByKey = HL.Utils.MergeTableByKey
local GetSpellInfo = _G['C_Spell'].GetSpellInfo
---@class Item
local Item = HL.Item
local UnitCanAttack = _G['UnitCanAttack']

M.Priest = {}
---@class Priest
local Priest = M.Priest
Priest.db = {}
Priest.PiTargetName = "None"
Priest.PiTargetName_Auto = ""
Priest.PiTargetName_Auto_Toast = false
Priest.PiTargetName_Auto_Enabled = false
Priest.PiFightRemains = 999

-- Spells
if not Spell.Priest then
    Spell.Priest = {}
end

---@class PRCustomTable
Spell.Priest.Custom = {
    MindSoothe = CreateSpell(453),
    WingBuffet = CreateSpell(357214),
    DivineStar = CreateMultiSpell(110744, 122121),
    BloodFury = CreateMultiSpell(20572, 33702, 33697),
    MassDispel = CreateSpell(32375),
    DissonantEchoes = CreateSpell(343144),
    Fade = CreateSpell(586),
    Shadowmeld = CreateSpell(58984),
    PsychicScream = CreateSpell(8122),
    PsychicHorror = CreateSpell(64044),
    VampiricEmbrace = CreateSpell(15286),
    Phantasm = CreateSpell(108942),
    ShackleUndead = CreateSpell(9484),
    VoidShift = CreateSpell(108968),
    PurifyDisease = CreateSpell(213634),
    Resurrection = CreateSpell(2006),
    DispelMagic = CreateSpell(528),
    AngelicFeather = CreateSpell(121536),
    AngelicFeatherBuff = CreateSpell(121557),
    VoidTendrils = CreateSpell(108920),
    LeapofFaith = CreateSpell(73325),
    BodyAndSoul = CreateSpell(64129),
    BodyAndSoulBuff = CreateSpell(65081),
    DominateMind = CreateSpell(205364),
    Purify = CreateSpell(527),
    ImprovedPurify = CreateSpell(390632),
    PowerInfusionBuff = CreateSpell(10060),
    DarkEnergy = CreateSpell(451018),
    -- PvP
    Thoughtsteal = CreateSpell(316262),
    -- PvP Shadow
    Psyfiend = CreateSpell(211522),
    -- PvP Holy
    HolyWard = CreateSpell(213610),
    GreaterHeal = CreateSpell(289666),
    RayofHope = CreateSpell(197268),
}

---@class PRCommonsTable
Spell.Priest.Commons = {
    -- Racials
    AncestralCall               = CreateSpell(274738),
    ArcanePulse                 = CreateSpell(260364),
    ArcaneTorrent               = CreateSpell(50613),
    BagofTricks                 = CreateSpell(312411),
    Berserking                  = CreateSpell(26297),
    BerserkingBuff              = CreateSpell(26297),
    BloodFury                   = CreateSpell(20572),
    BloodFuryBuff               = CreateSpell(20572),
    Fireblood                   = CreateSpell(265221),
    LightsJudgment              = CreateSpell(255647),
    -- Abilities
    DeathAndMadness             = CreateSpell(321291),
    DesperatePrayer             = CreateSpell(19236),
    DivineStar                  = CreateSpell(122121),
    HolyNova                    = CreateSpell(132157), -- Melee, 12
    MindBlast                   = CreateSpell(8092),
    PowerInfusion               = CreateSpell(10060),
    PowerWordFortitude          = CreateSpell(21562),
    PowerWordShield             = CreateSpell(17),
    ShadowWordDeath             = CreateSpell(32379),
    ShadowWordPain              = CreateSpell(589),
    ShadowWordPainDebuff        = CreateSpell(589),
    FlashHeal                   = CreateSpell(2061),
    Smite                       = CreateSpell(585),
    Renew                       = CreateSpell(139),
    -- Talents
    Mindgames                   = CreateSpell(375901),
    Shadowfiend                 = CreateSpell(34433),
    CrystallineReflection       = CreateSpell(373457),
    Rhapsody                    = CreateSpell(390622),
    PowerWordLife               = CreateSpell(373481),
    TwistofFate                 = CreateSpell(390972),
    -- Buffs
    AberrantSpellforgeBuff      = CreateSpell(451895),
    PowerWordFortitudeBuff      = CreateSpell(21562),
    RhapsodyBuff                = CreateSpell(390636),
    SpymastersReportBuff        = CreateSpell(451199), -- Stacking buff from before using Spymaster's Web trinket
    SpymastersWebBuff           = CreateSpell(444959), -- Buff from using Spymaster's Web trinket
    TwistofFateBuff             = CreateSpell(390978),
    -- Debuffs
    -- Other
    Pool                        = CreateSpell(999910)
}

---@class ArchonTable
Spell.Priest.Archon = {
    -- Talents
    EmpoweredSurges             = CreateSpell(453799),
    PerfectedForm               = CreateSpell(453917),
    PowerSurge                  = CreateSpell(453109),
}

---@class OrcaleTable
Spell.Priest.Oracle = {
    -- Talents
    PremonitionTalent           = CreateSpell(428924),
    PremonitionOfInsight        = CreateSpell(428933),
    PremonitionOfPiety          = CreateSpell(428930),
    PremonitionOfSolace         = CreateSpell(428934),
    PremonitionOfClairvoyance   = CreateSpell(440725),
}
  
---@class VoidweaverTable
Spell.Priest.Voidweaver = {
    -- Abilities
    VoidBlastAbility            = CreateSpell(450983),
    VoidWraithAbility           = CreateSpell(451235),
    -- Talents
    DepthofShadows              = CreateSpell(451308),
    DevourMatter                = CreateSpell(451840),
    EntropicRift                = CreateSpell(447444),
    InnerQuietus                = CreateSpell(448278),
    VoidBlast                   = CreateMultiSpell(450405, 450983, 450215),    
    VoidEmpowerment             = CreateSpell(450138),
    VoidWraith                  = CreateSpell(451234),
}

---@class ShadowTable
Spell.Priest.Shadow = {
  -- Base Spells
  MindFlay                    = CreateSpell(15407),
  Shadowform                  = CreateSpell(232698),
  VampiricTouch               = CreateSpell(34914),
  VoidBolt                    = CreateSpell(205448),
  VoidEruption                = CreateSpell(228260), -- Splash, 10
  -- Talents
  DarkAscension               = CreateSpell(391109),
  Deathspeaker                = CreateSpell(392507),
  DevouringPlague             = CreateSpell(335467),
  Dispersion                  = CreateSpell(47585),
  DistortedReality            = CreateSpell(409044),
  DivineStar                  = CreateSpell(122121),
  Halo                        = CreateSpell(120644),
  InescapableTorment          = CreateSpell(373427),
  InsidiousIre                = CreateSpell(373212),
  Mindbender                  = CreateSpell(200174),
  MindDevourer                = CreateSpell(373202),
  MindFlayInsanity            = CreateSpell(391403),
  MindMelt                    = CreateSpell(391090),
  MindSpike                   = CreateSpell(73510),
  MindSpikeInsanity           = CreateSpell(407466),
  MindsEye                    = CreateSpell(407470),
  Misery                      = CreateSpell(238558),
  PsychicLink                 = CreateSpell(199484),
  ShadowCrash                 = CreateSpell(205385), -- Splash, 8
  ShadowCrashTarget           = CreateSpell(457042),
  Silence                     = CreateSpell(15487),
  UnfurlingDarkness           = CreateSpell(341273),
  VoidTorrent                 = CreateSpell(263165),
  Voidtouched                 = CreateSpell(407430),
  WhisperingShadows           = CreateSpell(406777),
  -- Buffs
  DarkAscensionBuff           = CreateSpell(391109),
  DarkEvangelismBuff          = CreateSpell(391099),
  DeathspeakerBuff            = CreateSpell(392511),
  DevouredFearBuff            = CreateSpell(373319), -- Idol of Y'Shaarj buff
  DevouredPrideBuff           = CreateSpell(373316), -- Idol of Y'Shaarj buff
  MindDevourerBuff            = CreateSpell(373204),
  MindFlayInsanityBuff        = CreateSpell(391401),
  MindMeltBuff                = CreateSpell(391092),
  MindSpikeInsanityBuff       = CreateSpell(407468),
  ShadowformBuff              = CreateSpell(232698),
  UnfurlingDarknessBuff       = CreateSpell(341282),
  VoidformBuff                = CreateSpell(194249),
  -- Debuffs
  DevouringPlagueDebuff       = CreateSpell(335467),
  VampiricTouchDebuff         = CreateSpell(34914),
}
---@class PRCustomTable
Spell.Priest.Shadow = MergeTableByKey(Spell.Priest.Shadow, Spell.Priest.Custom)
---@class PRCommonsTable
Spell.Priest.Shadow = MergeTableByKey(Spell.Priest.Shadow, Spell.Priest.Commons, true)
---@class ArchonTable
Spell.Priest.Shadow = MergeTableByKey(Spell.Priest.Shadow, Spell.Priest.Archon)
---@class VoidweaverTable
Spell.Priest.Shadow = MergeTableByKey(Spell.Priest.Shadow, Spell.Priest.Voidweaver)

---@class DisciplineTable
Spell.Priest.Discipline = {    
    -- T29 Tier Set
    ShieldofAbsolution = CreateSpell(394624),

    -- Abilities
    DeathAndMadness = CreateSpell(321291),
    DispelMagic = CreateSpell(528),
    DesperatePrayer = CreateSpell(19236),
    HolyNova = CreateSpell(132157), -- Melee, 12
    MindBlast = CreateSpell(8092),
    PowerInfusion = CreateSpell(10060),
    PowerInfusionBuff = CreateSpell(10060),
    ShadowWordDeath = CreateSpell(32379),
    ShadowWordPain = CreateSpell(589),
    ShadowWordPainDebuff = CreateSpell(589),
    Smite = CreateSpell(585),
    PowerWordShield = CreateSpell(17),
    PowerWordFortitude = CreateSpell(21562),
    FlashHeal = CreateSpell(2061),
    AngelicFeather = CreateSpell(121536),
    ShadowPenance = CreateSpell(400169),
    ShadowDivineStar = CreateSpell(122121),
    ShadowHalo = CreateSpell(120517),
    Renew = CreateSpell(139),

    -- Talents
    Shadowfiend = CreateSpell(34433),
    BodyAndSoul = CreateSpell(64129),
    HarshDiscipline = CreateSpell(373180),
    PowerWordBarrier = CreateSpell(62618),
    LuminousBarrier = CreateSpell(271466),
    PainSuppression = CreateSpell(33206),
    VampiricEmbrace = CreateSpell(15286),
    VoidTendrils = CreateSpell(108920),
    LeapofFaith = CreateSpell(73325),
    Evangelism = CreateSpell(472433),
    LightsWrath = CreateSpell(373178),
    Rhapsody = CreateSpell(390622),
    UltimatePenitence = CreateSpell(421453),
    InescapableTorment = CreateSpell(373427),
    TwilightEquilibrium = CreateSpell(390705),
    EncroachingShadows = CreateSpell(472568),
    WealAndWoe = CreateSpell(390786),

    -- Buffs
    PowerWordFortitudeBuff = CreateSpell(21562),
    AngelicFeatherBuff = CreateSpell(121557),
    BodyAndSoulBuff = CreateSpell(65081),
    HarshDisciplineBuff = CreateSpell(373183),
    SurgeofLightBuff = CreateSpell(114255),
    Atonement = CreateSpell(194384),
    RhapsodyBuff = CreateSpell(390636),
    WealandWoeBuff = CreateSpell(390787),

    -- Debuffs
    -- Covenant Abilities
    AscendedNova = CreateSpell(325020), -- Melee, 8
    BoonoftheAscended = CreateSpell(325013),
    BoonoftheAscendedBuff = CreateSpell(325013),
    FaeGuardians = CreateSpell(327661),
    FaeGuardiansBuff = CreateSpell(327661),
    UnholyNova = CreateSpell(324724), -- Melee, 15
    -- Soulbind Abilities
    FieldofBlossoms = CreateSpell(319191),
    GroveInvigoration = CreateSpell(322721),
    PustuleEruption = CreateSpell(351094),
    VolatileSolvent = CreateSpell(323074),
    VolatileSolventHumanBuff = CreateSpell(323491),
    -- Trinket Effects
    ScarsofFraternalStrifeBuff4 = CreateSpell(368638),
    -- Other Item Effects
    TemptationBuff = CreateSpell(234143),
    -- Other
    Pool = CreateSpell(999910),
    -- Base Spells
    Penance = CreateSpell(47540),
    PowerWordRadiance = CreateSpell(194509),
    -- Talents
    Schism = CreateSpell(214621),
    Mindbender = CreateMultiSpell(123040, 34433),
    MindbenderTalent = CreateSpell(123040),
    --PowerWordSolace = CreateSpell(129250),
    ShadowCovenant = CreateSpell(314867),
    ShadowCovenantBuff = CreateSpell(322105),
    Halo = CreateSpell(120517),
    SpiritShell = CreateSpell(109964),
    PowerWordLife = CreateSpell(373481),
    PrayerofMending = CreateSpell(33076),
    PrayerofMendingBuff = CreateSpell(41635),
    DominateMind = CreateSpell(205364),

    -- hero Talents
    PremonitionOfInsight = CreateSpell(428933),
    PremonitionOfPiety = CreateSpell(428930),
    PremonitionOfSolace = CreateSpell(428934),
    PremonitionOfClairvoyance = CreateSpell(440725),

    VoidHeartBuff = CreateSpell(449887),
}
---@class PRCustomTable
Spell.Priest.Discipline = MergeTableByKey(Spell.Priest.Discipline, Spell.Priest.Custom)
---@class PRCommonsTable
Spell.Priest.Discipline = MergeTableByKey(Spell.Priest.Discipline, Spell.Priest.Commons, true)
---@class OrcaleTable
Spell.Priest.Discipline = MergeTableByKey(Spell.Priest.Discipline, Spell.Priest.Oracle)
---@class VoidweaverTable
Spell.Priest.Discipline = MergeTableByKey(Spell.Priest.Discipline, Spell.Priest.Voidweaver)

---@class PRHolyTable
Spell.Priest.Holy = {
    -- Custom
    Lightwell = CreateSpell(372835),

    -- Abilities
    DeathAndMadness = CreateSpell(321291),
    DispelMagic = CreateSpell(528),
    DesperatePrayer = CreateSpell(19236),
    HolyNova = CreateSpell(132157), -- Melee, 12
    MindBlast = CreateSpell(8092),
    PowerInfusion = CreateSpell(10060),
    PowerInfusionBuff = CreateSpell(10060),
    ShadowWordDeath = CreateSpell(32379),
    ShadowWordPain = CreateSpell(589),
    ShadowWordPainDebuff = CreateSpell(589),
    Smite = CreateSpell(585),
    PowerWordShield = CreateSpell(17),
    PowerWordFortitude = CreateSpell(21562),
    FlashHeal = CreateSpell(2061),
    Heal = CreateSpell(2060),
    -- Talents
    Shadowfiend = CreateSpell(34433),
    BodyAndSoul = CreateSpell(64129),
    VampiricEmbrace = CreateSpell(15286),
    Rhapsody = CreateSpell(390622),
    VoiceofHarmony = CreateSpell(390994),
    -- Buffs
    PowerWordFortitudeBuff = CreateSpell(21562),
    -- Debuffs
    -- Covenant Abilities
    AscendedNova = CreateSpell(325020), -- Melee, 8
    BoonoftheAscended = CreateSpell(325013),
    BoonoftheAscendedBuff = CreateSpell(325013),
    FaeGuardians = CreateSpell(327661),
    FaeGuardiansBuff = CreateSpell(327661),
    UnholyNova = CreateSpell(324724), -- Melee, 15
    -- Soulbind Abilities
    FieldofBlossoms = CreateSpell(319191),
    GroveInvigoration = CreateSpell(322721),
    PustuleEruption = CreateSpell(351094),
    VolatileSolvent = CreateSpell(323074),
    VolatileSolventHumanBuff = CreateSpell(323491),
    -- Trinket Effects
    ScarsofFraternalStrifeBuff4 = CreateSpell(368638),
    -- Other Item Effects
    TemptationBuff = CreateSpell(234143),
    -- Other
    Pool = CreateSpell(999910),
    -- Base Spells
    HolyFire = CreateSpell(14914),
    HolyFireDebuff = CreateSpell(14914),
    -- Talents
    PowerWordLife = CreateSpell(373481),

    -- Abilities
    AngelicFeather = CreateSpell(121536),
    DivineHymn = CreateSpell(64843),
    CircleofHealing = CreateSpell(204883),
    PrayerofHealing = CreateSpell(596),
    HolyWordSanctify = CreateSpell(34861),
    GuardianSpirit = CreateSpell(47788),
    HolyWordSerenity = CreateSpell(2050),
    HolyWordChastise = CreateSpell(88625),
    EmpyrealBlaze = CreateSpell(372616),

    -- Buffs
    EmpyrealBuff = CreateSpell(372617),
    LightweaverBuff = CreateSpell(390993),
    SurgeofLightBuff = CreateSpell(114255),
    SpiritofRedemption = CreateSpell(27827),
    AngelicFeatherBuff = CreateSpell(121557),
    BodyAndSoulBuff = CreateSpell(65081),
    RhapsodyBuff = CreateSpell(390636),
    ResonantWords = CreateSpell(372313),

    -- Debuffs
    ShackleUndead = CreateSpell(9484),
    HolyWordChastiseDebuff = CreateSpell(200200),

    -- Talents
    Halo = CreateSpell(120517),
    VoidTendrils = CreateSpell(108920),
    MindControl = CreateSpell(605),
    DominateMind = CreateSpell(205364),
    LeapofFaith = CreateSpell(73325),
    Renew = CreateSpell(139),
    PrayerofMending = CreateSpell(33076),
    PrayerofMendingBuff = CreateSpell(41635),
    DivineWord = CreateSpell(372760),

    -- Covenant Abilities
    Apotheosis = CreateSpell(200183),
    HolyWordSalvation = CreateSpell(265202),
    SymbolofHope = CreateSpell(64901),
}
---@class PRCustomTable
Spell.Priest.Holy = MergeTableByKey(Spell.Priest.Holy, Spell.Priest.Custom)
---@class PRCommonsTable
Spell.Priest.Holy = MergeTableByKey(Spell.Priest.Holy, Spell.Priest.Commons, true)
---@class ArchonTable
Spell.Priest.Holy = MergeTableByKey(Spell.Priest.Holy, Spell.Priest.Archon)
---@class OrcaleTable
Spell.Priest.Holy = MergeTableByKey(Spell.Priest.Holy, Spell.Priest.Oracle)


-- Items
if not Item.Priest then
    Item.Priest = {}
end

---@class PRCustomItemTable
Item.Priest.Custom = {
    IridaltheEarthsMaster                 = Item(208321, {16}),
    -- Dreambinder =Item(208321) -- TEMPORARY
}

---@class PRCommonsItemTable
Item.Priest.Commons = {
    -- TWW Trinkets
    AberrantSpellforge          = Item(212451, {13, 14}),
    SpymastersWeb               = Item(220202, {13, 14}),
}

---@class ShadowItemTable
Item.Priest.Shadow = {
      -- TWW Trinkets
  AberrantSpellforge          = Item(212451, {13, 14}),
  FlarendosPilotLight         = Item(230191, {13, 14}),
  GeargrindersSpareKeys       = Item(230197, {13, 14}),
  SpymastersWeb               = Item(220202, {13, 14}),
  -- TWW S2 Old Trinkets
  IngeniousManaBattery        = Item(169344, {13, 14}),
  -- TWW S2 Old Items
  HyperthreadWristwraps       = Item(168989, {9}),
  NeuralSynapseEnhancer       = Item(168973, {16}),
}
---@class PRCustomItemTable
Item.Priest.Shadow = MergeTableByKey(Item.Priest.Custom, Item.Priest.Shadow)
---@class PRCommonsItemTable
Item.Priest.Shadow = MergeTableByKey(Item.Priest.Commons, Item.Priest.Shadow)

---@class DisciplineItemTable
Item.Priest.Discipline = {
}
---@class PRCustomItemTable
Item.Priest.Discipline = MergeTableByKey(Item.Priest.Custom, Item.Priest.Discipline)
---@class PRCommonsItemTable
Item.Priest.Discipline = MergeTableByKey(Item.Priest.Commons, Item.Priest.Discipline)

---@class HolyItemTable
Item.Priest.Holy = {
}
---@class PRCustomItemTable
Item.Priest.Holy = MergeTableByKey(Item.Priest.Custom, Item.Priest.Holy)
---@class PRCommonsItemTable
Item.Priest.Holy = MergeTableByKey(Item.Priest.Commons, Item.Priest.Holy)

-- HeroDBC doesn't include Penance nor Dark Reprimand in their table. So we create custom one. 
-- Must place it here, else it will be overwritten by mistake later on in the original HeroDBC file.
HeroDBC.DBC.SpellDuration[47540] = { 2000, 3200 }
HeroDBC.DBC.SpellDuration[400169] = { 2000, 3200 }

-- Generic
Spell.Priest.Discipline.ShadowWordPain:SetGeneric(PRIEST_DISCIPLINE_SPECID, 'Generic1')
Spell.Priest.Discipline.Penance:SetGeneric(PRIEST_DISCIPLINE_SPECID, "Generic2")
Spell.Priest.Discipline.ShadowWordDeath:SetGeneric(PRIEST_DISCIPLINE_SPECID, "Generic3")
Spell.Priest.Discipline.DispelMagic:SetGeneric(PRIEST_DISCIPLINE_SPECID, "Generic4")
Spell.Priest.Discipline.DominateMind:SetGeneric(PRIEST_DISCIPLINE_SPECID, "Generic5")
Spell.Priest.Discipline.ShackleUndead:SetGeneric(PRIEST_DISCIPLINE_SPECID, "Generic6")

Spell.Priest.Discipline.PsychicScream.MeleeRange = 8
Spell.Priest.Discipline.DivineStar.Range = 30
Spell.Priest.Discipline.ShadowDivineStar.Range = 30
Spell.Priest.Discipline.Halo.Range = 30
Spell.Priest.Discipline.ShadowHalo.Range = 30
Spell.Priest.Discipline.VoidTendrils.MeleeRange = 8
Spell.Priest.Discipline.HolyNova.MeleeRange = 12
Spell.Priest.Discipline.UnholyNova.MeleeRange = 15
Spell.Priest.Discipline.PremonitionOfInsight.ForceDisplaySpellList = 428924
Spell.Priest.Discipline.PremonitionOfPiety.ForceDisplaySpellList = 428924
Spell.Priest.Discipline.PremonitionOfSolace.ForceDisplaySpellList = 428924
Spell.Priest.Discipline.PremonitionOfClairvoyance.ForceDisplaySpellList = 428924

Spell.Priest.Shadow.VampiricTouch:SetGeneric(PRIEST_SHADOW_SPECID, "Generic1")
Spell.Priest.Shadow.ShadowWordPain:SetGeneric(PRIEST_SHADOW_SPECID, "Generic2")
Spell.Priest.Shadow.ShadowWordDeath:SetGeneric(PRIEST_SHADOW_SPECID, "Generic3")
Spell.Priest.Shadow.DevouringPlague:SetGeneric(PRIEST_SHADOW_SPECID, "Generic4")
Spell.Priest.Shadow.DispelMagic:SetGeneric(PRIEST_SHADOW_SPECID, "Generic5")
Spell.Priest.Shadow.PsychicHorror:SetGeneric(PRIEST_SHADOW_SPECID, "Generic6")
Spell.Priest.Shadow.ShackleUndead:SetGeneric(PRIEST_SHADOW_SPECID, "Generic7")
Spell.Priest.Shadow.DominateMind:SetGeneric(PRIEST_SHADOW_SPECID, "Generic8")

Spell.Priest.Shadow.PsychicScream.MeleeRange = 8
Spell.Priest.Shadow.VoidTendrils.MeleeRange = 8
Spell.Priest.Shadow.ShadowCrash.Range = 40

Spell.Priest.Holy.ShadowWordPain:SetGeneric(PRIEST_HOLY_SPECID, "Generic1")
Spell.Priest.Holy.ShadowWordDeath:SetGeneric(PRIEST_HOLY_SPECID, "Generic2")
Spell.Priest.Holy.DispelMagic:SetGeneric(PRIEST_HOLY_SPECID, "Generic3")
Spell.Priest.Holy.HolyWordChastise:SetGeneric(PRIEST_HOLY_SPECID, "Generic4")
Spell.Priest.Holy.DominateMind:SetGeneric(PRIEST_HOLY_SPECID, "Generic5")
Spell.Priest.Holy.ShackleUndead:SetGeneric(PRIEST_HOLY_SPECID, "Generic6")

Spell.Priest.Holy.PsychicScream.MeleeRange = 8
Spell.Priest.Holy.DivineStar.Range = 30
Spell.Priest.Holy.Halo.Range = 30
Spell.Priest.Holy.VoidTendrils.MeleeRange = 8
Spell.Priest.Holy.HolyNova.MeleeRange = 12
Spell.Priest.Holy.UnholyNova.MeleeRange = 15

Priest.db.specMaxILvl = { -- Item level of SimCraft profiles (treated as max ilvl)
    [250] = 489.1, -- Blood DK
    [251] = 489.1, -- Frost DK
    [252] = 489.1, -- Unholy DK
    [577] = 488.6, -- Havoc DH
    [581] = 488.9, -- Vengeance DH
    [102] = 488.6, -- Balance Druid
    [103] = 488.6, -- Feral Druid
    [104] = 488.6, -- Guardian Druid
    [253] = 488.6, -- Beast Mastery Hunter
    [254] = 488.6, -- Marskmanship Hunter
    [255] = 488.6, -- Survival Hunter
    [268] = 489.1, -- Brewmaster Monk
    [269] = 488.6, -- Windwalker Monk
    [66] = 489.1, -- Protection Paladin
    [70] = 489.5, -- Retribution Paladin
    [259] = 488.6, -- Assassination Rogue
    [260] = 488.6, -- Outlaw Rogue
    [261] = 488.4, -- Subtlety Rogue
    [71] = 489.1, -- Arms Warrior
    [72] = 489.5, -- Fury Warrior
    [73] = 489.1, -- Protection Warrior
    [1467] = 488.6, -- Devestation Evoker
    [1473] = 488.6, -- Augmentation Evoker
    [265] = 488.6, -- Affliction Warlock
    [266] = 488.6, -- Demonology Warlock
    [267] = 488.6, -- Destruction Warlock
    [258] = 488.6, -- Shadow Priest
    [262] = 488.6, -- Elemental Shaman
    [263] = 488.6, -- Enhancment Shaman
    [62] = 488.6, -- Arcane Mage
    [63] = 488.6, -- Fire Mage
    [64] = 488.6, -- Frost Mage
}
Priest.db.specValueST = { -- Information provided by Bloodmallet for Castingpatchwerk
    [250] = 3625 * 0.9, --Blood Death Knight
    [251] = 6268, --Frost Death Knight
    [252] = 14380, --Unholy Death Knight
    [577] = 8205, --Havoc Demon Hunter
    [581] = 5660 * 0.9, --Vengeance Demon Hunter
    [102] = 6995, --Balance Druid
    [103] = 6645, --Feral Druid
    [104] = 4936 * 0.9, --Guardian Druid
    [1467] = 10668, --Devastation Evoker
    [1473] = 2197, --Augmentation Evoker
    [253] = 13265, --Beast_Mastery Hunter
    [254] = 10197, --Marksmanship Hunter
    [255] = 7307, --Survival Hunter
    [62] = 4882, --Arcane Mage
    [63] = 6618, --Fire Mage
    [64] = 6366, --Frost Mage
    [268] = 2600 * 0.9, --Brewmaster Monk
    [269] = 5751, --Windwalker Monk
    [66] = 5378 * 0.9, --Protection Paladin
    [70] = 8279, --Retribution Paladin
    [258] = 6928, --Shadow Priest
    [259] = 12950, --Assassination Rogue
    [260] = 4360, --Outlaw Rogue
    [261] = 5072, --Subtlety Rogue
    [262] = 6285, --Elemental Shaman
    [263] = 6709, --Enhancement Shaman
    [265] = 9153, --Affliction Warlock
    [266] = 7460, --Demonology Warlock
    [267] = 7796, --Destruction Warlock
    [71] = 8111, --Arms Warrior
    [72] = 5140, --Fury Warrior
    [73] = 6142 * 0.9, --Protection Warrior
}
Priest.db.specValueAOE3 = { -- Information provided by Bloodmallet for Castingpatchwerk3
    [250] = 5471 * 0.9, --Blood Death Knight
    [251] = 16360, --Frost Death Knight
    [252] = 13275, --Unholy Death Knight
    [577] = 14904, --Havoc Demon Hunter
    [581] = 10304 * 0.9, --Vengeance Demon Hunter
    [102] = 14671, --Balance Druid
    [103] = 12446, --Feral Druid
    [104] = 7617 * 0.9, --Guardian Druid
    [1467] = 15662, --Devastation Evoker
    [253] = 24262, --Beast_Mastery Hunter
    [254] = 19157, --Marksmanship Hunter
    [255] = 13091, --Survival Hunter
    [62] = 9345, --Arcane Mage
    [63] = 11370, --Fire Mage
    [64] = 10632, --Frost Mage
    [268] = 6572 * 0.9, --Brewmaster Monk
    [269] = 9911, --Windwalker Monk
    [66] = 7911 * 0.9, --Protection Paladin
    [70] = 10018, --Retribution Paladin
    [258] = 12236, --Shadow Priest
    [259] = 19294, --Assassination Rogue
    [260] = 4792, --Outlaw Rogue
    [261] = 7159, --Subtlety Rogue
    [262] = 11472, --Elemental Shaman
    [263] = 12354, --Enhancement Shaman
    [265] = 15839, --Affliction Warlock
    [266] = 10920, --Demonology Warlock
    [267] = 8524, --Destruction Warlock
    [71] = 14305, --Arms Warrior
    [72] = 7759, --Fury Warrior
    [73] = 9108 * 0.9, --Protection Warrior
}

Priest.db.specValueAOE5 = { -- Information provided by Bloodmallet for Castingpatchwerk5
    [250] = 8124 * 0.9, --Blood Death Knight
    [251] = 20288, --Frost Death Knight
    [252] = 19871, --Unholy Death Knight
    [577] = 19230, --Havoc Demon Hunter
    [581] = 14265 * 0.9, --Vengeance Demon Hunter
    [102] = 29811, --Balance Druid
    [103] = 18334, --Feral Druid
    [104] = 8602 * 0.9, --Guardian Druid
    [1467] = 23257, --Devastation Evoker
    [253] = 37667, --Beast_Mastery Hunter
    [254] = 30602, --Marksmanship Hunter
    [255] = 18340, --Survival Hunter
    [62] = 11193, --Arcane Mage
    [63] = 14315, --Fire Mage
    [64] = 11136, --Frost Mage
    [268] = 7404 * 0.9, --Brewmaster Monk
    [269] = 11857, --Windwalker Monk
    [66] = 10969 * 0.9, --Protection Paladin
    [70] = 13037, --Retribution Paladin
    [258] = 15659, --Shadow Priest
    [259] = 30078, --Assassination Rogue
    [260] = 3544, --Outlaw Rogue
    [261] = 8589, --Subtlety Rogue
    [262] = 10795, --Elemental Shaman
    [263] = 14315, --Enhancement Shaman
    [265] = 27021, --Affliction Warlock
    [266] = 15022, --Demonology Warlock
    [267] = 19004, --Destruction Warlock
    [71] = 20764, --Arms Warrior
    [72] = 11060, --Fury Warrior
    [73] = 11941 * 0.9, --Protection Warrior
}

function Priest.EvaluatePlayerPotential(unit, specID, ilvl, scenario)
    local maxIlvl = Priest.db.specMaxILvl[specID] or 0
    local baseValue = 0

    if scenario < 3 then
        baseValue = Priest.db.specValueST[specID] or 0
    end
    if baseValue == 0 and scenario > 2 and scenario < 5 then
        baseValue = Priest.db.specValueAOE3[specID] or 0
    end
    if baseValue == 0 and scenario > 4 then
        baseValue = Priest.db.specValueAOE5[specID] or 0
    end

    if maxIlvl > 0 then
        local potential = baseValue * (ilvl / maxIlvl)
        return potential
    else
        return 0
    end
end

function Priest.SortMembersPI(members, scenario)
    table.sort(members, function(x, y)
        local xIlvl = x.ItemLevel -- Assuming x has an ItemLevel field
        local yIlvl = y.ItemLevel -- Assuming y has an ItemLevel field
        local xSpecID = x:SpecID()-- Assuming x has a SpecID field
        local ySpecID = y:SpecID() -- Assuming y has a SpecID field
        local xPotential = Priest.EvaluatePlayerPotential(x.Unit, xSpecID, xIlvl, scenario)
        local yPotential = Priest.EvaluatePlayerPotential(y.Unit, ySpecID, yIlvl, scenario)
        return xPotential > yPotential
    end)
end


if Player:Class() == "PRIEST" then
    StaticPopupDialogs["PRIESTPOPUP"] = {
        text = "??: It seems you are Ventyhr.\nYou may have issues with the talent Mind Games.\n\nPlease change your Covenant in Oribos.",
        button1 = "OK",
    }

    if _G['IsSpellKnown'](323673) then
        StaticPopup_Show("PRIESTPOPUP")
    end

    -- This will following code will work for all 3 specs, so no need to create another one for Discipline.
    if Spell.Priest.Holy.ImprovedPurify:IsAvailable() then
        MainAddon.CONST.DispelList.Purify.DispelFlag = MainAddon.CONST.DispelFlag.Magic + MainAddon.CONST.DispelFlag.Disease
    else
        MainAddon.CONST.DispelList.Purify.DispelFlag = MainAddon.CONST.DispelFlag.Magic
    end

    HL:RegisterForEvent(function()
        if Spell.Priest.Holy.ImprovedPurify:IsAvailable() then
            MainAddon.CONST.DispelList.Purify.DispelFlag = MainAddon.CONST.DispelFlag.Magic + MainAddon.CONST.DispelFlag.Disease
        else
            MainAddon.CONST.DispelList.Purify.DispelFlag = MainAddon.CONST.DispelFlag.Magic
        end
    end, "PLAYER_TALENT_UPDATE", "TRAIT_CONFIG_UPDATED")

    ---@param rootDescription Menu
    local function RightClick(_, rootDescription, contextData)
        local unit = contextData.unit
        local unitName = contextData.name
    
        if Cache.Persistent.Player.Class[2] ~= "PRIEST" then
            return
        end
    
        if not unit or UnitCanAttack(Player:ID(), unit) then
            return
        end
    
        local SetUnset = Priest.PiTargetName == "None" and "Set" or Priest.PiTargetName == unitName and "Unset" or "Set"
        local colorCode = "|cff00ff00"
        local iconID = 135939
    
        rootDescription:CreateButton("|T" .. iconID .. ":24|t" .. " " .. colorCode .. SetUnset .. " Power Infusion" .. "|r", function()
            if Priest.PiTargetName == "None" then
                Priest.PiTargetName = unitName
                MainAddon:Print("Power Infusion on: " .. unitName .. " ", true, 2)
            else
                if Priest.PiTargetName == unitName then
                    Priest.PiTargetName = "None"
                    MainAddon:Print("Power Infusion removed for: " .. unitName .. " ", false, 2)
                else
                    MainAddon:Print("Power Infusion removed for: " .. Priest.PiTargetName .. " ", false, 2)
                    Priest.PiTargetName = unitName
                    MainAddon:Print("Power Infusion on: " .. unitName .. " ", true, 2)
                end
            end
        end)
    end
    Menu.ModifyMenu("MENU_UNIT_SELF", RightClick);
    Menu.ModifyMenu("MENU_UNIT_PLAYER", RightClick);
    Menu.ModifyMenu("MENU_UNIT_TARGET", RightClick);
    Menu.ModifyMenu("MENU_UNIT_FOCUS", RightClick);
    Menu.ModifyMenu("MENU_UNIT_RAID_PLAYER", RightClick);
    Menu.ModifyMenu("MENU_UNIT_PARTY", RightClick);
    
    local PISpellIDs = {
        [102560] = true, -- Boomkin: Incarn
        [194223] = true, -- Boomkin: Incarn
        [106951] = true, -- Feral Druid: Berserk
        [102543] = true, -- Feral Druid: Incarnation

        [190319] = true, -- Fire Mage: Combustion
        [12472] = true, -- Frost Mage: Icy Veins
        [365350] = true, -- Arcane Mage: Arcane Surge

        [231895] = true, -- Ret Paladin: Crusade
        [31884] = true, -- Ret Paladin: Avenging Wrath

        [375087] = true, -- Devestation Evoker: Dragonrage

        [107574] = true, -- Arms/Fury Warrior: Avatar

        [191427] = true, -- Demon Hunter: Meta

        [360952] = true, -- Survival Hunter: Coordinated Assault
        [359844] = true, -- BM Hunter: Call of the Wild
        [288613] = true, -- Marksman Hunter: Trueshot

        [114051] = true, -- Enhancement Shaman: Ascendance
        [114050] = true, -- Elemental Shaman: Ascendance

        [51271] = true, -- Frost Death Knight: Pillar of Frost
        [63560] = true, -- UDK: Dark Transformation

        [360194] = true, -- Assasination Rogue: Deathmark
        [121471] = true, -- Subtlety Rogue: Shadow Blades

        [123904] = true, -- Windwalker Monk: Invoke Xuen

        [265187] = true, -- Demo Lock: Tyrant
        [111898] = true, -- Demo Lock: Felguard
        [1122] = true, -- Destro Lock: Rain of Fire
        [205180] = true, -- Aff Lock: Darkglare
    }

    local PISpellIDs_LargerTable = {
        [102560] = true, -- Boomkin: Incarn
        [194223] = true, -- Boomkin: Incarn
        [106951] = true, -- Feral Druid: Berserk
        [102543] = true, -- Feral Druid: Incarnation

        [190319] = true, -- Fire Mage: Combustion
        [12472] = true, -- Frost Mage: Icy Veins
        [365350] = true, -- Arcane Mage: Arcane Surge

        [231895] = true, -- Ret Paladin: Crusade
        [31884] = true, -- Ret Paladin: Avenging Wrath

        [375087] = true, -- Devestation Evoker: Dragonrage

        [107574] = true, -- Arms/Fury Warrior: Avatar

        [191427] = true, -- Demon Hunter: Meta

        [360952] = true, -- Survival Hunter: Coordinated Assault
        [359844] = true, -- BM Hunter: Call of the Wild
        [288613] = true, -- Marksman Hunter: Trueshot
        [19574] = true, -- BM Hunter: Bestial Wrath

        [114051] = true, -- Enhancement Shaman: Ascendance
        [114050] = true, -- Elemental Shaman: Ascendance
        [51533] = true, -- Enhancement Shaman: Feral Spirit
        [198067] = true, -- Elemental Shaman: Fire Elemental

        [51271] = true, -- Frost Death Knight: Pillar of Frost
        [279302] = true, -- Frost Death Knight: Frostwyrm's Fury
        [63560] = true, -- UDK: Dark Transformation

        [360194] = true, -- Assasination Rogue: Deathmark
        [121471] = true, -- Subtlety Rogue: Shadow Blades
        [13750] = true, -- Outlaw Rogue: Adrenaline Rush
        [185313] = true, -- Outlaw Rogue: Adrenaline Rush

        [123904] = true, -- Windwalker Monk: Invoke Xuen
        [137639] = true, -- Windwalker Monk: Storm, Earth, and Fire

        [265187] = true, -- Demo Lock: Tyrant
        [111898] = true, -- Demo Lock: Felguard
        [1122] = true, -- Destro Lock: Rain of Fire
        [205180] = true, -- Aff Lock: Darkglare
    }
    
    -- Spec priority tiers for Power Infusion
    local PIMaxValueSpecs = {
        253, -- Hunter (Beast Master)
        259, -- Rogue (Assassination)
        577, -- Demon Hunter (Havoc)
        1467, -- Evoker (Devastation)
        267, -- Warlock (Destruction)
        102, -- Druid (Balance)
        265, -- Warlock (Affliction)
        266, -- Warlock (Demonology)
        251, -- Death Knight (Frost)
        63, -- Mage (Fire)
        262, -- Shaman (Elemental)
        70, -- Paladin (Retribution)
        254, -- Hunter (Marksman)
        71, -- Warrior (Arms)
        72, -- Warrior (Fury)
        263, -- Shaman (Enhancement)
        255, -- Hunter (Survival)
        64, -- Mage (Frost)
        103, -- Druid (Feral)
        269, -- Monk (Windwalker)
        62, -- Mage (Arcane)
        252, -- Unholy DK
    }
    
    local PIHighValueSpecs = {}
    local PIMedValueSpecs = {}
    local PILowValueSpecs = {
        261, -- Rogue (Subtlety)
        260, -- Rogue (Outlaw)
    }

    local PIBlacklistSpecs = {
        -- Tanks
        [73] = true, -- Warrior (Protection)
        [66] = true, -- Paladin (Protection)
        [250] = true, -- Death Knight (Blood)
        [268] = true, -- Monk (Brewmaster)
        [581] = true, -- Demon Hunter (Vengeance)
        [104] = true, -- Druid (Guardian)

        -- Healers
        [65] = true, -- Paladin (Holy)
        [256] = true, -- Priest (Discipline)
        [257] = true, -- Priest (Holy)
        [264] = true, -- Shaman (Restoration)
        [270] = true, -- Monk (Mistweaver)
        [1468] = true, -- Evoker (Preservation)
        [1473] = true, -- Evoker (Augmentation)
        [105] = true, -- Druid (Restoration)
    }
    
    Priest.PILastSuggestionTime = 0
    local PI_SUGGESTION_TIMEOUT = 3
    local GroupMembers_Cache = {}
    local SpellName_Cache = {}

    HL:RegisterForEvent(function(_, unit, _, spellID)
        if Cache.Persistent.Player.SpecID == 258 or not Priest.PiTargetName_Auto_Enabled then
            return
        end

        if not GroupMembers_Cache[unit] then
            return
        end
        
        if PISpellIDs[spellID] and Priest.PiTargetName == "None" or PISpellIDs_LargerTable[spellID] and Priest.PiTargetName == GroupMembers_Cache[unit].name then
            local now = GetTime()
            
            if now - Priest.PILastSuggestionTime < PI_SUGGESTION_TIMEOUT then
                return
            end

            local unitSpecID = GroupMembers_Cache[unit].specID
            if unitSpecID == 0 or PIBlacklistSpecs[unitSpecID] then
                return
            end
            
            local piSpell = Spell.Priest.Commons.PowerInfusion
            local piCooldown = piSpell:CooldownRemains(nil, true)
            
            if piCooldown < 5 then
                if Priest.PiFightRemains < 15 then
                    return
                end

                local unitName = GroupMembers_Cache[unit].name
                local spellName = ""
                if not SpellName_Cache[spellID] then
                    local spellData = GetSpellInfo(spellID)
                    spellName = spellData.name or ""
                    SpellName_Cache[spellID] = spellName
                else
                    spellName = SpellName_Cache[spellID]
                end
                                
                Priest.PiTargetName_Auto = unitName
                Priest.PILastSuggestionTime = now
                
                if Priest.PiTargetName_Auto_Toast and piCooldown == 0 then
                    MainAddon.UI:ShowToast("Power Infusion", unitName .. " has casted " .. spellName .. "!", MainAddon.GetTexture(piSpell), 5)
                end
                                    
                C_Timer.After(PI_SUGGESTION_TIMEOUT, function()
                    if Priest.PiTargetName_Auto == unitName then
                        Priest.PiTargetName_Auto = ""
                    end
                end)
            end
        end
    end, "UNIT_SPELLCAST_SUCCEEDED")

    HL:RegisterForEvent(function()
        GroupMembers_Cache = {}
        local InRaid = IsInRaid()
        local Units = InRaid and "raid" or "party"

        for i = 1, GetNumGroupMembers() do
            local unit = Units .. i
            local guid = UnitGUID(unit) or ""
            local specID = MainAddon.SpecIDByGUID(guid) or 0
            local name = UnitName(unit) or "unknown"
            if unit then
                GroupMembers_Cache[unit] = { specID = specID, name = name, guid = guid }
            end
        end
    end, "GROUP_ROSTER_UPDATE", "GROUP_JOINED", "GROUP_LEFT", "PLAYER_ENTERING_WORLD")
end