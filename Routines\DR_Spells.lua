---@class MainAddon
local MainAddon = MainAddon
-- HeroLib
local HL = HeroLibEx
---@class Spell
local Spell = HL.Spell
local CreateMultiSpell = MainAddon.CreateMultiSpell
local CreateSpell = MainAddon.CreateSpell
---@class Item
local Item = HL.Item
local MergeTableByKey = HL.Utils.MergeTableByKey

MainAddon.Druid = {}
MainAddon.Druid.InnervateTargetName = "None"

--- ============================ CONTENT ============================
-- Spell
if not Spell.Druid then
    Spell.Druid = {}
end

---@class DRCustomTable
Spell.Druid.Custom = {
    -- Forms
    MountForm = CreateSpell(210053),
    TravelForm = CreateSpell(783),

    -- Abilities
    UrsineVigor = CreateSpell(377842),
    UrsineVigorBuff = CreateSpell(393903),
    Cyclone = CreateSpell(33786),
    MassEntanglement = CreateSpell(102359),
    Hibernate = CreateSpell(2637),
    UrsolVortexDebuff = CreateSpell(127797),
    Rebirth = CreateSpell(20484),
    Ironbark = CreateSpell(102342),
    Dash = CreateSpell(1850),
    TigerDash = CreateSpell(252216),
    Typhoon = CreateSpell(132469),
    Soothe = CreateSpell(2908),
    StampedingRoar = CreateSpell(106898),
    IncapacitatingRoar = CreateSpell(99),
    WildChargeHumanoid = CreateSpell(102401),
    WildChargeBalance = CreateSpell(102383),
    UrsolVortex = CreateSpell(102793),
    Revive = CreateSpell(50769),
    Revitalize = CreateSpell(212040),
    EntanglingRoots = CreateSpell(339),
    SolarBeam = CreateSpell(78675),
    RemoveCorruption = CreateSpell(2782),
    Rejuvenation = CreateSpell(774),
    FelinePotential = CreateSpell(441702),
    LunarCalling = CreateSpell(429523),
    BoundlessMoonlight = CreateSpell(424058),
    LunarInsight = CreateSpell(429530),

    -- Restoration
    GroveGuardians = CreateSpell(102693),

    Mangle = CreateSpell(33917),
    ThrashDebuffBear = CreateSpell(192090),
    SwipeBear = CreateSpell(213771),

    
    -- PvP
    FaerieSwarm = CreateSpell(209749),
    DemoralizingRoar = CreateSpell(201664),
    Overrun = CreateSpell(202246),
    EmeraldSlumber = CreateSpell(329042),
    AlphaChallenge = CreateSpell(207017),
    GroveProtection = CreateSpell(354654),
    Thorns = CreateSpell(305496),
}

---@class DRCommonsTable
Spell.Druid.Commons = {
  -- Racials
  Berserking                            = CreateSpell(26297),
  Shadowmeld                            = CreateSpell(58984),
  -- Abilities
  Barkskin                              = CreateSpell(22812),
  BearForm                              = CreateSpell(5487),
  CatForm                               = CreateSpell(768),
  FerociousBite                         = CreateSpell(22568),
  MarkoftheWild                         = CreateSpell(1126),
  Moonfire                              = CreateSpell(8921),
  Prowl                                 = CreateMultiSpell(5215,102547),
  Regrowth                              = CreateSpell(8936),
  Shred                                 = CreateSpell(5221),
  -- Talents
  AstralInfluence                       = CreateSpell(197524),
  ConvoketheSpirits                     = CreateSpell(391528),
  FluidForm                             = CreateSpell(449193),
  FrenziedRegeneration                  = CreateSpell(22842),
  HeartoftheWild                        = CreateSpell(319454),
  InnerResilienceBuff                   = CreateSpell(450706),
  Innervate                             = CreateSpell(29166),
  Ironfur                               = CreateSpell(192081),
  LycarasMeditation                     = CreateSpell(474728),
  Maim                                  = CreateSpell(22570),
  MightyBash                            = CreateSpell(5211),
  MoonkinForm                           = CreateMultiSpell(24858,197625),
  NaturesVigil                          = CreateSpell(124974),
  PrimalFury                            = CreateSpell(159286),
  ProtectorofthePack                    = CreateSpell(378986),
  Rake                                  = CreateSpell(1822),
  Renewal                               = CreateSpell(108238),
  Rip                                   = CreateSpell(1079),
  SkullBash                             = CreateSpell(106839),
  Starfire                              = CreateSpell(194153),
  Starsurge                             = CreateMultiSpell(78674,197626),
  Sunfire                               = CreateSpell(93402),
  SurvivalInstincts                     = CreateSpell(61336),
  SymbioticRelationship                 = CreateSpell(474750),   
  ThrashBear                            = CreateSpell(77758),
  ThrashCat                             = CreateSpell(106830),
  Typhoon                               = CreateSpell(132469),
  WildCharge                            = CreateMultiSpell(16979,49376),
  -- Buffs
  FrenziedRegenerationBuff              = CreateSpell(22842),
  HeartoftheWildBuff                    = CreateSpell(319454),
  IronfurBuff                           = CreateSpell(192081),
  MarkoftheWildBuff                     = CreateSpell(1126),
  PoPHealBuff                           = CreateSpell(395336),
  SpymastersReportBuff                  = CreateSpell(451199), -- Stacking buff from before using Spymaster's Web trinket
  SpymastersWebBuff                     = CreateSpell(444959), -- Buff from using Spymaster's Web trinket
  SymbioticRelationshipBuff             = CreateMultiSpell(474754,474750),   
  -- Debuffs
  MoonfireDebuff                        = CreateSpell(164812),
  RakeDebuff                            = CreateSpell(155722),
  RipDebuff                             = CreateSpell(1079),
  SunfireDebuff                         = CreateSpell(164815),
  ThrashBearDebuff                      = CreateSpell(192090),
  ThrashCatDebuff                       = CreateSpell(405233),
  -- Other
  Pool                                  = CreateSpell(999910)
}

---@class DruidoftheClawTable
Spell.Druid.DruidoftheClaw = {
  -- Abilities
  RavageAbilityBear                     = CreateSpell(441605),
  RavageAbilityCat                      = CreateSpell(441591),
  -- Talents
  FountofStrength                       = CreateSpell(441675),
  Ravage                                = CreateSpell(441583),
  WildpowerSurge                        = CreateSpell(441691),
  -- Buffs
  FelinePotentialBuff                   = CreateSpell(441701),
  RavageBuffFeral                       = CreateSpell(441585),
  RavageBuffGuardian                    = CreateSpell(441602),
}

---@class ElunesChosenTable
Spell.Druid.ElunesChosen = {
  -- Talents
  BoundlessMoonlight                    = CreateSpell(424058),
  LunarCalling                          = CreateSpell(429523),
  LunarInsight                          = CreateSpell(429530),
  MoonGuardian                          = CreateSpell(429520),
}

---@class KeeperoftheGroveTable
Spell.Druid.KeeperoftheGrove = {
  -- Talents
  ControloftheDream                     = CreateSpell(434249),
  EarlySpring                           = CreateSpell(428937),
  PoweroftheDream                       = CreateSpell(434220),
  TreantsoftheMoon                      = CreateSpell(428544),
  -- Buffs
  HarmonyoftheGroveBuff                 = CreateSpell(428735),
}

---@class WildstalkerTable
Spell.Druid.Wildstalker = {
  -- Debuffs
  BloodseekerVinesDebuff                = CreateSpell(439531),
}

---@class BalanceTable
Spell.Druid.Balance = {
    -- Abilities
    EclipseLunar                          = CreateSpell(48518),
    EclipseSolar                          = CreateSpell(48517),
    Starfall                              = CreateSpell(191034),
    Wrath                                 = CreateSpell(190984),
    -- Talents
    AetherialKindling                     = CreateSpell(327541),
    AstralCommunion                       = CreateSpell(400636),
    AstralSmolder                         = CreateSpell(394058),
    BalanceofAllThings                    = CreateSpell(394048),
    CelestialAlignment                    = CreateMultiSpell(194223,383410), -- 194223 without Orbital Strike, 383410 with Orbital Strike
    ElunesGuidance                        = CreateSpell(393991),
    ForceofNature                         = CreateSpell(205636),
    FungalGrowth                          = CreateSpell(392999),
    FuryofElune                           = CreateSpell(202770),
    Incarnation                           = CreateMultiSpell(102560,390414), -- 102560 without Orbital Strike, 390414 with Orbital Strike
    IncarnationTalent                     = CreateSpell(394013),
    NaturesBalance                        = CreateSpell(202430),
    NaturesGrace                          = CreateSpell(450347),
    OrbitBreaker                          = CreateSpell(383197),
    OrbitalStrike                         = CreateSpell(390378),
    PowerofGoldrinn                       = CreateSpell(394046),
    PrimordialArcanicPulsar               = CreateSpell(393960),
    RattletheStars                        = CreateSpell(393954),
    Solstice                              = CreateSpell(343647),
    SouloftheForest                       = CreateSpell(114107),
    Starlord                              = CreateSpell(202345),
    Starweaver                            = CreateSpell(393940),
    StellarFlare                          = CreateSpell(202347),
    Swipe                                 = CreateSpell(213764),
    TouchtheCosmos                        = CreateSpell(450356),
    TwinMoons                             = CreateSpell(279620),
    UmbralEmbrace                         = CreateSpell(393760),
    UmbralIntensity                       = CreateSpell(383195),
    WaningTwilight                        = CreateSpell(393956),
    WarriorofElune                        = CreateSpell(202425),
    WhirlingStars                         = CreateSpell(468743),
    WildMushroom                          = CreateSpell(88747),
    WildSurges                            = CreateSpell(406890),
    -- New Moon Phases
    FullMoon                              = CreateSpell(274283),
    HalfMoon                              = CreateSpell(274282),
    NewMoon                               = CreateSpell(274281),
    -- Buffs
    BOATArcaneBuff                        = CreateSpell(394050),
    BOATNatureBuff                        = CreateSpell(394049),
    CABuff1                               = CreateSpell(194223),
    CABuff2                               = CreateSpell(383410),
    DreamstateBuff                        = CreateSpell(450346),
    IncarnationBuff1                      = CreateSpell(102560),
    IncarnationBuff2                      = CreateSpell(390414),
    PAPBuff                               = CreateSpell(393961),
    RattledStarsBuff                      = CreateSpell(393955),
    SolsticeBuff                          = CreateSpell(343648),
    StarfallBuff                          = CreateSpell(191034),
    StarlordBuff                          = CreateSpell(279709),
    StarweaversWarp                       = CreateSpell(393942),
    StarweaversWeft                       = CreateSpell(393944),
    TouchtheCosmosBuff                    = CreateSpell(450360),
    UmbralEmbraceBuff                     = CreateSpell(393763),
    WarriorofEluneBuff                    = CreateSpell(202425),
    -- Debuffs
    FungalGrowthDebuff                    = CreateSpell(81281),
    StellarFlareDebuff                    = CreateSpell(202347),
    -- Tier 29 Effects
    GatheringStarstuff                    = CreateSpell(394412),
    -- Legendary Effects
    BOATArcaneLegBuff                     = CreateSpell(339946),
    BOATNatureLegBuff                     = CreateSpell(339943),
    OnethsClearVisionBuff                 = CreateSpell(339797),
    OnethsPerceptionBuff                  = CreateSpell(339800),
    TimewornDreambinderBuff               = CreateSpell(340049)
}
---@class DRCustomTable
Spell.Druid.Balance = MergeTableByKey(Spell.Druid.Balance, Spell.Druid.Custom)
---@class DRCommonsTable
Spell.Druid.Balance = MergeTableByKey(Spell.Druid.Balance, Spell.Druid.Commons, true)
---@class DruidoftheClawTable
Spell.Druid.Balance = MergeTableByKey(Spell.Druid.Balance, Spell.Druid.DruidoftheClaw)
---@class ElunesChosenTable
Spell.Druid.Balance = MergeTableByKey(Spell.Druid.Balance, Spell.Druid.ElunesChosen)
---@class KeeperoftheGroveTable
Spell.Druid.Balance = MergeTableByKey(Spell.Druid.Balance, Spell.Druid.KeeperoftheGrove)
---@class WildstalkerTable
Spell.Druid.Balance = MergeTableByKey(Spell.Druid.Balance, Spell.Druid.Wildstalker)


---@class FeralTable
Spell.Druid.Feral = {
  -- Abilties
  -- Talents
  AdaptiveSwarm                         = CreateSpell(391888),
  ApexPredatorsCraving                  = CreateSpell(391881),
  AshamanesGuidance                     = CreateSpell(391548),
  Berserk                               = CreateSpell(106951),
  BerserkHeartoftheLion                 = CreateSpell(391174),
  Bloodtalons                           = CreateSpell(319439),
  BrutalSlash                           = CreateSpell(202028),
  CircleofLifeandDeath                  = CreateSpell(400320),
  CoiledtoSpring                        = CreateSpell(449537),
  DireFixation                          = CreateSpell(417710),
  DoubleClawedRake                      = CreateSpell(391700),
  DreadfulBleeding                      = CreateSpell(391045),
  FeralFrenzy                           = CreateSpell(274837),
  FranticMomentum                       = CreateSpell(391875),
  Incarnation                           = CreateSpell(102543),
  LionsStrength                         = CreateSpell(391972),
  LunarInspiration                      = CreateSpell(155580),
  LIMoonfire                            = CreateSpell(155625), -- Lunar Inspiration Moonfire
  MomentofClarity                       = CreateSpell(236068),
  Predator                              = CreateSpell(202021),
  PrimalWrath                           = CreateSpell(285381),
  RagingFury                            = CreateSpell(391078),
  RampantFerocity                       = CreateSpell(391709),
  RipandTear                            = CreateSpell(391347),
  Sabertooth                            = CreateSpell(202031),
  SavageFury                            = CreateSpell(449645),
  SouloftheForest                       = CreateSpell(158476),
  Swipe                                 = CreateSpell(106785),
  TearOpenWounds                        = CreateSpell(391785),
  ThrashingClaws                        = CreateSpell(405300),
  TigersFury                            = CreateSpell(5217),
  UnbridledSwarm                        = CreateSpell(391951),
  Veinripper                            = CreateSpell(391978),
  WildSlashes                           = CreateSpell(390864),
  -- Buffs
  ApexPredatorsCravingBuff              = CreateSpell(391882),
  BloodtalonsBuff                       = CreateSpell(145152),
  Clearcasting                          = CreateSpell(135700),
  CoiledtoSpringBuff                    = CreateSpell(449538),
  OverflowingPowerBuff                  = CreateSpell(405189),
  PredatorRevealedBuff                  = CreateSpell(408468), -- T30 P4
  PredatorySwiftnessBuff                = CreateSpell(69369),
  SabertoothBuff                        = CreateSpell(391722),
  SmolderingFrenzyBuff                  = CreateSpell(422751), -- T31 P2
  SuddenAmbushBuff                      = CreateSpell(391974),
  -- Debuffs
  AdaptiveSwarmDebuff                   = CreateSpell(391889),
  AdaptiveSwarmHeal                     = CreateSpell(391891),
  DireFixationDebuff                    = CreateSpell(417713),
  LIMoonfireDebuff                      = CreateSpell(155625),
}
---@class DRCustomTable
Spell.Druid.Feral = MergeTableByKey(Spell.Druid.Feral, Spell.Druid.Custom)
---@class DRCommonsTable
Spell.Druid.Feral = MergeTableByKey(Spell.Druid.Feral, Spell.Druid.Commons, true)
---@class DruidoftheClawTable
Spell.Druid.Feral = MergeTableByKey(Spell.Druid.Feral, Spell.Druid.DruidoftheClaw)
---@class ElunesChosenTable
Spell.Druid.Feral = MergeTableByKey(Spell.Druid.Feral, Spell.Druid.ElunesChosen)
---@class KeeperoftheGroveTable
Spell.Druid.Feral = MergeTableByKey(Spell.Druid.Feral, Spell.Druid.KeeperoftheGrove)
---@class WildstalkerTable
Spell.Druid.Feral = MergeTableByKey(Spell.Druid.Feral, Spell.Druid.Wildstalker)

---@class GuardianTable
Spell.Druid.Guardian = {
    -- Spells
    Dreamwalk = CreateSpell(193753),
    FerociousBite = CreateSpell(22568),
    FrenziedRegeneration = CreateSpell(22842),
    Growl = CreateSpell(6795),
    Ironfur = CreateSpell(192081),
    Maim = CreateSpell(22570),
    Mangle = CreateSpell(33917),
    Moonfire = CreateSpell(8921),
    MoonfireDebuff = CreateSpell(164812),
    Shred = CreateSpell(5221),
    StampedingRoar = CreateSpell(77761),
    Swipe = CreateSpell(213771),
    Thrash = CreateSpell(77758),
    ThrashDebuff = CreateSpell(192090),
    TreantForm = CreateSpell(114282),
    Wrath = CreateSpell(5176),
    Berserk = CreateSpell(50334),
    Maul = CreateSpell(6807),

    --Common
    ConvokeTheSpirits = CreateSpell(391528),
    ConvokeTheSpiritsTalent = CreateSpell(323764),
    Incarnation = CreateSpell(102558),
    IncarnationBuff = CreateSpell(102558),
    BerserkBear = CreateSpell(50334),
    BerserkBearBuff = CreateSpell(50334),
    BerserkBuff = CreateSpell(50334),
    Berserking = CreateSpell(23505),
    EmpowerBond = CreateSpell(326446),
    RageoftheSleeper = CreateSpell(200851),
    Pulverize = CreateSpell(80313),
    SwipeBear = CreateSpell(213771),
    Rake = CreateSpell(1822),
    RakeDebuff = CreateSpell(155722),
    Rip = CreateSpell(1079),
    RipDebuff = CreateSpell(1079),
    SwipeCat = CreateSpell(106785),
    SunfireDebuff = CreateSpell(164815),
    Starsurge = CreateSpell(197626),
    LunarBeam = CreateSpell(204066),

    --Guardian
    TirelessPursuit = CreateSpell(377801),
    NaturalRecovery = CreateSpell(377796),
    ImprovedSunfire = CreateSpell(231050),
    Sunfire = CreateSpell(93402),
    AstralInfluence = CreateSpell(197524),
    NurturingInstinct = CreateSpell(33873),
    Swiftmend = CreateSpell(18562),
    VerdantHeart = CreateSpell(301768),
    ImprovedBarkskin = CreateSpell(327993),
    KillerInstinct = CreateSpell(108299),
    ThickHide = CreateSpell(16931),
    ImprovedStampedingRoar = CreateSpell(288826),
    HeartoftheWild = CreateSpell(319454),
    LycarasTeachings = CreateSpell(378988),
    MattedFur = CreateSpell(385786),
    FelineSwiftness = CreateSpell(131768),
    ImprovedRejuvenation = CreateSpell(231040),
    WildGrowth = CreateSpell(48438),
    ProtectorofthePack = CreateSpell(378986),
    WellHonedInstincts = CreateSpell(377847),
    Starfire = CreateSpell(197628),
    FrontofthePack = CreateSpell(377835),
    Gore = CreateSpell(210706),
    GoreBuff = CreateSpell(93622),
    ImprovedSurvivalInstincts = CreateSpell(328767),
    UrsocsEndurance = CreateSpell(393611),
    MangleTalent = CreateSpell(231064),
    GoryFur = CreateSpell(200854),
    VulnerableFlesh = CreateSpell(372618),
    ElunesFavored = CreateSpell(370586),
    UrsocsGuidance = CreateSpell(393414),
    IncarnationGuardianofUrsoc = CreateSpell(394786), --Multi
    ConvoketheSpirits = CreateSpell(391528), --Multi
    CircleofLifeandDeath = CreateSpell(391969),
    FuryofNature = CreateSpell(370695),
    ReinforcedFur = CreateSpell(393618),
    AftertheWildfire = CreateSpell(371905), --Multi
    GuardianofElune = CreateSpell(155578), --Multi
    BloodFrenzy = CreateSpell(203962), --Multi
    SouloftheForest = CreateSpell(158477), --Multi
    SurvivaloftheFittest = CreateSpell(203965),
    BerserkPersistence = CreateSpell(377779),
    TwinMoonfire = CreateSpell(372567),
    ScintillatingMoonlight = CreateSpell(238049),
    GalacticGuardian = CreateSpell(203964),
    GalacticGuardianBuff = CreateSpell(213708),
    LayeredMane = CreateSpell(384721),
    BerserkRavage = CreateSpell(343240),
    UrsineAdept = CreateSpell(300346),
    UrsocsFury = CreateSpell(377210), --Multi
    DreamofCenarius = CreateSpell(372119), --Multi
    RendandTear = CreateSpell(204053), --Multi
    UntamedSavagery = CreateSpell(372943), --Multi
    Reinvigoration = CreateSpell(372945),
    BerserkUncheckedAggression = CreateSpell(377623),
    Earthwarden = CreateSpell(203974),
    FlashingClaws = CreateSpell(393427),
    ViciousCycle = CreateSpell(371999),
    ViciousCycleMaulBuff = CreateSpell(372015),
    ViciousCycleMangleBuff = CreateSpell(372019),
    ToothandClaw = CreateSpell(135288),
    ToothandClawBuff = CreateSpell(135286),
    ToothandClawDebuff = CreateSpell(135601),
    InnateResolve = CreateSpell(377811),
    BristlingFur = CreateSpell(155835), --Multi
    Brambles = CreateSpell(203953), --Multi
    InfectedWounds = CreateSpell(345208),
    Raze = CreateSpell(400254),
    ThornsofIron = CreateSpell(400222),

    --Buffs
    FrenziedRegenerationBuff = CreateSpell(22842),
    IronfurBuff = CreateSpell(192081),
    DreamofCenariusBuff = CreateSpell(372152),
    StackedDeckBuff                       = CreateSpell(1218537), -- TWW S2 4pc
}
---@class DRCustomTable
Spell.Druid.Guardian = MergeTableByKey(Spell.Druid.Guardian, Spell.Druid.Custom)
---@class DRCommonsTable
Spell.Druid.Guardian = MergeTableByKey(Spell.Druid.Guardian, Spell.Druid.Commons, true)
---@class DruidoftheClawTable
Spell.Druid.Guardian = MergeTableByKey(Spell.Druid.Guardian, Spell.Druid.DruidoftheClaw)
---@class ElunesChosenTable
Spell.Druid.Guardian = MergeTableByKey(Spell.Druid.Guardian, Spell.Druid.ElunesChosen)
---@class KeeperoftheGroveTable
Spell.Druid.Guardian = MergeTableByKey(Spell.Druid.Guardian, Spell.Druid.KeeperoftheGrove)
---@class WildstalkerTable
Spell.Druid.Guardian = MergeTableByKey(Spell.Druid.Guardian, Spell.Druid.Wildstalker)

---@class DRRestorationTable
Spell.Druid.Restoration = {
    -- Abilities
    FerociousBite = CreateSpell(22568),
    Moonfire = CreateSpell(8921),
    MoonfireDebuff = CreateSpell(164812),
    Rake = CreateSpell(1822),
    RakeDebuff = CreateSpell(155722),
    Rip = CreateSpell(1079),
    RipDebuff = CreateSpell(1079),
    Shred = CreateSpell(5221),
    -- Talents
    HeartoftheWild = CreateSpell(319454),
    HeartoftheWildBuff = CreateSpell(319454),
    Swipe = CreateSpell(106785),
    ThrashDebuff = CreateSpell(405233),
    Starsurge = CreateSpell(197626),
    FrenziedRegeneration = CreateSpell(22842),
    FrenziedRegenerationBuff = CreateSpell(22842),
    Sunfire = CreateSpell(93402),
    SunfireDebuff = CreateSpell(164815),
    ImprovedSunfire = CreateSpell(231050),
    ConvoketheSpirits = CreateSpell(391528),
    ImprovedNaturesCure = CreateSpell(392378),
    ProtectorofthePack = CreateSpell(378986),
    MasterShapeshifter = CreateSpell(289237),
    -- Other
    Pool = CreateSpell(999910),

    -- Restoration
    Lifebloom = CreateSpell(33763),
    DarkTitanLifebloom = CreateSpell(188550),
    RejuvenationGermimation = CreateSpell(155777),
    WildGrowth = CreateSpell(48438),
    Swiftmend = CreateSpell(18562),
    Efflorescence = CreateSpell(145205),
    EfflorescenceIsActive = CreateSpell(145205),
    Tranquility = CreateSpell(740),
    TranquilityStack = CreateSpell(157982),
    Nourish = CreateSpell(50464),
    Overgrowth = CreateSpell(203651),
    CenarionWard = CreateSpell(102351),
    CenarionWardHealing = CreateSpell(102352),
    ClearCasting = CreateSpell(16870),
    -- Utilities
    NaturesCure = CreateSpell(88423),
    -- Talents
    Flourish = CreateSpell(197721),
    IncarnationTreeofLife = CreateSpell(33891),
    IncarnationTreeofLifeBuff = CreateSpell(117679),
    SouloftheForest = CreateSpell(158478),
    Photosynthesis = CreateSpell(274902),
    Germination = CreateSpell(155675),
    GuardianAffinity = CreateSpell(197491),
    FeralAffinity = CreateSpell(197490),
    BalanceAffinity = CreateSpell(197632),
    InnerPeace = CreateSpell(197073),
    Abundance = CreateSpell(207383),
    AbundanceBuff = CreateSpell(207640),
    SpringBlossoms = CreateSpell(207385),
    Cultivation = CreateSpell(200390),
    Reforestation = CreateSpell(392356),
    Verdancy    = CreateSpell(392325),
    HarmoniousBlooming = CreateSpell(392256),
    -- Shapeshift
    AquaticForm = CreateSpell(276012),
    Soulshape = CreateSpell(310143),
    TreantForm = CreateSpell(114282),
    -- Balance Affinity
    Wrath = CreateSpell(5176),
    Starfire = CreateSpell(197628),
    LunarEclipse = CreateSpell(48518),
    SolarEclipse = CreateSpell(48517),

    AdaptiveSwarm = CreateSpell(391888),
    AdaptiveSwarmBuff = CreateSpell(391891),
    AdaptiveSwarmDeBuff = CreateSpell(391889),
    LifebloomResto = CreateSpell(188550),
    LifebloomResto2 = CreateSpell(33763),
    SouloftheForestBuff = CreateSpell(114108),
    ThrashResto = CreateSpell(77758),
    ThrashRestoDebuff = CreateSpell(192090),
    SwipeResto = CreateSpell(213771),
    NaturesSwiftness = CreateSpell(132158),
    Undergrowth = CreateSpell(392301),
    PoweroftheArchdruid = CreateSpell(392302),
    ProtectorofthePackBuff = CreateSpell(378987),
    ReforestationBuff = CreateSpell(392360),

    -- Potion
    PotionofChilledClarity = CreateSpell(371152),

    -- Trinket
    MoltenRadiance = CreateSpell(409898),

}
---@class DRCustomTable
Spell.Druid.Restoration = MergeTableByKey(Spell.Druid.Restoration, Spell.Druid.Custom)
---@class DRCommonsTable
Spell.Druid.Restoration = MergeTableByKey(Spell.Druid.Restoration, Spell.Druid.Commons, true)
---@class DruidoftheClawTable
Spell.Druid.Restoration = MergeTableByKey(Spell.Druid.Restoration, Spell.Druid.DruidoftheClaw)
---@class ElunesChosenTable
Spell.Druid.Restoration = MergeTableByKey(Spell.Druid.Restoration, Spell.Druid.ElunesChosen)
---@class KeeperoftheGroveTable
Spell.Druid.Restoration = MergeTableByKey(Spell.Druid.Restoration, Spell.Druid.KeeperoftheGrove)
---@class WildstalkerTable
Spell.Druid.Restoration = MergeTableByKey(Spell.Druid.Restoration, Spell.Druid.Wildstalker)

-- Items
if not Item.Druid then
    Item.Druid = {}
end
---@class DRCustomItemTable
Item.Druid.Custom = {
    -- Custom
    TreemouthFesteringSplinter            = Item(193652, {13, 14}),
    DecorationofFlame                     = Item(194299, {13, 14}),
    WardofFacelessIre                     = Item(203714, {13, 14}),
    EnduringDreadplate                    = Item(202616, {13, 14}),
    GranythsEnduringScale                 = Item(212757, {13, 14}),
    FyrakksTaintedRageheart               = Item(207174, {13, 14}),
    ShadowmoonInsignia                    = Item(150526, {13, 14}),

    -- DF Trinkets
    AlgetharPuzzleBox                     = Item(193701, {13, 14}),
    AshesoftheEmbersoul                   = Item(207167, {13, 14}),
    BandolierofTwistedBlades              = Item(207165, {13, 14}),
    BeacontotheBeyond                     = Item(203963, {13, 14}),
    IrideusFragment                       = Item(193743, {13, 14}),
    ManicGrieftorch                       = Item(194308, {13, 14}),
    MirrorofFracturedTomorrows            = Item(207581, {13, 14}),
    MydasTalisman                         = Item(158319, {13, 14}),
    SpoilsofNeltharus                     = Item(193773, {13, 14}),
    WitherbarksBranch                     = Item(109999, {13, 14}),
    VerdantBadge                          = Item(209343, {13, 14}),
    -- Other On-Use Items
    Djaruun                               = Item(202569, {16}),
    Jotungeirr                            = Item(186404, {16}),
}

---@class DRCommonsItemTable
Item.Druid.Commons = {
  -- TWW Trinkets
  ImperfectAscendancySerum              = Item(225654, {13, 14}),
  OvinaxsMercurialEgg                   = Item(220305, {13, 14}),
  TreacherousTransmitter                = Item(221023, {13, 14}),
  -- TWW Items
  BestinSlotsCaster                     = Item(232805, {16}),
  BestinSlotsMelee                      = Item(232526, {16}),
}

---@class BalanceItemTable
Item.Druid.Balance = {
    -- TWW Trinkets
    AberrantSpellforge                    = Item(212451, {13, 14}),
    ArakaraSacbrood                       = Item(219314, {13, 14}),
    SignetofthePriory                     = Item(219308, {13, 14}),
    SpymastersWeb                         = Item(220202, {13, 14}),
    -- TWW S2 Old Trinkets
    SoullettingRuby                       = Item(178809, {13, 14}),
    -- TWW Items
    NeuralSynapseEnhancer                 = Item(168973, {16}),
}
---@class DRCustomItemTable
Item.Druid.Balance = MergeTableByKey(Item.Druid.Custom, Item.Druid.Balance)
---@class DRCommonsItemTable
Item.Druid.Balance = MergeTableByKey(Item.Druid.Commons, Item.Druid.Balance)

---@class FeralItemTable
Item.Druid.Feral = {
    -- TWW Trinkets
    ConcoctionKissofDeath                 = Item(215174, {13, 14}),
    SikransEndlessArsenal                 = Item(212449, {13, 14}),
    TwinFangInstruments                   = Item(219319, {13, 14}),
}
---@class DRCustomItemTable
Item.Druid.Feral = MergeTableByKey(Item.Druid.Custom, Item.Druid.Feral)
---@class DRCommonsItemTable
Item.Druid.Feral = MergeTableByKey(Item.Druid.Commons, Item.Druid.Feral)

---@class GuardianItemTable
Item.Druid.Guardian = {
}
---@class DRCustomItemTable
Item.Druid.Guardian = MergeTableByKey(Item.Druid.Custom, Item.Druid.Guardian)
---@class DRCommonsItemTable
Item.Druid.Guardian = MergeTableByKey(Item.Druid.Commons, Item.Druid.Guardian)

---@class RestorationItemTable
Item.Druid.Restoration = {
    -- Trinkets
    VoidmendersShadowgem                  = Item(110007, {13, 14}),
    RashoksMoltenHeart                    = Item(202614, {13, 14}),
    NymuesUnravelingSpindle               = Item(208615, {13, 14}),

    -- Other On-Use Items
    Dreambinder                           = Item(208616, {16}),
    IridaltheEarthsMaster                 = Item(208321, {16}),
}
---@class DRCustomItemTable
Item.Druid.Restoration = MergeTableByKey(Item.Druid.Custom, Item.Druid.Restoration)
---@class DRCommonsItemTable
Item.Druid.Restoration = MergeTableByKey(Item.Druid.Commons, Item.Druid.Restoration)

-- Mouseover spells
Spell.Druid.Balance.IncapacitatingRoar.MeleeRange = 10
Spell.Druid.Balance.Typhoon.MeleeRange = 18

Spell.Druid.Balance.Sunfire:SetGeneric(DRUID_BALANCE_SPECID, 'Generic1')
Spell.Druid.Balance.Moonfire:SetGeneric(DRUID_BALANCE_SPECID, 'Generic2')
Spell.Druid.Balance.StellarFlare:SetGeneric(DRUID_BALANCE_SPECID, 'Generic3')
Spell.Druid.Balance.Soothe:SetGeneric(DRUID_BALANCE_SPECID, 'Generic4')
Spell.Druid.Balance.MightyBash:SetGeneric(DRUID_BALANCE_SPECID, 'Generic5')
Spell.Druid.Balance.Hibernate:SetGeneric(DRUID_BALANCE_SPECID, 'Generic6')
Spell.Druid.Balance.Sunfire.IsFocusTarget[DRUID_BALANCE_SPECID] = true
Spell.Druid.Balance.Moonfire.IsFocusTarget[DRUID_BALANCE_SPECID] = true
Spell.Druid.Balance.StellarFlare.IsFocusTarget[DRUID_BALANCE_SPECID] = true
Spell.Druid.Balance.SolarBeam.IsFocusTarget[DRUID_BALANCE_SPECID] = true

Spell.Druid.Feral.Rake:SetGeneric(DRUID_FERAL_SPECID, 'Generic1')
Spell.Druid.Feral.Rip:SetGeneric(DRUID_FERAL_SPECID, 'Generic2')
Spell.Druid.Feral.LIMoonfire:SetGeneric(DRUID_FERAL_SPECID, 'Generic3')
Spell.Druid.Feral.AdaptiveSwarm:SetGeneric(DRUID_FERAL_SPECID, 'Generic4')
Spell.Druid.Feral.Soothe:SetGeneric(DRUID_FERAL_SPECID, 'Generic5')
Spell.Druid.Feral.MightyBash:SetGeneric(DRUID_FERAL_SPECID, 'Generic6')
Spell.Druid.Feral.Hibernate:SetGeneric(DRUID_FERAL_SPECID, 'Generic7')
Spell.Druid.Feral.Rake.IsFocusTarget[DRUID_FERAL_SPECID] = true
Spell.Druid.Feral.Rip.IsFocusTarget[DRUID_FERAL_SPECID] = true
Spell.Druid.Feral.LIMoonfire.IsFocusTarget[DRUID_FERAL_SPECID] = true
Spell.Druid.Feral.AdaptiveSwarm.IsFocusTarget[DRUID_FERAL_SPECID] = true
Spell.Druid.Feral.SkullBash.IsFocusTarget[DRUID_FERAL_SPECID] = true

Spell.Druid.Feral.IncapacitatingRoar.MeleeRange = 10
Spell.Druid.Feral.Typhoon.MeleeRange = 18
Spell.Druid.Feral.Barkskin.offGCD = true
Spell.Druid.Feral.SurvivalInstincts.offGCD = true
Spell.Druid.Feral.TigersFury.offGCD = true
Spell.Druid.Feral.Renewal.offGCD = true
Spell.Druid.Feral.Berserk.offGCD = true
Spell.Druid.Feral.Incarnation.offGCD = true
Spell.Druid.Feral.Berserking.offGCD = true
Spell.Druid.Feral.NaturesVigil.offGCD = true

Spell.Druid.Guardian.Growl:SetGeneric(DRUID_GUARDIAN_SPECID, 'Generic1')
Spell.Druid.Guardian.Thrash:SetGeneric(DRUID_GUARDIAN_SPECID, 'Generic2')
Spell.Druid.Guardian.Pulverize:SetGeneric(DRUID_GUARDIAN_SPECID, 'Generic3')
Spell.Druid.Guardian.Moonfire:SetGeneric(DRUID_GUARDIAN_SPECID, 'Generic4')
Spell.Druid.Guardian.Soothe:SetGeneric(DRUID_GUARDIAN_SPECID, 'Generic5')
Spell.Druid.Guardian.MightyBash:SetGeneric(DRUID_GUARDIAN_SPECID, 'Generic6')
Spell.Druid.Guardian.Hibernate:SetGeneric(DRUID_GUARDIAN_SPECID, 'Generic7')

Spell.Druid.Guardian.Growl.offGCD = true
Spell.Druid.Guardian.Ironfur.offGCD = true
Spell.Druid.Guardian.Barkskin.offGCD = true
Spell.Druid.Guardian.SurvivalInstincts.offGCD = true
Spell.Druid.Guardian.BerserkBear.offGCD = true
Spell.Druid.Guardian.Incarnation.offGCD = true
Spell.Druid.Guardian.RageoftheSleeper.offGCD = true
Spell.Druid.Guardian.IncapacitatingRoar.MeleeRange = 10
Spell.Druid.Guardian.Typhoon.MeleeRange = 18

Spell.Druid.Restoration.Soothe:SetGeneric(DRUID_RESTORATION_SPECID, 'Generic1')
Spell.Druid.Restoration.Moonfire:SetGeneric(DRUID_RESTORATION_SPECID, 'Generic2')
Spell.Druid.Restoration.MightyBash:SetGeneric(DRUID_RESTORATION_SPECID, 'Generic3')
Spell.Druid.Restoration.Hibernate:SetGeneric(DRUID_RESTORATION_SPECID, 'Generic4')
Spell.Druid.Restoration.Sunfire:SetGeneric(DRUID_RESTORATION_SPECID, 'Generic5')
Spell.Druid.Restoration.Rake:SetGeneric(DRUID_RESTORATION_SPECID, 'Generic6')
Spell.Druid.Restoration.Rip:SetGeneric(DRUID_RESTORATION_SPECID, 'Generic7')


Spell.Druid.Restoration.IncapacitatingRoar.MeleeRange = 10
Spell.Druid.Restoration.Typhoon.MeleeRange = 18


-- Druids have a talent that improve the dispels to get more spell schools. This is a bit of a hack to get the correct dispel flag.
if Spell.Druid.Restoration.ImprovedNaturesCure:IsAvailable() then
    MainAddon.CONST.DispelList.NaturesCure.DispelFlag = MainAddon.CONST.DispelFlag.Magic + MainAddon.CONST.DispelFlag.Curse + MainAddon.CONST.DispelFlag.Poison
else
    MainAddon.CONST.DispelList.NaturesCure.DispelFlag = MainAddon.CONST.DispelFlag.Magic
end

HL:RegisterForEvent(function()
    if Spell.Druid.Restoration.ImprovedNaturesCure:IsAvailable() then
        MainAddon.CONST.DispelList.NaturesCure.DispelFlag = MainAddon.CONST.DispelFlag.Magic + MainAddon.CONST.DispelFlag.Curse + MainAddon.CONST.DispelFlag.Poison
    else
        MainAddon.CONST.DispelList.NaturesCure.DispelFlag = MainAddon.CONST.DispelFlag.Magic
    end
end, "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")