---@class MainAddon
local MainAddon = MainAddon
---@class MainAddon
local M = MainAddon
-- HeroLib
local HL = HeroLibEx
---@class Spell
local Spell = HL.Spell
local CreateSpell = MainAddon.CreateSpell
local CreateMultiSpell = MainAddon.CreateMultiSpell
---@class Item
local Item = HL.Item
local StaticPopupDialogs = _G['StaticPopupDialogs']
local MergeTableByKey = HL.Utils.MergeTableByKey
local mathmax          = math.max

--- ============================ CONTENT ============================
-- Spell
if not Spell.DemonHunter then
    Spell.DemonHunter = {}
end

M.DemonHunter = {}
---@class DemonHunter
local DemonHunter = M.DemonHunter


---@class DHCustomTable
Spell.DemonHunter.Custom = {
    Torment = CreateSpell(185245),
    SpectralSight = CreateSpell(188501),
    Darkness = CreateSpell(196718),
    Netherwalk = CreateSpell(196555),
    -- Utilities
    Imprison = CreateSpell(217832),
    ConsumeMagic = CreateSpell(278326),
    -- Racials
    ArcaneTorrent = CreateSpell(202719),
    -- PvP
    ReverseMagic = CreateSpell(205604),
    RainFromAbove = CreateSpell(206803),
    -- PvP Vengeance
    Tormentor = CreateSpell(207029),
    IllidansGrasp = CreateSpell(205630),
}

---@class DHCommonsTable
Spell.DemonHunter.Commons = {
    -- Racials
    ArcaneTorrent                         = CreateSpell(50613),
    -- Abilities
    Glide                                 = CreateSpell(131347),
    ImmolationAura                        = CreateSpell(258920),
    ImmolationAuraBuff                    = CreateSpell(258920),
    -- Talents
    AuraofPain                            = CreateSpell(207347),
    ChaosNova                             = CreateSpell(179057),
    CollectiveAnguish                     = CreateSpell(390152),
    Demonic                               = CreateSpell(213410),
    SigilofSpite                          = CreateSpell(390163),
    Felblade                              = CreateSpell(232893),
    FirstoftheIllidari                    = CreateSpell(235893),
    FlamesofFury                          = CreateSpell(389694),
    FoddertotheFlame                      = CreateSpell(391429),
    QuickenedSigils                       = CreateSpell(209281),
    SoulSigils                            = CreateSpell(395446),
    StudentofSuffering                    = CreateSpell(452412),
    TheHunt                               = CreateSpell(370965),
    UnhinderedAssault                     = CreateSpell(444931),
    VengefulRetreat                       = CreateSpell(198793),
    -- Sigils
    SigilofChains                         = CreateMultiSpell(202138, 389807),
    SigilofFlame                          = CreateMultiSpell(204596, 389810), -- 204596: Base ID, 389810: Precise
    SigilofMisery                         = CreateMultiSpell(207684, 389813), -- 207684: Base ID, 389813: Precise
    SigilofSilence                        = CreateMultiSpell(202137, 389809),
    -- Buffs
    ExplosiveAdrenalineBuff               = CreateSpell(1218713), -- Improvised Seaforium Pacemaker buff
    InnerResilienceBuff                   = CreateSpell(450706),  -- Tome of Light's Devotion buff
    JunkmaestrosBuff                      = CreateSpell(1219661), -- Junkmaestro's Mega Magnet buff
    -- Utility
    Disrupt                               = CreateSpell(183752),
    -- Debuffs
    SigilofFlameDebuff                    = CreateSpell(204598),
    SigilofMiseryDebuff                   = CreateSpell(207685),
    -- Other
    Pool                                  = CreateSpell(999910)
}
  
---@class AldrachiReaverTable
Spell.DemonHunter.AldrachiReaver = {
    -- Talents
    ArtoftheGlaive                        = CreateSpell(442290),
    FuryoftheAldrachi                     = CreateSpell(442718),
    KeenEngagement                        = CreateSpell(442497),
    ReaversGlaive                         = CreateSpell(442294),
    -- Buffs
    ArtoftheGlaiveBuff                    = CreateSpell(444661),
    GlaiveFlurryBuff                      = CreateSpell(442435),
    ThrilloftheFightAtkBuff               = CreateSpell(442695),
    ThrilloftheFightHavocDmgBuff          = CreateSpell(442688),
    ThrilloftheFightVengDmgBuff           = CreateSpell(1227062),
    WarbladesHungerBuff                   = CreateSpell(442503),
    -- Debuffs
    ReaversMarkDebuff                     = CreateSpell(442624),
    RendingStrikeBuff                     = CreateSpell(442442),
}
  
---@class FelScarredTable
Spell.DemonHunter.FelScarred = {
  -- Abilities
  AbyssalGaze                           = CreateSpell(452497),
  ConsumingFire                         = CreateSpell(452487),
  FelDesolation                         = CreateSpell(452486),
  Flamebound                            = CreateSpell(452413),
  SigilofDoom                           = CreateSpell(452490),
  SoulSunder                            = CreateSpell(452436),
  SpiritBurst                           = CreateSpell(452437),
  -- Talents
  Demonsurge                            = CreateSpell(454402),
  -- Buffs
  ConsumingFireBuff                     = CreateSpell(427912),
  DemonsurgeBuff                        = CreateSpell(452416),
  StudentofSufferingBuff                = CreateSpell(453239),
  -- Debuffs
  SigilofDoomDebuff                     = CreateSpell(462030),
}

---@class VengeanceTable
Spell.DemonHunter.Vengeance = {
  -- Abilities
  InfernalStrike                        = CreateSpell(189110),
  Shear                                 = CreateSpell(203782),
  SoulCleave                            = CreateSpell(228477),
  ThrowGlaive                           = CreateSpell(204157),
  -- Defensive
  DemonSpikes                           = CreateSpell(203720),
  -- Talents
  AscendingFlame                        = CreateSpell(428603),
  BulkExtraction                        = CreateSpell(320341),
  CycleofBinding                        = CreateSpell(389718),
  DarkglareBoon                         = CreateSpell(389708),
  DowninFlames                          = CreateSpell(389732),
  Fallout                               = CreateSpell(227174),
  FelDevastation                        = CreateSpell(212084),
  FieryBrand                            = CreateSpell(204021),
  FieryDemise                           = CreateSpell(389220),
  Fracture                              = CreateSpell(263642),
  IlluminatedSigils                     = CreateSpell(428557),
  SoulCarver                            = CreateSpell(207407),
  SpiritBomb                            = CreateSpell(247454),
  VolatileFlameblood                    = CreateSpell(390808),
  -- Sigils
  -- Utility
  Metamorphosis                         = CreateSpell(187827),
  -- Buffs
  DemonSpikesBuff                       = CreateSpell(203819),
  MetamorphosisBuff                     = CreateSpell(187827),
  SoulFurnaceBuff                       = CreateSpell(391166),
  SoulFurnaceDmgBuff                    = CreateSpell(391172),
  -- Debuffs
  FieryBrandDebuff                      = CreateSpell(207771),
}
---@class DHCustomTable
Spell.DemonHunter.Vengeance = MergeTableByKey(Spell.DemonHunter.Vengeance, Spell.DemonHunter.Custom)
---@class DHCommonsTable
Spell.DemonHunter.Vengeance = MergeTableByKey(Spell.DemonHunter.Vengeance, Spell.DemonHunter.Commons, true)
---@class AldrachiReaverTable
Spell.DemonHunter.Vengeance = MergeTableByKey(Spell.DemonHunter.Vengeance, Spell.DemonHunter.AldrachiReaver)
---@class FelScarredTable
Spell.DemonHunter.Vengeance = MergeTableByKey(Spell.DemonHunter.Vengeance, Spell.DemonHunter.FelScarred)

---@class HavocTable
Spell.DemonHunter.Havoc = {
  -- Abilities
  Annihilation                          = CreateSpell(201427),
  BladeDance                            = CreateSpell(188499),
  Blur                                  = CreateSpell(198589),
  ChaosStrike                           = CreateSpell(162794),
  DeathSweep                            = CreateSpell(210152),
  DemonsBite                            = CreateSpell(162243),
  FelRush                               = CreateSpell(195072),
  Metamorphosis                         = CreateSpell(191427),
  ThrowGlaive                           = CreateSpell(185123),
  -- Talents
  AFireInside                           = CreateSpell(427775),
  AnyMeansNecessary                     = CreateSpell(388114),
  BlindFury                             = CreateSpell(203550),
  BurningWound                          = CreateSpell(391189),
  ChaosTheory                           = CreateSpell(389687),
  ChaoticTransformation                 = CreateSpell(388112),
  CycleofHatred                         = CreateSpell(258887),
  DemonBlades                           = CreateSpell(203555),
  EssenceBreak                          = CreateSpell(258860),
  EyeBeam                               = CreateSpell(198013),
  FelBarrage                            = CreateSpell(258925),
  FelEruption                           = CreateSpell(211881),
  FirstBlood                            = CreateSpell(206416),
  FuriousGaze                           = CreateSpell(343311),
  FuriousThrows                         = CreateSpell(393029),
  GlaiveTempest                         = CreateSpell(342817),
  Inertia                               = CreateSpell(427640),
  Initiative                            = CreateSpell(388108),
  InnerDemon                            = CreateSpell(389693),
  IsolatedPrey                          = CreateSpell(388113),
  LooksCanKill                          = CreateSpell(320415),
  Momentum                              = CreateSpell(206476),
  Ragefire                              = CreateSpell(388107),
  RestlessHunter                        = CreateSpell(390142),
  ScreamingBrutality                    = CreateSpell(1220506),
  SerratedGlaive                        = CreateSpell(390154),
  ShatteredDestiny                      = CreateSpell(388116),
  Soulrend                              = CreateSpell(388106),
  Soulscar                              = CreateSpell(388106),
  TacticalRetreat                       = CreateSpell(389688),
  TrailofRuin                           = CreateSpell(258881),
  UnboundChaos                          = CreateSpell(347461),
  -- Buffs
  ChaosTheoryBuff                       = CreateSpell(390195),
  CycleofHatredBuff                     = CreateSpell(1214887),
  ExergyBuff                            = CreateSpell(208628),
  FelBarrageBuff                        = CreateSpell(258925),
  FuriousGazeBuff                       = CreateSpell(343312),
  InertiaBuff                           = CreateSpell(427641),
  InitiativeBuff                        = CreateSpell(391215),
  InnerDemonBuff                        = CreateSpell(390145),
  MetamorphosisBuff                     = CreateSpell(162264),
  MomentumBuff                          = CreateSpell(208628),
  NecessarySacrificeBuff                = CreateSpell(1217055), -- TWW S2 4pc Buff
  TacticalRetreatBuff                   = CreateSpell(389890),
  UnboundChaosBuff                      = CreateSpell(347462),
  WinningStreakBuff                     = CreateSpell(1220706), -- TWW S2 2pc Buff
  -- Debuffs
  BurningWoundDebuff                    = CreateSpell(391191),
  EssenceBreakDebuff                    = CreateSpell(320338),
  SerratedGlaiveDebuff                  = CreateSpell(390155),
}
---@class DHCustomTable
Spell.DemonHunter.Havoc = MergeTableByKey(Spell.DemonHunter.Havoc, Spell.DemonHunter.Custom)
---@class DHCommonsTable
Spell.DemonHunter.Havoc = MergeTableByKey(Spell.DemonHunter.Havoc, Spell.DemonHunter.Commons, true)
---@class AldrachiReaverTable
Spell.DemonHunter.Havoc = MergeTableByKey(Spell.DemonHunter.Havoc, Spell.DemonHunter.AldrachiReaver)
---@class FelScarredTable
Spell.DemonHunter.Havoc = MergeTableByKey(Spell.DemonHunter.Havoc, Spell.DemonHunter.FelScarred)

-- Items
if not Item.DemonHunter then
    Item.DemonHunter = {}
end

---@class DHCustomItemTable
Item.DemonHunter.Custom = {
}

---@class DHCommonsItemTable
Item.DemonHunter.Commons = {
  -- TWW Trinkets
  Blastmaster3000                       = Item(234717, {13, 14}),
  GeargrindersSpareKeys                 = Item(230197, {13, 14}),
  HouseofCards                          = Item(230027, {13, 14}),
  ImprovisedSeaforiumPacemaker          = Item(232541, {13, 14}),
  JunkmaestrosMegaMagnet                = Item(230189, {13, 14}),
  MadQueensMandate                      = Item(212454, {13, 14}),
  MisterLockNStalk                      = Item(230193, {13, 14}),
  RatfangToxin                          = Item(235359, {13, 14}),
  RavenousHoneyBuzzer                   = Item(219298, {13, 14}),
  SignetofthePriory                     = Item(219308, {13, 14}),
  TomeofLightsDevotion                  = Item(219309, {13, 14}),
  TreacherousTransmitter                = Item(221023, {13, 14}),
  -- TWW S2 Old Trinkets
  GrimCodex                             = Item(178811, {13, 14}),
  SkardynsGrace                         = Item(133282, {13, 14}),
}

---@class DHVengeanceItemTable
Item.DemonHunter.Vengeance = {
    AlgetharPuzzleBox                     = Item(193701, {13, 14}),
    BeacontotheBeyond                     = Item(203963, {13, 14}),
    DragonfireBombDispenser               = Item(202610, {13, 14}),
    ElementiumPocketAnvil                 = Item(202617, {13, 14}),
    IrideusFragment                       = Item(193743, {13, 14}),
    ManicGrieftorch                       = Item(194308, {13, 14}),
    MarkofDargrul                         = Item(137357, {13, 14}),
    ScreamingBlackDragonscale             = Item(202612, {13, 14}),
    StormEatersBoon                       = Item(194302, {13, 14}),
    WitherbarksBranch                     = Item(109999, {13, 14}),
    -- Custom
    TreemouthFesteringSplinter            = Item(193652, {13, 14}),
    DecorationofFlame                     = Item(194299, {13, 14}),
    WardofFacelessIre                     = Item(203714, {13, 14}),
    EnduringDreadplate                    = Item(202616, {13, 14}),
    GranythsEnduringScale                 = Item(212757, {13, 14}),
    FyrakksTaintedRageheart               = Item(207174, {13, 14}),
    ShadowmoonInsignia                    = Item(150526, {13, 14}),
}
---@class DHVengeanceItemTable
Item.DemonHunter.Vengeance = MergeTableByKey(Item.DemonHunter.Custom, Item.DemonHunter.Vengeance)
---@class DHCommonsItemTable
Item.DemonHunter.Vengeance = MergeTableByKey(Item.DemonHunter.Commons, Item.DemonHunter.Vengeance)


---@class DHHavocItemTable
Item.DemonHunter.Havoc = {
    AlgetharPuzzleBox                     = Item(193701, {13, 14}),
    BeacontotheBeyond                     = Item(203963, {13, 14}),
    DragonfireBombDispenser               = Item(202610, {13, 14}),
    ElementiumPocketAnvil                 = Item(202617, {13, 14}),
    IrideusFragment                       = Item(193743, {13, 14}),
    ManicGrieftorch                       = Item(194308, {13, 14}),
    MarkofDargrul                         = Item(137357, {13, 14}),
    ScreamingBlackDragonscale             = Item(202612, {13, 14}),
    StormEatersBoon                       = Item(194302, {13, 14}),
    WitherbarksBranch                     = Item(109999, {13, 14}),
}
---@class DHHavocItemTable
Item.DemonHunter.Havoc = MergeTableByKey(Item.DemonHunter.Custom, Item.DemonHunter.Havoc)
---@class DHCommonsItemTable
Item.DemonHunter.Havoc = MergeTableByKey(Item.DemonHunter.Commons, Item.DemonHunter.Havoc)

Spell.DemonHunter.Vengeance.Torment:SetGeneric(DEMONHUNTER_VENGEANCE_SPECID, "Generic1")
Spell.DemonHunter.Vengeance.ThrowGlaive:SetGeneric(DEMONHUNTER_VENGEANCE_SPECID, "Generic2")
Spell.DemonHunter.Vengeance.Imprison:SetGeneric(DEMONHUNTER_VENGEANCE_SPECID, "Generic3")

Spell.DemonHunter.Vengeance.InfernalStrike.offGCD = true
Spell.DemonHunter.Vengeance.DemonSpikes.offGCD = true
Spell.DemonHunter.Vengeance.ImmolationAura.MeleeRange = 8
Spell.DemonHunter.Vengeance.ConsumingFire.MeleeRange = 8
Spell.DemonHunter.Vengeance.InfernalStrike.MeleeRange = 8
Spell.DemonHunter.Vengeance.SigilofSpite.MeleeRange = 6
Spell.DemonHunter.Vengeance.SigilofChains.MeleeRange = 10
Spell.DemonHunter.Vengeance.SigilofSilence.MeleeRange = 5
Spell.DemonHunter.Vengeance.SigilofFlame.MeleeRange = 5
Spell.DemonHunter.Vengeance.SigilofMisery.MeleeRange = 5
Spell.DemonHunter.Vengeance.ChaosNova.MeleeRange = 10

Spell.DemonHunter.Havoc.DemonBlades:SetGeneric(DEMONHUNTER_HAVOC_SPECID, "Generic1")
Spell.DemonHunter.Havoc.ThrowGlaive:SetGeneric(DEMONHUNTER_HAVOC_SPECID, "Generic2")
Spell.DemonHunter.Havoc.Imprison:SetGeneric(DEMONHUNTER_HAVOC_SPECID, "Generic3")
Spell.DemonHunter.Havoc.FelEruption:SetGeneric(DEMONHUNTER_HAVOC_SPECID, "Generic4")
Spell.DemonHunter.Havoc.DemonBlades.Macro = "/target mouseover"
Spell.DemonHunter.Havoc.ChaosNova.MeleeRange = 10
Spell.DemonHunter.Havoc.SigilofFlame.MeleeRange = 5
Spell.DemonHunter.Havoc.SigilofMisery.MeleeRange = 5
Spell.DemonHunter.Havoc.SigilofSpite.MeleeRange = 6
Spell.DemonHunter.Havoc.FelRush.Range = 20
Spell.DemonHunter.Havoc.ImmolationAura.MeleeRange = 8
Spell.DemonHunter.Havoc.ConsumingFire.MeleeRange = 8

local Player = HeroLibEx.Unit.Player
if Player:Class() == "DEMONHUNTER" then
    StaticPopupDialogs["DHPOPUP"] = {
        text = "??: It seems you are Kyrian or Night Fae.\nYou may have issues with the talent The Hunt or Elysian Decree.\n\nPlease change your Covenant in Oribos.",
        button1 = "OK",
    }

    if _G['IsSpellKnown'](323639) or _G['IsSpellKnown'](306830) then
        StaticPopup_Show("DHPOPUP")
    end
end

--- ======= Demonsurge Tracker =====
DemonHunter.Demonsurge = {}
local Surge = DemonHunter.Demonsurge
-- Commons
Surge.ConsumingFire = false
Surge.SigilofDoom = false
-- Vengeance
Surge.FelDesolation = false
Surge.SoulSunder = false
Surge.SpiritBurst = false
-- Havoc
Surge.AbyssalGaze = false
Surge.Annihilation = false
Surge.DeathSweep = false

-- When we cast Meta, set Demonsurge buffs to active.
-- Then remove the buffs when we use them.
HL:RegisterForSelfCombatEvent(
  function(...)
    local SpellID = select(12, ...)
    if Player:HeroTreeID() == 34 then
      if SpellID == Spell.DemonHunter.Vengeance.Metamorphosis:ID() then
        Surge.ConsumingFire = true
        Surge.FelDesolation = true
        Surge.SigilofDoom = true
        Surge.SoulSunder = true
        Surge.SpiritBurst = true
      elseif SpellID == 200166 then -- Metamorphosis's impact ability ID is the one reported to the event.
        Surge.AbyssalGaze = true
        Surge.Annihilation = true
        Surge.ConsumingFire = true
        Surge.DeathSweep = true
        Surge.SigilofDoom = true
      elseif SpellID == Spell.DemonHunter.Vengeance.ConsumingFire:ID() or SpellID == Spell.DemonHunter.Havoc.ConsumingFire:ID() then
        Surge.ConsumingFire = false
      elseif SpellID == Spell.DemonHunter.Vengeance.SigilofDoom:ID() or SpellID == Spell.DemonHunter.Havoc.SigilofDoom:ID() then
        Surge.SigilofDoom = false
      elseif SpellID == Spell.DemonHunter.Vengeance.FelDesolation:ID() then
        Surge.FelDesolation = false
      elseif SpellID == Spell.DemonHunter.Vengeance.SoulSunder:ID() then
        Surge.SoulSunder = false
      elseif SpellID == Spell.DemonHunter.Vengeance.SpiritBurst:ID() then
        Surge.SpiritBurst = false
      elseif SpellID == Spell.DemonHunter.Havoc.AbyssalGaze:ID() then
        Surge.AbyssalGaze = false
      elseif SpellID == Spell.DemonHunter.Havoc.Annihilation:ID() then
        Surge.Annihilation = false
      elseif SpellID == Spell.DemonHunter.Havoc.DeathSweep:ID() then
        Surge.DeathSweep = false
      end
    end
  end
, "SPELL_CAST_SUCCESS")

-- Watch for the Meta aura.
-- These spells are buffed on hardcast Meta and Demonic Meta.
HL:RegisterForSelfCombatEvent(
  function(...)
    local SpellID = select(12, ...)
    if Player:HeroTreeID() == 34 then
      if SpellID == Spell.DemonHunter.Vengeance.MetamorphosisBuff:ID() then
        Surge.SpiritBurst = true
        Surge.SoulSunder = true
      elseif SpellID == Spell.DemonHunter.Havoc.MetamorphosisBuff:ID() then
        Surge.Annihilation = true
        Surge.DeathSweep = true
      end
    end
  end
, "SPELL_AURA_APPLIED")

-- Remove Demonsurge buffs when Meta ends.
HL:RegisterForSelfCombatEvent(
  function(...)
    local SpellID = select(12, ...)
    if Player:HeroTreeID() == 34 and (SpellID == Spell.DemonHunter.Vengeance.MetamorphosisBuff:ID() or SpellID == Spell.DemonHunter.Havoc.MetamorphosisBuff:ID()) then
      Surge.ConsumingFire = false
      Surge.SigilofDoom = false
      Surge.FelDesolation = false
      Surge.SoulSunder = false
      Surge.SpiritBurst = false
      Surge.AbyssalGaze = false
      Surge.Annihilation = false
      Surge.DeathSweep = false
    end
  end
, "SPELL_AURA_REMOVED")

--- ===== Soul Fragment Tracker =====
DemonHunter.Souls = {}
local Soul = DemonHunter.Souls
Soul.AuraSouls = 0
Soul.IncomingSouls = 0

-- Casted abilities that generate delayed Soul Fragments.
HL:RegisterForSelfCombatEvent(
  function(...)
    local SpellID = select(12, ...)
    local IncAmt = 0
    if SpellID == Spell.DemonHunter.Vengeance.Fracture:ID() or SpellID == Spell.DemonHunter.Vengeance.Shear:ID() then
      IncAmt = Player:BuffUp(Spell.DemonHunter.Vengeance.MetamorphosisBuff) and 3 or 2
    elseif SpellID == Spell.DemonHunter.Vengeance.SoulCarver:ID() then
      IncAmt = 3
      C_Timer.After(1, function() Soul.IncomingSouls = Soul.IncomingSouls + 1; end)
      C_Timer.After(2, function() Soul.IncomingSouls = Soul.IncomingSouls + 1; end)
      C_Timer.After(3, function() Soul.IncomingSouls = Soul.IncomingSouls + 1; end)
    elseif SpellID == Spell.DemonHunter.Vengeance.SigilofSpite:ID() then
      IncAmt = (Spell.DemonHunter.Vengeance.SoulSigils:IsAvailable()) and 4 or 3
    elseif Spell.DemonHunter.Vengeance.SoulSigils:IsAvailable() and
      (SpellID == Spell.DemonHunter.Vengeance.SigilofFlame:ID() or SpellID == Spell.DemonHunter.Vengeance.SigilofMisery:ID() or SpellID == Spell.DemonHunter.Vengeance.SigilofChains:ID() or SpellID == Spell.DemonHunter.Vengeance.SigilofSilence:ID()) then
      IncAmt = 1
    else
      IncAmt = 0
    end
    if IncAmt > 0 then
      Soul.IncomingSouls = Soul.IncomingSouls + IncAmt
    end
  end
, "SPELL_CAST_SUCCESS")

-- T31 4pc "flare-up" Sigil damage, which spawns a delayed Soul Fragment.
HL:RegisterForSelfCombatEvent(
  function(...)
    local SpellID = select(12, ...)
    if SpellID == 425672 then
      Soul.IncomingSouls = Soul.IncomingSouls + 1
    end
  end
, "SPELL_DAMAGE")

-- The initial application of the Soul Fragments buff.
HL:RegisterForSelfCombatEvent(
  function(...)
    local SpellID = select(12, ...)
    if SpellID == 203981 then
      Soul.AuraSouls = 1
      Soul.IncomingSouls = mathmax(0, Soul.IncomingSouls - 1)
    end
  end
, "SPELL_AURA_APPLIED")

-- Triggers every time we add stacks to the Soul Fragments buff.
HL:RegisterForSelfCombatEvent(
  function(...)
    local SpellID, _, _, _, Amount = select(12, ...)
    if SpellID == 203981 then
      Soul.AuraSouls = Amount
      Soul.IncomingSouls = mathmax(0, Soul.IncomingSouls - Amount)
    end
  end
, "SPELL_AURA_APPLIED_DOSE")

-- Triggers every time we remove stacks from the Soul Fragments buff.
HL:RegisterForSelfCombatEvent(
  function(...)
    local SpellID, _, _, _, Amount = select(12, ...)
    if SpellID == 203981 then
      Soul.AuraSouls = Amount
    end
  end
, "SPELL_AURA_REMOVED_DOSE")

-- Triggers when the soul Fragments buff is removed entirely.
HL:RegisterForSelfCombatEvent(
  function(...)
    local SpellID, _, _, _, Amount = select(12, ...)
    if SpellID == 203981 then
      Soul.AuraSouls = 0
    end
  end
, "SPELL_AURA_REMOVED")