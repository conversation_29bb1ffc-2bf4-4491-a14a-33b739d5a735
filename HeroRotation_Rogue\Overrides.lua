--- ============================ HEADER ============================
  -- HeroLib
  local HL      = HeroLib
  local Cache   = HeroCache
  local Unit    = HL.Unit
  local Player  = Unit.Player
  local Pet     = Unit.Pet
  local Target  = Unit.Target
  local Spell   = HL.Spell
  local Item    = HL.Item
-- HeroRotation
  local HR      = HeroRotation
-- Spells
  local SpellAssa     = Spell.Rogue.Assassination
  local SpellOutlaw   = Spell.Rogue.Outlaw
  local SpellSubtlety = Spell.Rogue.Subtlety
-- Lua

--- ============================ CONTENT ============================
-- Assassination, ID: 259

-- Outlaw, ID: 260

-- Subtlety, ID: 261

-- Example (Arcane Mage)
-- HL.AddCoreOverride ("Spell.IsCastableP",
-- function (self, Range, AoESpell, ThisUnit, BypassRecovery, Offset)
--   if Range then
--     local RangeUnit = ThisUnit or Target
--     return self:IsLearned() and self:CooldownRemains( BypassRecovery, Offset or "Auto") == 0 and RangeUnit:IsInRange( Range, AoESpell )
--   elseif self == SpellArcane.MarkofAluneth then
--     return self:IsLearned() and self:CooldownRemains( BypassRecovery, Offset or "Auto") == 0 and not Player:IsCasting(self)
--   else
--     return self:IsLearned() and self:CooldownRemains( BypassRecovery, Offset or "Auto") == 0
--   end
-- end
-- , 62)
