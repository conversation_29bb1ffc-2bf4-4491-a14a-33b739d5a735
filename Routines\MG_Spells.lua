---@class MainAddon
local MainAddon = MainAddon
---@class MainAddon
local M = MainAddon
local HL = HeroLibEx
---@class Unit
local Unit = HL.Unit
---@class Unit
local Player = Unit.Player
---@class Spell
local Spell = HL.Spell
local CreateSpell = MainAddon.CreateSpell
local CreateMultiSpell = MainAddon.CreateMultiSpell
---@class Item
local Item = HL.Item
local MergeTableByKey = HL.Utils.MergeTableByKey
-- File Locals
M.Mage = {}

--- ============================ CONTENT ============================

-- Spells
if not Spell.Mage then
    Spell.Mage = {}
end
---@class MGCustomTable
Spell.Mage.Custom = {
    -- Custom 
    WingBuffet = CreateSpell(357214),
    BloodFury = CreateMultiSpell(20572, 33702, 33697),
    Polymorph = CreateSpell(118),
    MassPolymorph = CreateSpell(383121),
    ConeofCold = CreateSpell(120),
    IceCold = CreateSpell(414658),
    GreaterInvisibility = CreateSpell(110959),
    Slipstream = CreateSpell(236457),
    GreaterInvisibilityBuff = CreateSpell(113862),
    Quickflame = CreateSpell(450807),
    FreezingWinds = CreateSpell(382106),
    BagofTricks = CreateSpell(312411),
    Blink = CreateMultiSpell(1953, 212653),
    SlowFall = CreateSpell(130),
    TimeWarp = CreateSpell(80353),
    AlterTime = CreateSpell(342245),
    BlastWave = CreateSpell(157981),
    FocusMagic = CreateSpell(321358),
    IceBlock = CreateSpell(45438),
    Invisibility = CreateSpell(66),
    Meteor = CreateSpell(153561),
    RemoveCurse = CreateSpell(475),
    RingOfFrost = CreateSpell(113724),
    SpellSteal = CreateSpell(30449),
    TemporalWarp = CreateSpell(386539),
    TemporalWarpBuff = CreateSpell(386540),
    Supernova = CreateSpell(157980),
    GravityLapse = CreateSpell(449700),
    ArcanePhoenix = CreateSpell(448659),
    HypothermiaDebuff = CreateSpell(41425),
    -- Barrier
    MassBarrier = CreateSpell(414660),
    EnergizedBarriers = CreateSpell(386828),
    PrismaticBarrier = CreateSpell(235450),
    IceBarrier = CreateSpell(11426),
    BlazingBarrier = CreateSpell(235313),
    -- PvP
    RingofFire = CreateSpell(353082),
    IceWall = CreateSpell(352278),
    -- PvP Arcane
    Arcanosphere = CreateSpell(353128),
    MassInvisibility = CreateSpell(198158),
    -- PvP Fire
    GreaterPyroblast = CreateSpell(203286),
    -- PvP Frost
    IceForm = CreateSpell(198144),
    FrostBomb = CreateSpell(390614),
    Snowdrift = CreateSpell(389794),
}

---@class MGCommonsTable
Spell.Mage.Commons = {
    -- Racials
    AncestralCall                         = CreateSpell(274738),
    Berserking                            = CreateSpell(26297),
    BloodFury                             = CreateSpell(20572),
    Fireblood                             = CreateSpell(265221),
    LightsJudgment                        = CreateSpell(255647),
    -- Abilities
    ArcaneExplosion                       = CreateSpell(1449), --Melee, 10
    ArcaneIntellect                       = CreateSpell(1459),
    Frostbolt                             = CreateSpell(116),
    FrostNova                             = CreateSpell(122),
    -- Talents
    Counterspell                          = CreateSpell(2139),
    DragonsBreath                         = CreateSpell(31661),
    IceFloes                              = CreateSpell(108839),
    IceNova                               = CreateSpell(157997), --splash, 8
    MirrorImage                           = CreateSpell(55342),
    ShiftingPower                         = CreateSpell(382440), --Melee 15
    -- Buffs
    ArcaneIntellectBuff                   = CreateSpell(1459),
    MirrorImageBuff                       = CreateSpell(55342),
    -- Debuffs
    -- Trinket Effects
    SpymastersWebBuff                     = Spell(444959), -- Buff from using Spymaster's Web trinket
    EtherealPowerlinkBuff                 = Spell(449954), -- Buff from Treacherous Transmitter trinket
    SpymastersReportBuff                  = Spell(451199), -- Buff from using Spymaster's Web trinket
    -- Pool
    Pool                                  = CreateSpell(999910)
}

---@class FrostfireTable
Spell.Mage.Frostfire = {
  -- Abilities
  FrostfireBolt                         = CreateSpell(431044),
  -- Talents
  ExcessFire                            = CreateSpell(438595),
  ExcessFrost                           = CreateSpell(438600),
  IsothermicCore                        = CreateSpell(431095),
  -- Buffs
  ExcessFireBuff                        = CreateSpell(438624),
  ExcessFrostBuff                       = CreateSpell(438611),
  FrostfireEmpowermentBuff              = CreateSpell(431177),
}

---@class SpellslingerTable
Spell.Mage.Spellslinger = {
    -- Talents
    Resonance                             = CreateSpell(453823),
    ShiftingShards                        = CreateSpell(444675),
    SplinteringSorcery                    = CreateSpell(443739),
    Splinterstorm                         = CreateSpell(443742),
    TimeLoop                              = CreateSpell(445255),
    UnerringProficiency                   = CreateSpell(444974),
    -- Buffs
    UnerringProficiencyBuff               = CreateSpell(444981),
}

---@class SunfuryTable  
Spell.Mage.Sunfury = {
    -- Talents
    ConsortiumsBauble                     = CreateSpell(451894),
    SpellfireSpheres                      = CreateSpell(448601),
    SunfuryExecution                      = CreateSpell(449349),
    -- Buffs
    ArcaneSoulBuff                        = CreateSpell(451038),
    BurdenofPowerBuff                     = CreateSpell(451049),
    GloriousIncandescenceBuff             = CreateSpell(451073),
    SpellfireSpheresBuff                  = CreateSpell(449400),
}

---@class ArcaneTable
Spell.Mage.Arcane = {
  -- Abilities
  ArcaneBlast                           = CreateSpell(30451),
  -- Talents
  ArcaneBarrage                         = CreateSpell(44425), --Splash, 10
  ArcaneBombardment                     = CreateSpell(384581),
  ArcaneFamiliar                        = CreateSpell(205022),
  ArcaneHarmony                         = CreateSpell(384452),
  ArcaneMissiles                        = CreateSpell(5143),
  ArcaneOrb                             = CreateSpell(153626), --Splash, 16
  ArcaneSurge                           = CreateSpell(365350),
  ArcaneTempo                           = CreateSpell(383980),
  ArcingCleave                          = CreateSpell(231564),
  ChargedOrb                            = CreateSpell(384651),
  Enlightened                           = CreateSpell(321387),
  Evocation                             = CreateSpell(12051),
  HighVoltage                           = CreateSpell(461248),
  Impetus                               = CreateSpell(383676),
  ImprovedClearcasting                  = CreateSpell(321420),
  MagisSpark                            = CreateSpell(454016),
  OrbBarrage                            = CreateSpell(384858),
  PresenceofMind                        = CreateSpell(205025),
  Reverberate                           = CreateSpell(281482),
  Supernova                             = CreateSpell(157980), --Splash, 8
  TouchoftheMagi                        = CreateSpell(321507), --Splash, 8
  Leydrinker                            = CreateSpell(452196),
  -- Buffs
  AetherAttunementBuff                  = CreateSpell(453601),
  AethervisionBuff                      = CreateSpell(467634),
  ArcaneFamiliarBuff                    = CreateSpell(210126),
  ArcaneHarmonyBuff                     = CreateSpell(384455),
  ArcaneSurgeBuff                       = CreateSpell(365362),
  ArcaneTempoBuff                       = CreateSpell(383997),
  ClearcastingBuff                      = CreateSpell(263725),
  ClarityBuff                           = CreateSpell(1216178), -- TWW S2 Tier 2pc
  EnlightenedBuff                       = CreateSpell(1217242),
  IntuitionBuff                         = CreateMultiSpell(455681, 1223797),
  LeydrinkerBuff                        = CreateSpell(453758),
  NetherPrecisionBuff                   = CreateSpell(383783),
  PresenceofMindBuff                    = CreateSpell(205025),
  SiphonStormBuff                       = CreateSpell(384267),
  -- Debuffs
  MagisSparkABDebuff                    = CreateSpell(453912),--450004
  MagisSparkABarDebuff                  = CreateSpell(451911),
  MagisSparkAMDebuff                    = CreateSpell(453898),
  TouchoftheMagiDebuff                  = CreateSpell(210824),
  -- Misc
  StopAM                                = CreateSpell(363653),
}
---@class MGCustomTable
Spell.Mage.Arcane = MergeTableByKey(Spell.Mage.Arcane, Spell.Mage.Custom)
---@class MGCommonsTable
Spell.Mage.Arcane = MergeTableByKey(Spell.Mage.Arcane, Spell.Mage.Commons, true)
---@class FrostfireTable
Spell.Mage.Arcane = MergeTableByKey(Spell.Mage.Arcane, Spell.Mage.Frostfire)
---@class SpellslingerTable
Spell.Mage.Arcane = MergeTableByKey(Spell.Mage.Arcane, Spell.Mage.Spellslinger)
---@class SunfuryTable
Spell.Mage.Arcane = MergeTableByKey(Spell.Mage.Arcane, Spell.Mage.Sunfury)

---@class FireTable
Spell.Mage.Fire = {
    -- Abilities
    Fireball                              = CreateSpell(133),
    Flamestrike                           = CreateSpell(2120),
    -- Talents
    AlexstraszasFury                      = CreateSpell(235870),
    CalloftheSunKing                      = CreateSpell(343222),
    Combustion                            = CreateSpell(190319),
    FeeltheBurn                           = CreateSpell(383391),
    FlameAccelerant                       = CreateSpell(203275),
    FireBlast                             = CreateSpell(108853),
    Firestarter                           = CreateSpell(205026),
    FlamePatch                            = CreateSpell(205037),
    Hyperthermia                          = CreateSpell(383860),
    ImprovedScorch                        = CreateSpell(383604),
    Kindling                              = CreateSpell(155148),
    PhoenixFlames                         = CreateSpell(257541),
    PhoenixReborn                         = CreateSpell(453123),
    Pyroblast                             = CreateSpell(11366),
    Quickflame                            = CreateSpell(450807),
    Scald                                 = CreateSpell(450746),
    Scorch                                = CreateSpell(2948),
    SearingTouch                          = CreateSpell(269644),
    SpontaneousCombustion                 = CreateSpell(451875),
    SunKingsBlessing                      = CreateSpell(383886),
    UnleashedInferno                      = CreateSpell(416506),
    -- Buffs
    CombustionBuff                        = CreateSpell(190319),
    FeeltheBurnBuff                       = CreateSpell(383395),
    FlameAccelerantBuff                   = CreateSpell(453283),
    FlamesFuryBuff                        = CreateSpell(409964), -- T30 4pc bonus
    HeatShimmerBuff                       = CreateSpell(458964),
    HeatingUpBuff                         = CreateSpell(48107),
    HotStreakBuff                         = CreateSpell(48108),
    HyperthermiaBuff                      = CreateSpell(383874),
    RollinHotBuff                         = CreateSpell(1219035), -- TWW S2 Tier 4pc
    SunKingsBlessingBuff                  = CreateSpell(383882),
    FuryoftheSunKingBuff                  = CreateSpell(383883),
    -- Debuffs
    IgniteDebuff                          = CreateSpell(12654),
    ImprovedScorchDebuff                  = CreateSpell(383608),

    -- Pool
    Pool                                  = CreateSpell(999910)
}
---@class MGCustomTable
Spell.Mage.Fire = MergeTableByKey(Spell.Mage.Fire, Spell.Mage.Custom)
---@class MGCommonsTable
Spell.Mage.Fire = MergeTableByKey(Spell.Mage.Fire, Spell.Mage.Commons, true)
---@class FrostfireTable
Spell.Mage.Fire = MergeTableByKey(Spell.Mage.Fire, Spell.Mage.Frostfire)
---@class SpellslingerTable
Spell.Mage.Fire = MergeTableByKey(Spell.Mage.Fire, Spell.Mage.Spellslinger)
---@class SunfuryTable
Spell.Mage.Fire = MergeTableByKey(Spell.Mage.Fire, Spell.Mage.Sunfury)

---@class FrostTable
Spell.Mage.Frost = {    
  -- Abilities
  Blizzard                              = CreateSpell(190356), --splash, 16
  ConeofCold                            = CreateSpell(120),--Melee, 12
  FireBlast                             = CreateSpell(319836),
  ColdSnap                              = CreateSpell(235219),
  -- Talents
  ColdFront                             = CreateSpell(382110),
  ColdestSnap                           = CreateSpell(417493),
  CometStorm                            = CreateSpell(153595), --splash, 6
  DeathsChill                           = CreateSpell(450331),
  DeepShatter                           = CreateSpell(378749),
  Flurry                                = CreateSpell(44614),
  FreezingRain                          = CreateSpell(270233),
  Frostbite                             = CreateSpell(378756),
  FrozenOrb                             = CreateSpell(84714), --splash, 16
  FrozenTouch                           = CreateSpell(205030),
  GlacialSpike                          = CreateSpell(199786), --splash, 8 (with splitting ice)
  IceCaller                             = CreateSpell(236662),
  IceLance                              = CreateSpell(30455), --splash, 8 (with splitting ice)
  IcyVeins                              = CreateSpell(12472),
  RayofFrost                            = CreateSpell(205021),
  SlickIce                              = CreateSpell(382144),
  SplinteringCold                       = CreateSpell(379049),
  SplinteringRay                        = CreateSpell(418733),
  -- Pet Abilities
  Freeze                                = CreateSpell(33395, "Pet"), --splash, 8
  -- Buffs
  BrainFreezeBuff                       = CreateSpell(190446),
  DeathsChillBuff                       = CreateSpell(454371),
  ExtendedBankrollBuff                  = CreateSpell(1216914), -- TWW S2 Tier 4pc
  FingersofFrostBuff                    = CreateSpell(44544),
  FreezingRainBuff                      = CreateSpell(270232),
  FreezingWindsBuff                     = CreateSpell(382106),
  GlacialSpikeBuff                      = CreateSpell(199844),
  IciclesBuff                           = CreateSpell(205473),
  IcyVeinsBuff                          = CreateSpell(12472),
  -- Debuffs
  FreezingWindsDebuff                   = CreateSpell(1216988),
  WintersChillDebuff                    = CreateSpell(228358),
  HypothermiaDebuff                     = CreateSpell(41425),
}
---@class MGCustomTable
Spell.Mage.Frost = MergeTableByKey(Spell.Mage.Frost, Spell.Mage.Custom)
---@class MGCommonsTable
Spell.Mage.Frost = MergeTableByKey(Spell.Mage.Frost, Spell.Mage.Commons, true)
---@class FrostfireTable
Spell.Mage.Frost = MergeTableByKey(Spell.Mage.Frost, Spell.Mage.Frostfire)
---@class SpellslingerTable
Spell.Mage.Frost = MergeTableByKey(Spell.Mage.Frost, Spell.Mage.Spellslinger)
---@class SunfuryTable
Spell.Mage.Frost = MergeTableByKey(Spell.Mage.Frost, Spell.Mage.Sunfury)

-- Items
if not Item.Mage then
    Item.Mage = {}
end

---@class MGCustomItemTable
Item.Mage.Custom = {
    -- Custom
    GladiatorsBadge = Item(201807, { 13, 14 }),
    PocketsizedComputationDevice = Item(167555, { 13, 14 }),
    NeuralSynapseEnhancer = Item(168973, { 13, 14 }),
    HyperthreadWristwraps                 = Item(168989, {9}),
}

---@class MGCommonsItemTable
Item.Mage.Commons = {
  -- DF Trinkets
  NymuesUnravelingSpindle               = Item(208615, {13, 14}),
  DragonfireBombDispenser               = Item(202610, {13, 14}),
  HornofValor                           = Item(133642, {13, 14}),
  IrideusFragment                       = Item(193743, {13, 14}),
  MoonlitPrism                          = Item(137541, {13, 14}),
  SpoilsofNeltharus                     = Item(193773, {13, 14}),
  TimebreachingTalon                    = Item(193791, {13, 14}),
  -- TWW Trinkets
  BurstofKnowledge                      = Item(231424, {13, 14}),
  HouseofCards                          = Item(230027, {13, 14}),
  AberrantSpellforge                    = Item(212451, {13, 14}),
  FearbreakersEcho                      = Item(224449, {13, 14}),
  HighSpeakersAccretion                 = Item(219303, {13, 14}),
  ImperfectAscendancySerum              = Item(225654, {13, 14}),
  MadQueensMandate                      = Item(212454, {13, 14}),
  MereldarsToll                         = Item(219313, {13, 14}),
  SignetofthePriory                     = Item(219308, {13, 14}),
  SpymastersWeb                         = Item(220202, {13, 14}),
  TreacherousTransmitter                = Item(221023, {13, 14}),
  -- TWW Gladiator's Badges
  ForgedGladiatorsBadge                 = Item(218713, {13, 14}),
  -- DF Gladiator's Badges
  CrimsonGladiatorsBadge                = Item(201807, {13, 14}),
  DraconicGladiatorsBadge               = Item(216279, {13, 14}),
  ObsidianGladiatorsBadge               = Item(205708, {13, 14}),
  VerdantGladiatorsBadge                = Item(209343, {13, 14}),
  -- Other On-Use Items
  Dreambinder                           = Item(208616, {16}),
}

---@class ArcaneItemTable
Item.Mage.Arcane = {
  -- TWW Trinkets
  AberrantSpellforge                    = Item(212451, {13, 14}),
  FearbreakersEcho                      = Item(224449, {13, 14}),
  HighSpeakersAccretion                 = Item(219303, {13, 14}),
  MadQueensMandate                      = Item(212454, {13, 14}),
  MereldarsToll                         = Item(219313, {13, 14}),
  QuickwickCandlestick                  = Item(225649, {13, 14}),
  SignetofthePriory                     = Item(219308, {13, 14}),
}
---@class MGCustomItemTable
Item.Mage.Arcane = MergeTableByKey(Item.Mage.Arcane, Item.Mage.Custom)
---@class MGCommonsItemTable
Item.Mage.Arcane = MergeTableByKey(Item.Mage.Arcane, Item.Mage.Commons)

---@class FireItemTable
Item.Mage.Fire = {
}
---@class MGCustomItemTable
Item.Mage.Fire = MergeTableByKey(Item.Mage.Fire, Item.Mage.Custom)
---@class MGCommonsItemTable
Item.Mage.Fire = MergeTableByKey(Item.Mage.Fire, Item.Mage.Commons)

---@class FrostItemTable
Item.Mage.Frost = {
    -- TWW Trinkets
    BurstofKnowledge                      = Item(231424, {13, 14}),
}
---@class MGCustomItemTable
Item.Mage.Frost = MergeTableByKey(Item.Mage.Frost, Item.Mage.Custom)
---@class MGCommonsItemTable
Item.Mage.Frost = MergeTableByKey(Item.Mage.Frost, Item.Mage.Commons)


Spell.Mage.Arcane.ArcaneExplosion.MeleeRange = 10
Spell.Mage.Arcane.BlastWave.MeleeRange = 6.5
Spell.Mage.Arcane.ConeofCold.MeleeRange = 12
Spell.Mage.Arcane.DragonsBreath.MeleeRange = 12

Spell.Mage.Fire.ArcaneExplosion.MeleeRange = 10
Spell.Mage.Fire.BlastWave.MeleeRange = 6.5
Spell.Mage.Fire.ConeofCold.MeleeRange = 8
Spell.Mage.Fire.DragonsBreath.MeleeRange = 8

Spell.Mage.Frost.FrostNova.MeleeRange = 12
Spell.Mage.Frost.ArcaneExplosion.MeleeRange = 10
Spell.Mage.Frost.BlastWave.MeleeRange = 6.5
Spell.Mage.Frost.ConeofCold.MeleeRange = 8
Spell.Mage.Frost.DragonsBreath.MeleeRange = 8

-- Generic
Spell.Mage.Arcane.Polymorph:SetGeneric(MAGE_ARCANE_SPECID, 'Generic1')
Spell.Mage.Arcane.SpellSteal:SetGeneric(MAGE_ARCANE_SPECID, 'Generic2')
Spell.Mage.Arcane.Supernova:SetGeneric(MAGE_ARCANE_SPECID, 'Generic3')

Spell.Mage.Fire.Polymorph:SetGeneric(MAGE_FIRE_SPECID, 'Generic1')
Spell.Mage.Fire.SpellSteal:SetGeneric(MAGE_FIRE_SPECID, 'Generic2')
Spell.Mage.Fire.Scorch:SetGeneric(MAGE_FIRE_SPECID, 'Generic3')

Spell.Mage.Frost.Polymorph:SetGeneric(MAGE_FROST_SPECID, 'Generic1')
Spell.Mage.Frost.SpellSteal:SetGeneric(MAGE_FROST_SPECID, 'Generic2')
Spell.Mage.Frost.IceLance:SetGeneric(MAGE_FROST_SPECID, 'Generic3')
Spell.Mage.Frost.ConeofCold.MeleeRange = 12
Spell.Mage.Frost.FrozenOrb.Range = 40