---@class MainAddon
local MainAddon = MainAddon
local HL = HeroLibEx;
---@class Unit
local Unit = HL.Unit;
---@class Unit
local Player = Unit.Player;
local Party = Unit.Party;
local Arena = Unit.Arena;
---@class Unit
local Target = Unit.Target;
---@class Spell
local Spell = HL.Spell
local UnitIsUnit = UnitIsUnit
local pairs = pairs
local setmetatable = setmetatable
local unitIDtargets = setmetatable({}, { -- string cache for faster performance
    __index = function(t, v)
        t[v] = v .. "target"
        return t[v]
    end,
})


function Unit:IsFocused(specs, burst, deffensive, range)
    local unitID = self.UnitID

    if self:IsEnemy() then
        local playerTarget = "playertarget"
        if UnitIsUnit(playerTarget, unitID) then
            return true, 'On Player'
        end
        for _, PartyUnit in pairs(Unit.Party) do
            if PartyUnit:Exists() then
                local partyTarget = unitIDtargets[PartyUnit.UnitID]
                if UnitIsUnit(partyTarget, unitID)
                        and not UnitIsUnit(partyTarget, "player")
                        and (not specs or
                                (
                                    specs == "DAMAGER" and PartyUnit:IsADamager() or
                                    specs == "MELEE" and PartyUnit:IsAMelee() or
                                    specs == "HEALER" and PartyUnit:IsAHealer() or
                                    specs == "TANK" and PartyUnit:IsATank() or
                                    specs == "RANGED" and PartyUnit:IsARanged() or
                                    specs == "CASTER" and PartyUnit:IsACaster()
                                )
                            )
                        and (not burst or PartyUnit:DamageBuffsUp())
                        and (not deffensive or self:DeffBuffsRemains() < 2)
                        and (not range or PartyUnit:IsInRange(range)) then
                    return true, 'On Party'
                end
            end
        end
    else
        for _, ArenaUnit in pairs(Unit.Arena) do
            if ArenaUnit:Exists() then
                local arenaTarget = unitIDtargets[ArenaUnit.UnitID]
                if UnitIsUnit(arenaTarget, unitID)
                        and (not specs or
                        (
                                specs == "DAMAGER" and ArenaUnit:IsADamager() or
                                        specs == "MELEE" and ArenaUnit:IsAMelee() or
                                        specs == "HEALER" and ArenaUnit:IsAHealer() or
                                        specs == "TANK" and ArenaUnit:IsATank() or
                                        specs == "RANGED" and ArenaUnit:IsARanged() or
                                        specs == "CASTER" and ArenaUnit:IsACaster()
                        )
                )
                        and (not burst or ArenaUnit:DamageBuffsUp()) then
                    --and (not deffensive or self:DeffBuffsRemains() < 2)
                    --and (not range or ArenaUnit:IsInRange(range)) then
                    return true, 'On Arena'
                end
            end
        end
    end

    return false
end
function Unit:DamageBuffsUp()
    return self:HasBuffList(MainAddon.CONST.DamageBuffs, true)
end
function Unit:DisarmBuffsUp()
    return self:HasBuffList(MainAddon.CONST.DisarmPvP, true)
end
function Unit:DisarmBuffsArenaUp()
    return self:HasBuffList(MainAddon.CONST.DisarmArenaPvP, true)
end
function Unit:DefensiveBuffsUp()
    return self:HasBuffList(MainAddon.CONST.DeffBuffs, true)
end
function Unit:DeffBuffsRemains(BypassRecovery)
    for spellID, key in pairs(MainAddon.CONST.DeffBuffs) do
        local DeffBuffSpell = spellID
        if self:BuffUp(DeffBuffSpell, true) then
            return self:BuffRemains(DeffBuffSpell, nil, BypassRecovery), 'Deff Up'
        end
    end

    return 0
end
local DamagePhysImun
local DamageMagicImun
local CCTotalImunPhys
local CCMagicImun
local KickImunPhys
function Unit:AbsentImun(dmgType, ccImun, kickImun, slowImun, stunImun, fearImun, skipKarma, skipBreakable)
    local isMagic = dmgType == "Magic"
    local isEnemy = self:IsEnemy()

    --Check PvP_ImmunityBuffs
    if self:HasDebuffList(MainAddon.CONST.PvP_ImmunityBuffs, true) then
        return true, 'PvP Immunity Buffs'
    end

    -- STOP Damage on Breakable
    if isEnemy and not skipBreakable or (not ccImun and not kickImun and not slowImun and not stunImun and not fearImun) then
        if self:InBreakable() then
            return false, "Dont break CC"
        end
    end

    if Player:InPvP() and self:IsAPlayer() and isEnemy then
        --Check TotalImun
        if self:HasBuffList(MainAddon.CONST.ImunBuffs.TotalImun, true) then
            return false, "TotalImun"
        end

        --Check CCTotalImun
        if ccImun then
            if self:HasBuffList(MainAddon.CONST.ImunBuffs.CCTotalImun, true) then
                return false, "CCTotalImun"
            end
        end

        --Check KickImun
        if kickImun then
            if self:HasBuffList(MainAddon.CONST.ImunBuffs.KickImun, true) then
                return false, "KickImun"
            end
        end

        --Check Freedom
        if slowImun then
            if self:HasBuffList(MainAddon.CONST.ImunBuffs.Freedom, true) then
                return false, "Freedom"
            end
        end

        --check StunImun
        if stunImun then
            if self:HasBuffList(MainAddon.CONST.ImunBuffs.StunImun, true) then
                return false, "StunImun"
            end
        end

        --check FearImun
        if fearImun then
            if self:HasBuffList(MainAddon.CONST.ImunBuffs.FearImun, true) then
                return false, "FearImun"
            end
        end

        if isMagic then
            --Check CCMagicImun
            if ccImun then
                if self:HasBuffList(MainAddon.CONST.ImunBuffs.CCMagicImun, true) then
                    return false, "CCMagicImun"
                end
            end
            --Check DamageMagicImun
            if self:HasBuffList(MainAddon.CONST.ImunBuffs.DamageMagicImun, true) then
                return false, "DamageMagicImun"
            end
        else
            --Check DamagePhysImun
            if self:HasBuffList(MainAddon.CONST.ImunBuffs.DamagePhysImun, true) then
                return false, "DamagePhysImun"
            end
        end

        if not skipKarma and not self:WithOutKarmed() then
            return false, "With Karma"
        end
    end

    return true, "Fallback"
end

function Unit:WithOutKarmed()
    if self:IsEnemy() then
        if self:BuffUp(267217, true) then
            if Player:DebuffRemains(25771, true) >= 20 then
                return false
            else
                for _, PartyUnit in pairs(Unit.Party) do
                    if PartyUnit:Exists() then
                        if PartyUnit:DebuffRemains(25771, true) >= 20 then
                            return false
                        end
                    end
                end
            end
        end
    else
        if self:BuffUp(267217, true) then
            for _, ArenaUnit in pairs(Unit.Arena) do
                if ArenaUnit:Exists() then
                    if ArenaUnit:DebuffRemains(25771, true) >= 20 then
                        return false
                    end
                end
            end
        end
    end
    return true
end

function Unit:HasPvPImmunity()
    if not self:Exists() then
        return false, 'Unit does not exist'
    end
    if self:HasBuffList(MainAddon.CONST.PvP_ImmunityBuffs, true) then
        return true, 'Has Immunity Buffs'
    end
    return false, "Fallback"
end

function Unit:InDisarm()
    if self:HasDebuffList(MainAddon.CONST.CCList.Disarm, true) then
        return true, 'InDisarm'
    end
    return false
end

function Unit:InSlow()
    if self:HasDebuffList(MainAddon.CONST.CCList.Slow, true) then
        return true, 'InSlow'
    end
    return false
end

function Unit:InBreakable()
    if self:HasDebuffList(MainAddon.CONST.CCList.Breakable, true) then
        return true, 'InBreakable'
    end
    return false
end

function Unit:ShouldSlow(dmgType)
    if self:MaxSpeed() >= 100 and (self:IsAPlayer() or not Player:InPvP()) and not self:InCC() and not self:InSlow() and self:HealthPercentage() > 35 and not Player:DamageBuffsUp() and self:AbsentImun(dmgType, true, nil, true, nil, nil, true) then
        return true
    end

    return false
end

function Unit:GetDMG()
    local total, Hits, phys, magic, swingD, takenDS, takenH = MainAddon.getDMG(self)
    return total, Hits, phys, magic, swingD, takenDS, takenH
end

function MainAddon.AllyDamageDebuffsUp(Unit)
    local class = Unit:Class()
    if class == "ROGUE" then
        if Player:DebuffUp(79140, true) then
            return true
        end

        for _, PartyUnit in pairs(Party) do
            if PartyUnit:DebuffUp(79140, true) or (Unit:SpecID() == 259 and PartyUnit:DebuffUp(408, true)) then
                return true
            end
        end
    end
    if class == "WARRIOR" then
        if Player:DebuffUp(208086, true) then
            return true
        end

        for _, PartyUnit in pairs(Party) do
            if PartyUnit:DebuffUp(208086, true) then
                return true
            end
        end
    end
    return false
end

function Unit:ShouldDisarm(dmgType)
    -- if Player:InArena() then
    --     if self:Class() ~= "DRUID" and (self:IsAMelee() or self:IsARanged()) and (self:DisarmBuffsArenaUp() or MainAddon.AllyDamageDebuffsUp(self) or MainAddon.AllyLowestHP() <= 30) and not self:InCC() and not self:InDisarm() and self:AbsentImun(dmgType, true, nil, nil, nil, nil, true) then
    --         return true
    --     end
    -- else
    if self:Class() ~= "DRUID" and (not Player:InArena() or self:IsAMelee() or self:IsARanged()) and (self:DisarmBuffsUp() or (MainAddon.AllyDamageDebuffsUp(self) or MainAddon.AllyLowestHP() <= 30) and Player:InArena()) and not self:InCC() and not self:InDisarm() and self:AbsentImun(dmgType, true, nil, nil, nil, nil, true) then
        return true
    end
    -- end
    return false
end


function Player:ShouldReflectPvP(delay)
    local latency = delay + HL.Latency()
    local castRemains = 0
    local ArenaUnit
    local spellID
    if Player:InArena() then
        for i = 1, 3 do
            ArenaUnit = Arena['arena' .. i]
            if ArenaUnit:Exists() then
                castRemains = ArenaUnit:CastRemains()
                spellID = ArenaUnit:CastSpellID()
                if castRemains > 0 and castRemains <= latency then
                    -- Reflect Damage
                    if self:IsFocused("DAMAGER") and MainAddon.getDMG(self) > 0 and (MainAddon.CONST.PvP_Reflect[spellID] and MainAddon.CONST.PvP_Reflect[spellID].Type and MainAddon.CONST.PvP_Reflect[spellID].Type == "Damage" or ArenaUnit:BuffUp(190319, true)) then
                        return true, 'Reflect Spell Damage'
                    end
                    -- Reflect CC's
                    if (not self:IsFocused("DAMAGER") or MainAddon.getDMG(self) < 3) and
                            (
                                    MainAddon.CONST.PvP_Reflect[spellID] and
                                            (MainAddon.CONST.PvP_Reflect[spellID].Type == "Incapacitate" and
                                                    self:GetDR("incapacitate") >= 0.5 or
                                                    MainAddon.CONST.PvP_Reflect[spellID].Type == "Disorient" and
                                                            self:GetDR("disorient") >= 0.5)
                            ) then
                        return true, 'Reflect CC'
                    end
                end
                -- Reflect Convoke
                if self:IsFocused("DAMAGER") and MainAddon.getDMG(self) > 0 and ArenaUnit:ChannelSpellID() == 323764 then
                    return true, 'Reflect Convoke'
                end
            end
        end
    end
    return false
end

function Unit:DisarmRemains()
    local HasDebuff, AuraDuration, AuraExpirationTime, Remains = self:HasDebuffList(MainAddon.CONST.CCList.Disarm, true)
    if HasDebuff then
        return Remains
    end
    return 0
end

function Unit:InIncapacitate()
    if self:HasDebuffList(MainAddon.CONST.CCList.Incapacitate, true) then
        return true, 'InIncapacitate'
    end
    return false
end

function Unit:IncapacitateRemains()
    local HasDebuff, AuraDuration, AuraExpirationTime, Remains = self:HasDebuffList(MainAddon.CONST.CCList.Incapacitate, true)
    if HasDebuff then
        return Remains
    end
    return 0
end

function Unit:InDisorient()
    if self:HasDebuffList(MainAddon.CONST.CCList.Disorient, true) then
        return true, 'InDisorient'
    end
    return false
end

function Unit:DisorientRemains()
    local HasDebuff, AuraDuration, AuraExpirationTime, Remains = self:HasDebuffList(MainAddon.CONST.CCList.Disorient, true)
    if HasDebuff then
        return Remains
    end
    return 0
end

function Unit:BreakableRemains()
    local HasDebuff, AuraDuration, AuraExpirationTime, Remains = self:HasDebuffList(MainAddon.CONST.CCList.Breakable, true)
    if HasDebuff then
        return Remains
    end
    return 0
end

function Unit:InFear()
    if self:HasDebuffList(MainAddon.CONST.CCList.Fear, true) then
        return true, 'InFear'
    end
    return false
end

function Unit:FearRemains()
    local HasDebuff, AuraDuration, AuraExpirationTime, Remains = self:HasDebuffList(MainAddon.CONST.CCList.Fear, true)
    if HasDebuff then
        return Remains
    end
    return 0
end

function Unit:InSleep()
    if self:HasDebuffList(MainAddon.CONST.CCList.Sleep, true) then
        return true, 'InSleep'
    end
    return false
end

function Unit:SleepRemains()
    local HasDebuff, AuraDuration, AuraExpirationTime, Remains = self:HasDebuffList(MainAddon.CONST.CCList.Sleep, true)
    if HasDebuff then
        return Remains
    end
    return 0
end

function Unit:InSilence()
    if self:HasDebuffList(MainAddon.CONST.CCList.Silence, true) then
        return true, 'InSilence'
    end
    return false
end

function Unit:SilenceRemains()
    local HasDebuff, AuraDuration, AuraExpirationTime, Remains = self:HasDebuffList(MainAddon.CONST.CCList.Silence, true)
    if HasDebuff then
        return Remains
    end
    return 0
end

function Unit:InPhysStun()
    if self:HasDebuffList(MainAddon.CONST.CCList.PhysStun, true) then
        return true, 'InPhysStun'
    end
    return false
end

function Unit:PhysStunRemains()
    local HasDebuff, AuraDuration, AuraExpirationTime, Remains = self:HasDebuffList(MainAddon.CONST.CCList.PhysStun, true)
    if HasDebuff then
        return Remains
    end
    return 0
end

function Unit:InStun()
    if self:HasDebuffList(MainAddon.CONST.CCList.Stun, true) then
        return true, 'InStun'
    end
    return false
end

function Unit:StunRemains()
    local HasDebuff, AuraDuration, AuraExpirationTime, Remains = self:HasDebuffList(MainAddon.CONST.CCList.Stun, true)
    if HasDebuff then
        return Remains
    end
    return 0
end

function Unit:InRoot()
    if self:HasDebuffList(MainAddon.CONST.CCList.Root, true) then
        return true, 'InRoot'
    end
    return false
end

function Unit:RootRemains()
    local HasDebuff, AuraDuration, AuraExpirationTime, Remains = self:HasDebuffList(MainAddon.CONST.CCList.Root, true)
    if HasDebuff then
        return Remains
    end
    return 0
end

function Unit:SlowRemains()
    local HasDebuff, AuraDuration, AuraExpirationTime, Remains = self:HasDebuffList(MainAddon.CONST.CCList.Slow, true)
    if HasDebuff then
        return Remains
    end
    return 0
end

function Unit:InMagicSlow()
    if self:HasDebuffList(MainAddon.CONST.CCList.InMagicSlow, true) then
        return true, 'InMagicSlow'
    end
    return false
end

function Unit:MagicSlowRemains()
    local HasDebuff, AuraDuration, AuraExpirationTime, Remains = self:HasDebuffList(MainAddon.CONST.CCList.InMagicSlow, true)
    if HasDebuff then
        return Remains
    end
    return 0
end

do
    local function GetBoomy()
        for i, v in pairs(Arena) do
            if v:SpecID() == 102 then
                return true
            end
        end

        return false
    end

    local function GetHealer()
        for i, v in pairs(Party) do
            if v:IsAHealer() then
                return true
            end
        end
    end

    function Unit:ShouldFreedom()
        if self:IsEnemy() then
            return false
        end

        if Player:InPvP() then
            local IsHoldFreedom = GetBoomy() and GetHealer()
            if ((IsHoldFreedom and self:IsAHealer() and self:InRoot() and self:DebuffUp(78675, true)) or not IsHoldFreedom and ((self:IsFocused(nil, true) or self:IsAMelee() and self:DamageBuffsUp()) and (self:Speed() > 0 and self:Speed() < 64 or self:InRoot()))) and self:AbsentImun() then
                return true, 'Should Freedom'
            end
        else
            if self:HasDebuffList(MainAddon.CONST.PvE_Freedom, true) then
                return true, 'Should Freedom PvE'
            end
        end

        return false
    end
end

function Unit:ShouldSAC()
    if self:InLoS() and self:IsFocused("DAMAGER", true) and self:GetDMG() > 0 and not self:DefensiveBuffsUp() and self:AbsentImun() then
        return true
    end

    return false
end

function MainAddon.GetFriendlyHeal()
    for _, v in pairs(Party) do
        if v:IsAHealer() then
            return v
        end
    end

    return Unit.Raid.raid40
end

function MainAddon.GetEnemyHeal()
    for _, v in pairs(Arena) do
        if v:IsAHealer() then
            return v
        end
    end

    return Unit.Raid.raid40
end

function Player:ShouldStopDragonsBreath()
    local unitHeal = MainAddon.GetFriendlyHeal()

    if unitHeal:Exists() and unitHeal:DebuffUp(321707, true) then
        return true
    end

    return false
end

function MainAddon.HasInvisibleUnit()
    -- @return boolean, Unit
    if Player:InArena() then
        for i = 1, 3 do
            local ArenaUnit = Arena['arena' .. i]
            local class = ArenaUnit:Class()
            if class and not ArenaUnit:IsDeadOrGhost() and (class == "MAGE" or class == "ROGUE" or class == "DRUID") and not ArenaUnit:IsVisible() then
                return true, ArenaUnit
            end
        end
    end

    return false, nil
end

--PVP SPELLS
local ShacklesofMalediction = Spell(356567)

function Unit:StopShackles()
    if self:IsChanneling(ShacklesofMalediction) then
        return true
    end
    return false
end

function Player:ShouldWarBanner()
    if Player:InArena() then
        local combatTime = HL.CombatTime()
        local friendHeal = MainAddon.GetFriendlyHeal()

        if combatTime == 0 then
            -- Warbanner in SAP Opener
            for _, v in pairs(Party) do
                if v:DebuffUp(6770, true) then
                    return true, 'SAP'
                end
            end
        else
            if MainAddon.AllyLowestHP() <= 30 then
                return true, 'Group Low HP'
            end

            local castRemains = 0
            local ArenaUnit
            local spellID
            for i = 1, 3 do
                ArenaUnit = Arena['arena' .. i]
                if ArenaUnit:Exists() then
                    if ArenaUnit:BuffUp(185313, true) or ArenaUnit:BuffUp(185422, true) then
                        return true, 'Shadow Dance'
                    end

                    castRemains = ArenaUnit:CastRemains()
                    spellID = ArenaUnit:CastSpellID()
                    if castRemains > 0 and castRemains <= 0.3 then
                        if (not friendHeal:IsFocused("DAMAGER") or MainAddon.getDMG(friendHeal) < 3) and
                                (
                                        MainAddon.CONST.PvP_Reflect[spellID] and
                                                (
                                                        MainAddon.CONST.PvP_Reflect[spellID].Type == "Incapacitate" and
                                                                friendHeal:GetDR("incapacitate") == 1 or
                                                                MainAddon.CONST.PvP_Reflect[spellID].Type == "Disorient" and
                                                                        friendHeal:GetDR("disorient") == 1
                                                )
                                ) then
                            return true, 'Stop CC'
                        end
                    end
                end
            end
        end
    else
        if Player:DamageBuffsUp() then
            return true
        end
    end
end

function Player:EnemiesInBreakable(range, OnlyHeal)
    local ArenaUnit
    local enemyRange = range or 10
    if Player:InArena() then
        for i = 1, 3 do
            ArenaUnit = Arena['arena' .. i]
            if ArenaUnit:Exists() then
                if (not OnlyHeal or ArenaUnit:IsAHealer()) and ArenaUnit:IsInRange(enemyRange) and not ArenaUnit:IsUnit(Target) and ArenaUnit:InBreakable() then
                    return true
                end
            end
        end
    end

    return false
end