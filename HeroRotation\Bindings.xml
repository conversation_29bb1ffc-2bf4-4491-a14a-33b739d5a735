<Bindings>
  <Binding name="HEROROTATION_CDS" description="Toggle On/Off the CDs." header="HEROROTATION" category="ADDONS">
    HeroRotation.CmdHandler("cds");
  </Binding>
  <Binding name="HEROROTATION_AOE" description="Toggle On/Off the AoE." category="ADDONS">
    HeroRotation.CmdHandler("aoe");
  </Binding>
  <Binding name="HEROROTATION_TOGGLE" description="Toggle On/Off the Addon." category="ADDONS">
    HeroRotation.CmdHandler("toggle");
  </Binding>
  <Binding name="HEROROTATION_UNLOCK" description="Unlock the addon's icons to move them." category="ADDONS">
    HeroRotation.CmdHandler("lock");
  </Binding>
  <Binding name="HEROROTATION_LOCK" description="Lock the addon's icon in place." category="ADDONS">
    HeroRotation.CmdHandler("unlock");
  </Binding>
</Bindings>
