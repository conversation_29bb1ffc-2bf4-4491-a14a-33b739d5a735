function A_251(...)
  --HR Update: feat(FrostDK): Update to latest APL for 11.1.5
  --REMEMBER: Cast(<PERSON><PERSON>, Player)
  ---@class MainAddon
  local MainAddon = MainAddon
  local M = MainAddon
  local HealingEngine = MainAddon.HealingEngine
  -- HeroLib
  local HL = HeroLibEx
  ---@class Unit
  local Unit = HL.Unit
  ---@class Unit
  local Player = Unit.Player
  ---@class Unit
  local Target = Unit.Target
  ---@class Unit
  local MouseOver = Unit.MouseOver
  ---@class Unit
  local Arena = Unit.Arena
  ---@class Spell
  local Spell = HL.Spell
  ---@class Item
  local Item = HL.Item
  -- HeroRotation
  local Cast = M.Cast
  local ForceCastDisplay = M.ForceCastDisplay
  local CastCycle = MainAddon.CastCycle
  local CastTargetIf = MainAddon.CastTargetIf
  local TargetIsValid = false
  local CDsON = MainAddon.CDsON
  local AoEON = MainAddon.AoEON

  ---@class DeathKnight
  local DeathKnight = M.DeathKnight

  -- Spells
  local S = Spell.DeathKnight.Frost
  local I = Item.DeathKnight.Frost

  --LUA
  local Delay                = C_Timer.After
  local GetInventoryItemLink = _G['GetInventoryItemLink']
  local GetMouseFoci = _G['GetMouseFoci']
  local strsplit   = _G['strsplit']
  local IsEquippedItemType = _G['C_Item']['IsEquippedItemType']
  local GetTime = _G['GetTime']
  local num = M.num
  local bool = M.bool

  local Temp = {
      dmgPhys = "Physical",
      dmgMagic = "Magic",
  }

  local GetSetting = MainAddon.Config.GetClassSetting
  local Config_Key = MainAddon.GetClassVariableName()
  local Config_Color = '09bbe7'
  local Config_Table = {
      key = Config_Key,
      title = 'Death Knight - Frost',
      subtitle = '?? ' .. MainAddon.Version,
      width = 600,
      height = 700,
      profiles = true,
      config = {
          { type = 'header', text = '', size = 24, align = 'Center', color = Config_Color },
          { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
          { type = 'header', text = 'DPS', color = Config_Color },
          { type = 'spacer' },
          { type = 'checkspin', text = 'Stand still threshold (in seconds)', key = 'DPSMovingValue', min = 0, max = 10, default_spin = 0.35, default_check = false },
          { type = 'dropdown', text = '    Affected Spells:', 
            key = 'DPSMovingValueSpells', 
            multiselect = true, 
            list = { 
              { text = 'Abomination Limb', key = 'abomlimb' },
              { text = 'Breath of Sindragosa', key = 'bos' }, 
              { text = 'Death and Decay', key = 'dnd' }, 
              { text = 'Pillar of Frost', key = 'pof' }, 
              { text = 'Reapers Mark', key = 'mark' } 
            }, 
            default = { 'abomlimb', 'bos', 'dnd', 'pof', 'mark' } 
          },                           
          { type = 'checkspin', text = ' Runic power to start Breath of Sindragosa', key = 'bosrunictreshold', icon = S.BreathofSindragosa:ID(), min = 1, max = 100, default = 68 },
          { type = 'checkspin', text = ' Check for enemies in melee (in %)', key = 'meleeratio', min = 1, max = 100, default_spin = 20, default_check = false, },
          { type = 'dropdown', text = '    Affected Spells:', 
            key = 'meleeratiospells', 
            multiselect = true, 
            list = { 
              { text = 'Abomination Limb', key = 'ratioAbom' }, 
              { text = 'Breath of Sindragosa', key = 'ratioBoS' }, 
              { text = 'Death and Decay', key = 'ratioDnD' }, 
              { text = 'Pillar of Frost', key = 'ratioPoF' }, 
              { text = 'Reapers Mark', key = 'ratioMark' } 
            }, 
            default = { 'ratioAbom', 'ratioBoS', 'ratioDnD', 'ratioPoF', 'ratioMark' } 
          },
          { type = 'checkbox', text = ' Use Anti-Magic Shell offensively', key = 'amsoffensive', icon = S.AntiMagicShell:ID(),  default = false },
          { type = 'header', text = 'Defensives', color = Config_Color },
          { type = 'checkspin', text = ' Death Strike: Proc (Solo)', key = 'darksuccor', icon = S.DeathStrike:ID(), min = 1, max = 100, default_spin = 75, default_check = true },
          { type = 'checkspin', text = ' Death Strike: Proc (DG/Raid)', key = 'darksuccorraid', icon = S.DeathStrike:ID(), min = 1, max = 100, default_spin = 25, default_check = true },
          { type = 'checkspin', text = ' Death Strike: Emergency', key = 'dsemergency', icon = S.DeathStrike:ID(), min = 1, max = 100, default_spin = 15, default_check = true },
          { type = 'checkspin', text = ' Death Pact', key = 'dp', icon = S.DeathPact:ID(),  min = 1, max = 100, default_spin = 55, default_check = true },
          { type = 'checkspin', text = ' Icebound Fortitude', key = 'ib', icon = S.IceboundFortitude:ID(),  min = 1, max = 100, default_spin = 40, default_check = true },
          { type = 'spacer' },
          { type = 'header', text = 'Utilities', color = Config_Color },
          { type = 'checkbox', text = ' Lichborne (Anti-Fear)', key = 'lichborne', icon = S.Lichborne:ID(),  default = true },
          { type = 'checkbox', text = ' Death and Decay when moving', icon = S.DeathAndDecay:ID(), default = false, key = 'DnDMoving' },
          { type = 'checkbox', text = ' Death Grip OOR', key = 'deathgrip_ooc', icon = S.DeathGrip:ID(), default = false },
          { type = 'dropdown', text = ' Raise Ally', key = 'auto_raise_ally', icon = S.RaiseAlly:ID(), multiselect = true, list = { { text = 'Target', key = 'auto_raise_ally_target' }, { text = 'MouseOver', key = 'auto_raise_ally_mouseover' } }, default = {} },
          { type = 'checkbox', text = ' Toast message about Breath of Sindragosa', icon = S.BreathofSindragosa:ID(), key = 'toast_breath', default = true},
          { type = 'spacer' },
          { type = 'spacer' },
          { type = 'spacer' },
          { type = 'spacer' },
          { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
      }
  }
  Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
  Config_Table.config = MainAddon.BuildDPSTrinketUI(Config_Table.config, Config_Color)
  Config_Table.config = MainAddon.BuildCombatPotionUI(Config_Table.config, "Frost", Config_Color)
  MainAddon.SetConfig(251, Config_Table)

  -- Custom
  local BreathTimer = 0
  local BreathTimeStamp = GetTime()
  local DPSMovingValue_check  = GetSetting('DPSMovingValue_check', false)
  local DPSMovingValue_spin   = GetSetting('DPSMovingValue_spin', 0.5)
  local DPSMovingValueSpells  = GetSetting('DPSMovingValueSpells', {})
  
  local function TotalEnemies_Players()
      local TotalEnemies = 0
      ---@param arenaUnit Unit
      for i, arenaUnit in pairs(Arena) do
          if arenaUnit:Exists() and not arenaUnit:IsDeadOrGhost() then
              TotalEnemies = TotalEnemies + 1
          end
      end

      return TotalEnemies
  end
  ---@param TargetUnit Unit
  local function EvaluateInterrupt(TargetUnit)
      local spellID = TargetUnit:CastSpellID() or TargetUnit:ChannelSpellID() or 0
      if spellID > 0 then
          return TargetUnit:IsInterruptible() and TargetUnit:CastRemains() > 0.15
      end
      return false
  end
  local function MindFreeze_Bos()
      local EnemiesMindFreeze = Player:GetEnemiesInRangeFilter(15)

      if MouseOver:Exists() and EvaluateInterrupt(MouseOver) and S.MindFreeze:IsReady(MouseOver) then
          MainAddon.SetTopColor(1, "Interrupt Mouseover")
      end

      if S.MindFreeze:IsCastable() then
          if CastCycle(S.MindFreeze, EnemiesMindFreeze, EvaluateInterrupt, false, nil, nil, true) then
              return "Interrupt BoS"
          end
      end
  end
  local function PlayerDefensives()
      local autorebith = GetSetting('auto_raise_ally', {})
      if S.RaiseAlly:IsReady(MouseOver) and autorebith['auto_raise_ally_mouseover'] then
        local GetMouseFociCache = GetMouseFoci()
        ---@class Frame
        local MouseFocus = GetMouseFociCache[1]

          local FrameName = MouseFocus and not MouseFocus:IsForbidden() and MouseFocus:GetName() or "None"
          if FrameName ~= "WorldFrame" and FrameName ~= "None" then
              if MouseOver:EvaluateRebirth() then
                  if Cast(S.RaiseAlly) then
                      MainAddon.UI:ShowToast("Raise Ally", MouseOver:Name(), MainAddon.GetTexture(S.RaiseAlly))
                      return "RaiseAlly MouseOver"
                  end
              end
          end
      end
      if S.RaiseAlly:IsReady() and autorebith['auto_raise_ally_target'] then
          if Target:EvaluateRebirth() then
              if Cast(S.RaiseAlly) then
                  MainAddon.UI:ShowToast("Raise Ally", Target:Name(), MainAddon.GetTexture(S.RaiseAlly))
                  return "Intercession Target"
              end
          end
      end

      if GetSetting('dsemergency_check', false) then
          if S.DeathStrike:IsReady() and Player:HealthPercentage() <= GetSetting('dsemergency_spin', 30) then
              if Cast(S.DeathStrike) then
                  return "death_strike standard 10";
              end
          end
      end

      if GetSetting('darksuccor_check', false) and (not Player:IsInDungeonArea() and not Player:IsInRaidArea()) then
          if S.DeathStrike:IsReady() and Player:BuffUp(S.DarkSuccorBuff) and Player:HealthPercentage() <= GetSetting('darksuccor_spin', 30) then
              if Cast(S.DeathStrike) then
                  return "death_strike low hp or proc";
              end
          end
      end

      if GetSetting('darksuccorraid_check', false) and (Player:IsInDungeonArea() or Player:IsInRaidArea()) then
          if S.DeathStrike:IsReady() and Player:BuffUp(S.DarkSuccorBuff) and Player:HealthPercentage() <= GetSetting('darksuccorraid_spin', 30) then
              if Cast(S.DeathStrike) then
                  return "death_strike low hp or proc raid";
              end
          end
      end

      if GetSetting('lichborne', false) and (Player:IsFeared() or Player:IsSleeping()) then
          if S.Lichborne:IsReady(Player, nil, nil, true) then
              if Cast(S.Lichborne) then
                  return "lichborne";
              end
          end
      end

      if GetSetting('dsemergency_check', false) then
          if S.DeathPact:IsReady() and Player:HealthPercentage() <= GetSetting('dsemergency_spin', 30) then
              if Cast(S.DeathPact) then
                  return "deathpact pve";
              end
          end

      end

      if GetSetting('ibfortitude_check', false) then
          if S.IceboundFortitude:IsReady(Player) and Player:HealthPercentage() <= GetSetting('ibfortitude_spin', 30) then
              if Cast(S.IceboundFortitude) then
                  return "icebound_fortitude pve";
              end
          end
      end

      if GetSetting('dp_check', false) then
          if S.DeathPact:IsReady() and Player:HealthPercentage() <= GetSetting('dp_spin', 30) then
              if Cast(S.DeathPact) then
                  return "deathpact pve";
              end
          end
      end
  end

  -- Create table to exclude above trinkets from On Use function
  local OnUseExcludes = {
    I.TreacherousTransmitter:ID(),
  }

  -- HR Forgot to localize
  local VarBreathRPCost = 1.7
  local VarStaticRimeBuffs = S.RageoftheFrozenChampion:IsAvailable() or S.Icebreaker:IsAvailable()
  local customRotaEnabled = GetSetting('customrota', false)
  local bosRunicTreshold = GetSetting('bosrunictreshold', 50)
  local VarBreathRPThreshold = (customRotaEnabled and bosRunicTreshold) or 50  
  local VarERWBreathRPTrigger = 70
  local VarERWBreathRuneTrigger = 3
  local VarOblitRunePooling = 4
  local VarBreathRimeRPThreshold = 60
  local VarTrueBreathCD

  --- ===== Rotation Variables =====
  local VarPillarCD = (S.Icecap:IsAvailable()) and 45 or 60
  local MainHandLink, OffHandLink
  local MainHandRuneforge, OffHandRuneforge
  local UsingRazorice, UsingFallenCrusader
  local Var2HCheck
  local VarRWBuffs = S.GatheringStorm:IsAvailable() or S.BitingCold:IsAvailable()
  local VarSTPlanning, VarAddsRemain, VarUseBreath, VarSendingCDs
  local VarRimeBuffs, VarRPBuffs, VarCDCheck
  local VarOblitPoolingTime, VarBreathPoolingTime
  local VarPoolingRunes, VarPoolingRP
  local VarGAPriority, VarBreathDying
  local VarFWFBuffs
  local EnemiesMelee = {}
  local EnemiesMeleeCount = 0
  local BossFightRemains = 11111
  local FightRemains = 11111

  --- ===== Trinket Variables =====
  ---@type Item
  local Trinket1, Trinket2
  ---@type Spell
  local VarTrinket1ID, VarTrinket2ID
  local VarTrinket1Level, VarTrinket2Level
  local VarTrinket1Spell, VarTrinket2Spell
  local VarTrinket1Range, VarTrinket2Range
  local VarTrinket1CastTime, VarTrinket2CastTime
  local VarTrinket1CD, VarTrinket2CD
  local VarTrinket1Ex, VarTrinket2Ex
  local VarTrinket1Sync, VarTrinket2Sync
  local VarTrinket1Buffs, VarTrinket2Buffs
  local VarTrinket1Duration, VarTrinket2Duration
  local VarTrinketPriority, VarDamageTrinketPriority
  local VarTrinket1Manual, VarTrinket2Manual

  --- ===== Trinket Variables (from Precombat) =====
  local VarTrinketFailures = 0
  local function SetTrinketVariables()
    local T1, T2 = Player:GetTrinketData(OnUseExcludes)
  
    -- If we don't have trinket items, try again in 5 seconds.
    if VarTrinketFailures < 5 and ((T1.ID == 0 or T2.ID == 0) or (T1.SpellID > 0 and not T1.Usable or T2.SpellID > 0 and not T2.Usable)) then
      VarTrinketFailures = VarTrinketFailures + 1
      Delay(5, function()
          SetTrinketVariables()
        end
      )
      return
    end
  
    Trinket1 = T1.Object
    Trinket2 = T2.Object

    VarTrinket1ID = T1.ID
    VarTrinket2ID = T2.ID
  
    VarTrinket1Level = T1.Level
    VarTrinket2Level = T2.Level
  
    VarTrinket1Spell = T1.Spell
    VarTrinket1Range = T1.Range
    VarTrinket1CastTime = T1.CastTime
    VarTrinket2Spell = T2.Spell
    VarTrinket2Range = T2.Range
    VarTrinket2CastTime = T2.CastTime
  
    VarTrinket1CD = T1.Cooldown
    VarTrinket2CD = T2.Cooldown
  
    VarTrinket1Ex = T1.Excluded
    VarTrinket2Ex = T2.Excluded
  
    VarTrinket1Sync = 0.5
    if Trinket1:HasUseBuff() and (S.PillarofFrost:IsAvailable() and not S.BreathofSindragosa:IsAvailable() and (VarTrinket1CD % VarPillarCD == 0) or S.BreathofSindragosa:IsAvailable() and (120 % VarTrinket1CD == 0)) then
      VarTrinket1Sync = 1
    end
    VarTrinket2Sync = 0.5
    if Trinket2:HasUseBuff() and (S.PillarofFrost:IsAvailable() and not S.BreathofSindragosa:IsAvailable() and (VarTrinket2CD % VarPillarCD == 0) or S.BreathofSindragosa:IsAvailable() and (120 % VarTrinket2CD == 0)) then
      VarTrinket2Sync = 1
    end
  
    VarTrinket1Buffs = Trinket1:HasCooldown() and VarTrinket1ID ~= I.ImprovisedSeaforiumPacemaker:ID() and Trinket1:HasUseBuff() or VarTrinket1ID == I.TreacherousTransmitter:ID()
    VarTrinket2Buffs = Trinket2:HasCooldown() and VarTrinket2ID ~= I.ImprovisedSeaforiumPacemaker:ID() and Trinket2:HasUseBuff() or VarTrinket2ID == I.TreacherousTransmitter:ID()
  
    -- Note: If BuffDuration is 0, set variable to 1 instead to avoid divide by zero errors.
    VarTrinket1Duration = VarTrinket1ID == I.TreacherousTransmitter:ID() and 15 or (Trinket1:BuffDuration() > 0 and Trinket1:BuffDuration() or 1)
    VarTrinket2Duration = VarTrinket2ID == I.TreacherousTransmitter:ID() and 15 or (Trinket2:BuffDuration() > 0 and Trinket2:BuffDuration() or 1)
  
    VarTrinketPriority = 1
    local T1Level = (T1.ID ~= 0) and Trinket1:Level() or 0
    local T2Level = (T2.ID ~= 0) and Trinket2:Level() or 0
    if not VarTrinket1Buffs and VarTrinket2Buffs and (Trinket2:HasCooldown() or not Trinket1:HasCooldown()) or VarTrinket2Buffs and ((VarTrinket2CD / VarTrinket2Duration) * (VarTrinket2Sync) * (1 + ((T2Level - T1Level) / 100))) > ((VarTrinket1CD / VarTrinket1Duration) * (VarTrinket1Sync) * (1 + ((T1Level - T2Level) / 100))) then
      VarTrinketPriority = 2
    end
  
    VarDamageTrinketPriority = 1
    if not VarTrinket1Buffs and not VarTrinket2Buffs and T2Level >= T1Level then
      VarDamageTrinketPriority = 2
    end
  
    VarTrinket1Manual = T1.ID == I.TreacherousTransmitter:ID()
    VarTrinket2Manual = T2.ID == I.TreacherousTransmitter:ID()
  end
  SetTrinketVariables()

  local function SetSpellVariables()
    VarBreathRPCost = 17
    VarStaticRimeBuffs = S.RageoftheFrozenChampion:IsAvailable() or S.Icebreaker:IsAvailable() or S.BindinDarkness:IsAvailable()
    VarBreathRPThreshold = 50
    VarERWBreathRPTrigger = 70
    VarERWBreathRuneTrigger = 3
    VarOblitRunePooling = 4
    VarBreathRimeRPThreshold = 60
  end
  SetSpellVariables()

  --- ===== Weapon Variables =====
  local function SetWeaponVariables()
    MainHandLink = GetInventoryItemLink("player", 16) or ""
    OffHandLink = GetInventoryItemLink("player", 17) or ""
    MainHandRuneforge = select(3, strsplit(":", MainHandLink))
    OffHandRuneforge = select(3, strsplit(":", OffHandLink))
    UsingRazorice = (MainHandRuneforge == "3370" or OffHandRuneforge == "3370")
    UsingFallenCrusader = (MainHandRuneforge == "3368" or OffHandRuneforge == "3368")
    Var2HCheck = IsEquippedItemType("Two-Hand")
  end
  SetWeaponVariables()

  --- ===== Event Registrations =====
  HL:RegisterForEvent(function()
    BossFightRemains = 11111
    FightRemains = 11111
  end, "PLAYER_REGEN_ENABLED")

  HL:RegisterForEvent(function()
    VarRWBuffs = S.GatheringStorm:IsAvailable() or S.BitingCold:IsAvailable()
    VarTrinketFailures = 0
    SetTrinketVariables()
    SetWeaponVariables()
    SetSpellVariables()
  end, "PLAYER_EQUIPMENT_CHANGED", "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB", "PLAYER_SPECIALIZATION_CHANGED")

  --- ===== CastTargetIf Filter Functions =====
  ---@param TargetUnit Unit
  local function EvaluateTargetIfFilterFrostStrike(TargetUnit)
    -- target_if=max:((talent.shattering_blade&debuff.razorice.stack=5)*5)+(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice
    return (num(S.ShatteringBlade:IsAvailable() and TargetUnit:DebuffStack(S.RazoriceDebuff) == 5) * 5) + (TargetUnit:DebuffStack(S.RazoriceDebuff) + 1) / (TargetUnit:DebuffRemains(S.RazoriceDebuff) + 1) * num(UsingRazorice)
  end
  ---@param TargetUnit Unit
  local function EvaluateTargetIfFilterObliterate(TargetUnit)
    -- target_if=max:(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice+((hero_tree.deathbringer&debuff.reapers_mark_debuff.down)*5)
    return (TargetUnit:DebuffStack(S.RazoriceDebuff) + 1) / (TargetUnit:DebuffRemains(S.RazoriceDebuff) + 1) * num(UsingRazorice) + (num(Player:HeroTreeID() == 33 and Target:DebuffDown(S.ReapersMarkDebuff)) * 5)
  end
  ---@param TargetUnit Unit
  local function EvaluateTargetIfFilterRazoriceStacks(TargetUnit)
    -- target_if=max:(debuff.razorice.stack)
    return TargetUnit:DebuffStack(S.RazoriceDebuff)
  end
  ---@param TargetUnit Unit
  local function EvaluateTargetIfFilterRazoriceStacksModified(TargetUnit)
    -- target_if=max:(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice
    return (TargetUnit:DebuffStack(S.RazoriceDebuff) + 1) / (TargetUnit:DebuffRemains(S.RazoriceDebuff) + 1) * num(UsingRazorice)
  end

  --- ===== CastTargetIf Condition Functions =====
  ---@param TargetUnit Unit
  local function EvaluateTargetIfFrostStrikeAoE(TargetUnit)
    -- if=!variable.pooling_runic_power&debuff.razorice.stack=5&talent.shattering_blade&(talent.shattered_frost|active_enemies<4)
    -- Note: Variable, talent, and enemy count checks performed before CastTargetIf.
    return TargetUnit:DebuffStack(S.RazoriceDebuff) == 5
  end
  ---@param TargetUnit Unit
  local function EvaluateTargetIfFrostStrikeObliteration(TargetUnit)
    -- if=debuff.razorice.stack=5&talent.shattering_blade&talent.a_feast_of_souls&buff.a_feast_of_souls.up&!talent.arctic_assault
    -- Note: All but RazoriceDebuff stacks checked before CastTargetIf.
    return TargetUnit:DebuffStack(S.RazoriceDebuff)
  end
  ---@param TargetUnit Unit
  local function EvaluateTargetIfFrostStrikeObliteration2(TargetUnit)
    -- if=(rune<2|variable.rp_buffs|debuff.razorice.stack=5&talent.shattering_blade)&(!talent.glacial_advance|active_enemies=1|talent.shattered_frost)
    -- Note: '&(!talent.glacial_advance|active_enemies=1|talent.shattered_frost)' performed before CastTargetIf.
    return Player:Rune() < 2 or VarRPBuffs or TargetUnit:DebuffStack(S.RazoriceDebuff) == 5 and S.ShatteringBlade:IsAvailable()
  end
  ---@param TargetUnit Unit
  local function EvaluateTargetIfGlacialAdvanceAoE(TargetUnit)
    -- if=!variable.pooling_runic_power&(variable.ga_priority|debuff.razorice.stack<5)
    -- Note: pooling_runic_power check performed before CastTargetIf.
    return VarGAPriority or TargetUnit:DebuffStack(S.RazoriceDebuff) < 5
  end
  ---@param TargetUnit Unit
  local function EvaluateTargetIfGlacialAdvanceObliteration(TargetUnit)
    -- if=(variable.ga_priority|debuff.razorice.stack<5)&(!death_knight.runeforge.razorice&(debuff.razorice.stack<5|debuff.razorice.remains<gcd*3)|((variable.rp_buffs|rune<2)&active_enemies>1))
    return (VarGAPriority or TargetUnit:DebuffStack(S.RazoriceDebuff) < 5) and (not UsingRazorice and (TargetUnit:DebuffStack(S.RazoriceDebuff) < 5 or TargetUnit:DebuffRemains(S.RazoriceDebuff) < Player:GCD() * 3) or ((VarRPBuffs or Player:Rune() < 2) and EnemiesMeleeCount > 1))
  end

  --- ===== CastCycle Functions =====
  ---@param TargetUnit Unit
  local function EvaluateCycleReapersMarkCDs(TargetUnit)
    -- target_if=first:!debuff.reapers_mark_debuff.up
    return TargetUnit:DebuffDown(S.ReapersMarkDebuff)
  end

  local function Precombat()
    -- snapshot_stats
    -- variable,name=trinket_1_sync,op=setif,value=1,value_else=0.5,condition=trinket.1.has_use_buff&(talent.pillar_of_frost&!talent.breath_of_sindragosa&(trinket.1.cooldown.duration%%cooldown.pillar_of_frost.duration=0)|talent.breath_of_sindragosa&(cooldown.breath_of_sindragosa.duration%%trinket.1.cooldown.duration=0))
    -- variable,name=trinket_2_sync,op=setif,value=1,value_else=0.5,condition=trinket.2.has_use_buff&(talent.pillar_of_frost&!talent.breath_of_sindragosa&(trinket.2.cooldown.duration%%cooldown.pillar_of_frost.duration=0)|talent.breath_of_sindragosa&(cooldown.breath_of_sindragosa.duration%%trinket.2.cooldown.duration=0))
    -- variable,name=trinket_1_buffs,value=trinket.1.has_cooldown&!trinket.1.is.improvised_seaforium_pacemaker&(trinket.1.has_use_buff|trinket.1.has_buff.strength|trinket.1.has_buff.mastery|trinket.1.has_buff.versatility|trinket.1.has_buff.haste|trinket.1.has_buff.crit)|trinket.1.is.treacherous_transmitter
    -- variable,name=trinket_2_buffs,value=trinket.2.has_cooldown&!trinket.2.is.improvised_seaforium_pacemaker&(trinket.2.has_use_buff|trinket.2.has_buff.strength|trinket.2.has_buff.mastery|trinket.2.has_buff.versatility|trinket.2.has_buff.haste|trinket.2.has_buff.crit)|trinket.2.is.treacherous_transmitter
    -- variable,name=trinket_1_duration,op=setif,value=15,value_else=trinket.1.proc.any_dps.duration,condition=trinket.1.is.treacherous_transmitter
    -- variable,name=trinket_2_duration,op=setif,value=15,value_else=trinket.2.proc.any_dps.duration,condition=trinket.2.is.treacherous_transmitter
    -- variable,name=trinket_priority,op=setif,value=2,value_else=1,condition=!variable.trinket_1_buffs&variable.trinket_2_buffs&(trinket.2.has_cooldown|!trinket.1.has_cooldown)|variable.trinket_2_buffs&((trinket.2.cooldown.duration%variable.trinket_2_duration)*(1.5+trinket.2.has_buff.strength)*(variable.trinket_2_sync)*(1+((trinket.2.ilvl-trinket.1.ilvl)%100)))>((trinket.1.cooldown.duration%variable.trinket_1_duration)*(1.5+trinket.1.has_buff.strength)*(variable.trinket_1_sync)*(1+((trinket.1.ilvl-trinket.2.ilvl)%100)))
    -- variable,name=damage_trinket_priority,op=setif,value=2,value_else=1,condition=!variable.trinket_1_buffs&!variable.trinket_2_buffs&trinket.2.ilvl>=trinket.1.ilvl
    -- variable,name=trinket_1_manual,value=trinket.1.is.treacherous_transmitter
    -- variable,name=trinket_2_manual,value=trinket.2.is.treacherous_transmitter
    -- Note: Manual trinkets handled via OnUseExcludes.
    -- Note: Moved the above variable definitions to initial profile load, SPELLS_CHANGED, and PLAYER_EQUIPMENT_CHANGED.
    -- variable,name=rw_buffs,value=talent.gathering_storm|talent.biting_cold
    -- Note: Handling during variable declaration and SPELLS_CHANGED/LEARNED_SPELL_IN_TAB events.
    -- variable,name=breath_rp_cost,value=dbc.power.9067.cost_per_tick%10
    -- variable,name=static_rime_buffs,value=talent.rage_of_the_frozen_champion|talent.icebreaker|talent.bind_in_darkness
    -- variable,name=breath_rp_threshold,default=50,op=reset
    -- variable,name=erw_breath_rp_trigger,default=70,op=reset
    -- variable,name=erw_breath_rune_trigger,default=3,op=reset
    -- variable,name=oblit_rune_pooling,default=4,op=reset
    -- variable,name=breath_rime_rp_threshold,default=60,op=reset
    -- Note: Handling the above during variable declaration, PLAYER_EQUIPMENT_CHANGED, and SPELLS_CHANGED/LEARNED_SPELL_IN_TAB.
    -- Manually added openers: HowlingBlast if at range, RemorselessWinter if in melee
    if S.HowlingBlast:IsReady() and not Target:IsInRange(8) then
      if Cast(S.HowlingBlast) then return "howling_blast precombat 2"; end
    end
    if S.RemorselessWinter:IsReady() and Target:IsInRange(8) then
      if Cast(S.RemorselessWinter) then return "remorseless_winter precombat 4"; end
    end
  end
  
  local function AoE()
    -- obliterate,target_if=max:(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice+((hero_tree.deathbringer&debuff.reapers_mark_debuff.down)*5),if=buff.killing_machine.react&talent.cleaving_strikes&buff.death_and_decay.up
    if S.Obliterate:IsReady() and (Player:BuffUp(S.KillingMachineBuff) and S.CleavingStrikes:IsAvailable() and Player:BuffUp(S.DeathAndDecayBuff)) then
      if CastTargetIf(S.Obliterate, EnemiesMelee, "max", EvaluateTargetIfFilterObliterate, nil) then return "obliterate aoe 2"; end
    end
    -- frost_strike,target_if=max:((talent.shattering_blade&debuff.razorice.stack=5)*5)+(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice,if=!variable.pooling_runic_power&debuff.razorice.stack=5&talent.shattering_blade&(talent.shattered_frost|active_enemies<4)
    if S.FrostStrike:IsReady() and (not VarPoolingRP and S.ShatteringBlade:IsAvailable() and (S.ShatteredFrost:IsAvailable() or EnemiesMeleeCount < 4)) then
      if CastTargetIf(S.FrostStrike, EnemiesMelee, "max", EvaluateTargetIfFilterFrostStrike, EvaluateTargetIfFrostStrikeAoE) then return "frost_strike aoe 4"; end
    end
    -- howling_blast,if=buff.rime.react
    if S.HowlingBlast:IsReady() and (Player:BuffUp(S.RimeBuff)) then
      if Cast(S.HowlingBlast) then return "howling_blast aoe 6"; end
    end
    -- glacial_advance,target_if=max:(debuff.razorice.stack),if=!variable.pooling_runic_power&(variable.ga_priority|debuff.razorice.stack<5)
    if S.GlacialAdvance:IsReady() and (not VarPoolingRP) then
      if CastTargetIf(S.GlacialAdvance, EnemiesMelee, "max", EvaluateTargetIfFilterRazoriceStacks, EvaluateTargetIfGlacialAdvanceAoE) then return "glacial_advance aoe 8"; end
    end
    -- obliterate
    if S.Obliterate:IsReady() then
      if Cast(S.Obliterate) then return "obliterate aoe 10"; end
    end
    -- frost_strike,target_if=max:((talent.shattering_blade&debuff.razorice.stack=5)*5)+(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice,if=!variable.pooling_runic_power
    if S.FrostStrike:IsReady() and (not VarPoolingRP) then
      if CastTargetIf(S.FrostStrike, EnemiesMelee, "max", EvaluateTargetIfFilterFrostStrike, nil) then return "frost_strike aoe 12"; end
    end
    -- horn_of_winter,if=rune<2&runic_power.deficit>25&(!talent.breath_of_sindragosa|variable.true_breath_cooldown>cooldown.horn_of_winter.duration-15)
    if S.HornofWinter:IsReady() and (Player:Rune() < 2 and Player:RunicPowerDeficit() > 25 and (not S.BreathofSindragosa:IsAvailable() or VarTrueBreathCD > 30)) then
      if Cast(S.HornofWinter) then return "horn_of_winter aoe 14"; end
    end
    -- arcane_torrent,if=runic_power.deficit>25
    if S.ArcaneTorrent:IsReady() and (Player:RunicPowerDeficit() > 25) then
      if Cast(S.ArcaneTorrent) then return "arcane_torrent aoe 16"; end
    end
    -- abomination_limb
    if S.AbominationLimb:IsReady() and not Target:IsInRange(20) then
      if Cast(S.AbominationLimb) then return "abomination_limb_talent aoe 18"; end
    end
  end
  
  local function Breath()
    -- obliterate,target_if=max:(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice+((hero_tree.deathbringer&debuff.reapers_mark_debuff.down)*5),if=buff.killing_machine.react=2
    if S.Obliterate:IsReady() and (Player:BuffStack(S.KillingMachineBuff) == 2) then
      if CastTargetIf(S.Obliterate, EnemiesMelee, "max", EvaluateTargetIfFilterObliterate, nil) then return "obliterate breath 1"; end
    end
    -- howling_blast,if=(variable.rime_buffs|!buff.killing_machine.react&buff.pillar_of_frost.up&talent.obliteration&!buff.bonegrinder_frost.up)&runic_power>(variable.breath_rime_rp_threshold-(talent.rage_of_the_frozen_champion*(dbc.effect.842306.base_value%10)))|!dot.frost_fever.ticking
    -- Note: dbc.effect.842306.base_value as of 11.1.5.60822 is 60.
    -- Note: Value derived from simc command: simc spell_query=effect.id=842306
    if S.HowlingBlast:IsReady() and ((VarRimeBuffs or Player:BuffDown(S.KillingMachineBuff) and Player:BuffUp(S.PillarofFrostBuff) and S.Obliteration:IsAvailable() and Player:BuffDown(S.BonegrinderFrostBuff) and Player:RunicPower() > (VarBreathRimeRPThreshold - (num(S.RageoftheFrozenChampion:IsAvailable()) * 6))) or Target:DebuffDown(S.FrostFeverDebuff)) then
      if Cast(S.HowlingBlast) then return "howling_blast breath 2"; end
    end
    -- horn_of_winter,if=rune<2&runic_power.deficit>30&(!buff.empower_rune_weapon.up|runic_power<variable.breath_rp_cost*2*gcd.max)
    if S.HornofWinter:IsReady() and (Player:Rune() < 2 and Player:RunicPowerDeficit() > 30 and (Player:BuffDown(S.EmpowerRuneWeaponBuff) or Player:RunicPower() < VarBreathRPCost * 2 * Player:GCD())) then
      if Cast(S.HornofWinter) then return "horn_of_winter breath 4"; end
    end
    -- obliterate,target_if=max:(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice+((hero_tree.deathbringer&debuff.reapers_mark_debuff.down)*5),if=buff.killing_machine.react|runic_power.deficit>20
    if S.Obliterate:IsReady() and (Player:BuffUp(S.KillingMachineBuff) or Player:RunicPowerDeficit() > 20) then
      if CastTargetIf(S.Obliterate, EnemiesMelee, "max", EvaluateTargetIfFilterObliterate, nil) then return "obliterate breath 6"; end
    end
    -- soul_reaper,if=fight_remains>5&target.time_to_pct_35<5&target.time_to_pct_0>5&active_enemies<=1&rune>2
    if S.SoulReaper:IsReady() and (FightRemains > 5 and Target:TimeToX(35) < 5 and Target:TimeToDie() > 5 and EnemiesMeleeCount <= 1 and Player:Rune() > 2) then
      if Cast(S.SoulReaper) then return "soul_reaper breath 8"; end
    end
    -- remorseless_winter,if=variable.breath_dying
    if S.RemorselessWinter:IsReady() and (VarBreathDying) then
      if Cast(S.RemorselessWinter) then return "remorseless_winter breath 10"; end
    end
    -- death_and_decay,if=!death_and_decay.ticking&(variable.st_planning&talent.unholy_ground&runic_power.deficit>=10&!talent.obliteration|variable.breath_dying)
    if S.DeathAndDecay:IsReady() and (Player:BuffDown(S.DeathAndDecayBuff) and (VarSTPlanning and S.UnholyGround:IsAvailable() and Player:RunicPowerDeficit() >= 10 and not S.Obliteration:IsAvailable() or VarBreathDying)) then
      if Cast(S.DeathAndDecay, Player) then return "death_and_decay breath 12"; end
    end
    -- howling_blast,if=variable.breath_dying
    if S.HowlingBlast:IsReady() and (VarBreathDying) then
      if Cast(S.HowlingBlast) then return "howling_blast breath 14"; end
    end
    -- arcane_torrent,if=runic_power<60
    if S.ArcaneTorrent:IsReady() and (Player:RunicPower() < 60) then
      if Cast(S.ArcaneTorrent) then return "arcane_torrent breath 16"; end
    end
    -- howling_blast,if=buff.rime.react
    if S.HowlingBlast:IsReady() and (Player:BuffUp(S.RimeBuff)) then
      if Cast(S.HowlingBlast) then return "howling_blast breath 18"; end
    end
  end
  
  local function ColdHeart()
    -- chains_of_ice,if=fight_remains<gcd&(rune<2|!buff.killing_machine.react&(!main_hand.2h&buff.cold_heart.stack>=4|main_hand.2h&buff.cold_heart.stack>8)|buff.killing_machine.react&(!main_hand.2h&buff.cold_heart.stack>8|main_hand.2h&buff.cold_heart.stack>10))
    if S.ChainsofIce:IsReady() and (FightRemains < Player:GCD() and (Player:Rune() < 2 or Player:BuffDown(S.KillingMachineBuff) and (not Var2HCheck and Player:BuffStack(S.ColdHeartBuff) >= 4 or Var2HCheck and Player:BuffStack(S.ColdHeartBuff) > 8) or Player:BuffUp(S.KillingMachineBuff) and (not Var2HCheck and Player:BuffStack(S.ColdHeartBuff) > 8 or Var2HCheck and Player:BuffStack(S.ColdHeartBuff) > 10))) then
      if Cast(S.ChainsofIce) then return "chains_of_ice cold_heart 2"; end
    end
    -- chains_of_ice,if=!talent.obliteration&buff.pillar_of_frost.up&buff.cold_heart.stack>=10&(buff.pillar_of_frost.remains<gcd*(1+(talent.frostwyrms_fury&cooldown.frostwyrms_fury.ready))|buff.unholy_strength.up&buff.unholy_strength.remains<gcd)
    if S.ChainsofIce:IsReady() and (not S.Obliteration:IsAvailable() and Player:BuffUp(S.PillarofFrostBuff) and Player:BuffStack(S.ColdHeartBuff) >= 10 and (Player:BuffRemains(S.PillarofFrostBuff) < Player:GCD() * (1 + num(S.FrostwyrmsFury:IsAvailable() and S.FrostwyrmsFury:IsReady())) or Player:BuffUp(S.UnholyStrengthBuff) and Player:BuffRemains(S.UnholyStrengthBuff) < Player:GCD())) then
      if Cast(S.ChainsofIce) then return "chains_of_ice cold_heart 4"; end
    end
    -- chains_of_ice,if=!talent.obliteration&death_knight.runeforge.fallen_crusader&!buff.pillar_of_frost.up&cooldown.pillar_of_frost.remains>15&(buff.cold_heart.stack>=10&buff.unholy_strength.up|buff.cold_heart.stack>=13)
    if S.ChainsofIce:IsReady() and (not S.Obliteration:IsAvailable() and UsingFallenCrusader and Player:BuffDown(S.PillarofFrostBuff) and S.PillarofFrost:CooldownRemains() > 15 and (Player:BuffStack(S.ColdHeartBuff) >= 10 and Player:BuffUp(S.UnholyStrengthBuff) or Player:BuffStack(S.ColdHeartBuff) >= 13)) then
      if Cast(S.ChainsofIce) then return "chains_of_ice cold_heart 6"; end
    end
    -- chains_of_ice,if=!talent.obliteration&!death_knight.runeforge.fallen_crusader&buff.cold_heart.stack>=10&!buff.pillar_of_frost.up&cooldown.pillar_of_frost.remains>20
    if S.ChainsofIce:IsReady() and (not S.Obliteration:IsAvailable() and not UsingFallenCrusader and Player:BuffStack(S.ColdHeartBuff) >= 10 and Player:BuffDown(S.PillarofFrostBuff) and S.PillarofFrost:CooldownRemains() > 20) then
      if Cast(S.ChainsofIce) then return "chains_of_ice cold_heart 8"; end
    end
    -- chains_of_ice,if=talent.obliteration&!buff.pillar_of_frost.up&(buff.cold_heart.stack>=14&buff.unholy_strength.up|buff.cold_heart.stack>=19|cooldown.pillar_of_frost.remains<3&buff.cold_heart.stack>=14)
    if S.ChainsofIce:IsReady() and (S.Obliteration:IsAvailable() and Player:BuffDown(S.PillarofFrostBuff) and (Player:BuffStack(S.ColdHeartBuff) >= 14 and Player:BuffUp(S.UnholyStrengthBuff) or Player:BuffStack(S.ColdHeartBuff) >= 19 or S.PillarofFrost:CooldownRemains() < 3 and Player:BuffStack(S.ColdHeartBuff) >= 14)) then
      if Cast(S.ChainsofIce) then return "chains_of_ice cold_heart 10"; end
    end
  end
  
  local function Cooldowns()
    -- Burst Potion
    if Target:IsInRange(8) then
        if MainAddon.UsePotion() then
            MainAddon.SetTopColor(1, "Combat Potion")
        end
    end
    -- remorseless_winter,if=variable.rw_buffs&variable.sending_cds&(!talent.arctic_assault|!buff.pillar_of_frost.up)&(cooldown.pillar_of_frost.remains>20|cooldown.pillar_of_frost.remains<gcd.max*3|(buff.gathering_storm.stack=10&buff.remorseless_winter.remains<gcd.max))&fight_remains>10
    if S.RemorselessWinter:IsReady() and (VarRWBuffs and VarSendingCDs and (not S.ArcticAssault:IsAvailable() or Player:BuffDown(S.PillarofFrostBuff)) and (S.PillarofFrost:CooldownRemains() > 20 or S.PillarofFrost:CooldownRemains() < Player:GCD() * 3 or (Player:BuffStack(S.GatheringStormBuff) == 10 and Player:BuffRemains(S.RemorselessWinter) < Player:GCD())) and FightRemains > 10) then
      if Cast(S.RemorselessWinter) then return "remorseless_winter cooldowns 2"; end
    end
    -- chill_streak,if=variable.sending_cds&(!talent.arctic_assault|!buff.pillar_of_frost.up)
    if S.ChillStreak:IsReady() and (VarSendingCDs and (not S.ArcticAssault:IsAvailable() or Player:BuffDown(S.PillarofFrostBuff))) then
      if Cast(S.ChillStreak) then return "chill_streak cooldowns 4"; end
    end
    -- empower_rune_weapon,if=talent.obliteration&!talent.breath_of_sindragosa&buff.pillar_of_frost.up|fight_remains<20
    if S.EmpowerRuneWeapon:IsReady() and (S.Obliteration:IsAvailable() and not S.BreathofSindragosa:IsAvailable() and Player:BuffUp(S.PillarofFrostBuff) or BossFightRemains < 20) then
      if Cast(S.EmpowerRuneWeapon) then return "empower_rune_weapon cooldowns 6"; end
    end
    -- empower_rune_weapon,if=buff.breath_of_sindragosa.up&(runic_power<40|runic_power<variable.erw_breath_rp_trigger&rune<variable.erw_breath_rune_trigger)
    if S.EmpowerRuneWeapon:IsReady() and (Player:BuffUp(S.BreathofSindragosa) and (Player:RunicPower() < 40 or Player:RunicPower() < VarERWBreathRPTrigger and Player:Rune() < VarERWBreathRuneTrigger)) then
      if Cast(S.EmpowerRuneWeapon) then return "empower_rune_weapon cooldowns 8"; end
    end
    -- empower_rune_weapon,if=!talent.breath_of_sindragosa&!talent.obliteration&!buff.empower_rune_weapon.up&rune<5&(cooldown.pillar_of_frost.remains<7|buff.pillar_of_frost.up|!talent.pillar_of_frost)
    if S.EmpowerRuneWeapon:IsReady() and (not S.BreathofSindragosa:IsAvailable() and not S.Obliteration:IsAvailable() and Player:BuffDown(S.EmpowerRuneWeaponBuff) and Player:Rune() < 5 and (S.PillarofFrost:CooldownRemains() < 7 or Player:BuffUp(S.PillarofFrostBuff) or not S.PillarofFrost:IsAvailable())) then
      if Cast(S.EmpowerRuneWeapon) then return "empower_rune_weapon cooldowns 10"; end
    end
    -- pillar_of_frost,if=talent.obliteration&!talent.breath_of_sindragosa&(!hero_tree.deathbringer|(rune>=2|(rune>=1&cooldown.empower_rune_weapon.ready)))&variable.sending_cds|fight_remains<20
    if S.PillarofFrost:IsReady() and (S.Obliteration:IsAvailable() and not S.BreathofSindragosa:IsAvailable() and (Player:HeroTreeID() ~= 33 or (Player:Rune() >= 2 or (Player:Rune() >= 1 and S.EmpowerRuneWeapon:CooldownUp()))) and VarSendingCDs or BossFightRemains < 20) then
      if Cast(S.PillarofFrost) then return "pillar_of_frost cooldowns 12"; end
    end
    -- pillar_of_frost,if=talent.breath_of_sindragosa&variable.sending_cds&(cooldown.breath_of_sindragosa.remains>10|!variable.use_breath)&buff.unleashed_frenzy.up&(!hero_tree.deathbringer|rune>1)
    if S.PillarofFrost:IsReady() and (S.BreathofSindragosa:IsAvailable() and VarSendingCDs and (S.BreathofSindragosa:CooldownRemains() > 10 or not VarUseBreath) and Player:BuffUp(S.UnleashedFrenzyBuff) and (Player:HeroTreeID() ~= 33 or Player:Rune() > 1)) then
      if Cast(S.PillarofFrost) then return "pillar_of_frost cooldowns 14"; end
    end
    -- pillar_of_frost,if=!talent.obliteration&!talent.breath_of_sindragosa&variable.sending_cds
    if S.PillarofFrost:IsReady() and (not S.Obliteration:IsAvailable() and not S.BreathofSindragosa:IsAvailable() and VarSendingCDs) then
      if Cast(S.PillarofFrost) then return "pillar_of_frost cooldowns 16"; end
    end
    -- breath_of_sindragosa,use_off_gcd=1,if=!buff.breath_of_sindragosa.up&runic_power>variable.breath_rp_threshold&(rune<2|runic_power>80)&(cooldown.pillar_of_frost.ready&variable.use_breath|fight_remains<30)|(time<10&rune<1)
    if S.BreathofSindragosa:IsReady() and (Player:BuffDown(S.BreathofSindragosa) and Player:RunicPower() > VarBreathRPThreshold and (Player:Rune() < 2 or Player:RunicPower() > 80) and (S.PillarofFrost:CooldownUp() and VarUseBreath or BossFightRemains < 30) or (HL.CombatTime() < 10 and Player:Rune() < 1)) then
      if Cast(S.BreathofSindragosa) then return "breath_of_sindragosa cooldowns 18"; end
    end
    -- reapers_mark,target_if=first:debuff.reapers_mark_debuff.down,if=buff.pillar_of_frost.up|cooldown.pillar_of_frost.remains>5
    if S.ReapersMark:IsReady() and (Player:BuffUp(S.PillarofFrostBuff) or S.PillarofFrost:CooldownRemains() > 5) then
      if CastCycle(S.ReapersMark, EnemiesMelee, EvaluateCycleReapersMarkCDs) then return "reapers_mark cooldowns 20"; end
    end
    -- frostwyrms_fury,if=hero_tree.rider_of_the_apocalypse&talent.apocalypse_now&variable.sending_cds&(!talent.breath_of_sindragosa&buff.pillar_of_frost.up|buff.breath_of_sindragosa.up)|fight_remains<20
    if S.FrostwyrmsFury:IsReady() and (Player:HeroTreeID() == 32 and S.ApocalypseNow:IsAvailable() and VarSendingCDs and (not S.BreathofSindragosa:IsAvailable() and Player:BuffUp(S.PillarofFrostBuff) or Player:BuffUp(S.BreathofSindragosa)) or BossFightRemains < 30) then
      if Cast(S.FrostwyrmsFury) then return "frostwyrms_fury cooldowns 22"; end
    end
    -- frostwyrms_fury,if=!talent.apocalypse_now&active_enemies=1&(talent.pillar_of_frost&buff.pillar_of_frost.up&!talent.obliteration|!talent.pillar_of_frost)&(!raid_event.adds.exists|raid_event.adds.in>cooldown.frostwyrms_fury.duration+raid_event.adds.duration)&variable.fwf_buffs|fight_remains<3
    if S.FrostwyrmsFury:IsReady() and (not S.ApocalypseNow:IsAvailable() and EnemiesMeleeCount == 1 and (S.PillarofFrost:IsAvailable() and Player:BuffUp(S.PillarofFrostBuff) and not S.Obliteration:IsAvailable() or not S.PillarofFrost:IsAvailable()) and VarFWFBuffs or BossFightRemains < 3) then
      if Cast(S.FrostwyrmsFury) then return "frostwyrms_fury cooldowns 24"; end
    end
    -- frostwyrms_fury,if=!talent.apocalypse_now&active_enemies>=2&(talent.pillar_of_frost&buff.pillar_of_frost.up|raid_event.adds.exists&raid_event.adds.up&raid_event.adds.in<cooldown.pillar_of_frost.remains-raid_event.adds.in-raid_event.adds.duration)&variable.fwf_buffs
    if S.FrostwyrmsFury:IsReady() and (not S.ApocalypseNow:IsAvailable() and EnemiesMeleeCount >= 2 and (S.PillarofFrost:IsAvailable() and Player:BuffUp(S.PillarofFrostBuff)) and VarFWFBuffs) then
      if Cast(S.FrostwyrmsFury) then return "frostwyrms_fury cooldowns 26"; end
    end
    -- frostwyrms_fury,if=!talent.apocalypse_now&talent.obliteration&(talent.pillar_of_frost&buff.pillar_of_frost.up&!main_hand.2h|!buff.pillar_of_frost.up&main_hand.2h&cooldown.pillar_of_frost.remains|!talent.pillar_of_frost)&variable.fwf_buffs&(!raid_event.adds.exists|raid_event.adds.in>cooldown.frostwyrms_fury.duration+raid_event.adds.duration)
    if S.FrostwyrmsFury:IsReady() and (not S.ApocalypseNow:IsAvailable() and S.Obliteration:IsAvailable() and (S.PillarofFrost:IsAvailable() and Player:BuffUp(S.PillarofFrostBuff) and not Var2HCheck or Player:BuffDown(S.PillarofFrostBuff) and Var2HCheck and S.PillarofFrost:CooldownDown() or not S.PillarofFrost:IsAvailable()) and VarFWFBuffs) then
      if Cast(S.FrostwyrmsFury) then return "frostwyrms_fury cooldowns 28"; end
    end
    -- raise_dead,use_off_gcd=1
    if S.RaiseDead:IsReady() then
      if Cast(S.RaiseDead) then return "raise_dead cooldowns 30"; end
    end
    -- soul_reaper,if=talent.reaper_of_souls&buff.reaper_of_souls.up&buff.killing_machine.react<2
    if S.SoulReaper:IsReady() and (S.ReaperofSouls:IsAvailable() and Player:BuffUp(S.ReaperofSoulsBuff) and Player:BuffStack(S.KillingMachineBuff) < 2) then
      if Cast(S.SoulReaper) then return "soul_reaper cooldowns 32"; end
    end
    -- frostscythe,if=!buff.killing_machine.react&!buff.pillar_of_frost.up
    if S.Frostscythe:IsReady() and (Player:BuffDown(S.KillingMachineBuff) and Player:BuffDown(S.PillarofFrostBuff)) then
      if Cast(S.Frostscythe) then return "frostscythe cooldowns 34"; end
    end
    -- Note: For below any_dnd checks, all lines check for "!buff.death_and_decay.up", so checking outside the condition sets.
    -- Note: Multiple lines use the same target count checks, so using the below variable.
    local CleaveCheck = EnemiesMeleeCount > 5 or S.CleavingStrikes:IsAvailable() and EnemiesMeleeCount >= 2
    if S.DeathAndDecay:IsReady() and Player:BuffDown(S.DeathAndDecayBuff) and (
      -- any_dnd,if=hero_tree.deathbringer&!buff.death_and_decay.up&variable.st_planning&cooldown.reapers_mark.remains<gcd.max*2&rune>2
      (Player:HeroTreeID() == 33 and VarSTPlanning and S.ReapersMark:CooldownRemains() < Player:GCD() * 2 and Player:Rune() > 2) or
      -- any_dnd,if=!buff.death_and_decay.up&raid_event.adds.remains>5&(buff.pillar_of_frost.up&buff.killing_machine.react&(talent.enduring_strength|buff.pillar_of_frost.remains>5))&(active_enemies>5|talent.cleaving_strikes&active_enemies>=2)
      ((Player:BuffUp(S.PillarofFrostBuff) and Player:BuffUp(S.KillingMachineBuff) and (S.EnduringStrength:IsAvailable() or Player:BuffRemains(S.PillarofFrostBuff) > 5)) and CleaveCheck) or
      -- any_dnd,if=!buff.death_and_decay.up&raid_event.adds.remains>5&(!buff.pillar_of_frost.up&(cooldown.death_and_decay.charges=2&cooldown.pillar_of_frost.remains))&(active_enemies>5|talent.cleaving_strikes&active_enemies>=2)
      ((Player:BuffDown(S.PillarofFrostBuff) and (S.DeathAndDecay:Charges() == 2 and S.PillarofFrost:CooldownDown())) and CleaveCheck) or
      -- any_dnd,if=!buff.death_and_decay.up&raid_event.adds.remains>5&(!buff.pillar_of_frost.up&(cooldown.death_and_decay.charges=1&cooldown.pillar_of_frost.remains>(cooldown.death_and_decay.duration-(cooldown.death_and_decay.duration*(cooldown.death_and_decay.charges_fractional%%1)))))&(active_enemies>5|talent.cleaving_strikes&active_enemies>=2)
      ((Player:BuffDown(S.PillarofFrostBuff) and (S.DeathAndDecay:Charges() == 1 and S.PillarofFrost:CooldownRemains() > (30 - (30 * (S.DeathAndDecay:ChargesFractional() % 1))))) and CleaveCheck) or
      -- any_dnd,if=!buff.death_and_decay.up&raid_event.adds.remains>5&(!buff.pillar_of_frost.up&(!talent.the_long_winter&cooldown.pillar_of_frost.remains<gcd.max*2)|fight_remains<15)&(active_enemies>5|talent.cleaving_strikes&active_enemies>=2)
      ((Player:BuffDown(S.PillarofFrostBuff) and (not S.TheLongWinter:IsAvailable() and S.PillarofFrost:CooldownRemains() < Player:GCD() * 2) or BossFightRemains < 15) and CleaveCheck)
    ) then
      if Cast(S.DeathAndDecay, Player) then return "death_and_decay cooldowns 36"; end
    end
  end
  
  local function HighPrioActions()
    -- -- mind_freeze,if=target.debuff.casting.react
    -- YUNO: yeah i dont think so...
    -- if S.MindFreeze:IsReady() then if Cast(S.MindFreeze) then return "mind_freeze high_prio_actions"; end end
    -- invoke_external_buff,name=power_infusion,if=(buff.pillar_of_frost.up|!talent.pillar_of_frost)&(talent.obliteration|talent.breath_of_sindragosa&buff.breath_of_sindragosa.up|!talent.breath_of_sindragosa&!talent.obliteration)
    -- Note: Not handling external buffs.
    -- antimagic_shell,if=runic_power.deficit>40&death_knight.first_ams_cast<time&(!talent.breath_of_sindragosa|talent.breath_of_sindragosa&variable.true_breath_cooldown>cooldown.antimagic_shell.duration)
    -- In simc, the default of this setting is 20s.
    -- TODO: Maybe make this a setting?
    local VarAMSCD = S.AntiMagicBarrier:IsAvailable() and 40 or 60
    VarAMSCD = S.UnyieldingWill:IsAvailable() and VarAMSCD + 20 or VarAMSCD
    if GetSetting("amsoffensive", false) and S.AntiMagicShell:IsReady() and (Player:RunicPowerDeficit() > 40 and 20 < HL.CombatTime() and (not S.BreathofSindragosa:IsAvailable() or S.BreathofSindragosa:IsAvailable() and VarTrueBreathCD > VarAMSCD)) then
      if Cast(S.AntiMagicShell) then return "antimagic_shell high_prio_actions 2"; end
    end
    -- howling_blast,if=!dot.frost_fever.ticking&active_enemies>=2&(!talent.breath_of_sindragosa|!buff.breath_of_sindragosa.up)&(!talent.obliteration|talent.wither_away|talent.obliteration&(!cooldown.pillar_of_frost.ready|buff.pillar_of_frost.up&!buff.killing_machine.react))
    if S.HowlingBlast:IsReady() and (Target:DebuffDown(S.FrostFeverDebuff) and EnemiesMeleeCount >= 2 and (not S.BreathofSindragosa:IsAvailable() or Player:BuffDown(S.BreathofSindragosa)) and (not S.Obliteration:IsAvailable() or S.WitherAway:IsAvailable() or S.Obliteration:IsAvailable() and (S.PillarofFrost:CooldownDown() or Player:BuffUp(S.PillarofFrostBuff) and Player:BuffDown(S.KillingMachineBuff)))) then
      if Cast(S.HowlingBlast) then return "howling_blast high_prio_actions 4"; end
    end
  end
  
  local function Obliteration()
    -- obliterate,target_if=max:(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice+((hero_tree.deathbringer&debuff.reapers_mark_debuff.down)*5),if=buff.killing_machine.react&(buff.exterminate.up|fight_remains<gcd*2)
    if S.Obliterate:IsReady() and (Player:BuffUp(S.KillingMachineBuff) and (Player:BuffUp(S.ExterminateBuff) or BossFightRemains < Player:GCD() * 2)) then
      if CastTargetIf(S.Obliterate, EnemiesMelee, "max", EvaluateTargetIfFilterObliterate, nil) then return "obliterate obliteration 2"; end
    end
    -- frost_strike,target_if=max:((talent.shattering_blade&debuff.razorice.stack=5)*5)+(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice,if=debuff.razorice.stack=5&talent.shattering_blade&talent.a_feast_of_souls&buff.a_feast_of_souls.up
    if S.FrostStrike:IsReady() and (S.ShatteringBlade:IsAvailable() and S.AFeastofSouls:IsAvailable() and Player:BuffUp(S.AFeastofSoulsBuff)) then
      if CastTargetIf(S.FrostStrike, EnemiesMelee, "max", EvaluateTargetIfFilterFrostStrike, EvaluateTargetIfFrostStrikeObliteration) then return "frost_strike obliteration 4"; end
    end
    -- soul_reaper,if=fight_remains>5&target.time_to_pct_35<5&target.time_to_pct_0>5&active_enemies<=1&rune>2&!buff.killing_machine.react
    if S.SoulReaper:IsReady() and (FightRemains > 5 and Target:TimeToX(35) < 5 and Target:TimeToDie() > 5 and EnemiesMeleeCount <= 1 and Player:Rune() > 2 and Player:BuffDown(S.KillingMachineBuff)) then
      if Cast(S.SoulReaper) then return "soul_reaper obliteration 6"; end
    end
    -- obliterate,target_if=max:(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice,if=buff.killing_machine.react
    if S.Obliterate:IsReady() and (Player:BuffUp(S.KillingMachineBuff)) then
      if CastTargetIf(S.Obliterate, EnemiesMelee, "max", EvaluateTargetIfFilterRazoriceStacksModified, nil) then return "obliterate obliteration 8"; end
    end
    -- glacial_advance,target_if=max:(debuff.razorice.stack),if=(variable.ga_priority|debuff.razorice.stack<5)&(!death_knight.runeforge.razorice&(debuff.razorice.stack<5|debuff.razorice.remains<gcd*3)|((variable.rp_buffs|rune<2)&active_enemies>1))
    if S.GlacialAdvance:IsReady() then
      if CastTargetIf(S.GlacialAdvance, EnemiesMelee, "max", EvaluateTargetIfFilterRazoriceStacks, EvaluateTargetIfGlacialAdvanceObliteration) then return "glacial_advance obliteration 10"; end
    end
    -- frost_strike,target_if=max:((talent.shattering_blade&debuff.razorice.stack=5)*5)+(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice,if=(rune<2|variable.rp_buffs|debuff.razorice.stack=5&talent.shattering_blade)&(!talent.glacial_advance|active_enemies=1|talent.shattered_frost)
    if S.FrostStrike:IsReady() and (not S.GlacialAdvance:IsAvailable() or EnemiesMeleeCount == 1 or S.ShatteredFrost:IsAvailable()) then
      if CastTargetIf(S.FrostStrike, EnemiesMelee, "max", EvaluateTargetIfFilterFrostStrike, EvaluateTargetIfFrostStrikeObliteration2) then return "frost_strike obliteration 12"; end
    end
    -- howling_blast,if=buff.rime.react
    if S.HowlingBlast:IsReady() and (Player:BuffUp(S.RimeBuff)) then
      if Cast(S.HowlingBlast) then return "howling_blast obliteration 14"; end
    end
    -- frost_strike,target_if=max:((talent.shattering_blade&debuff.razorice.stack=5)*5)+(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice,if=!talent.glacial_advance|active_enemies=1|talent.shattered_frost
    if S.FrostStrike:IsReady() and (not S.GlacialAdvance:IsAvailable() or EnemiesMeleeCount == 1 or S.ShatteredFrost:IsAvailable()) then
      if CastTargetIf(S.FrostStrike, EnemiesMelee, "max", EvaluateTargetIfFilterFrostStrike, nil) then return "frost_strike obliteration 16"; end
    end
    -- glacial_advance,target_if=max:(debuff.razorice.stack),if=variable.ga_priority
    if S.GlacialAdvance:IsReady() and (VarGAPriority) then
      if CastTargetIf(S.GlacialAdvance, EnemiesMelee, "max", EvaluateTargetIfFilterRazoriceStacks, nil) then return "glacial_advance obliteration 18"; end
    end
    -- frost_strike,target_if=max:((talent.shattering_blade&debuff.razorice.stack=5)*5)+(debuff.razorice.stack+1)%(debuff.razorice.remains+1)*death_knight.runeforge.razorice
    if S.FrostStrike:IsReady() then
      if CastTargetIf(S.FrostStrike, EnemiesMelee, "max", EvaluateTargetIfFilterFrostStrike, nil) then return "frost_strike obliteration 20"; end
    end
    -- horn_of_winter,if=rune<3
    if S.HornofWinter:IsReady() and (Player:Rune() < 3) then
      if Cast(S.HornofWinter) then return "horn_of_winter obliteration 22"; end
    end
    -- arcane_torrent,if=rune<1&runic_power<30
    if S.ArcaneTorrent:IsReady() and (Player:Rune() < 1 and Player:RunicPower() < 30) then
      if Cast(S.ArcaneTorrent) then return "arcane_torrent obliteration 24"; end
    end
    -- howling_blast,if=!buff.killing_machine.react
    if S.HowlingBlast:IsReady() and (Player:BuffDown(S.KillingMachineBuff)) then
      if Cast(S.HowlingBlast) then return "howling_blast obliteration 26"; end
    end
  end  
  
  local function Racials()
    if (VarCDCheck) then
      -- blood_fury,use_off_gcd=1,if=variable.cooldown_check
      if S.BloodFury:IsReady() then
        if Cast(S.BloodFury) then return "blood_fury racials 2"; end
      end
      -- berserking,use_off_gcd=1,if=variable.cooldown_check
      if S.Berserking:IsReady() then
        if Cast(S.Berserking) then return "berserking racials 4"; end
      end
      -- arcane_pulse,if=variable.cooldown_check
      if S.ArcanePulse:IsReady() then
        if Cast(S.ArcanePulse) then return "arcane_pulse racials 6"; end
      end
      -- lights_judgment,if=variable.cooldown_check
      if S.LightsJudgment:IsReady() then
        if Cast(S.LightsJudgment) then return "lights_judgment racials 8"; end
      end
      -- ancestral_call,use_off_gcd=1,if=variable.cooldown_check
      if S.AncestralCall:IsReady() then
        if Cast(S.AncestralCall) then return "ancestral_call racials 10"; end
      end
      -- fireblood,use_off_gcd=1,if=variable.cooldown_check
      if S.Fireblood:IsReady() then
        if Cast(S.Fireblood) then return "fireblood racials 12"; end
      end
    end
    -- bag_of_tricks,if=talent.obliteration&!buff.pillar_of_frost.up&buff.unholy_strength.up
    if S.BagofTricks:IsReady() and (S.Obliteration:IsAvailable() and Player:BuffDown(S.PillarofFrostBuff) and Player:BuffUp(S.UnholyStrengthBuff)) then
      if Cast(S.BagofTricks) then return "bag_of_tricks racials 14"; end
    end
    -- bag_of_tricks,if=!talent.obliteration&buff.pillar_of_frost.up&(buff.unholy_strength.up&buff.unholy_strength.remains<gcd*3|buff.pillar_of_frost.remains<gcd*3)
    if S.BagofTricks:IsReady() and (not S.Obliteration:IsAvailable() and Player:BuffUp(S.PillarofFrostBuff) and (Player:BuffUp(S.UnholyStrengthBuff) and Player:BuffRemains(S.UnholyStrengthBuff) < Player:GCD() * 3 or Player:BuffRemains(S.PillarofFrostBuff) < Player:GCD() * 3)) then
      if Cast(S.BagofTricks) then return "bag_of_tricks racials 16"; end
    end
  end
  
  local function SingleTarget()
    -- frost_strike,if=talent.a_feast_of_souls&debuff.razorice.stack=5&talent.shattering_blade&buff.a_feast_of_souls.up
    if S.FrostStrike:IsReady() and (S.AFeastofSouls:IsAvailable() and Target:DebuffStack(S.RazoriceDebuff) == 5 and S.ShatteringBlade:IsAvailable() and Player:BuffUp(S.AFeastofSoulsBuff)) then
      if Cast(S.FrostStrike) then return "frost_strike single_target 2"; end
    end
    -- obliterate,if=buff.killing_machine.react=2|buff.exterminate.up
    if S.Obliterate:IsReady() and (Player:BuffStack(S.KillingMachineBuff) == 2 or Player:BuffUp(S.ExterminateBuff)) then
      if Cast(S.Obliterate) then return "obliterate single_target 4"; end
    end
    -- horn_of_winter,if=(!talent.breath_of_sindragosa|variable.true_breath_cooldown>cooldown.horn_of_winter.duration-15)&cooldown.pillar_of_frost.remains<variable.oblit_pooling_time
    if S.HornofWinter:IsReady() and ((not S.BreathofSindragosa:IsAvailable() or VarTrueBreathCD > 30) and S.PillarofFrost:CooldownRemains() < VarOblitPoolingTime) then
      if Cast(S.HornofWinter) then return "horn_of_winter single_target 6"; end
    end
    -- frost_strike,if=debuff.razorice.stack=5&talent.shattering_blade
    if S.FrostStrike:IsReady() and (Target:DebuffStack(S.RazoriceDebuff) == 5 and S.ShatteringBlade:IsAvailable()) then
      if Cast(S.FrostStrike) then return "frost_strike single_target 8"; end
    end
    -- soul_reaper,if=fight_remains>5&target.time_to_pct_35<5&target.time_to_pct_0>5&!buff.killing_machine.react
    if S.SoulReaper:IsReady() and (FightRemains > 5 and Target:TimeToX(35) < 5 and Target:TimeToDie() > 5 and Player:BuffDown(S.KillingMachineBuff)) then
      if Cast(S.SoulReaper) then return "soul_reaper single_target 10"; end
    end
    -- obliterate,if=buff.killing_machine.react&rune>3
    if S.Obliterate:IsReady() and (Player:BuffUp(S.KillingMachineBuff) and Player:Rune() > 3) then
      if Cast(S.Obliterate) then return "obliterate single_target 12"; end
    end
    -- howling_blast,if=buff.rime.react
    if S.HowlingBlast:IsReady() and Player:BuffUp(S.RimeBuff) then
      if Cast(S.HowlingBlast) then return "howling_blast single_target 14"; end
    end
    -- frost_strike,if=!variable.pooling_runic_power&runic_power.deficit<=30
    if S.FrostStrike:IsReady() and (not VarPoolingRP and Player:RunicPowerDeficit() <= 30) then
      if Cast(S.FrostStrike) then return "frost_strike single_target 16"; end
    end
    -- obliterate,if=cooldown.pillar_of_frost.remains>4*gcd.max|buff.gathering_storm.up
    if S.Obliterate:IsReady() and (S.PillarofFrost:CooldownRemains() > 4 * Player:GCD() or Player:BuffUp(S.GatheringStormBuff)) then
      if Cast(S.Obliterate) then return "obliterate single_target 18"; end
    end
    -- horn_of_winter,if=rune<2&runic_power.deficit>25&(!talent.breath_of_sindragosa|variable.true_breath_cooldown>cooldown.horn_of_winter.duration-15)
    if S.HornofWinter:IsReady() and (Player:Rune() < 2 and Player:RunicPowerDeficit() > 25 and (not S.BreathofSindragosa:IsAvailable() or VarTrueBreathCD > 30)) then
      if Cast(S.HornofWinter) then return "horn_of_winter single_target 20"; end
    end
    -- arcane_torrent,if=!talent.breath_of_sindragosa&runic_power.deficit>20
    if S.ArcaneTorrent:IsReady() and (not S.BreathofSindragosa:IsAvailable() and Player:RunicPowerDeficit() > 20) then
      if Cast(S.ArcaneTorrent) then return "arcane_torrent single_target 22"; end
    end
    -- frost_strike
    if S.FrostStrike:IsReady() then
      if Cast(S.FrostStrike) then return "frost_strike single_target 24"; end
    end
    -- abomination_limb
    if S.AbominationLimb:IsCastable() then
      if Cast(S.AbominationLimb) then return "abomination_limb_talent single_target 26"; end
    end
  end
  
  local function Trinkets()
    -- use_item,use_off_gcd=1,name=treacherous_transmitter,if=cooldown.pillar_of_frost.remains<6&variable.sending_cds&(variable.trinket_1_buffs&variable.trinket_2_buffs|!talent.breath_of_sindragosa|cooldown.breath_of_sindragosa.remains<6)|fight_remains<30
    if I.TreacherousTransmitter:IsEquippedAndReady() and (S.PillarofFrost:CooldownRemains() < 6 and VarSendingCDs and (VarTrinket1Buffs and VarTrinket2Buffs or not S.BreathofSindragosa:IsAvailable() or S.BreathofSindragosa:CooldownRemains() < 6) or BossFightRemains < 30) then
      if Cast(I.TreacherousTransmitter) then return "treacherous_transmitter trinkets 2"; end
    end
    -- use_item,slot=trinket1,if=!trinket.1.cast_time>0&variable.trinket_1_buffs&!variable.trinket_1_manual&buff.pillar_of_frost.remains>variable.trinket_1_duration%2&(!trinket.2.has_cooldown|trinket.2.cooldown.remains|variable.trinket_priority=1)
    if Trinket1 and Trinket1:IsReady() and not VarTrinket1Ex and not Player:IsItemBlacklisted(Trinket1) and (VarTrinket1CastTime == 0 and VarTrinket1Buffs and not VarTrinket1Manual and Player:BuffRemains(S.PillarofFrostBuff) > VarTrinket1Duration / 2 and (not Trinket2:HasCooldown() or Trinket2:CooldownDown() or VarTrinketPriority == 1)) then
      if Cast(Trinket1) then return "Generic use_item for " .. Trinket1:Name() .. " trinkets 4"; end
    end
    -- use_item,slot=trinket2,if=!trinket.2.cast_time>0&variable.trinket_2_buffs&!variable.trinket_2_manual&buff.pillar_of_frost.remains>variable.trinket_2_duration%2&(!trinket.1.has_cooldown|trinket.1.cooldown.remains|variable.trinket_priority=2)
    if Trinket2 and Trinket2:IsReady() and not VarTrinket2Ex and not Player:IsItemBlacklisted(Trinket2) and (VarTrinket2CastTime == 0 and VarTrinket2Buffs and not VarTrinket2Manual and Player:BuffRemains(S.PillarofFrostBuff) > VarTrinket2Duration / 2 and (not Trinket1:HasCooldown() or Trinket1:CooldownDown() or VarTrinketPriority == 2)) then
      if Cast(Trinket2) then return "Generic use_item for " .. Trinket2:Name() .. " trinkets 6"; end
    end
    -- use_item,slot=trinket1,use_off_gcd=1,if=trinket.1.cast_time>0&variable.trinket_1_buffs&!variable.trinket_1_manual&cooldown.pillar_of_frost.ready&variable.sending_cds&(!talent.breath_of_sindragosa|(cooldown.breath_of_sindragosa.remains|runic_power>variable.breath_rp_threshold))&(!trinket.2.has_cooldown|trinket.2.cooldown.remains|variable.trinket_priority=1)|variable.trinket_1_duration>=fight_remains
    if Trinket1 and Trinket1:IsReady() and not VarTrinket1Ex and not Player:IsItemBlacklisted(Trinket1) and (VarTrinket1CastTime > 0 and VarTrinket1Buffs and not VarTrinket1Manual and S.PillarofFrost:CooldownUp() and VarSendingCDs and (not S.BreathofSindragosa:IsAvailable() or (S.BreathofSindragosa:CooldownDown() or Player:RunicPower() > VarBreathRPThreshold)) and (not Trinket2:HasCooldown() or Trinket2:CooldownDown() or VarTrinketPriority == 1) or VarTrinket1Duration >= FightRemains) then
      if Cast(Trinket1) then return "Generic use_item for " .. Trinket1:Name() .. " trinkets 8"; end
    end
    -- use_item,slot=trinket2,use_off_gcd=1,if=trinket.2.cast_time>0&variable.trinket_2_buffs&!variable.trinket_2_manual&cooldown.pillar_of_frost.ready&variable.sending_cds&(!talent.breath_of_sindragosa|(cooldown.breath_of_sindragosa.remains|runic_power>variable.breath_rp_threshold))&(!trinket.1.has_cooldown|trinket.1.cooldown.remains|variable.trinket_priority=2)|variable.trinket_2_duration>=fight_remains
    if Trinket2 and Trinket2:IsReady() and not VarTrinket2Ex and not Player:IsItemBlacklisted(Trinket2) and (VarTrinket2CastTime > 0 and VarTrinket2Buffs and not VarTrinket2Manual and S.PillarofFrost:CooldownUp() and VarSendingCDs and (not S.BreathofSindragosa:IsAvailable() or (S.BreathofSindragosa:CooldownDown() or Player:RunicPower() > VarBreathRPThreshold)) and (not Trinket1:HasCooldown() or Trinket1:CooldownDown() or VarTrinketPriority == 2) or VarTrinket2Duration >= FightRemains) then
      if Cast(Trinket2) then return "Generic use_item for " .. Trinket2:Name() .. " trinkets 10"; end
    end
    -- use_item,slot=trinket1,if=!variable.trinket_1_buffs&!variable.trinket_1_manual&(variable.damage_trinket_priority=1|(!trinket.2.has_cooldown|trinket.2.cooldown.remains))&((trinket.1.cast_time>0&(!talent.breath_of_sindragosa|!buff.breath_of_sindragosa.up|!variable.breath_dying)&!buff.pillar_of_frost.up|!trinket.1.cast_time>0)&(!variable.trinket_2_buffs|cooldown.pillar_of_frost.remains>20)|!talent.pillar_of_frost)|fight_remains<15
    if Trinket1 and Trinket1:IsReady() and not VarTrinket1Ex and not Player:IsItemBlacklisted(Trinket1) and (not VarTrinket1Buffs and not VarTrinket1Manual and (VarDamageTrinketPriority == 1 or (not Trinket2:HasCooldown() or Trinket2:CooldownDown())) and ((VarTrinket1CastTime > 0 and (not S.BreathofSindragosa:IsAvailable() or Player:BuffDown(S.BreathofSindragosa) or not VarBreathDying) and Player:BuffDown(S.PillarofFrostBuff) or VarTrinket1CastTime == 0) and (not VarTrinket2Buffs or S.PillarofFrost:CooldownRemains() > 20) or not S.PillarofFrost:IsAvailable()) or BossFightRemains < 15) then
      if Cast(Trinket1) then return "Generic use_item for " .. Trinket1:Name() .. " trinkets 12"; end
    end
    -- use_item,slot=trinket2,if=!variable.trinket_2_buffs&!variable.trinket_2_manual&(variable.damage_trinket_priority=2|(!trinket.1.has_cooldown|trinket.1.cooldown.remains))&((trinket.2.cast_time>0&(!talent.breath_of_sindragosa|!buff.breath_of_sindragosa.up|!variable.breath_dying)&!buff.pillar_of_frost.up|!trinket.2.cast_time>0)&(!variable.trinket_1_buffs|cooldown.pillar_of_frost.remains>20)|!talent.pillar_of_frost)|fight_remains<15
    if Trinket2 and Trinket2:IsReady() and not VarTrinket2Ex and not Player:IsItemBlacklisted(Trinket2) and (not VarTrinket2Buffs and not VarTrinket2Manual and (VarDamageTrinketPriority == 2 or (not Trinket1:HasCooldown() or Trinket1:CooldownDown())) and ((VarTrinket2CastTime > 0 and (not S.BreathofSindragosa:IsAvailable() or Player:BuffDown(S.BreathofSindragosa) or not VarBreathDying) and Player:BuffDown(S.PillarofFrostBuff) or VarTrinket2CastTime == 0) and (not VarTrinket1Buffs or S.PillarofFrost:CooldownRemains() > 20) or not S.PillarofFrost:IsAvailable()) or BossFightRemains < 15) then
      if Cast(Trinket2) then return "Generic use_item for " .. Trinket2:Name() .. " trinkets 14"; end
    end
    -- use_item,slot=main_hand,if=buff.pillar_of_frost.up|(buff.breath_of_sindragosa.up&cooldown.pillar_of_frost.remains)|(variable.trinket_1_buffs&variable.trinket_2_buffs&(trinket.1.cooldown.remains<cooldown.pillar_of_frost.remains|trinket.2.cooldown.remains<cooldown.pillar_of_frost.remains)&cooldown.pillar_of_frost.remains>20)|fight_remains<15
    local ItemToUse, _, ItemRange = Player:GetUseableItems(OnUseExcludes, nil, true)
    if ItemToUse and (Player:BuffUp(S.PillarofFrostBuff) or (Player:BuffUp(S.BreathofSindragosa) and S.PillarofFrost:CooldownDown()) or (VarTrinket1Buffs and VarTrinket2Buffs and (Trinket1:CooldownRemains() < S.PillarofFrost:CooldownRemains() or Trinket2:CooldownRemains() < S.PillarofFrost:CooldownRemains()) and S.PillarofFrost:CooldownRemains() > 20) or BossFightRemains < 15) then
      if Cast(ItemToUse) then return "Generic use_item for " .. ItemToUse:Name() .. " trinkets 16"; end
    end
  end  
  

  local function Variables()
    -- variable,name=st_planning,op=setif,value=1,value_else=0,condition=active_enemies=1&(!raid_event.adds.exists|!raid_event.adds.in|raid_event.adds.in>15)
    VarSTPlanning = EnemiesMeleeCount == 1 or not AoEON()
    -- variable,name=adds_remain,value=active_enemies>=2&(!raid_event.adds.exists|!raid_event.pull.exists&raid_event.adds.remains>5|raid_event.pull.exists&raid_event.adds.in>20)
    VarAddsRemain = EnemiesMeleeCount >= 2 and AoEON()
    -- variable,name=use_breath,value=variable.st_planning|active_enemies>=2&(!raid_event.adds.exists|!raid_event.pull.exists&raid_event.adds.remains>15|(raid_event.pull.exists&!raid_event.adds.has_boss&raid_event.adds.remains>30))
    -- Note: This line is more complex than sending_cds, but only because of raid_event conditions, which we don't/can't handle. Simplifying  it a bit.
    VarUseBreath = VarSTPlanning or VarAddsRemain
    -- variable,name=sending_cds,value=(variable.st_planning|variable.adds_remain)
    VarSendingCDs = VarSTPlanning or VarAddsRemain
    -- variable,name=rime_buffs,value=buff.rime.react&(variable.static_rime_buffs|talent.avalanche&!talent.arctic_assault&debuff.razorice.stack<5)
    VarRimeBuffs = Player:BuffUp(S.RimeBuff) and (VarStaticRimeBuffs or S.Avalanche:IsAvailable() and not S.ArcticAssault:IsAvailable() and Target:DebuffStack(S.RazoriceDebuff) < 5)
    -- variable,name=rp_buffs,value=talent.unleashed_frenzy&(buff.unleashed_frenzy.remains<gcd.max*3|buff.unleashed_frenzy.stack<3)|talent.icy_talons&(buff.icy_talons.remains<gcd.max*3|buff.icy_talons.stack<(3+(2*talent.smothering_offense)))
    VarRPBuffs = S.UnleashedFrenzy:IsAvailable() and (Player:BuffRemains(S.UnleashedFrenzyBuff) < Player:GCD() * 3 or Player:BuffStack(S.UnleashedFrenzyBuff) < 3) or S.IcyTalons:IsAvailable() and (Player:BuffRemains(S.IcyTalonsBuff) < Player:GCD() * 3 or Player:BuffStack(S.IcyTalonsBuff) < (3 + (2 * num(S.SmotheringOffense:IsAvailable()))))
    -- variable,name=cooldown_check,value=(!talent.breath_of_sindragosa|buff.breath_of_sindragosa.up)&(talent.pillar_of_frost&buff.pillar_of_frost.up&(talent.obliteration&buff.pillar_of_frost.remains>10|!talent.obliteration)|!talent.pillar_of_frost&buff.empower_rune_weapon.up|!talent.pillar_of_frost&!talent.empower_rune_weapon|active_enemies>=2&buff.pillar_of_frost.up)
    VarCDCheck = (not S.BreathofSindragosa:IsAvailable() or Player:BuffUp(S.BreathofSindragosa)) and (S.PillarofFrost:IsAvailable() and Player:BuffUp(S.PillarofFrostBuff) and (S.Obliteration:IsAvailable() and Player:BuffRemains(S.PillarofFrostBuff) > 10 or not S.Obliteration:IsAvailable()) or not S.PillarofFrost:IsAvailable() and Player:BuffUp(S.EmpowerRuneWeaponBuff) or not S.PillarofFrost:IsAvailable() and not S.EmpowerRuneWeapon:IsAvailable() or EnemiesMeleeCount >= 2 and Player:BuffUp(S.PillarofFrostBuff))
    -- variable,name=true_breath_cooldown,op=setif,value=cooldown.breath_of_sindragosa.remains,value_else=cooldown.pillar_of_frost.remains,condition=cooldown.breath_of_sindragosa.remains>cooldown.pillar_of_frost.remains
    VarTrueBreathCD = (S.BreathofSindragosa:CooldownRemains() > S.PillarofFrost:CooldownRemains()) and S.BreathofSindragosa:CooldownRemains() or S.PillarofFrost:CooldownRemains()
    -- variable,name=oblit_pooling_time,op=setif,value=((cooldown.pillar_of_frost.remains+1)%gcd.max)%((rune+1)*((runic_power+5)))*100,value_else=3,condition=rune<variable.oblit_rune_pooling&cooldown.pillar_of_frost.remains<10
    VarOblitPoolingTime = 3
    if Player:Rune() < VarOblitRunePooling and S.PillarofFrost:CooldownRemains() < 10 then
      VarOblitPoolingTime = ((S.PillarofFrost:CooldownRemains() + 1) / Player:GCD()) / ((Player:Rune() + 1) * (Player:RunicPower() + 5)) * 100
    end
    -- variable,name=breath_pooling_time,op=setif,value=((variable.true_breath_cooldown+1)%gcd.max)%((rune+1)*(runic_power+20))*100,value_else=0,condition=runic_power.deficit>10&variable.true_breath_cooldown<10
    VarBreathPoolingTime = 0
    if Player:RunicPowerDeficit() > 10 and VarTrueBreathCD < 10 then
      VarBreathPoolingTime = ((VarTrueBreathCD + 1) / Player:GCD()) / ((Player:Rune() + 1) * (Player:RunicPower() + 20)) * 100
    end
    -- variable,name=pooling_runes,value=rune<variable.oblit_rune_pooling&talent.obliteration&(!talent.breath_of_sindragosa|variable.true_breath_cooldown)&cooldown.pillar_of_frost.remains<variable.oblit_pooling_time
    VarPoolingRunes = Player:Rune() < VarOblitRunePooling and S.Obliteration:IsAvailable() and (not S.BreathofSindragosa:IsAvailable() or bool(VarTrueBreathCD)) and S.PillarofFrost:CooldownRemains() < VarOblitPoolingTime
    -- variable,name=pooling_runic_power,value=talent.breath_of_sindragosa&(variable.true_breath_cooldown<variable.breath_pooling_time|fight_remains<30&!cooldown.breath_of_sindragosa.remains)
    VarPoolingRP = S.BreathofSindragosa:IsAvailable() and (VarTrueBreathCD < VarBreathPoolingTime or FightRemains < 30 and S.BreathofSindragosa:CooldownUp())
    -- variable,name=ga_priority,value=(!talent.shattered_frost&talent.shattering_blade&active_enemies>=4)|(!talent.shattered_frost&!talent.shattering_blade&active_enemies>=2)
    VarGAPriority = (not S.ShatteredFrost:IsAvailable() and S.ShatteringBlade:IsAvailable() and EnemiesMeleeCount >= 4) or (not S.ShatteredFrost:IsAvailable() and not S.ShatteringBlade:IsAvailable() and EnemiesMeleeCount >= 2)
    -- variable,name=breath_dying,value=runic_power<variable.breath_rp_cost*2*gcd.max&rune.time_to_2>runic_power%variable.breath_rp_cost
    VarBreathDying = Player:RunicPower() < VarBreathRPCost * 2 * Player:GCD() and Player:RuneTimeToX(2) > Player:RunicPower() / VarBreathRPCost
    -- variable,name=fwf_buffs,value=(buff.pillar_of_frost.remains<gcd.max|(buff.unholy_strength.up&buff.unholy_strength.remains<gcd.max)|(talent.bonegrinder.rank=2&buff.bonegrinder_frost.up&buff.bonegrinder_frost.remains<gcd.max))&(active_enemies>1|debuff.razorice.stack=5|!death_knight.runeforge.razorice&(!talent.glacial_advance|!talent.avalanche|!talent.arctic_assault)|talent.shattering_blade)
    VarFWFBuffs = (Player:BuffRemains(S.PillarofFrostBuff) < Player:GCD() or (Player:BuffUp(S.UnholyStrengthBuff) and Player:BuffRemains(S.UnholyStrengthBuff) < Player:GCD()) or (S.Bonegrinder:TalentRank() == 2 and Player:BuffUp(S.BonegrinderFrostBuff) and Player:BuffRemains(S.BonegrinderFrostBuff) < Player:GCD())) and (EnemiesMeleeCount > 1 or Target:DebuffStack(S.RazoriceDebuff) == 5 or not UsingRazorice and (not S.GlacialAdvance:IsAvailable() or not S.Avalanche:IsAvailable() or not S.ArcticAssault:IsAvailable()) or S.ShatteringBlade:IsAvailable())
  end
  

  local function APL()
      TargetIsValid = MainAddon.TargetIsValid()
      EnemiesMelee = Player:GetEnemiesInMeleeRange(5)
      if AoEON() then
        EnemiesMeleeCount = #EnemiesMelee
      else
        EnemiesMeleeCount = 1
      end
      
      if M.TargetIsValid() or Player:AffectingCombat() then
          -- Calculate fight_remains
          BossFightRemains = HL.BossFightRemains()
          FightRemains = BossFightRemains
          if FightRemains == 11111 then
            FightRemains = HL.FightRemains(EnemiesMelee, false)
          end

          -- Calculate GCDMax
          GCDMax = Player:GCD() + 0.25
      end
      
      if GetSetting('deathgrip_ooc', false) and Player:AffectingCombat() then
          local ShouldReturn = DeathKnight:DeathGrip_OOR();
          if ShouldReturn then
              return ShouldReturn;
          end
      end

      if TargetIsValid then
          -- Trinkets
          local shouldReturn = MainAddon.TrinketDPS()
          if shouldReturn then
              return shouldReturn
          end  
          -- call precombat
          if not Player:AffectingCombat() then
              local ShouldReturn = Precombat(); if ShouldReturn then return ShouldReturn; end
          end
          -- auto_attack
          -- call_action_list,name=variables
          Variables()
          -- call_action_list,name=trinkets
          local ShouldReturn = Trinkets(); if ShouldReturn then return ShouldReturn; end
          -- call_action_list,name=high_prio_actions
          local ShouldReturn = HighPrioActions(); if ShouldReturn then return ShouldReturn; end
          -- call_action_list,name=cooldowns
          local ShouldReturn = Cooldowns(); if ShouldReturn then return ShouldReturn; end
          -- call_action_list,name=racials
          local ShouldReturn = Racials(); if ShouldReturn then return ShouldReturn; end
          -- call_action_list,name=cold_heart,if=talent.cold_heart&(!buff.killing_machine.up|talent.breath_of_sindragosa)&((debuff.razorice.stack=5|!death_knight.runeforge.razorice&!talent.glacial_advance&!talent.avalanche&!talent.arctic_assault)|fight_remains<=gcd)
          if S.ColdHeart:IsAvailable() and (Player:BuffDown(S.KillingMachineBuff) or S.BreathofSindragosa:IsAvailable()) and ((Target:DebuffStack(S.RazoriceDebuff) == 5 or not UsingRazorice and not S.GlacialAdvance:IsAvailable() and not S.Avalanche:IsAvailable() and not S.ArcticAssault:IsAvailable()) or BossFightRemains <= Player:GCD() + 0.5) then
            local ShouldReturn = ColdHeart(); if ShouldReturn then return ShouldReturn; end
          end
          -- run_action_list,name=breath,if=buff.breath_of_sindragosa.up
          if Player:BuffUp(S.BreathofSindragosa) then
            local ShouldReturn = Breath(); if ShouldReturn then return ShouldReturn; end
            if Cast(S.Pool) then return "Wait for Breath()"; end
          end
          -- run_action_list,name=obliteration,if=talent.obliteration&buff.pillar_of_frost.up&!buff.breath_of_sindragosa.up
          if S.Obliteration:IsAvailable() and Player:BuffUp(S.PillarofFrostBuff) and Player:BuffDown(S.BreathofSindragosa) then
            local ShouldReturn = Obliteration(); if ShouldReturn then return ShouldReturn; end
            if Cast(S.Pool) then return "Wait for Obliteration()"; end
          end
          -- call_action_list,name=aoe,if=active_enemies>=2
          if EnemiesMeleeCount >= 2 and AoEON() then
            local ShouldReturn = AoE(); if ShouldReturn then return ShouldReturn; end
          end
          -- call_action_list,name=single_target,if=active_enemies=1
          if EnemiesMeleeCount == 1 or not AoEON() then
            local ShouldReturn = SingleTarget(); if ShouldReturn then return ShouldReturn; end
          end
          -- nothing to cast, wait for resouces
          if Cast(S.Pool) then return "Wait/Pool Resources"; end
      end
  end

  local function EnemyRotationPvP()
      local EnemiesPvP40 = Player:GetEnemiesInPvP(40)
      local EnemiesPvP40Count = #EnemiesPvP40
      local EnemiesPvPMelee = Player:GetEnemiesInPvP(5)
      local EnemiesPvPMeleeCount = #EnemiesPvPMelee
      local TotalEnemies = TotalEnemies_Players()

      if Player:InArena() then
          for i = 1, 3 do
              ---@class Unit
              local TargetedUnit = Arena["arena" .. i]

              if S.Strangulate:IsReady(TargetedUnit) and TargetedUnit:IsAHealer() and Player:BuffUp(S.PillarofFrostBuff) and not TargetedUnit:InCC() and TargetedUnit:AbsentImun(Temp.dmgMagic, true, true) then
                  if ForceCastDisplay(S.Strangulate, 6, TargetedUnit) then
                      return "Strangulate Healer " .. TargetedUnit.UnitID;
                  end
              end

              if S.ChainsofIce:IsReady(TargetedUnit) and TargetedUnit:ShouldSlow(Temp.dmgMagic) then
                  if ForceCastDisplay(S.ChainsofIce, 6, TargetedUnit) then
                      return "ChainsOfIce Arena " .. TargetedUnit.UnitID;
                  end
              end
          end
      end

      if ((Player:InArena() and EnemiesPvPMeleeCount >= TotalEnemies) or not Player:InArena()) and Player:BuffRemains(S.AbominationLimb) >= 3 then
          if S.PillarofFrost:IsReady(Player) then
              if Cast(S.PillarofFrost) then
                  return 'pillar pvp wombo combo'
              end
          end
          if S.FrostwyrmsFury:IsReady() and Target:AbsentImun('Magic') then
              if Cast(S.FrostwyrmsFury) then
                  return 'frostwyrm pvp wombo combo'
              end
          end
          if S.ChillStreak:IsReady() and Target:AbsentImun('Magic') then
              if Cast(S.ChillStreak) then
                  return 'chilstreak pvp wombo combo'
              end
          end
          if S.EmpowerRuneWeapon:IsReady(Player) then
              if Cast(S.EmpowerRuneWeapon) then
                  return 'chilstreak pvp wombo combo'
              end
          end
      end
  end
  local function MainRotation()
      S.RemorselessWinter.MeleeRange = Player:CombatTime() > 5 and 8 or 14
      S.Frostscythe.MeleeRange = Player:CombatTime() > 5 and 10 or 6

      --DEFENSIVES
      if HL.CombatTime() > 0 then
          local ShouldReturn = PlayerDefensives()
          if ShouldReturn then
              return ShouldReturn
          end
      end

      --MINDREEZE TO KEEP BOS
      if Player:BuffUp(S.BreathofSindragosa) then
          local ShouldReturn = MindFreeze_Bos();
          if ShouldReturn then
              return ShouldReturn;
          end
      end

      return APL()
  end
  
  local function Init()
      S.FrostFeverDebuff:RegisterAuraTracking()
  end
  MainAddon.SetAPL(251, MainRotation, Init)

  local OldFRIsReady
  OldFRIsReady = HL.AddCoreOverride("Spell.IsReady",
          function(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
              local BaseCheck, Reason = OldFRIsReady(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
              if MainAddon.PlayerSpecID() == 251 then

              -- movement check (stand-still threshold)
              DPSMovingValue_check  = GetSetting('DPSMovingValue_check', false)
              DPSMovingValue_spin   = GetSetting('DPSMovingValue_spin', 0.5)
              DPSMovingValueSpells  = GetSetting('DPSMovingValueSpells', {})

              if DPSMovingValue_check then
                  local stillTime = Player:IsStandingStillFor()

                  -- only block these spells when their checkbox is enabled
                  if (self == S.AbominationLimb    and DPSMovingValueSpells['abomlimb']) or
                    (self == S.BreathofSindragosa   and DPSMovingValueSpells['bos']) or
                    (self == S.DeathAndDecay        and DPSMovingValueSpells['dnd']) or
                    (self == S.PillarofFrost        and DPSMovingValueSpells['pof']) or
                    (self == S.ReapersMark          and DPSMovingValueSpells['mark']) then

                      if stillTime <= DPSMovingValue_spin then
                          return false, ("Must stand still %.1fs"):format(DPSMovingValue_spin)
                      end
                  end
              end
 
              -- meleeratio setting
              local meleeratioEnabled = GetSetting('meleeratio_check', true)
              local meleeratioValue   = GetSetting('meleeratio_spin', 30)
              local meleeratioSpells  = GetSetting('meleeratiospells', {})

              if meleeratioEnabled and BossFightRemains == 11111 then
                  -- total enemies in 40y
                  local enemiesAll       = Player:GetEnemiesInRange(40)
                  local enemiesCount     = #enemiesAll
                  -- total in melee (8y)
                  local enemiesMeleeCount = #Player:GetEnemiesInMeleeRange(8)

                  local currentRatio = 0
                  if enemiesCount > 0 then
                      currentRatio = (enemiesMeleeCount / enemiesCount) * 100
                  end

                  -- only block these spells when their checkbox is enabled
                  if (self == S.AbominationLimb    and meleeratioSpells['ratioAbom']) or
                    (self == S.BreathofSindragosa  and meleeratioSpells['ratioBoS']) or
                    (self == S.DeathAndDecay      and meleeratioSpells['ratioDnD']) or
                    (self == S.PillarofFrost      and meleeratioSpells['ratioPoF']) or
                    (self == S.ReapersMark        and meleeratioSpells['ratioMark']) then

                      if currentRatio <= meleeratioValue then
                          return false, ("Not enough enemies in melee range (%.0f%%)"):format(currentRatio)
                      end
                  end
              end


              if S.BreathofSindragosa:IsAvailable() then
                  if self == S.PillarofFrost then
                      if (S.BreathofSindragosa:CooldownRemains(nil, true) > 1 
                      and S.BreathofSindragosa:CooldownRemains(nil, true) <= 44 
                      and not S.BreathofSindragosa:IsBlocked())
                      or S.BreathofSindragosa:IsBlocked() and S.BreathofSindragosa:CooldownRemains(nil, true) <= 44 then
                           return false, 'Pillar'
                       end
                   end
                   if S.BreathofSindragosa:IsBlocked() or S.BreathofSindragosa:IsQueued() then
                       if self == S.PillarofFrost then
                          if not S.BreathofSindragosa:IsQueued() and S.BreathofSindragosa:CooldownRemains(nil, true) < 45 then
                               return false, 'BoS CD Soon - Pillar'
                           end

                          if S.BreathofSindragosa:IsQueued() and S.BreathofSindragosa:CooldownRemains(nil, true) > Player:GCD() then
                               return false, 'BoS CD - Pillar'
                           end
                      end

                       if self == S.AbominationLimb then
                           return false, "Only with bos"
                       end

                      if self == S.EmpowerRuneWeapon then
                          if not Player:BuffUp(S.BreathofSindragosa) then
                              return false, 'Missing debuff'
                          end
                       end
                   end
               end


                if self == S.DeathAndDecay then
                    if S.DeathAndDecay:TimeSinceLastCast() <= 2 then
                        return false
                    end
                end


                if self == S.RemorselessWinter then
                   if S.PillarofFrost:CooldownRemains(nil, true) >= 1.5 and S.PillarofFrost:CooldownRemains(nil, true) <= 18 then
                       return false
                   end
                end

                -- if not Player:IsInRaidArea() and not Player:IsInEncounter() and not Target:IsBoss() and not Target:IsInBossList() then
                --     if self == S.PillarofFrost then
                --         if HL.FilteredFightRemains(Enemies10yd, "<", 25) and not Player:BuffUp(S.EmpowerRuneWeaponBuff) and not Player:BuffUp(S.DeathsDueBuff) and not Player:BuffUp(S.PillarOfFrostBuff) then
                --             return false, 'Mobs Dying'
                --         end

                --         if not Player:BuffUp(S.PillarOfFrostBuff) and S.PillarofFrost:CooldownRemains() > 3 and S.PillarofFrost:CooldownRemains() < 45 then
                --             return false, 'Sync up with Pillar'
                --         end
                --     end

                --     if self == S.EmpowerRuneWeapon then
                --         if HL.FilteredFightRemains(Enemies10yd, "<", 25) and not Player:BuffUp(S.EmpowerRuneWeaponBuff) and not Player:BuffUp(S.DeathsDueBuff) and not Player:BuffUp(S.PillarOfFrostBuff) then
                --             return false, 'Mobs Dying'
                --         end

                --         if S.PillarofFrost:CooldownRemains() > 3 and S.PillarofFrost:CooldownRemains() < 45 then
                --             return false, 'Sync up with Pillar'
                --         end
                --     end
              end
              return BaseCheck, Reason
          end
  , 251);

  local OldIsCastable
  OldIsCastable = HL.AddCoreOverride("Spell.IsCastable",
    function (self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
      if MainAddon.PlayerSpecID() == 251 then
          if not GetSetting('DnDMoving', false) then
            if self == S.DeathAndDecay then
              if (Player:IsMoving() or Player:IsStandingStillFor() < 1.2) then
                return false, "Moving"
              end
            end
          end
      end

      local BaseCheck = OldIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
      return BaseCheck
    end
  , 251)

  HL:RegisterForSelfCombatEvent(
    function (...)
        local SpellID = select(12, ...)
        if SpellID == S.BreathofSindragosa:ID() then
            BreathTimer =  GetTime() - BreathTimeStamp
            if GetSetting('toast_breath', false) then
                MainAddon.UI:ShowToast("Breath of Sindragosa", "Uptime: " .. tostring((math.floor(BreathTimer * 10) / 10)) .. ' seconds.', 1029007)
                BreathTimer = 0
            end
        end
  end, "SPELL_AURA_REMOVED")

  HL:RegisterForSelfCombatEvent(
      function (...)
          local SpellID = select(12, ...)
          if SpellID == S.BreathofSindragosa:ID() then
              BreathTimeStamp = GetTime()
          end
  end, "SPELL_AURA_APPLIED")
end