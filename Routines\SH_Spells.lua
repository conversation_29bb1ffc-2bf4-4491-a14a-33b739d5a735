---@class MainAddon
local MainAddon = MainAddon
-- HeroLib
local HL = HeroLibEx
---@class Spell
local Spell = HL.Spell
local CreateSpell = MainAddon.CreateSpell
local CreateMultiSpell = MainAddon.CreateMultiSpell
---@class Item
local Item = HL.Item
local IsSpellKnown = _G['IsSpellKnown']
local MergeTableByKey = HL.Utils.MergeTableByKey
local C_Timer = _G['C_Timer']
local select = _G['select']
local pairs = _G['pairs']
local GetTime = _G['GetTime']
local Delay = C_Timer.After

MainAddon.Shaman = {}
---@class Shaman
local Shaman = MainAddon.Shaman

-- Spells
if not Spell.Shaman then
    Spell.Shaman = {}
end
---@class SHCustomTable
Spell.Shaman.Custom = {
    -- HR forgot
    SplinteredElements = CreateSpell(382042),

    BloodFury = CreateMultiSpell(20572, 33702, 33697),
    -- Custom
    CleanseSpirit = CreateSpell(51886),
    HealingStreamTotem = CreateSpell(5394),
    GhostWolf = CreateSpell(2645),
    EarthbindTotem = CreateSpell(2484),
    ChainHeal = CreateSpell(1064),
    FeralLunge = CreateSpell(196884),
    GustofWind = CreateSpell(192063),
    Thunderstorm = CreateSpell(51490),
    Skyfury = CreateSpell(462854),
    StoneBulwarkTotem = CreateSpell(108270),
    Purge = CreateSpell(370),
    GreaterPurge = CreateSpell(378773),
    Hex = CreateSpell(51514),
    AncestralGuidance = CreateSpell(108281),
    LightningLasso = CreateSpell(305483),
    WindRushTotem = CreateSpell(192077),
    TotemicProjection = CreateSpell(108287),
    TremorTotem = CreateSpell(8143),
    EarthgrabTotem = CreateSpell(51485),
    TidecallersGuard = CreateSpell(457481),
    SearingTotem = CreateSpell(461242),
    SpiritWalk = CreateSpell(58875),
    PoisonCleansingTotem = CreateSpell(383013),
    
    -- PvP
    StaticFieldTotem = CreateSpell(355580),
    GroundingTotem = CreateSpell(204336),
    UnleashShield = CreateSpell(356736),
    CounterstrikeTotem = CreateSpell(204331),
    SkyfuryTotem = CreateSpell(204330),
}

---@class SHCommonsTable
Spell.Shaman.Commons = {
    -- Racials
    AncestralCall                         = CreateSpell(274738),
    BagofTricks                           = CreateSpell(312411),
    Berserking                            = CreateSpell(26297),
    BloodFury                             = CreateSpell(33697),
    Fireblood                             = CreateSpell(265221),
    -- Abilities
    Bloodlust                             = CreateMultiSpell(2825,32182), -- Bloodlust/Heroism
    FlameShock                            = CreateMultiSpell(188389,470411),
    FlametongueWeapon                     = CreateSpell(318038),
    FrostShock                            = CreateSpell(196840),
    HealingSurge                          = CreateSpell(8004),
    LightningBolt                         = CreateSpell(188196),
    LightningShield                       = CreateSpell(192106),
    -- Talents
    AstralShift                           = CreateSpell(108271),
    CapacitorTotem                        = CreateSpell(192058),
    ChainLightning                        = CreateSpell(188443),
    EarthElemental                        = CreateSpell(198103),
    EarthShield                           = CreateSpell(974),
    ElementalBlast                        = CreateSpell(117014),
    ElementalOrbit                        = CreateSpell(383010),
    LavaBurst                             = CreateSpell(51505),
    DeeplyRootedElements                  = CreateSpell(378270),
    NaturesSwiftness                      = CreateSpell(378081),
    PrimordialWave                        = CreateSpell(375982),
    SpiritwalkersGrace                    = CreateSpell(79206),
    TotemicRecall                         = CreateSpell(108285),
    WindShear                             = CreateSpell(57994),
    -- Buffs
    EarthShieldOtherBuff                  = CreateSpell(974),
    EarthShieldSelfBuff                   = CreateSpell(383648),
    LightningShieldBuff                   = CreateSpell(192106),
    PrimordialWaveBuff                    = CreateSpell(375986),
    SpiritwalkersGraceBuff                = CreateSpell(79206),
    SplinteredElementsBuff                = CreateSpell(382043),
    -- Debuffs
    FlameShockDebuff                      = CreateSpell(188389),
    LightningRodDebuff                    = CreateSpell(197209),
    -- Other Class Debuffs
    ChaosBrandDebuff                      = CreateSpell(1490),
    HuntersMarkDebuff                     = CreateSpell(257284),
    -- Trinket Effects
    SpymastersReportBuff                  = CreateSpell(451199), -- Stacking buff from before using Spymaster's Web trinket
    SpymastersWebBuff                     = CreateSpell(444959), -- Buff from using Spymaster's Web trinket
    -- Misc
    Pool                                  = CreateSpell(999910),
}
  
---@class FarseerTable
Spell.Shaman.Farseer = {
    -- Talents
    AncestralSwiftness                    = CreateSpell(443454),
    CalloftheAncestors                    = CreateSpell(443450),
    ElementalReverb                       = CreateSpell(443418),
    PrimordialCapacity                    = CreateSpell(443448),
}
  
---@class StormbringerTable
Spell.Shaman.Stormbringer = {
  -- Abilities
  TempestAbility                        = CreateSpell(452201),
  TempestOverload                       = CreateSpell(463351),
  -- Talents
  ArcDischarge                          = CreateSpell(455096),
  AwakeningStorms                       = CreateSpell(455129),
  RollingThunder                        = CreateSpell(454026),
  Supercharge                           = CreateSpell(455110),
  Tempest                               = CreateSpell(454009),
  -- Buffs
  ArcDischargeBuff                      = CreateSpell(455097),
  AwakeningStormsBuff                   = CreateSpell(462131),
  TempestBuff                           = CreateSpell(454015),
}
  
---@class TotemicTable
Spell.Shaman.Totemic = {
    -- Talents
    AmplificationCore                     = CreateSpell(445029),
    Earthsurge                            = CreateSpell(455590),
    LivelyTotems                          = CreateSpell(445034),
    SurgingTotem                          = CreateSpell(444995),
    TotemicRebound                        = CreateSpell(445025),
    -- Buffs
    LivelyTotemsBuff                      = CreateSpell(461242),
    TotemicReboundBuff                    = CreateSpell(458269),
    WhirlingAirBuff                       = CreateSpell(453409),
    WhirlingEarthBuff                     = CreateSpell(453406),
    WhirlingFireBuff                      = CreateSpell(453405),
}

---@class EnhancementTable
Spell.Shaman.Enhancement = {    
  -- Abilities
  VoltaicBlazeAbility                   = CreateSpell(470057),
  Windstrike                            = CreateSpell(115356),
  -- Talents
  AlphaWolf                             = CreateSpell(198434),
  Ascendance                            = CreateSpell(114051),
  AshenCatalyst                         = CreateSpell(390370),
  ConvergingStorms                      = CreateSpell(384363),
  CrashLightning                        = CreateSpell(187874),
  CrashingStorms                        = CreateSpell(334308),
  DoomWinds                             = CreateSpell(384352),
  ElementalAssault                      = CreateSpell(210853),
  ElementalSpirits                      = CreateSpell(262624),
  FeralSpirit                           = CreateSpell(51533),
  FireNova                              = CreateSpell(333974),
  FlowingSpirits                        = CreateSpell(469314),
  Hailstorm                             = CreateSpell(334195),
  HotHand                               = CreateSpell(201900),
  IceStrike                             = CreateMultiSpell(342240, 470194),
  LashingFlames                         = CreateSpell(334046),
  LavaLash                              = CreateSpell(60103),
  MoltenAssault                         = CreateSpell(334033),
  OverflowingMaelstrom                  = CreateSpell(384149),
  PrimordialStorm                       = CreateSpell(1218047),
  PrimordialStormAbility                = CreateSpell(1218090),
  RagingMaelstrom                       = CreateSpell(384143),
  StaticAccumulation                    = CreateSpell(384411),
  Stormblast                            = CreateSpell(319930),
  Stormflurry                           = CreateSpell(344357),
  Stormstrike                           = CreateSpell(17364),
  Sundering                             = CreateSpell(197214),
  SwirlingMaelstrom                     = CreateSpell(384359),
  TempestStrikes                        = CreateSpell(428071),
  ThorimsInvocation                     = CreateSpell(384444),
  UnrelentingStorms                     = CreateSpell(470490),
  UnrulyWinds                           = CreateSpell(390288),
  VoltaicBlaze                          = CreateSpell(470053),
  WindfuryTotem                         = CreateSpell(8512),
  WindfuryWeapon                        = CreateSpell(33757),
  WitchDoctorsAncestry                  = CreateSpell(384447),
  -- Buffs
  AscendanceBuff                        = CreateSpell(114051),
  AshenCatalystBuff                     = CreateSpell(390371),
  ConvergingStormsBuff                  = CreateSpell(198300),
  CracklingThunderBuff                  = CreateSpell(409834),
  CrashLightningBuff                    = CreateSpell(187878),
  CLCrashLightningBuff                  = CreateSpell(333964),
  DoomWindsBuff                         = CreateSpell(466772),
  FeralSpiritBuff                       = CreateSpell(333957),
  GatheringStormsBuff                   = CreateSpell(198300),
  HailstormBuff                         = CreateSpell(334196),
  HotHandBuff                           = CreateSpell(215785),
  IceStrikeBuff                         = CreateSpell(384357),
  LegacyoftheFrostWitchBuff             = CreateSpell(384451),
  MaelstromWeaponBuff                   = CreateSpell(344179),
  PrimordialStormBuff                   = CreateSpell(1218125),
  StormblastBuff                        = CreateSpell(470466),
  StormFrenzyBuff                       = CreateSpell(462725),
  StormsurgeBuff                        = CreateSpell(201846),
  WindfuryTotemBuff                     = CreateSpell(327942),
  WinningStreakBuff                     = CreateSpell(1218616), -- TWW S2 2pc
  -- Debuffs
  LashingFlamesDebuff                   = CreateSpell(334168),
  -- Elemental Spirits Buffs
  CracklingSurgeBuff                    = CreateSpell(224127),
  EarthenWeaponBuff                     = CreateSpell(392375),
  ElectrostaticWagerBuff                = CreateSpell(1223410), -- TWW S2 4pc
  ElectrostaticWagerDmg                 = CreateSpell(1223332), -- TWW S2 4pc
  LegacyoftheFrostWitch                 = CreateSpell(335901),
  IcyEdgeBuff                           = CreateSpell(224126),
  MoltenWeaponBuff                      = CreateSpell(224125),
  -- Tier 29 Buffs
  MaelstromofElementsBuff               = CreateSpell(394677),
  -- Tier 30 Buffs
  VolcanicStrengthBuff                  = CreateSpell(409833),
}
---@class SHCustomTable
Spell.Shaman.Enhancement = MergeTableByKey(Spell.Shaman.Enhancement, Spell.Shaman.Custom)
---@class SHCommonsTable
Spell.Shaman.Enhancement = MergeTableByKey(Spell.Shaman.Enhancement, Spell.Shaman.Commons, true)
---@class FarseerTable
Spell.Shaman.Enhancement = MergeTableByKey(Spell.Shaman.Enhancement, Spell.Shaman.Farseer)
---@class StormbringerTable
Spell.Shaman.Enhancement = MergeTableByKey(Spell.Shaman.Enhancement, Spell.Shaman.Stormbringer)
---@class TotemicTable
Spell.Shaman.Enhancement = MergeTableByKey(Spell.Shaman.Enhancement, Spell.Shaman.Totemic)

---@class ElementalTable
Spell.Shaman.Elemental = {
  -- Abilities
  EarthShock                            = CreateSpell(8042),
  Earthquake                            = CreateMultiSpell(61882, 462620),
  FireElemental                         = CreateSpell(198067),
  -- Talents
  Ascendance                            = CreateSpell(114050),
  EchoChamber                           = CreateSpell(382032),
  EchooftheElementals                   = CreateSpell(462864),
  EchooftheElements                     = CreateSpell(333919),
  EchoesofGreatSundering                = CreateSpell(384087),
  ElectrifiedShocks                     = CreateSpell(382086),
  EruptingLava                          = CreateSpell(468574),
  EyeoftheStorm                         = CreateSpell(381708),
  FirstAscendant                        = CreateSpell(462440),
  FlowofPower                           = CreateSpell(385923),
  FluxMelting                           = CreateSpell(381776),
  FuryoftheStorms                       = CreateSpell(191717),
  FusionofElements                      = CreateSpell(462840),
  Icefury                               = CreateSpell(210714),
  ImprovedFlametongueWeapon             = CreateSpell(382027),
  LightningRod                          = CreateSpell(210689),
  LiquidMagmaTotem                      = CreateSpell(192222),
  MagmaChamber                          = CreateSpell(381932),
  MasteroftheElements                   = CreateSpell(16166),
  MountainsWillFall                     = CreateSpell(381726),
  PoweroftheMaelstrom                   = CreateSpell(191861),
  PrimalElementalist                    = CreateSpell(117013),
  SearingFlames                         = CreateSpell(381782),
  SkybreakersFieryDemise                = CreateSpell(378310),
  SplinteredElements                    = CreateSpell(382042),
  StormElemental                        = CreateSpell(192249),
  Stormkeeper                           = CreateSpell(191634),
  SurgeofPower                          = CreateSpell(262303),
  SwellingMaelstrom                     = CreateSpell(384359),
  ThunderstrikeWard                     = CreateSpell(462757),
  -- Buffs
  AscendanceBuff                        = CreateSpell(1219480),
  EchoesofGreatSunderingBuff            = CreateSpell(384088),
  FluxMeltingBuff                       = CreateSpell(381777),
  FuryofStormsBuff                      = CreateSpell(191716),
  FusionofElementsFire                  = CreateSpell(462843),
  FusionofElementsNature                = CreateSpell(462841),
  IcefuryBuff                           = CreateSpell(210714),
  LavaSurgeBuff                         = CreateSpell(77762),
  MagmaChamberBuff                      = CreateSpell(381933),
  MasteroftheElementsBuff               = CreateSpell(260734),
  PoweroftheMaelstromBuff               = CreateSpell(191877),
  StormFrenzyBuff                       = CreateSpell(462725),
  StormkeeperBuff                       = CreateSpell(191634),
  SurgeofPowerBuff                      = CreateSpell(285514),
  WindGustBuff                          = CreateSpell(263806),
  -- Debuffs
  ElectrifiedShocksDebuff               = CreateSpell(382089),
  -- Tier Bonuses
  MaelstromSurgeBuff                    = Spell(457727), -- TWWS1 4pc
}
---@class SHCustomTable
Spell.Shaman.Elemental = MergeTableByKey(Spell.Shaman.Elemental, Spell.Shaman.Custom)
---@class SHCommonsTable
Spell.Shaman.Elemental = MergeTableByKey(Spell.Shaman.Elemental, Spell.Shaman.Commons, true)
---@class FarseerTable
Spell.Shaman.Elemental = MergeTableByKey(Spell.Shaman.Elemental, Spell.Shaman.Farseer)
---@class StormbringerTable
Spell.Shaman.Elemental = MergeTableByKey(Spell.Shaman.Elemental, Spell.Shaman.Stormbringer)
---@class TotemicTable
Spell.Shaman.Elemental = MergeTableByKey(Spell.Shaman.Elemental, Spell.Shaman.Totemic)

---@class RestorationTable
Spell.Shaman.Restoration = {
    -- Custom
    PurifySpirit = CreateSpell(77130),
    ImprovedPurifySpirit = CreateSpell(383016),
    ManaSpring = CreateSpell(381930),
    Downpour = CreateSpell(462603),
    DownpourBuff = CreateSpell(462488),
    MasterOfTheElements = CreateSpell(462375),
    MasterOfTheElementsBuff = CreateSpell(462377),
    
    ElementalOrbit = CreateSpell(383010),
    HealingSurge = CreateSpell(8004),
    ChainLightning = CreateSpell(188443),
    LavaBurst = CreateSpell(51505),
    EarthElemental = CreateSpell(198103),
    SpiritwalkersGrace = CreateSpell(79206),
    WindShear = CreateSpell(57994),
    CapacitorTotem = CreateSpell(192058),
    FrostShock = CreateSpell(196840),
    LightningLasso = CreateSpell(305483),
    Thunderstorm = CreateSpell(51490),
    NaturesSwiftness = CreateSpell(378081),
    EarthShield = CreateSpell(974),
    LightningBolt = CreateSpell(188196),

    Stormkeeper = CreateSpell(383009),
    HealingWave = CreateSpell(77472),
    Riptide = CreateSpell(61295),
    HealingTideTotem = CreateSpell(108280),
    SpiritLinkTotem = CreateSpell(98008),
    PrimordialWaveResto = CreateSpell(428332),
    UnleashLife = CreateSpell(73685),
    HealingRain = CreateSpell(73920),
    AcidRain = CreateSpell(378443),
    ManaTideTotem = CreateSpell(16191),
    EarthenWallTotem = CreateSpell(198838),
    AncestralProtectionTotem = CreateSpell(207399),
    CloudburstTotem = CreateSpell(157153),
    EarthlivingWeapon = CreateSpell(382021),
    Wellspring = CreateSpell(197995),
    Ascendance = CreateSpell(114052),
    LavaSurge = CreateSpell(77756),
    Undercurrent = CreateSpell(382194),
    Undulation = CreateSpell(200071),
    RecallCloudburstTotem = CreateSpell(201764),
    Tidebringer = CreateSpell(236501),
    HighTide = CreateSpell(157154),
    SurgingTotem = CreateSpell(444995),
    AncestralSwiftness = CreateSpell(443454),

    -- Buffs
    UndercurrentBuff = CreateSpell(383235),
    TidalWaves = CreateSpell(53390),
    UndulationBuff = CreateSpell(216251),
    UnleashLifeBuff = CreateSpell(73685),
    PrimordialWaveBuff = CreateSpell(375986),
    WaterShield = CreateSpell(52127),
    TidebringeBuff = CreateSpell(236502),
    HighTideBuff = CreateSpell(288675),
    CloudburstTotemBuff = CreateSpell(157504),
}
---@class SHCustomTable
Spell.Shaman.Restoration = MergeTableByKey(Spell.Shaman.Restoration, Spell.Shaman.Custom)
---@class SHCommonsTable
Spell.Shaman.Restoration = MergeTableByKey(Spell.Shaman.Restoration, Spell.Shaman.Commons, true)
---@class FarseerTable
Spell.Shaman.Restoration = MergeTableByKey(Spell.Shaman.Restoration, Spell.Shaman.Farseer)
---@class StormbringerTable
Spell.Shaman.Restoration = MergeTableByKey(Spell.Shaman.Restoration, Spell.Shaman.Stormbringer)
---@class TotemicTable
Spell.Shaman.Restoration = MergeTableByKey(Spell.Shaman.Restoration, Spell.Shaman.Totemic)

if not Item.Shaman then
    Item.Shaman = {}
end

---@class SHCustomItemTable
Item.Shaman.Custom = {
    Dreambinder                           = Item(208616, {16}),
    IridaltheEarthsMaster                 = Item(208321, {16}),
}

---@class SHCommonsItemTable
Item.Shaman.Commons = {
  -- TWW Trinkets
  FunhouseLens                          = Item(234217, {13, 14}),
  HouseofCards                          = Item(230027, {13, 14}),
  SpymastersWeb                         = Item(220202, {13, 14}),
  SkardynsGrace                         = Item(133282, {13, 14}),
  TreacherousTransmitter                = Item(221023, {13, 14}),
  -- TWW Items
  BestinSlotsCaster                     = Item(232805, {16}),
  -- DF Trinkets
  AlgetharPuzzleBox                     = Item(193701, {13, 14}),
  BeacontotheBeyond                     = Item(203963, {13, 14}),
  ElementiumPocketAnvil                 = Item(202617, {13, 14}),
  ManicGrieftorch                       = Item(194308, {13, 14}),
}

---@class SHEnhItemTable
Item.Shaman.Enhancement = {
}
---@class SHCustomItemTable
Item.Shaman.Enhancement = MergeTableByKey(Item.Shaman.Enhancement, Item.Shaman.Custom)
---@class SHCommonsItemTable
Item.Shaman.Enhancement = MergeTableByKey(Item.Shaman.Enhancement, Item.Shaman.Commons)

---@class SHElementalItemTable
Item.Shaman.Elemental = {
    -- TWW Trinkets
    HouseofCards                          = Item(230027, {13, 14}),
    SpymastersWeb                         = Item(220202, {13, 14}),
    -- TWW S2 Previous Expansion Items
    NeuralSynapseEnhancer                 = Item(168973, {16}),
}
---@class SHCustomItemTable
Item.Shaman.Elemental = MergeTableByKey(Item.Shaman.Elemental, Item.Shaman.Custom)
---@class SHCommonsItemTable
Item.Shaman.Elemental = MergeTableByKey(Item.Shaman.Elemental, Item.Shaman.Commons)

---@class SHRestoItemTable
Item.Shaman.Restoration = {
}
---@class SHCustomItemTable
Item.Shaman.Restoration = MergeTableByKey(Item.Shaman.Restoration, Item.Shaman.Custom)
---@class SHCommonsItemTable
Item.Shaman.Restoration = MergeTableByKey(Item.Shaman.Restoration, Item.Shaman.Commons)

Spell.Shaman.Enhancement.PrimordialStormAbility.ForceDisplaySpellList = 1218047
Spell.Shaman.Enhancement.TempestAbility.ForceDisplaySpellList = 454009
Spell.Shaman.Enhancement.Windstrike.Range = 30
Spell.Shaman.Enhancement.Thunderstorm.MeleeRange = 8
Spell.Shaman.Enhancement.FlameShock:SetGeneric(SHAMAN_ENHANCEMENT_SPECID, 'Generic1', 470411)
Spell.Shaman.Enhancement.LavaLash:SetGeneric(SHAMAN_ENHANCEMENT_SPECID, 'Generic2')
Spell.Shaman.Enhancement.PrimordialWave:SetGeneric(SHAMAN_ENHANCEMENT_SPECID, 'Generic3')
Spell.Shaman.Enhancement.ChainLightning:SetGeneric(SHAMAN_ENHANCEMENT_SPECID, 'Generic4')
Spell.Shaman.Enhancement.LightningBolt:SetGeneric(SHAMAN_ENHANCEMENT_SPECID, 'Generic5')
Spell.Shaman.Enhancement.Windstrike:SetGeneric(SHAMAN_ENHANCEMENT_SPECID, 'Generic6')
if Spell.Shaman.Enhancement.GreaterPurge:IsAvailable() then
    Spell.Shaman.Enhancement.GreaterPurge:SetGeneric(SHAMAN_ENHANCEMENT_SPECID, 'Generic7')
else
    Spell.Shaman.Enhancement.Purge:SetGeneric(SHAMAN_ENHANCEMENT_SPECID, 'Generic7')
end
Spell.Shaman.Enhancement.Hex:SetGeneric(SHAMAN_ENHANCEMENT_SPECID, 'Generic8')


Spell.Shaman.Elemental.TempestAbility.ForceDisplaySpellList = 454009
Spell.Shaman.Elemental.Thunderstorm.MeleeRange = 8
Spell.Shaman.Elemental.FlameShock:SetGeneric(SHAMAN_ELEMENTAL_SPECID, 'Generic1', 470411)
Spell.Shaman.Elemental.EarthShock:SetGeneric(SHAMAN_ELEMENTAL_SPECID, 'Generic2')
Spell.Shaman.Elemental.LavaBurst:SetGeneric(SHAMAN_ELEMENTAL_SPECID, 'Generic3')
Spell.Shaman.Elemental.ElementalBlast:SetGeneric(SHAMAN_ELEMENTAL_SPECID, 'Generic4')
Spell.Shaman.Elemental.PrimordialWave:SetGeneric(SHAMAN_ELEMENTAL_SPECID, 'Generic5')
Spell.Shaman.Elemental.ChainLightning:SetGeneric(SHAMAN_ELEMENTAL_SPECID, 'Generic6')
Spell.Shaman.Elemental.LightningBolt:SetGeneric(SHAMAN_ELEMENTAL_SPECID, 'Generic7')
if Spell.Shaman.Elemental.GreaterPurge:IsAvailable() then
    Spell.Shaman.Elemental.GreaterPurge:SetGeneric(SHAMAN_ELEMENTAL_SPECID, 'Generic8')
else
    Spell.Shaman.Elemental.Purge:SetGeneric(SHAMAN_ELEMENTAL_SPECID, 'Generic8')
end
Spell.Shaman.Elemental.Hex:SetGeneric(SHAMAN_ELEMENTAL_SPECID, 'Generic9')

Spell.Shaman.Restoration.Thunderstorm.MeleeRange = 8
Spell.Shaman.Restoration.FlameShock:SetGeneric(SHAMAN_RESTORATION_SPECID, 'Generic1', 470411)
if Spell.Shaman.Restoration.GreaterPurge:IsAvailable() then
    Spell.Shaman.Restoration.GreaterPurge:SetGeneric(SHAMAN_RESTORATION_SPECID, 'Generic2')
else
    Spell.Shaman.Restoration.Purge:SetGeneric(SHAMAN_RESTORATION_SPECID, 'Generic2')
end
Spell.Shaman.Restoration.Hex:SetGeneric(SHAMAN_RESTORATION_SPECID, 'Generic3')


if Spell.Shaman.Restoration.ImprovedPurifySpirit:IsAvailable() then
    MainAddon.CONST.DispelList.PurifySpirit.DispelFlag = MainAddon.CONST.DispelFlag.Magic + MainAddon.CONST.DispelFlag.Curse
else
    MainAddon.CONST.DispelList.PurifySpirit.DispelFlag = MainAddon.CONST.DispelFlag.Magic
end

HL:RegisterForEvent(function()
    if Spell.Shaman.Restoration.ImprovedPurifySpirit:IsAvailable() then
        MainAddon.CONST.DispelList.PurifySpirit.DispelFlag = MainAddon.CONST.DispelFlag.Magic + MainAddon.CONST.DispelFlag.Curse
    else
        MainAddon.CONST.DispelList.PurifySpirit.DispelFlag = MainAddon.CONST.DispelFlag.Magic
    end

    if Spell.Shaman.Enhancement.GreaterPurge:IsAvailable() then
        Spell.Shaman.Enhancement.GreaterPurge:SetGeneric(SHAMAN_ENHANCEMENT_SPECID, 'Generic7')
    else
        Spell.Shaman.Enhancement.Purge:SetGeneric(SHAMAN_ENHANCEMENT_SPECID, 'Generic7')
    end

    if Spell.Shaman.Elemental.GreaterPurge:IsAvailable() then
        Spell.Shaman.Elemental.GreaterPurge:SetGeneric(SHAMAN_ELEMENTAL_SPECID, 'Generic8')
    else
        Spell.Shaman.Elemental.Purge:SetGeneric(SHAMAN_ELEMENTAL_SPECID, 'Generic8')
    end

    if Spell.Shaman.Restoration.GreaterPurge:IsAvailable() then
        Spell.Shaman.Restoration.GreaterPurge:SetGeneric(SHAMAN_RESTORATION_SPECID, 'Generic2')
    else
        Spell.Shaman.Restoration.Purge:SetGeneric(SHAMAN_RESTORATION_SPECID, 'Generic2')
    end
end, "PLAYER_TALENT_UPDATE", "TRAIT_CONFIG_UPDATED")

--Shaman Auto Afflictd


local Player = HeroLibEx.Unit.Player
local Shared = MainAddon.Shared


--Lets do a global Shaman spell list
Spell.Shaman.Global = {
    ThunderousPaws = CreateSpell(378076),
    GhostWolf = CreateSpell(2645),
}
if Player:Class() == "SHAMAN" then
    Spell.Shaman.Global.ThunderousPaws:AddToListenedSpells()
    StaticPopupDialogs["ENHCPOPUP"] = {
        text = "??: It seems you are Necrolord.\nYou may have issues with the talent Primordial Wave.\n\nPlease change your Covenant in Oribos.",
        button1 = "OK",
    }

    --Primordial Wave
    if IsSpellKnown(326059) then
        StaticPopup_Show("ENHCPOPUP")
    end
end


Shaman.LastSKCast = 0
Shaman.LastSKBuff = 0
Shaman.LastRollingThunderTick = 0
Shaman.FeralSpiritCount = 0
Shaman.CracklingSurgeStacks = 0
Shaman.IcyEdgeStacks = 0
Shaman.MoltenWeaponStacks = 0
Shaman.TempestMaelstrom = 0
Shaman.SearingTotemActive = false
Shaman.SearingTotemGUID = 0

--- ============================ CONTENT ============================
HL:RegisterForSelfCombatEvent(
  function (...)
    local SourceGUID, _, _, _, _, _, _, _, SpellID = select(4, ...)
    if SourceGUID == Player:GUID() and SpellID == 191634 then
      Shaman.LastSKCast = GetTime()
    end
  end
  , "SPELL_CAST_SUCCESS"
)

HL:RegisterForSelfCombatEvent(
  function (...)
    local DestGUID, _, _, _, SpellID = select(8, ...)
    if DestGUID == Player:GUID() and SpellID == 191634 then
      Shaman.LastSKBuff = GetTime()
      C_Timer.After(0.1, function()
        if Shaman.LastSKBuff ~= Shaman.LastSKCast then
          Shaman.LastRollingThunderTick = Shaman.LastSKBuff
        end
      end)
    end
  end
  , "SPELL_AURA_APPLIED", "SPELL_AURA_APPLIED_DOSE"
)

--- ===== Wolf and Wolf Buffs Tracker =====
HL:RegisterForSelfCombatEvent(
  function (...)
    local SpellID = select(12, ...)
    if SpellID == 262627 or SpellID == 426516 then
      -- Note: 262627 is the spell ID for Feral Spirit
      -- Note: 426516 is the spell ID for the extra wolf from Rolling Thunder or TWW S1 4pc
      Shaman.FeralSpiritCount = Shaman.FeralSpiritCount + 1
      Delay(15, function()
        Shaman.FeralSpiritCount = Shaman.FeralSpiritCount - 1
      end)
    end
    if SpellID == 469332 then
      -- Note: 469332 is the spell ID for wolf summoned by Flowing Spirits
      Shaman.FeralSpiritCount = Shaman.FeralSpiritCount + 1
      Delay(8, function()
        Shaman.FeralSpiritCount = Shaman.FeralSpiritCount - 1
      end)
    end
  end
  , "SPELL_SUMMON"
)

HL:RegisterForCombatEvent(
  function (...)
    local DestGUID, _, _, _, SpellID = select(8, ...)
    if DestGUID == Player:GUID() then
      if SpellID == 224125 then -- Molten Weapon Buff
        Shaman.MoltenWeaponStacks = Shaman.MoltenWeaponStacks + 1
      elseif SpellID == 224126 then -- Icy Edge Buff
        Shaman.IcyEdgeStacks = Shaman.IcyEdgeStacks + 1
      elseif SpellID == 224127 then -- Crackling Surge Buff
        Shaman.CracklingSurgeStacks = Shaman.CracklingSurgeStacks + 1
      end
    end
  end
  , "SPELL_AURA_APPLIED"
)

HL:RegisterForCombatEvent(
  function (...)
    local DestGUID, _, _, _, SpellID = select(8, ...)
    if DestGUID == Player:GUID() then
      if SpellID == 224125 then -- Molten Weapon Buff
        Shaman.MoltenWeaponStacks = Shaman.MoltenWeaponStacks - 1
      elseif SpellID == 224126 then -- Icy Edge Buff
        Shaman.IcyEdgeStacks = Shaman.IcyEdgeStacks - 1
      elseif SpellID == 224127 then -- Crackling Surge Buff
        Shaman.CracklingSurgeStacks = Shaman.CracklingSurgeStacks - 1
      end
    end
  end
  , "SPELL_AURA_REMOVED"
)

--- ===== Fire Elemental Tracker =====
Shaman.FireElemental = {
  GreaterActive = false,
  LesserActive = false
}
Shaman.StormElemental = {
  GreaterActive = false,
  LesserActive = false
}

HL:RegisterForSelfCombatEvent(
  function (...)
    local DestGUID, _, _, _, SpellID = select(8, ...)
    -- Fire Elemental. SpellIDs are without and with Primal Elementalist
    if SpellID == 188592 or SpellID == 118291 then
      Shaman.FireElemental.GreaterActive = true
      C_Timer.After(24, function()
        Shaman.FireElemental.GreaterActive = false
      end)
    elseif SpellID == 462992 or SpellID == 462991 then
      Shaman.FireElemental.LesserActive = true
      C_Timer.After(12, function()
        Shaman.FireElemental.LesserActive = false
      end)
    -- Storm Elemental. SpellIDs are without and with Primal Elementalist
    elseif SpellID == 157299 or SpellID == 157319 then
      Shaman.StormElemental.GreaterActive = true
      C_Timer.After(24, function()
        Shaman.StormElemental.GreaterActive = false
      end)
    elseif SpellID == 462993 or SpellID == 462990 then
      Shaman.StormElemental.LesserActive = true
      C_Timer.After(12, function()
        Shaman.StormElemental.LesserActive = false
      end)
    end
  end
  , "SPELL_SUMMON"
)

--- ===== Tempest Maelstrom Counter =====
HL:RegisterForSelfCombatEvent(
  function (...)
    local SpellID = select(12, ...)
    if SpellID == 344179 then
      Shaman.TempestMaelstrom = Shaman.TempestMaelstrom + 1
      if Shaman.TempestMaelstrom >= 40 then
        Shaman.TempestMaelstrom = Shaman.TempestMaelstrom - 40
      end
    end
  end
  , "SPELL_AURA_APPLIED", "SPELL_AURA_APPLIED_DOSE"
)

-- ===== Searing Totem Tracker =====
HL:RegisterForSelfCombatEvent(
  function (...)
    local DestGUID, DestName, _, _, SpellID = select(8, ...)
    if SpellID == 458101 and DestName == "Searing Totem" then
      Shaman.SearingTotemActive = true
      Shaman.SearingTotemGUID = DestGUID
    end
  end
  , "SPELL_SUMMON"
)

HL:RegisterForCombatEvent(
  function (...)
    local DestGUID = select(8, ...)
    if DestGUID == Shaman.SearingTotemGUID then
      Shaman.SearingTotemActive = false
      Shaman.SearingTotemGUID = 0
    end
  end
  , "UNIT_DIED"
)

--- ============================ CUSTOM ============================

local SpellReactionTable = {
  { spellID = 328756, timeLeft = 2 }, -- Repulsive Visage
}

function Shaman.EvaluateTremor(ThisAction)
  for _, entry in ipairs(SpellReactionTable) do
      local spellID = entry.spellID
      local timeLeft = entry.timeLeft
      local action = ThisAction
      
      local found, _, remainingTime = MainAddon.GetEnemyCastingSpell(spellID)
      
      if found and remainingTime <= timeLeft then
          if MainAddon.Cast(action) then
              return "Casting " .. action:Name() .. " for spellID " .. spellID
          end
      end
  end
end