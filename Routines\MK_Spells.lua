---@class MainAddon
local MainAddon = MainAddon
-- HeroLib
local HL = HeroLibEx
---@class HeroCache
local Cache = HeroCache
---@class Spell
local Spell = HL.Spell
local CreateSpell = MainAddon.CreateSpell
local CreateMultiSpell = MainAddon.CreateMultiSpell
---@class Spell
local MultiSpell = HL.MultiSpell
---@class Item
local Item = HL.Item
local MergeTableByKey = HL.Utils.MergeTableByKey
local C_TimerAfter = _G['C_Timer']['After']
local GetUnitName = _G["GetUnitName"]
local UnitInParty = _G["UnitInParty"]
local UnitInRaid = _G["UnitInRaid"]
local UnitCanAttack = _G['UnitCanAttack']

--SPECID localization
local MONK_BREWMASTER_SPECID = MONK_BREWMASTER_SPECID
local MONK_WINDWALKER_SPECID = MONK_WINDWALKER_SPECID
local MONK_MISTWEAVER_SPECID = MONK_MISTWEAVER_SPECID

MainAddon.Monk = {}
MainAddon.Monk.WhitelistUnitFreedom = {}
---@class Monk
local Monk = MainAddon.Monk


-- Spell
if not Spell.Monk then
    Spell.Monk = {};
end

---@class MKCustomTable
Spell.Monk.Custom = {
    BloodFury = CreateMultiSpell(20572, 33702, 33697),
    PurifiedChiBuff = CreateSpell(325092),
    -- Talents
    PoweroftheThunderKing = CreateSpell(459809),
    VivaciousVivification                 = CreateSpell(392883),
    -- Buff
    VivaciousVivificationBuff             = CreateSpell(392883),
    -- Debuff
    KegSmashDebuff                        = CreateSpell(121253),
    -- PvP
    GrappleWeapon = CreateSpell(233759),
    MightyOxKick = CreateSpell(202370),
    -- PvP Brewmaster
    AvertHarm = CreateSpell(202162),
    NimbleBrew = CreateSpell(213664),
    Admonishment = CreateSpell(207025),
}

---@class MKCommonsTable
Spell.Monk.Commons = {
  -- Racials
  AncestralCall                         = CreateSpell(274738),
  ArcanePulse                           = CreateSpell(260364),
  ArcaneTorrent                         = CreateSpell(25046),
  AzeriteSurge                          = CreateSpell(436344),
  BagofTricks                           = CreateSpell(312411),
  Berserking                            = CreateSpell(26297),
  BloodFury                             = CreateSpell(20572),
  GiftoftheNaaru                        = CreateSpell(59547),
  Haymaker                              = CreateSpell(287712),
  Fireblood                             = CreateSpell(265221),
  LightsJudgment                        = CreateSpell(255647),
  QuakingPalm                           = CreateSpell(107079),
  RocketBarrage                         = CreateSpell(69041),
  Shadowmeld                            = CreateSpell(58984),
  -- Abilities
  CracklingJadeLightning                = CreateSpell(117952),
  ExpelHarm                             = CreateSpell(322101),
  LegSweep                              = CreateSpell(119381),
  Provoke                               = CreateSpell(115546),
  Resuscitate                           = CreateSpell(115178),
  RisingSunKick                         = CreateSpell(107428),
  Roll                                  = CreateSpell(109132),
  TigerPalm                             = CreateSpell(100780),
  TouchofDeath                          = CreateSpell(322109),
  Transcendence                         = CreateSpell(101643),
  TranscendenceTransfer                 = CreateSpell(119996),
  Vivify                                = CreateSpell(116670),
  -- Talents
  BonedustBrew                          = CreateSpell(386276),
  Celerity                              = CreateSpell(115173),
  ChiBurst                              = CreateSpell(123986),
  ChiTorpedo                            = CreateSpell(115008),
  ChiWave                               = CreateSpell(115098),
  DampenHarm                            = CreateSpell(122278),
  Detox                                 = CreateSpell(218164),
  Disable                               = CreateSpell(116095),
  DiffuseMagic                          = CreateSpell(122783),
  EyeoftheTiger                         = CreateSpell(196607),
  FastFeet                              = CreateSpell(388809),
  ImpTouchofDeath                       = CreateSpell(322113),
  InnerStrengthBuff                     = CreateSpell(261769),
  Paralysis                             = CreateSpell(115078),
  RingofPeace                           = CreateSpell(116844),
  RushingJadeWind                       = CreateSpell(116847),
  SpearHandStrike                       = CreateSpell(116705),
  StrengthofSpirit                      = CreateSpell(387276),
  SummonWhiteTigerStatue                = CreateSpell(388686),
  TigerTailSweep                        = CreateSpell(264348),
  TigersLust                            = CreateSpell(116841),
  -- Buffs
  BonedustBrewBuff                      = CreateSpell(386276),
  BonedustBrewDebuff                    = CreateSpell(386276),
  DampenHarmBuff                        = CreateSpell(122278),
  JunkmaestrosBuff                      = CreateSpell(1219661), -- Junkmaestro's Mega Magnet buff
  PressurePointBuff                     = CreateSpell(393053),
  RushingJadeWindBuff                   = CreateSpell(116847),
  DiffuseMagicBuff                      = CreateSpell(122783),

  -- Debuffs
  -- Item Effects
  CalltoDominanceBuff                   = CreateSpell(403380), -- Neltharion trinket buff
  DomineeringArroganceBuff              = CreateSpell(411661), -- Neltharion trinket buff2
  TheEmperorsCapacitorBuff              = CreateSpell(393039),
  -- Misc
  PoolEnergy                            = CreateSpell(999910),
  StopFoF                               = CreateSpell(363653)
}

---@class ConduitoftheCelestialsTable
Spell.Monk.ConduitoftheCelestials = {
    -- Talents
    CelestialConduit                      = CreateSpell(443028),
    UnityWithin                           = CreateSpell(443589),
    -- Buffs
    HeartoftheJadeSerpentBuff             = CreateSpell(456368),
    HeartoftheJadeSerpentCDRBuff          = CreateSpell(443421),
    HeartoftheJadeSerpentCDRCelestialBuff = CreateSpell(443616),
    StrengthoftheBlackOx                  = CreateSpell(443112),
}

---@class MasterofHarmonyTable
Spell.Monk.MasterofHarmony = {
-- Talents
  AspectOfHarmony                       = CreateSpell(450508),
 -- Buffs
  AspectOfHarmonyBuffStage1             = CreateSpell(450521),
  AspectOfHarmonyBuffStage2             = CreateSpell(450526),
  AspectOfHarmonyBuffStage3             = CreateSpell(450531),
  AspectOfHarmonyBuffSpending           = CreateSpell(450711),
  BalancedStratagemBuff                 = CreateSpell(451508),
}
  
---@class ShadoPanTable
Spell.Monk.ShadoPan = {
      -- Talents
  FlurryStrikes                         = CreateSpell(450615),
  WisdomoftheWall                       = CreateSpell(450994),
  -- Buffs
  WisdomoftheWallFlurryBuff             = CreateSpell(452688),
  WisdomoftheWallMasterBuff             = CreateSpell(452685),
  WisdomoftheWallDodgeBuff             = CreateSpell(451242),
  WisdomoftheWallCritBuff             = CreateSpell(452684),
}

---@class WindwalkerTable
Spell.Monk.Windwalker = {
  -- Abilities
  BlackoutKick                          = CreateSpell(100784),
  FlyingSerpentKick                     = CreateSpell(101545),
  FlyingSerpentKickLand                 = CreateSpell(115057),
  SpinningCraneKick                     = CreateSpell(101546),
  -- Talents
  CourageousImpulse                     = CreateSpell(451495),
  CraneVortex                           = CreateSpell(388848),
  EnergyBurst                           = CreateSpell(451498),
  GaleForce                             = CreateSpell(451580),
  JadefireHarmony                       = CreateSpell(391412),
  JadefireStomp                         = CreateSpell(388193),
  FistsofFury                           = CreateSpell(113656),
  GloryoftheDawn                        = CreateSpell(392958),
  HitCombo                              = CreateSpell(196740),
  InnerPeace                            = CreateSpell(397768),
  InvokeXuenTheWhiteTiger               = CreateSpell(123904),
  KnowledgeoftheBrokenTemple            = CreateSpell(451529),
  LastEmperorsCapacitor                 = CreateSpell(392989),
  MemoryoftheMonastery                  = CreateSpell(454969),
  OrderedElements                       = CreateSpell(451463),
  RevolvingWhirl                        = CreateSpell(451524),
  SequencedStrikes                      = CreateSpell(451515),
  ShadowboxingTreads                    = CreateSpell(392982),
  SingularlyFocusedJade                 = CreateSpell(451573),
  SlicingWinds                          = CreateSpell(1217413),
  SlicingWindsDamage                    = CreateSpell(1217411),
  StormEarthAndFire                     = CreateSpell(137639),
  StormEarthAndFireFixate               = CreateSpell(221771),
  StrikeoftheWindlord                   = CreateSpell(392983),
  TeachingsoftheMonastery               = CreateSpell(116645),
  WhirlingDragonPunch                   = CreateSpell(152175),
  XuensBattlegear                       = CreateSpell(392993),
  XuensBond                             = CreateSpell(392986),
  -- Defensive
  FortifyingBrew                        = CreateSpell(243435),
  TouchofKarma                          = CreateSpell(122470),
  -- Buffs
  BlackoutKickBuff                      = CreateSpell(116768),
  ChiEnergyBuff                         = CreateSpell(393057),
  DanceofChijiBuff                      = CreateSpell(325202),
  HiddenMastersForbiddenTouchBuff       = CreateSpell(213114),
  InvokersDelightBuff                   = CreateSpell(388663),
  MemoryoftheMonasteryBuff              = CreateSpell(454970),
  OrderedElementsBuff                   = CreateSpell(451462),
  StormEarthAndFireBuff                 = CreateSpell(137639),
  TeachingsoftheMonasteryBuff           = CreateSpell(202090),
  TheEmperorsCapacitorBuff              = CreateSpell(393039),
  -- Debuffs
  AcclamationDebuff                     = CreateSpell(451433),
  GaleForceDebuff                       = CreateSpell(451582),
  MarkoftheCraneDebuff                  = CreateSpell(228287),
  -- Tier 31 Effects
  BlackoutReinforcementBuff             = CreateSpell(424454),
  -- TWW2 Effects
  CashoutBuff                           = CreateSpell(1216498), -- TWW2 4pc
}
---@class MKCustomTable
Spell.Monk.Windwalker = MergeTableByKey(Spell.Monk.Windwalker, Spell.Monk.Custom)
---@class MKCommonsTable
Spell.Monk.Windwalker = MergeTableByKey(Spell.Monk.Windwalker, Spell.Monk.Commons, true)
---@class ConduitoftheCelestialsTable
Spell.Monk.Windwalker = MergeTableByKey(Spell.Monk.Windwalker, Spell.Monk.ConduitoftheCelestials)
---@class MasterofHarmonyTable
Spell.Monk.Windwalker = MergeTableByKey(Spell.Monk.Windwalker, Spell.Monk.MasterofHarmony)
---@class ShadoPanTable
Spell.Monk.Windwalker = MergeTableByKey(Spell.Monk.Windwalker, Spell.Monk.ShadoPan)


---@class BrewmasterTable
Spell.Monk.Brewmaster = {
    -- Abilities
    BlackoutKick                          = CreateSpell(205523),
    Clash                                 = CreateSpell(324312),
    SpinningCraneKick                     = CreateSpell(322729),
    -- Talents
    BlackoutCombo                         = CreateSpell(196736),
    BlackOxBrew                           = CreateSpell(115399),
    BreathofFire                          = CreateSpell(115181),
    BobandWeave                           = CreateSpell(280515),
    CelestialFlames                       = CreateSpell(325177),
    CharredPassions                       = CreateSpell(386965),
    ExplodingKeg                          = CreateSpell(325153),
    FluidityofMotion                      = CreateSpell(387230),
    HighTolerance                         = CreateSpell(196737),
    ImprovedInvokeNiuzao                  = CreateSpell(322740),
    ImprovedPurifyingBrew                 = CreateSpell(343743),
    InvokeNiuzao                          = CreateSpell(132578),
    KegSmash                              = CreateSpell(121253),
    LightBrewing                          = CreateSpell(325093),
    PresstheAdvantage                     = CreateSpell(418359),
    Shuffle                               = CreateSpell(322120),
    SpecialDelivery                       = CreateSpell(196730),
    SummonBlackOxStatue                   = CreateSpell(115315),
    WeaponsofOrder                        = CreateSpell(387184),
    -- Defensive
    CelestialBrew                         = CreateSpell(322507),
    FortifyingBrew                        = CreateSpell(115203),
    PurifyingBrew                         = CreateSpell(119582),
    -- Buffs
    BlackoutComboBuff                     = CreateSpell(228563),
    CharredPassionsBuff                   = CreateSpell(386963),
    ElusiveBrawlerBuff                    = CreateSpell(195630),
    FortifyingBrewBuff                    = CreateSpell(120954),
    PresstheAdvantageBuff                 = CreateSpell(418361),
    WeaponsofOrderBuff                    = CreateSpell(387184),
    CelestialBrewBuff                     = CreateSpell(322507),
    -- Debuffs
    BreathofFireDotDebuff                 = CreateSpell(123725),
    WeaponsofOrderDebuff                  = CreateSpell(387179),
    -- Stagger Levels
    HeavyStagger                          = CreateSpell(124273),
    ModerateStagger                       = CreateSpell(124274),
    LightStagger                          = CreateSpell(124275),
    -- TWW2 Effects
    OpportunisticStrikeBuff               = CreateSpell(1217999), -- TWW2 4pc
    LuckOfTheDrawBuff                     = CreateSpell(1217990),  -- TWW2 2pc
}
---@class MKCustomTable
Spell.Monk.Brewmaster = MergeTableByKey(Spell.Monk.Brewmaster, Spell.Monk.Custom)
---@class MKCommonsTable
Spell.Monk.Brewmaster = MergeTableByKey(Spell.Monk.Brewmaster, Spell.Monk.Commons, true)
---@class ConduitoftheCelestialsTable
Spell.Monk.Brewmaster = MergeTableByKey(Spell.Monk.Brewmaster, Spell.Monk.ConduitoftheCelestials)
---@class MasterofHarmonyTable
Spell.Monk.Brewmaster = MergeTableByKey(Spell.Monk.Brewmaster, Spell.Monk.MasterofHarmony)
---@class ShadoPanTable
Spell.Monk.Brewmaster = MergeTableByKey(Spell.Monk.Brewmaster, Spell.Monk.ShadoPan)

---@class MistweaverTable
Spell.Monk.Mistweaver = {
    -- Abilities
    BlackoutKick = CreateSpell(100784),
    CracklingJadeLightning = CreateSpell(117952),
    EnvelopingMist = CreateSpell(124682),
    ExpelHarm = CreateSpell(322101),
    InvokeYulonTheJadeSerpent = CreateSpell(322118),
    LifeCocoon = CreateSpell(116849),
    RenewingMist = CreateSpell(115151),
    RenewingMistBuff = CreateSpell(119611),
    Revival = CreateSpell(115310),
    RisingSunKick = CreateSpell(107428),
    SoothingMist = CreateSpell(115175),
    SpinningCraneKick = CreateSpell(101546),
    SpinningCraneKickDebuff = CreateSpell(228287),
    ThunderFocusTea = CreateSpell(116680),
    TigerPalm = CreateSpell(100780),
    TouchOfDeath = CreateSpell(322109),
    MysticTouchDebuff = CreateSpell(113746),
    RapidDiffusion = CreateSpell(388847),

    -- Buffs
    AwakenedFaelineBuff = CreateSpell(389387),
    AncientConcordance = CreateSpell(389391),
    TheRedCraneBuff = CreateSpell(343820),
    VivaciousVivificationBuff = CreateSpell(392883),
    ManaTeaStack = CreateSpell(115867),
    ManaTeaBuff = CreateSpell(197908),
    YulonBuff = CreateSpell(389422),
    ChiHarmony = CreateSpell(423439),
    ZenPulse = CreateSpell(446334),
    InvokeChiJiTheRedCraneBuff = CreateSpell(343820),
    JadeEmpowermentBuff = CreateSpell(467317),

    -- Debuffs

    -- Talents
    Celerity = CreateSpell(115173),
    ChiWave = CreateSpell(115098),
    ChiBurst = CreateSpell(123986),
    EyeOfTheTiger = CreateSpell(196607),
    GoodKarma = CreateSpell(280195),
    InnerStrengthBuff = CreateSpell(261769),
    InvokeChiJiTheRedCrane = CreateSpell(325197),
    LifecyclesEnvelopingMistBuff = CreateSpell(197919),
    LifecyclesVivifyBuff = CreateSpell(197916),
    ManaTea = CreateSpell(115294),
    RefreshingJadeWind = CreateSpell(196725),
    SummonJadeSerpentStatue = CreateSpell(115313),
    VivaciousVivification = CreateSpell(392883),
    SheilunsGift = CreateSpell(399491),
    AncientTeachings = CreateSpell(388023),
    AncientTeachingsBuff = CreateSpell(388026),
    TeachingsOfTheMonastery = CreateSpell(116645),
    TeachingsOfTheMonasteryBuff = CreateSpell(202090),
    RisingMist = CreateSpell(274909),
    SecretInfusion = CreateSpell(388491),
    AwakenedFaeline = CreateSpell(388779),
    Upwelling = CreateSpell(274963),
    EchoingReverberation = CreateSpell(388604),
    AccumulatingMist = CreateSpell(388564),
    AccumulatingMistBuff = CreateSpell(388566),
    ImprovedTouchOfDeath = CreateSpell(322113),
    ImprovedDetox = CreateSpell(388874),
    ShaoShaosLesson = CreateSpell(400089),
    RushingWindKick = CreateSpell(467307),
    JadeEmpowerment = CreateSpell(467316),
    EmperorsFavor = CreateSpell(471761),
    PeacefulMending = CreateSpell(388593),
    PeerIntoPeace = CreateSpell(440008),


    -- Defensive
    DiffuseMagic = CreateSpell(122783), -- Talent
    HealingElixir = CreateSpell(122281), -- Talent

    -- Utility
    ChiTorpedo = CreateSpell(115008), -- Talent
    Detox = CreateMultiSpell(115450),
    Disable = CreateSpell(116095),
    EnergizingElixir = CreateSpell(115288), -- Talent
    LegSweep = CreateSpell(119381), -- Talent
    Paralysis = CreateSpell(115078),
    Provoke = CreateSpell(115546),
    Reawaken = CreateSpell(212051),
    Resuscitate = CreateSpell(115178),
    Roll = CreateSpell(109132),
    SongOfChiJi = CreateSpell(198898), -- Talent
    SpearHandStrike = CreateSpell(116705),
    TigersLust = CreateSpell(116841), -- Talent
    TigerTailSweep = CreateSpell(264348),
    Transcendence = CreateSpell(101643),
    TranscendenceTransfer = CreateSpell(119996),
    Vivify = CreateSpell(116670),

    -- Shadowlands Legendary
    ChiEnergyBuff = CreateSpell(337571),
    InvokersDelight = CreateSpell(338321),
    KeefersSkyreachDebuff = CreateSpell(344021),
    RecentlyRushingTigerPalm = CreateSpell(337341),
    TheEmperorsCapacitor = CreateSpell(337291),

    -- Misc
    PoolEnergy = CreateSpell(999910),

    -- Spec
    FortifyingBrew = CreateSpell(115203),
    RingofPeace = CreateSpell(116844),
    SummonWhiteTigerStatue = CreateSpell(450639),

    FaelineStomp = CreateSpell(388193),
    Restoral = CreateSpell(388615),
}
---@class MKCustomTable
Spell.Monk.Mistweaver = MergeTableByKey(Spell.Monk.Mistweaver, Spell.Monk.Custom)
---@class MKCommonsTable
Spell.Monk.Mistweaver = MergeTableByKey(Spell.Monk.Mistweaver, Spell.Monk.Commons, true)
---@class ConduitoftheCelestialsTable
Spell.Monk.Mistweaver = MergeTableByKey(Spell.Monk.Mistweaver, Spell.Monk.ConduitoftheCelestials)
---@class MasterofHarmonyTable
Spell.Monk.Mistweaver = MergeTableByKey(Spell.Monk.Mistweaver, Spell.Monk.MasterofHarmony)
---@class ShadoPanTable
Spell.Monk.Mistweaver = MergeTableByKey(Spell.Monk.Mistweaver, Spell.Monk.ShadoPan)

-- Items
if not Item.Monk then
    Item.Monk = {};
end

---@class MKCustomItemTable
Item.Monk.Custom = {
    TreemouthFesteringSplinter            = Item(193652, {13, 14}),
    DecorationofFlame                     = Item(194299, {13, 14}),
    WardofFacelessIre                     = Item(203714, {13, 14}),
    EnduringDreadplate                    = Item(202616, {13, 14}),
    GranythsEnduringScale                 = Item(212757, {13, 14}),
    FyrakksTaintedRageheart               = Item(207174, {13, 14}),
    ShadowmoonInsignia                    = Item(150526, {13, 14}),

    IridaltheEarthsMaster                 = Item(208321, {16}),
    Dreambinder                           = Item(208616, {16}),
}

---@class MKCommonsItemTable
Item.Monk.Commons = {
    -- Trinkets
    AlgetharPuzzleBox                     = Item(193701, {13, 14}),
    AshesoftheEmbersoul                   = Item(207167, {13, 14}),
    BeacontotheBeyond                     = Item(203963, {13, 14}),
    DragonfireBombDispenser               = Item(202610, {14, 14}),
    EruptingSpearFragment                 = Item(193769, {13, 14}),
    IrideusFragment                       = Item(193743, {13, 14}),
    ManicGrieftorch                       = Item(194308, {13, 14}),
    MirrorofFracturedTomorrows            = Item(207581, {13, 14}),
    NeltharionsCalltoDominance            = Item(204202, {13, 14}),
    WitherbarksBranch                     = Item(109999, {13, 14}),
      -- TWW Trinkets
    ImperfectAscendancySerum              = Item(225654, {13, 14}),
    MadQueensMandate                      = Item(212454, {13, 14}),
    SignetofthePriory                     = Item(219308, {13, 14}),
    TreacherousTransmitter                = Item(221023, {13, 14}),
    JunkmaestrosMegaMagnet                = Item(230189, {13, 14}),
    -- Other On-Use Items
    Djaruun                               = Item(202569, {16}),
}

---@class WindwalkerItemTable
Item.Monk.Windwalker = {
}
---@class MKCustomItemTable
Item.Monk.Windwalker = MergeTableByKey(Item.Monk.Custom, Item.Monk.Windwalker)
---@class MKCommonsItemTable
Item.Monk.Windwalker = MergeTableByKey(Item.Monk.Commons, Item.Monk.Windwalker)

---@class BrewmasterItemTable
Item.Monk.Brewmaster = {
}
---@class MKCustomItemTable
Item.Monk.Brewmaster = MergeTableByKey(Item.Monk.Custom, Item.Monk.Brewmaster)
---@class MKCommonsItemTable
Item.Monk.Brewmaster = MergeTableByKey(Item.Monk.Commons, Item.Monk.Brewmaster)

---@class MistweaverItemTable
Item.Monk.Mistweaver = {
}
---@class MKCustomItemTable
Item.Monk.Mistweaver = MergeTableByKey(Item.Monk.Custom, Item.Monk.Mistweaver)
---@class MKCommonsItemTable
Item.Monk.Mistweaver = MergeTableByKey(Item.Monk.Commons, Item.Monk.Mistweaver)

-- Generic
Spell.Monk.Brewmaster.Provoke:SetGeneric(MONK_BREWMASTER_SPECID, "Generic1")
Spell.Monk.Brewmaster.Paralysis:SetGeneric(MONK_BREWMASTER_SPECID, "Generic2")
Spell.Monk.Brewmaster.Provoke.offGCD = true
Spell.Monk.Brewmaster.PurifyingBrew.offGCD = true
Spell.Monk.Brewmaster.FortifyingBrew.offGCD = true
Spell.Monk.Brewmaster.DampenHarm.offGCD = true
Spell.Monk.Brewmaster.DiffuseMagic.offGCD = true
Spell.Monk.Brewmaster.BlackOxBrew.offGCD = true
Spell.Monk.Brewmaster.LegSweep.MeleeRange = 10
Spell.Monk.Brewmaster.InvokeNiuzao.MeleeRange = 5
Spell.Monk.Brewmaster.SummonWhiteTigerStatue.MeleeRange = 5
Spell.Monk.Brewmaster.WeaponsofOrder.MeleeRange = 5
Spell.Monk.Brewmaster.BonedustBrew.MeleeRange = 5
Spell.Monk.Brewmaster.ExplodingKeg.MeleeRange = 5
Spell.Monk.Brewmaster.SpinningCraneKick.MeleeRange = 8

Spell.Monk.Mistweaver.CracklingJadeLightning:SetGeneric(MONK_MISTWEAVER_SPECID, "Generic1")
Spell.Monk.Mistweaver.Paralysis:SetGeneric(MONK_MISTWEAVER_SPECID, "Generic2")
Spell.Monk.Mistweaver.DiffuseMagic.offGCD = true
Spell.Monk.Mistweaver.LegSweep.MeleeRange = 10
Spell.Monk.Mistweaver.FaelineStomp.MeleeRange = 20
Spell.Monk.Mistweaver.SummonWhiteTigerStatue.MeleeRange = 5

Spell.Monk.Windwalker.TigerPalm:SetGeneric(MONK_WINDWALKER_SPECID, "Generic1")
Spell.Monk.Windwalker.BlackoutKick:SetGeneric(MONK_WINDWALKER_SPECID, "Generic2")
Spell.Monk.Windwalker.RisingSunKick:SetGeneric(MONK_WINDWALKER_SPECID, "Generic3")
Spell.Monk.Windwalker.FistsofFury:SetGeneric(MONK_WINDWALKER_SPECID, "Generic4")
Spell.Monk.Windwalker.Paralysis:SetGeneric(MONK_WINDWALKER_SPECID, "Generic5")
Spell.Monk.Windwalker.TouchofDeath:SetGeneric(MONK_WINDWALKER_SPECID, "Generic6")
Spell.Monk.Windwalker.StrikeoftheWindlord:SetGeneric(MONK_WINDWALKER_SPECID, "Generic7")
Spell.Monk.Windwalker.DiffuseMagic.offGCD = true
Spell.Monk.Windwalker.SummonWhiteTigerStatue.MeleeRange = 8
Spell.Monk.Windwalker.LegSweep.MeleeRange = 10
Spell.Monk.Windwalker.ChiBurst.MeleeRange = 8
Spell.Monk.Windwalker.SpinningCraneKick.MeleeRange = 8
Spell.Monk.Windwalker.InvokeXuenTheWhiteTiger.MeleeRange = 8

C_TimerAfter(2, function()
    if MainAddon.PlayerSpecID() == 270 then
        -- This will following code will work for all 3 specs, so no need to create another one for Discipline.
        if Spell.Monk.Mistweaver.ImprovedDetox:IsAvailable() then
            MainAddon.CONST.DispelList.Detox.DispelFlag = MainAddon.CONST.DispelFlag.Magic + MainAddon.CONST.DispelFlag.Disease + MainAddon.CONST.DispelFlag.Poison
        else
            MainAddon.CONST.DispelList.Detox.DispelFlag = MainAddon.CONST.DispelFlag.Magic
        end
    else
        MainAddon.CONST.DispelList.Detox.DispelFlag = MainAddon.CONST.DispelFlag.Disease + MainAddon.CONST.DispelFlag.Poison
    end
    
        HL:RegisterForEvent(function()
            if MainAddon.PlayerSpecID() == 270 then
                if Spell.Monk.Mistweaver.ImprovedDetox:IsAvailable() then
                    MainAddon.CONST.DispelList.Detox.DispelFlag = MainAddon.CONST.DispelFlag.Magic + MainAddon.CONST.DispelFlag.Disease + MainAddon.CONST.DispelFlag.Poison
                else
                    MainAddon.CONST.DispelList.Detox.DispelFlag = MainAddon.CONST.DispelFlag.Magic
                end
            else
                MainAddon.CONST.DispelList.Detox.DispelFlag = MainAddon.CONST.DispelFlag.Disease + MainAddon.CONST.DispelFlag.Poison
            end
        end, "PLAYER_TALENT_UPDATE", "TRAIT_CONFIG_UPDATED")
end)


local Player = HeroLibEx.Unit.Player
if Player:Class() == "MONK" then

    -- Blessing of Freedom right-click menu
    Monk.WhitelistUnitFreedom = {}

    ---@param rootDescription Menu
    local function RightClick(_, rootDescription, contextData)
        local unit = contextData.unit
        local unitName = contextData.name

        if Cache.Persistent.Player.Class[2] ~= "MONK" then
            return
        end

        if not unit or UnitCanAttack(Player:ID(), unit) then
            return
        end

        local unitFound = false
        for i, v in pairs(Monk.WhitelistUnitFreedom) do
            if v == unitName then
                unitFound = true
                break
            end
        end

        local SetUnset = (not Monk.WhitelistUnitFreedom or #Monk.WhitelistUnitFreedom == 0) and "Set" or unitFound and "Unset" or "Set"
        local colorCode = unitFound and "|cffff0000" or "|cff00ff00"
        local iconID = 651727

        rootDescription:CreateButton("|T" .. iconID .. ":24|t" .. " " .. colorCode .. SetUnset .. " Tiger's Lust" .. "|r", function()
            if not Monk.WhitelistUnitFreedom or #Monk.WhitelistUnitFreedom == 0 then
                table.insert(Monk.WhitelistUnitFreedom, unitName)
                MainAddon:Print("Tiger's Lust on: " .. unitName .. " ", true, 2)
            else
                if unitFound then
                    for i, v in pairs(Monk.WhitelistUnitFreedom) do
                        if v == unitName then
                            table.remove(Monk.WhitelistUnitFreedom, i)
                            break
                        end
                    end
                    MainAddon:Print("Tiger's Lust on: " .. unitName .. " ", false, 2)
                else
                    table.insert(Monk.WhitelistUnitFreedom, unitName)
                    MainAddon:Print("Tiger's Lust on: " .. unitName .. " ", true, 2)
                end
            end
        end)
    end
    Menu.ModifyMenu("MENU_UNIT_SELF", RightClick);
    Menu.ModifyMenu("MENU_UNIT_PLAYER", RightClick);
    Menu.ModifyMenu("MENU_UNIT_TARGET", RightClick);
    Menu.ModifyMenu("MENU_UNIT_FOCUS", RightClick);
    Menu.ModifyMenu("MENU_UNIT_RAID_PLAYER", RightClick);
    Menu.ModifyMenu("MENU_UNIT_PARTY", RightClick);
end

-- Helper function for Devour Affix dispel using Diffuse Magic
local VoidRift = CreateSpell(440313)
function Monk.ShouldUseDiffuseMagic()
    if not MainAddon.IsCurrentAffix("Xal'atath's Bargain: Devour") then return false end
    if not Player:MythicKeyIsCharged() then return false end
    if not Spell.Monk.Commons.DiffuseMagic:IsLearned() then return false end
    if not Spell.Monk.Commons.DiffuseMagic:IsReady(Player) then return false end
    
    local hasVoidRift = Player:DebuffUp(VoidRift, true)
    if not hasVoidRift then return false end
    
    return true
end