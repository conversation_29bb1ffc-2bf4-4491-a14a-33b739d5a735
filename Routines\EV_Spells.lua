---@class MainAddon
local MainAddon = MainAddon
local HL = HeroLibEx
---@class HeroCache
local Cache = HeroCache;
---@class Spell
local Spell = HL.Spell
local CreateSpell = MainAddon.CreateSpell
local CreateMultiSpell = MainAddon.CreateMultiSpell
---@class Item
local Item = HL.Item
-- LUAs
local MergeTableByKey = HL.Utils.MergeTableByKey
local C_Timer = _G['C_Timer']

MainAddon.Evoker = {}
---@class Evoker
local Evoker = MainAddon.Evoker

-- Spell
if not Spell.Evoker then
    Spell.Evoker = {}
end
---@class EVCustomTable
Spell.Evoker.Custom = {
    Zephyr = CreateSpell(374227),
    TailSwipe = CreateSpell(368970),
    WingBuffet = CreateSpell(357214),
    OppressingRoar = CreateMultiSpell(372048, 406971), -- with and without Overawe
    Landslide = CreateSpell(358385),
    SleepWalk = CreateSpell(360806),
    RenewingBlaze = CreateSpell(374348),

    -- PvP
    TimeStop = CreateSpell(378441),
    SwoopUp = CreateSpell(370388),
    NullifyingShroud = CreateSpell(378464),
    ChronoLoop = CreateSpell(383005)
}

---@class EVCommonsTable
Spell.Evoker.Commons = {
    -- Racials
    TailSwipe                             = CreateSpell(368970),
    WingBuffet                            = CreateSpell(357214),
    -- Abilities
    AzureStrike                           = CreateSpell(362969),
    BlessingoftheBronze                   = CreateSpell(364342),
    DeepBreath                            = CreateSpell(357210),
    Disintegrate                          = CreateSpell(356995),
    EmeraldBlossom                        = CreateSpell(355913),
    FireBreath                            = CreateMultiSpell(357208,382266), -- with and without Font of Magic
    Hover                                 = CreateSpell(358267),
    LivingFlame                           = CreateSpell(361469),
    -- Talents
    AncientFlame                          = CreateSpell(369990),
    BlastFurnace                          = CreateSpell(375510),
    LeapingFlames                         = CreateSpell(369939),
    ObsidianScales                        = CreateSpell(363916),
    ScarletAdaptation                     = CreateSpell(372469),
    SourceofMagic                         = CreateSpell(369459),
    TipTheScales                          = CreateSpell(370553),
    Unravel                               = CreateSpell(368432),
    VerdantEmbrace                        = CreateSpell(360995),
    -- Buffs/Debuffs
    AncientFlameBuff                      = CreateSpell(375583),
    BlessingoftheBronzeBuff               = CreateSpell(381748),
    FireBreathDebuff                      = CreateSpell(357209),
    HoverBuff                             = CreateSpell(358267),
    LeapingFlamesBuff                     = CreateSpell(370901),
    ScarletAdaptationBuff                 = CreateSpell(372470),
    SourceofMagicBuff                     = CreateSpell(369459),
    TipTheScalesBuff                      = CreateSpell(370553),
    -- DF Trinket Effects
    SpoilsofNeltharusCrit                 = CreateSpell(381954),
    SpoilsofNeltharusHaste                = CreateSpell(381955),
    SpoilsofNeltharusMastery              = CreateSpell(381956),
    SpoilsofNeltharusVers                 = CreateSpell(381957),
    -- TWW Trinket Effects
    SpymastersReportBuff                  = CreateSpell(451199),
    SpymastersWebBuff                     = CreateSpell(444959),
    -- Utility
    Quell                                 = CreateSpell(351338),
    -- Other
    Pool                                  = CreateSpell(999910)
}

---@class ChronowardenTable
Spell.Evoker.Chronowarden = {
    -- Abilitiies
    ChronoFlames                          = CreateSpell(431443),
    -- Talents
    ChronoFlame                           = CreateSpell(431442),
    ThreadsofFate                         = CreateSpell(431715),
}

---@class FlameshaperTable
Spell.Evoker.Flameshaper = {
    -- Talents
    Engulf                                = CreateSpell(443328),
    Enkindle                              = CreateSpell(444016),
    FanTheFlames                          = CreateSpell(444318),
    FlameSiphon                           = CreateSpell(444140),
    FulminousRoar                         = CreateSpell(1218447),
    -- Buffs
    EnkindleBuff                          = CreateSpell(445740),
    -- Debuffs
    EnkindleDebuff                        = CreateSpell(444017),
}
  
---@class ScalecommanderTable
Spell.Evoker.Scalecommander = {
    -- Abilities
    DeepBreathManeuverability             = CreateSpell(433874),
    -- Talents
    Bombardments                          = CreateSpell(434300),
    Maneuverability                       = CreateSpell(433871),
    MassDisintegrate                      = CreateSpell(436335),
    MassEruption                          = CreateSpell(438587),
    MeltArmor                             = CreateSpell(441176),
    Wingleader                            = CreateSpell(441206),
    -- Buffs
    MassDisintegrateBuff                  = CreateSpell(436336),
    MassEruptionBuff                      = CreateSpell(438588),
    -- Debuffs
    BombardmentsDebuff                    = CreateSpell(434473),
    MeltArmorDebuff                       = CreateSpell(441172),
}

---@class DevastationTable
Spell.Evoker.Devastation = {
    -- Talents
    Animosity                             = CreateSpell(375797),
    ArcaneIntensity                       = CreateSpell(375618),
    ArcaneVigor                           = CreateSpell(386342),
    AzureCelerity                         = CreateSpell(1219723),
    Burnout                               = CreateSpell(375801),
    Catalyze                              = CreateSpell(386283),
    Causality                             = CreateSpell(375777),
    ChargedBlast                          = CreateSpell(370455),
    Dragonrage                            = CreateSpell(375087),
    EngulfingBlaze                        = CreateSpell(370837),
    EssenceAttunement                     = CreateSpell(375722),
    EternitySurge                         = CreateMultiSpell(359073,382411), -- with and without Font of Magic
    EternitysSpan                         = CreateSpell(375757),
    EventHorizon                          = CreateSpell(411164),
    EverburningFlame                      = CreateSpell(370819),
    EyeofInfinity                         = CreateSpell(369375),
    FeedtheFlames                         = CreateSpell(369846),
    Firestorm                             = CreateSpell(368847),
    FontofMagic                           = CreateSpell(375783),
    ImminentDestruction                   = CreateSpell(370781),
    Iridescence                           = CreateSpell(370867),
    PowerSwell                            = CreateSpell(370839),
    Pyre                                  = CreateSpell(357211),
    RagingInferno                         = CreateSpell(405659),
    RubyEmbers                            = CreateSpell(365937),
    Scintillation                         = CreateSpell(370821),
    ScorchingEmbers                       = CreateSpell(370819),
    ShatteringStar                        = CreateSpell(370452),
    Snapfire                              = CreateSpell(370783),
    Tyranny                               = CreateSpell(376888),
    Volatility                            = CreateSpell(369089),
    -- Buffs
    BlazingShardsBuff                     = CreateSpell(409848),
    BurnoutBuff                           = CreateSpell(375802),
    ChargedBlastBuff                      = CreateSpell(370454),
    EmeraldTranceBuff                     = CreateSpell(424155), -- T31 2pc
    EssenceBurstBuff                      = CreateSpell(359618),
    ImminentDestructionBuff               = CreateSpell(411055),
    IridescenceBlueBuff                   = CreateMultiSpell(386399,399370),
    IridescenceRedBuff                    = CreateSpell(386353),
    JackpotBuff                           = CreateSpell(1217769), -- TWW2 4pc
    LimitlessPotentialBuff                = CreateSpell(394402),
    PowerSwellBuff                        = CreateSpell(376850),
    SnapfireBuff                          = CreateSpell(370818),
    -- Debuffs
    LivingFlameDebuff                     = CreateSpell(361500),
    ShatteringStarDebuff                  = CreateSpell(370452),
}
---@class EVCustomTable
Spell.Evoker.Devastation = MergeTableByKey(Spell.Evoker.Devastation, Spell.Evoker.Custom)
---@class EVCommonsTable
Spell.Evoker.Devastation = MergeTableByKey(Spell.Evoker.Devastation, Spell.Evoker.Commons, true)
---@class ChronowardenTable
Spell.Evoker.Devastation = MergeTableByKey(Spell.Evoker.Devastation, Spell.Evoker.Chronowarden)
---@class FlameshaperTable
Spell.Evoker.Devastation = MergeTableByKey(Spell.Evoker.Devastation, Spell.Evoker.Flameshaper)
---@class ScalecommanderTable
Spell.Evoker.Devastation = MergeTableByKey(Spell.Evoker.Devastation, Spell.Evoker.Scalecommander)

---@class PreservationTable
Spell.Evoker.Preservation = {
    -- Custom
    Engulf = CreateSpell(443328),
    DreamBreathBuff = CreateSpell(355941),
    FontofMagic = CreateSpell(375783),

    -- Racials

    -- Abilities
    BlessingoftheBronze = CreateSpell(364342),
    BlessingoftheBronzeBuff = CreateSpell(381748),
    AzureStrike = CreateSpell(362969),
    DeepBreath = CreateSpell(357210),
    Disintegrate = CreateSpell(356995),
    FireBreath = CreateMultiSpell(357208, 382266), -- with and without Font of Magic
    LivingFlame = CreateSpell(361469),
    Hover = CreateSpell(358267),
    EmeraldBlossom = CreateSpell(355913),
    RenewingBlaze = CreateSpell(374348),
    -- Talents
    BlastFurnace = CreateSpell(375510),
    ObsidianScales = CreateSpell(363916),
    TipTheScales = CreateSpell(370553),
    Landslide = CreateSpell(358385),
    -- Buffs/Debuffs
    FireBreathDebuff = CreateSpell(357209),
    LeapingFlamesBuff = CreateSpell(370901),
    LeapingFlamesTalent = CreateSpell(369939),
    EssenceBurst = CreateSpell(369299),
    -- External Buffs
    PowerInfusionBuff = CreateSpell(10060),
    -- Utility
    Quell = CreateSpell(351338),
    -- Other
    Pool = CreateSpell(999910),

    -- Preservation
    DreamBreath = CreateMultiSpell(355936, 382614),
    SleepWalk = CreateSpell(360806),
    EmeraldCommunion = CreateSpell(370960),
    TimeSpiral = CreateSpell(374968),
    Zephyr = CreateSpell(374227),
    Rescue = CreateSpell(370665),
    EssenceAttunement = CreateSpell(375722),
    Unravel = CreateSpell(368432),
    OppressingRoar = CreateMultiSpell(372048, 406971), -- with and without Overawe
    SourceofMagic = CreateSpell(369459),
    CauterizingFlame = CreateSpell(374251),
    Naturalize = CreateSpell(360823),
    TiptheScales = CreateSpell(370553),
    VerdantEmbrace = CreateSpell(360995),
    Expunge = CreateSpell(365585),
    DreamFlight = CreateSpell(359816),
    Stasis = CreateSpell(370537),
    StasisFull = CreateSpell(370562),
    TemporalAnomaly = CreateSpell(373861),
    Rewind = CreateSpell(363534),
    TimeDilation = CreateSpell(357170),
    Spiritbloom = CreateMultiSpell(367226, 382731),
    Echo = CreateSpell(364343),
    Reversion = CreateSpell(366155),
    CallofYsera = CreateSpell(373834),
    Dreamwalker = CreateSpell(377082),
    CycleofLife = CreateSpell(371832),
    -- Trinket Effects
    SpoilsofNeltharusCrit = CreateSpell(381954),
    SpoilsofNeltharusHaste = CreateSpell(381955),
    SpoilsofNeltharusMastery = CreateSpell(381956),
    SpoilsofNeltharusVers = CreateSpell(381957),

    -- PvP
    TimeStop = CreateSpell(378441),
    SwoopUp = CreateSpell(370388),
    NullifyingShroud = CreateSpell(378464),
    ChronoLoop = CreateSpell(383005),
    -- PvP Preservation
    DreamProjection = CreateSpell(377509)
}
---@class EVCustomTable
Spell.Evoker.Preservation = MergeTableByKey(Spell.Evoker.Preservation, Spell.Evoker.Custom)
---@class EVCommonsTable
Spell.Evoker.Preservation = MergeTableByKey(Spell.Evoker.Preservation, Spell.Evoker.Commons, true)
---@class ChronowardenTable
Spell.Evoker.Preservation = MergeTableByKey(Spell.Evoker.Preservation, Spell.Evoker.Chronowarden)
---@class FlameshaperTable
Spell.Evoker.Preservation = MergeTableByKey(Spell.Evoker.Preservation, Spell.Evoker.Flameshaper)
---@class ScalecommanderTable
Spell.Evoker.Preservation = MergeTableByKey(Spell.Evoker.Preservation, Spell.Evoker.Scalecommander)

---@class AugmentationTable
Spell.Evoker.Augmentation = {
    -- Custom
    MoltenEmbers = CreateSpell(459725),
    HoverBuff = CreateSpell(358267),
    -- Racials
    TailSwipe = CreateSpell(368970),
    WingBuffet = CreateSpell(357214),

    AzureStrike = CreateSpell(362969),
    BlessingoftheBronze = CreateSpell(364342),
    DeepBreath = CreateSpell(357210),
    EmeraldBlossom = CreateSpell(355913),
    FireBreath =    CreateSpell(382266),
    FuryOfTheAspects = CreateSpell(390386),
    Hover = CreateSpell(358267),
    LivingFlame = CreateSpell(361469),
    Return = CreateSpell(361227),
    BlackAttunement = CreateSpell(403264),
    BronzeAttunement = CreateSpell(403265),

    ForgerOfMountains = CreateSpell(375528),
    AncientFlame = CreateSpell(369990),
    WallopingBlow = CreateSpell(387341),
    TwinGuardian = CreateSpell(370888),
    Rescue = CreateSpell(370665),
    ObsidianBulwark = CreateSpell(375406),
    Tailwind = CreateSpell(375556),
    BountifulBloom = CreateSpell(370886),
    AttunedToTheDream = CreateSpell(376930),
    SleepWalk = CreateSpell(360806),
    CauterizingFlame = CreateSpell(374251),
    Enkindled = CreateSpell(375554),
    HeavyWingbeats = CreateSpell(368838), --Multi
    ClobberingSweep = CreateSpell(375443), --Multi
    Overawe = CreateSpell(374346),
    OppressingRoar = CreateMultiSpell(372048, 406971), -- with and without Overawe
    Exuberance = CreateSpell(375542),
    DraconicLegacy = CreateSpell(376166),
    Recall = CreateSpell(371806),
    InnateMagic = CreateSpell(375520),
    ObsidianScales = CreateSpell(363916),
    Landslide = CreateSpell(358385),
    Expunge = CreateSpell(365585),
    ProtractedTalons = CreateSpell(369909),
    Unravel = CreateSpell(368432),
    BlastFurnace = CreateSpell(375510),
    InstinctiveArcana = CreateSpell(376164),
    Quell = CreateSpell(351338),
    NaturalConvergence = CreateSpell(369913),
    ScarletAdaptation = CreateSpell(372469),
    VerdantEmbrace = CreateSpell(360995),
    TerrorOfTheSkies = CreateSpell(371032),
    LeapingFlames = CreateSpell(369939),
    RenewingBlaze = CreateSpell(374348),
    FociOfLife = CreateSpell(375574), --Multi
    FireWithin = CreateSpell(375577), --Multi
    Zephyr = CreateSpell(374227),
    LushGrowth = CreateSpell(375561),
    Panacea = CreateSpell(387761),
    ExtendedFlight = CreateSpell(375517),
    TipTheScales = CreateSpell(370553),
    TimeSpiral = CreateSpell(374968),
    AerialMastery = CreateSpell(365933),
    RegenerativeMagic = CreateSpell(387787),
    SourceofMagic = CreateSpell(369459),
    InherentResistance = CreateSpell(375544),
    PotentMana = CreateSpell(418101),

    BestowWeyrnstone = CreateSpell(408233),
    InfernoSBlessing = CreateSpell(410261),
    EbonMight = CreateSpell(395152),
    ImposingPresence = CreateSpell(371016), --Multi
    InnerRadiance = CreateSpell(386405), --Multi
    Eruption = CreateSpell(395160),
    PowerNexus = CreateSpell(369908),
    UnyieldingDomain = CreateSpell(412733), --Multi
    TectonicLocus = CreateSpell(408002), --Multi
    Upheaval = CreateSpell(408092),
    SeismicSlam = CreateSpell(408543),
    Volcanism = CreateSpell(406904),
    FontofMagic = CreateSpell(408083),
    RicochetingPyroclast = CreateSpell(406659),
    BlisteringScales = CreateSpell(360827),
    ReactiveHide = CreateSpell(409329),
    RegenerativeChitin = CreateSpell(406907), --Multi
    MoltenBlood = CreateSpell(410643), --Multi
    HoardedPower = CreateSpell(375796),
    Overlord = CreateSpell(410260),
    SymbioticBloom = CreateSpell(410685),
    AspectsFavor = CreateSpell(407243),
    DraconicAttunements = CreateSpell(403208),
    EssenceAttunement = CreateSpell(375722),
    EssenceBurst = CreateSpell(396187),
    PupilofAlexstrasza = CreateSpell(407814),
    EchoingStrike = CreateSpell(410784), --Multi
    DefyFate = CreateSpell(404195),
    Anachronism = CreateSpell(407869),
    SpatialParadox = CreateSpell(406732),
    PlotTheFuture = CreateSpell(407866),
    MotesOfPossibility = CreateSpell(409267),
    Accretion = CreateSpell(407876),
    IgnitionRush = CreateSpell(408775),
    MomentumShift = CreateSpell(408004),
    TimeSkip = CreateSpell(404977),
    BreathofEons = CreateMultiSpell(403631, 442204),
    PerilousFate = CreateSpell(410253), --Multi
    ChronoWard = CreateSpell(409676), --Multi
    Prescience = CreateSpell(409311),
    ProlongLife = CreateSpell(410687), --Multi
    DreamofSpring = CreateSpell(414969), --Multi
    Geomancy = CreateSpell(410787),
    FateMirror = CreateSpell(412774),
    Timelessness = CreateSpell(412710),
    TomorrowToday = CreateSpell(412723), --Multi
    InterwovenThreads = CreateSpell(412713), --Multi
    StretchTime = CreateSpell(410352),
    
    --Buff
    PrescienceBuff = CreateSpell(410089),
    BlisteringScalesBuff = CreateSpell(360827),
    BlackAttunementBuff = CreateSpell(403264),
    BronzeAttunementBuff = CreateSpell(403265),
    LeapingFlamesBuff = CreateSpell(370901),
    EbonMightOtherBuff = CreateSpell(395152),
    EbonMightSelfBuff = CreateSpell(395296),
    EssenceBurstBuff = CreateSpell(392268),
    AncientFlameBuff = CreateSpell(375583),
    BlessingoftheBronzeBuff = CreateSpell(381748),

    -- Debuffs
    TemporalWoundDebuff = CreateSpell(409560),
    FireBreathDebuff = CreateSpell(357209),
    SpoilsofNeltharusMastery = CreateSpell(381956),

    Pool = CreateSpell(999910),
}
---@class EVCustomTable
Spell.Evoker.Augmentation = MergeTableByKey(Spell.Evoker.Augmentation, Spell.Evoker.Custom)
---@class EVCommonsTable
Spell.Evoker.Augmentation = MergeTableByKey(Spell.Evoker.Augmentation, Spell.Evoker.Commons, true)
---@class ChronowardenTable
Spell.Evoker.Augmentation = MergeTableByKey(Spell.Evoker.Augmentation, Spell.Evoker.Chronowarden)
---@class FlameshaperTable
Spell.Evoker.Augmentation = MergeTableByKey(Spell.Evoker.Augmentation, Spell.Evoker.Flameshaper)
---@class ScalecommanderTable
Spell.Evoker.Augmentation = MergeTableByKey(Spell.Evoker.Augmentation, Spell.Evoker.Scalecommander)

-- Items
if not Item.Evoker then
    Item.Evoker = {}
end

---@class EVCommonsItemTable
Item.Evoker.Commons = {
    -- TWW Trinkets
    SpymastersWeb                         = Item(220202, {13, 14}),
    -- TWW Items
    BestinSlotsCaster                     = Item(232805, {16}),
    -- TWW S2 Old Items
    NeuralSynapseEnhancer                 = Item(168973, {16}),
}
---@class DevastationItemTable
Item.Evoker.Devastation = {
  -- DF Items
  KharnalexTheFirstLight                = Item(195519, {16}),
  -- TWW Trinkets
  HouseofCards                          = Item(230027, {13, 14}),
  SignetofthePriory                     = Item(219308, {13, 14}),
}
---@class EVCommonsItemTable
Item.Evoker.Devastation = MergeTableByKey(Item.Evoker.Commons, Item.Evoker.Devastation)

---@class PreservationItemTable
Item.Evoker.Preservation = {
    -- Potions
    PotionofSpectralIntellect = Item(171273),
    -- Trinkets
    AshesoftheEmbersoul                   = Item(207167, {13, 14}),
    BalefireBranch                        = Item(159630, {13, 14}),
    BeacontotheBeyond                     = Item(203963, {13, 14}),
    BelorrelostheSuncaller                = Item(207172, {13, 14}),
    DragonfireBombDispenser               = Item(202610, {13, 14}),
    IrideusFragment                       = Item(193743, {13, 14}),
    MirrorofFracturedTomorrows            = Item(207581, {13, 14}),
    NeltharionsCalltoChaos                = Item(204201, {13, 14}),
    NymuesUnravelingSpindle               = Item(208615, {13, 14}),
    SpoilsofNeltharus                     = Item(193773, {13, 14}),
    -- Items
    Dreambinder                           = Item(208616, {16}),
    Iridal                                = Item(208321, {16}),
    KharnalexTheFirstLight                = Item(195519, {16}),
    -- Trinkets (SL)
    ShadowedOrbofTorment                  = Item(186428, {13, 14}),
}
---@class EVCommonsItemTable
Item.Evoker.Preservation = MergeTableByKey(Item.Evoker.Commons, Item.Evoker.Preservation)

---@class AugmentationItemTable
Item.Evoker.Augmentation = {
    -- Trinkets
    AshesoftheEmbersoul                   = Item(207167, {13, 14}),
    BalefireBranch                        = Item(159630, {13, 14}),
    BeacontotheBeyond                     = Item(203963, {13, 14}),
    BelorrelostheSuncaller                = Item(207172, {13, 14}),
    DragonfireBombDispenser               = Item(202610, {13, 14}),
    IrideusFragment                       = Item(193743, {13, 14}),
    MirrorofFracturedTomorrows            = Item(207581, {13, 14}),
    NeltharionsCalltoChaos                = Item(204201, {13, 14}),
    NymuesUnravelingSpindle               = Item(208615, {13, 14}),
    SpoilsofNeltharus                     = Item(193773, {13, 14}),
    -- Items
    Dreambinder                           = Item(208616, {16}),
    Iridal                                = Item(208321, {16}),
    KharnalexTheFirstLight                = Item(195519, {16}),
    -- Trinkets (SL)
    ShadowedOrbofTorment                  = Item(186428, {13, 14}),
}
---@class EVCommonsItemTable
Item.Evoker.Augmentation = MergeTableByKey(Item.Evoker.Commons, Item.Evoker.Augmentation)

Spell.Evoker.Devastation.FireBreath.Range = 25
Spell.Evoker.Devastation.EternitySurge.Range = 25
Spell.Evoker.Devastation.DeepBreath.Range = 25
Spell.Evoker.Devastation.OppressingRoar.MaximumRange = 25
Spell.Evoker.Devastation.OppressingRoar.MinimumRange = 0
Spell.Evoker.Devastation.TailSwipe.MeleeRange = 8
Spell.Evoker.Devastation.WingBuffet.MeleeRange = 15
Spell.Evoker.Devastation.SleepWalk:SetGeneric(EVOKER_DEVASTATION_SPECID, "Generic1")

Spell.Evoker.Preservation.FireBreath.Range = 25
Spell.Evoker.Preservation.OppressingRoar.MaximumRange = 30
Spell.Evoker.Preservation.OppressingRoar.MinimumRange = 0
Spell.Evoker.Preservation.TailSwipe.MeleeRange = 8
Spell.Evoker.Preservation.WingBuffet.MeleeRange = 15
Spell.Evoker.Preservation.SleepWalk:SetGeneric(EVOKER_PRESERVATION_SPECID, "Generic1")

Spell.Evoker.Augmentation.BreathofEons.Range = 25
Spell.Evoker.Augmentation.FireBreath.Range = 25
Spell.Evoker.Augmentation.OppressingRoar.MaximumRange = 30
Spell.Evoker.Augmentation.OppressingRoar.MinimumRange = 0
Spell.Evoker.Augmentation.TailSwipe.MeleeRange = 8
Spell.Evoker.Augmentation.WingBuffet.MeleeRange = 15
Spell.Evoker.Augmentation.SleepWalk:SetGeneric(EVOKER_AUGMENTATION_SPECID, "Generic1")


C_Timer.After(6, function()
    if Cache.Persistent.Player.Class[3] == 13 then
        local SpecID = MainAddon.PlayerSpecID();
        local PlayerClass, PlayerSpec = HL.SpecID_ClassesSpecs[SpecID][1], string.gsub(HL.SpecID_ClassesSpecs[SpecID][2], "%s+", "")
        local Overawe = CreateSpell(374346)
        local OppressingRoar = CreateSpell(372048)
        local OppressingRoarName = OppressingRoar:Name()
        if not Overawe:IsAvailable() and OppressingRoar:IsAvailable() then
            for Spec, Spells in pairs(HL.Spell[PlayerClass]) do
                if Spec == PlayerSpec then
                    for SpellKey, Spell in pairs(Spells) do
                        local stringWithoutSpaces = string.gsub(OppressingRoarName, "%s+", "")
                        if SpellKey == stringWithoutSpaces then
                            MainAddon.CONST.Stuns[SpellKey] = HL.Spell[PlayerClass][PlayerSpec][SpellKey]
                            MainAddon.CONST.HardCC[SpellKey] = HL.Spell[PlayerClass][PlayerSpec][SpellKey]
                        end
                    end
                end
            end
        end
    end
end)

Evoker.FirestormTracker = {}
Evoker.LastFBEmpowerLevel = 0
Evoker.LastFBFullDuration = 0
Evoker.LastESEmpowerLevel = 0

--- ============================ CONTENT ============================
--- ======= NON-COMBATLOG =======
HL:RegisterForEvent(
  function(Event, Arg1, Arg2)
    -- Ensure it's the player
    if Arg1 ~= "player"then
      return
    end

    if Arg2 == "ESSENCE" then
      Cache.Persistent.Player.LastPowerUpdate = GetTime()
    end
  end,
  "UNIT_POWER_UPDATE"
)

HL:RegisterForSelfCombatEvent(
  function(_, _, _, _, _, _, _, DestGUID, _, _, _, SpellID)
    if SpellID == 369374 then
      Evoker.FirestormTracker[DestGUID] = GetTime()
    end
  end,
  "SPELL_DAMAGE"
)

HL:RegisterForCombatEvent(
  function(_, _, _, _, _, _, _, DestGUID)
    if Evoker.FirestormTracker[DestGUID] then
      Evoker.FirestormTracker[DestGUID] = nil
    end
  end,
  "UNIT_DIED",
  "UNIT_DESTROYED"
)

HL:RegisterForSelfCombatEvent(
  function(...)
    local SpellID, _, _, EmpowerLevel = select(12, ...)
    if SpellID == 357208 or SpellID == 382266 then
      -- Fire Breath
      Evoker.LastFBEmpowerLevel = EmpowerLevel
      Evoker.LastFBFullDuration = 24 - ((EmpowerLevel - 1) * 6)
    elseif SpellID == 359073 then
      -- Eternity Surge
      Evoker.LastESEmpowerLevel = EmpowerLevel
    end
  end,
  "SPELL_EMPOWER_END"
)