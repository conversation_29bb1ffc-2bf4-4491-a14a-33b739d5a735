function A_66(...)
    -- HR UPDATE: feat(ProtPal): UPdate to latest APL (15/03/25)
    -- REMEMBER: GetSetting('ashield_logic', {})['as_dps']
    -- Addon
    ---@class MainAddon
    local MainAddon = MainAddon
    local M = MainAddon
    ---@class HealingEngine
    local HealingEngine = MainAddon.HealingEngine
    -- HeroLib
    local HL = HeroLibEx
    ---@class Unit
    local Unit = HL.Unit
    ---@class Unit
    local Player = Unit.Player
    ---@class Unit
    local Target = Unit.Target
    ---@class Unit
    local MouseOver = Unit.MouseOver
    ---@class Unit
    local Boss = Unit.Boss
    ---@class Unit
    local Boss1 = Boss.boss1
    ---@class Spell
    local Spell = HL.Spell
    ---@class Item
    local Item = HL.Item
    -- HeroRotation
    local Cast = M.Cast
    local CastAlly = M.CastAlly
    local AoEON = M.AoEON
    local CastCycleAlly = MainAddon.CastCycleAlly
    local CastTargetIf = M.CastTargetIf
    -- LUAs
    local GetMouseFoci = _G['GetMouseFoci']
    local GetTotemTimeLeft = _G['GetTotemTimeLeft']
    local GetTime = _G['GetTime']
    local mathmax    = math.max
    local mathmin    = math.min

    ---@class Paladin
    local Paladin = M.Paladin

    -- Define S/I for spell and item arrays
    local S = Spell.Paladin.Protection
    local I = Item.Paladin.Protection

    local OnUseExcludes = {
        193652,-- Treemouth's Festering Splinter
        207174,-- Fyrakk's Tainted Rageheart
        212757,-- Granyth's Enduring Scale
        202616,-- Enduring Dreadplate
        203714,-- Ward of Faceless Ire
        194299,-- Decoration of Flame
        150526,-- Shadowmoon Insignia
        193634,-- Burgeoning Seed
        212450,-- Swarmlord's Authority
    }

    -- Toggle Setting
    MainAddon.Toggle.Special["HoldingHP"] = {
        Icon = MainAddon.GetTexture(S.FlashofLight),
        Name = "Holding Holy Power",
        Description = "Holding Holy Power.",
        Spec = 66
    }

    MainAddon.Toggle.Special["BuildingSanctification"] = {
        Icon = MainAddon.GetTexture(S.Consecration),
        Name = "Building Sanctification buff",
        Description = "Building Sanctification buff for empowering Consecration.",
        Spec = 66
    }

    local GetSetting = MainAddon.Config.GetClassSetting
    local Config_Key = MainAddon.GetClassVariableName()
    local Config_Color = 'F48CBA'
    local Config_Table = {
        key = Config_Key,
        title = 'Paladin - Protection',
        subtitle = '?? ' .. MainAddon.Version,
        width = 600,
        height = 700,
        profiles = true,
        config = {
            { type = 'header', text = '', size = 24, align = 'Center', color = Config_Color },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
            { type = 'header', text = 'DPS', color = Config_Color },
            { type = 'dropdown',
              text = " Avenger's Shield", key = 'ashield_logic',
              icon = S.AvengersShield:ID(),
              multiselect = true,
              list = {
                  { text = 'Pull', key = 'as_pull' },
                  { text = 'DPS', key = 'as_dps' },
                  { text = 'Interrupt', key = 'as_kick' },
              },
              default = {
                'as_pull',
                'as_dps',
                'as_kick',
              },
            },
            { type = 'checkbox', text = ' Consecration when moving', icon = S.Consecration:ID(), default = false, key = 'ConsecrationMoving' },
            { type = 'checkbox', text = ' Eye of Tyr when moving', icon = S.EyeofTyr:ID(), default = false, key = 'EoTMoving' },
            { type = 'spacer' },
            { type = 'header', text = 'Defensives', color = Config_Color },
            { type = 'checkspin', text = ' Divine Shield', icon = S.DivineShield:ID(), key = 'DivineShield', min = 1, max = 100, default_spin = 40, default_check = true },
            { type = 'checkspin', text = ' Guardian of Ancient Kings', icon = S.GuardianofAncientKings:ID(), key = 'GuardianOfAncientKings', min = 1, max = 100, default_spin = 30, default_check = true },
            { type = 'checkspin', text = ' Ardent Defender', icon = S.ArdentDefender:ID(), key = 'ArdentDefender', min = 1, max = 100, default_spin = 35, default_check = true },
            { type = 'checkspin', text = ' Eye of Tyr', icon = S.EyeofTyr:ID(), key = 'EoT', min = 1, max = 100, default_spin = 75, default_check = true },
            { type = 'spinner', text = ' Shield Of The Righteous - Refresh buff below threshold (sec)', icon = S.ShieldoftheRighteous:ID(), key = 'sotr_refresh', min = 0.1, max = 5, default = 2.5 },
            { type = 'spacer' },
            { type = 'checkspin', text = ' Word of Glory (Self Emergency)', icon = S.WordofGlory:ID(), key = 'WordofGlory_self_emergency', min = 1, max = 100, default_spin = 30, default_check = true },
            { type = 'checkspin', text = ' Word of Glory (Self)', icon = S.WordofGlory:ID(), key = 'WordofGlory_self', min = 1, max = 100, default_spin = 55, default_check = true },
            { type = 'checkspin', text = ' Word of Glory (Party/Raid)', icon = S.WordofGlory:ID(), key = 'WordofGlory_party', min = 1, max = 100, default_spin = 45, default_check = true },
            { type = 'checkspin', text = ' Word of Glory (Healer OOR/Dead)', icon = S.WordofGlory:ID(), key = 'WordofGlory_healer_oor_dead', min = 1, max = 100, default_spin = 70, default_check = true },
            { type = 'dropdown',
              text = ' Word of Glory - Targets', key = 'wog_targets',
              icon = S.WordofGlory:ID(),
              multiselect = true,
              list = {
                  { text = 'Healer', key = 'healer' },
                  { text = 'Tank', key = 'tank' },
                  { text = 'DPS', key = 'dps' },
              },
              default = {
                  "healer",
                  "dps",
              },
            },
            { type = 'spacer' },
            { type = 'spinner', text = ' Free Word of Glory', icon = S.WordofGlory:ID(), key = 'WordofGlory_free', min = 1, max = 100, default = 80 },
            { type = 'dropdown',
              text = ' Free Word of Glory - Targets', key = 'freewog_targets',
              icon = S.WordofGlory:ID(),
              multiselect = true,
              list = {
                  { text = 'Healer', key = 'healer' },
                  { text = 'Tank', key = 'tank' },
                  { text = 'DPS', key = 'dps' },
              },
              default = {
                  "healer",
                  "tank"
              },
            },
            { type = 'spacer' },
            { type = 'checkspin', text = ' Blessing of Sacrifice', icon = S.BlessingOfSacrifice:ID(), key = 'sacrifice_party', min = 1, max = 100, default_spin = 35, default_check = true },
            { type = 'checkspin', text = ' Blessing of Protection - Damage Taken (% of Unit health)', icon = S.BlessingOfProtection:ID(), key = 'protection_party', min = 1, max = 100, default_spin = 20, default_check = true },
            { type = 'spacer' },
            { type = 'checkspin', text = ' Lay on Hands (Self)', icon = S.LayonHands:ID(), key = 'layonhands_self', min = 1, max = 100, default_spin = 20, default_check = true },
            { type = 'spinner', text = ' Lay on Hands (Self) - Delay', icon = S.LayonHands:ID(), key = 'layonhands_self_delay', min = 0, max = 3, default = 0.5},
            { type = 'checkspin', text = ' Lay on Hands (Party/Raid)', icon = S.LayonHands:ID(), key = 'layonhands_party', min = 1, max = 100, default_spin = 15, default_check = true },
            { type = 'spinner', text = ' Lay on Hands (Party/Raid) - Delay', icon = S.LayonHands:ID(), key = 'layonhands_party_delay', min = 0, max = 3, default = 0.5},
            { type = 'dropdown',
              text = ' Lay on Hands - Targets', key = 'layonhands_targets',
              icon = 633,
              multiselect = true,
              list = {
                  { text = 'Healer', key = 'healer' },
                  { text = 'Tank', key = 'tank' },
                  { text = 'DPS', key = 'dps' },
              },
              default = {
                  "healer"
              },
            },
            { type = 'spacer' },
            { type = 'checkspin', text = ' Flash of Light (Out of Combat)', icon = S.FlashofLight:ID(), key = 'FlashOfLight', min = 1, max = 100, default_spin = 70, default_check = true },
            { type = 'spacer' },
            { type = 'header', text = 'Utilities', color = Config_Color },
            { type = 'dropdown',
                text = 'Aura', key = 'aura',
                list = {
                    { text = 'Devotion', key = 1 },
                    { text = 'Concentration', key = 3 },
                },
                default = 1
            },
            { type = 'dropdown',
                text = ' Intercession', key = 'autorebirth',
                multiselect = true,
                icon = S.Intercession:ID(),
                list = {
                    { text = 'Target', key = 'autorebirth_target' },
                    { text = 'MouseOver', key = 'autorebirth_mouseover' },
                },
                default = {
                    'autorebirth_mouseover'
                },
            },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
        }
    }
    Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
    Config_Table.config = MainAddon.BuildTankingTrinketUI(Config_Table.config, Config_Color)
    Config_Table.config = MainAddon.BuildCombatPotionUI(Config_Table.config, "Protection", Config_Color)
    MainAddon.SetConfig(66, Config_Table)


    --- ===== Start Custom =====
    local IsInCombat
    local DivineShieldIsUp
    local ArdentDefenderIsUp
    local GuardianOfAncientKingsIsUp
    local Tanks, Healers, Members, Damagers, Melees
    local FreeHammerOfLight = false
    local FreeHammerOfLightUntil = 0

    local function UpdateAvengersShield()
        if GetSetting('ashield_logic', {'as_kick'})['as_kick'] and not MainAddon.CONST.Interrupts.AvengersShield then
            MainAddon.CONST.Interrupts.AvengersShield = S.AvengersShield
        end
        if not GetSetting('ashield_logic', {'as_kick'})['as_kick'] then
            MainAddon.CONST.Interrupts.AvengersShield = nil
        end
    end

    local old_OnValueChanged = MainAddon.Interface.usedGUIs.CORE_PaladinProtection_CONFIG.elements.ashield_logic.eventListeners.OnValueChanged
    MainAddon.Interface.usedGUIs.CORE_PaladinProtection_CONFIG.elements.ashield_logic.eventListeners.OnValueChanged = function(...)
        old_OnValueChanged(...)
        UpdateAvengersShield()
    end
    
    local function BuildingSanctificationBuff()
        if Player:IsInDungeonArea() then
            if Player:InstanceID() == 643 then
                if Boss1:NPCID() == 40825 and not Boss1:BuffUp(429037, true) then
                    return true
                end
            end
        end
        return false
    end

    ---@param TargetedUnit Unit
    local function EvaluateWoG(TargetedUnit)
        return not Player:IsUnit(TargetedUnit) and TargetedUnit:HealthPercentage() <= GetSetting('WordofGlory_party_spin', 30)
    end
    ---@param TargetedUnit Unit
    local function EvaluateWoGFree(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= GetSetting('WordofGlory_free', 30)
    end
    ---@param TargetedUnit Unit
    local function EvaluateWoGHealerDeadOrOOR(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= GetSetting('WordofGlory_healer_oor_dead_spin', 30)
    end
    ---@param TargetedUnit Unit
    local function EvaluateLoH(TargetedUnit)
        return TargetedUnit:DebuffDown(S.ForberanceDebuff, true) and TargetedUnit:RealHealthPercentage() <= GetSetting('layonhands_party_spin', 30)
    end
    ---@param TargetedUnit Unit
    local function EvaluateBoS(TargetedUnit)
        return not Player:IsUnit(TargetedUnit) and TargetedUnit:RealHealthPercentage() <= GetSetting('sacrifice_party_spin', 30)
    end
    ---@param TargetedUnit Unit
    local function EvaluateBoP(TargetedUnit)
        local BoP_DMGTaken = GetSetting('protection_party_spin', 30) /100
        return M.incdmgswing(TargetedUnit) >= (TargetedUnit:MaxHealth() * BoP_DMGTaken) and not TargetedUnit:IsUnit(Player) and not TargetedUnit:HasPvPImmunity() and not TargetedUnit:IsATank()
    end

    --RP ICD
    local RP_ICD = 1 -- Internal cooldown (in seconds) of Righteous Protector
    local RP_LastUse = 0
    HL:RegisterForSelfCombatEvent(
            function(_, _, _, _, _, _, _, _, _, _, _, SpellID)
                if SpellID == S.ShieldoftheRighteous:ID() or SpellID == S.WordofGlory:ID() then
                    RP_LastUse = GetTime()
                end
            end,
            "SPELL_CAST_SUCCESS"
    )

    local function RP_ICD_Ready()
        return GetTime() - RP_LastUse >= RP_ICD
    end

    local function PartyHealing()
        --Argument is true cause we only want party members.
        Tanks, Healers, Members, Damagers, Melees = HealingEngine:Fetch()
        if IsInCombat then
            if S.LayonHands:IsReady(Player) and GetSetting('layonhands_party_check', false) then
                local LoH_Targets = GetSetting('layonhands_targets', {})
                if HL.Utils.tableCount(LoH_Targets) > 0 then
                    local LoH_Tanks = LoH_Targets['tank']
                    local LoH_Healers = LoH_Targets['healer']
                    local LoH_DPS = LoH_Targets['dps']
                    local delay = GetSetting('layonhands_party_delay', 0.5)

                    if LoH_Tanks and Tanks then
                        if CastCycleAlly(S.LayonHands, Tanks, EvaluateLoH, nil, nil, nil, delay) then
                            return "Lay On Hands LoH_Tanks"
                        end
                    end

                    if LoH_Healers and Healers then
                        if CastCycleAlly(S.LayonHands, Healers, EvaluateLoH, nil, nil, nil, delay) then
                            return "Lay On Hands LoH_Healers"
                        end
                    end

                    if LoH_DPS and Damagers then
                        if CastCycleAlly(S.LayonHands, Damagers, EvaluateLoH, nil, nil, nil, delay) then
                            return "Lay On Hands LoH_DPS"
                        end
                    end
                end
            end

            if S.BlessingOfSacrifice:IsReady(Player) and GetSetting('sacrifice_party_check', false) then
                if CastCycleAlly(S.BlessingOfSacrifice, Members, EvaluateBoS) then
                    return "Blessing Of Sacrifice"
                end
            end

            if S.BlessingOfProtection:IsReady(Player) and GetSetting('protection_party_check', false) then
                if CastCycleAlly(S.BlessingOfProtection, Members, EvaluateBoP) then
                    return "Blessing Of Protection"
                end
            end
        end

        if S.WordofGlory:IsReady(Player) then
            if GetSetting('WordofGlory_party_check', false) then
                local WoG_Targets = GetSetting('wog_targets', {})
                if HL.Utils.tableCount(WoG_Targets) > 0 then
                    local WoG_Tanks = WoG_Targets['tank']
                    local WoG_Healers = WoG_Targets['healer']
                    local WoG_DPS = WoG_Targets['dps']

                    if WoG_Tanks and Tanks then
                        if CastCycleAlly(S.WordofGlory, Tanks, EvaluateWoG) then
                            return "WordOfGlory WoG_Tanks"
                        end
                    end

                    if WoG_Healers and Healers then
                        if CastCycleAlly(S.WordofGlory, Healers, EvaluateWoG) then
                            return "WordOfGlory WoG_Healers"
                        end
                    end

                    if WoG_DPS and Damagers then
                        if CastCycleAlly(S.WordofGlory, Damagers, EvaluateWoG) then
                            return "WordOfGlory WoG_DPS"
                        end
                    end
                end
            end

            if Player:BuffUp(S.ShiningLightFreeBuff) then
                local FreeWoG_Targets = GetSetting('freewog_targets', {})
                if HL.Utils.tableCount(FreeWoG_Targets) > 0 then
                    local FreeWoG_Tanks = FreeWoG_Targets['tank']
                    local FreeWoG_Healers = FreeWoG_Targets['healer']
                    local FreeWoG_DPS = FreeWoG_Targets['dps']

                    if FreeWoG_Tanks and Tanks then
                        if CastCycleAlly(S.WordofGlory, Tanks, EvaluateWoGFree) then
                            return "WordOfGlory Free WoG_Tanks"
                        end
                    end

                    if FreeWoG_Healers and Healers then
                        if CastCycleAlly(S.WordofGlory, Healers, EvaluateWoGFree) then
                            return "WordOfGlory Free WoG_Healers"
                        end
                    end

                    if FreeWoG_DPS and Damagers then
                        if CastCycleAlly(S.WordofGlory, Damagers, EvaluateWoGFree) then
                            return "WordOfGlory Free WoG_DPS"
                        end
                    end
                end
            end

            if GetSetting('WordofGlory_healer_oor_dead_check', false) and Player:IsInDungeonArea() then
                if #Healers == 0 then
                    if CastCycleAlly(S.WordofGlory, Members, EvaluateWoGHealerDeadOrOOR) then
                        return "Word of Glory - Healer Dead or OOR"
                    end
                end
            end
        end
    end

    local function Utilities()
        if GetSetting('aura', 1) == 1 then
            if S.DevotionAura:IsReady(Player) and Player:BuffDown(S.DevotionAura, true) and not Player:Mounted() then
                if Cast(S.DevotionAura) then
                    return "devotion_aura";
                end
            end
        end
        
        if GetSetting('aura', 1) == 3 then
            if S.ConcentrationAura:IsReady(Player) and Player:BuffDown(S.ConcentrationAura, true) and not Player:Mounted() then
                if Cast(S.ConcentrationAura) then
                    return "concentration_aura";
                end
            end
        end

        if IsInCombat then
            local autorebith = GetSetting('autorebirth', {})
            if autorebith['autorebirth_mouseover'] and S.Intercession:IsReady(MouseOver) then
                local GetMouseFociCache = GetMouseFoci()
                ---@class Frame
                local MouseFocus = GetMouseFociCache[1]

                local FrameName = MouseFocus and not MouseFocus:IsForbidden() and MouseFocus:GetName() or "None"
                if FrameName ~= "WorldFrame" and FrameName ~= "None" then
                    if MouseOver:EvaluateRebirth() then
                        if Cast(S.Intercession) then 
                            MainAddon.UI:ShowToast("Intercession", MouseOver:Name(), MainAddon.GetTexture(S.Intercession))
                            return "Intercession MouseOver" 
                        end
                    end
                end
            end
            if autorebith['autorebirth_target'] and S.Intercession:IsReady() then
                if Target:EvaluateRebirth() then
                    if Cast(S.Intercession) then 
                        MainAddon.UI:ShowToast("Intercession", Target:Name(), MainAddon.GetTexture(S.Intercession))
                        return "Intercession Target" 
                    end
                end
            end
		else
			if Target:EvaluateRebirth() then
				if S.Redemption:IsReady(Player) then
					if Cast(S.Redemption) then
						return 'Resurrection';
					end
				end
			end
        end

        if S.RiteofSanctification:IsReady(Player) then
            if Cast(S.RiteofSanctification) then return "Rite of Sanctification"; end
        end

        if S.RiteofAdjuration:IsReady(Player) then
            if Cast(S.RiteofAdjuration) then return "Rite of Adjuration"; end
        end
    end
    --- ===== End Custom =====

    --- ===== Rotation Variables =====
    local ActiveMitigationNeeded
    local IsTanking
    local Enemies8y, Enemies30y
    local EnemiesCount8y, EnemiesCount30y
    local VarSanctificationMaxStack = 5
    local BossFightRemains = 11111
    local FightRemains = 11111

    --- ===== Helper Functions =====
    HL:RegisterForEvent(function()
        BossFightRemains = 11111
        FightRemains = 11111
    end, "PLAYER_REGEN_ENABLED")
    
    -- YUNO: Added in herorotations but not called in dps part of the rotation
    -- local function HammerfallICD()
    --     if not S.Hammerfall:IsAvailable() then return 0 end
    --     local LastCast = mathmin(S.ShieldoftheRighteous:TimeSinceLastCast(), S.WordofGlory:TimeSinceLastCast())
    --     return mathmax(0, 1 - LastCast)
    -- end

    local function HPGTo2Dawn()
        if not S.OfDuskandDawn:IsAvailable() then return -1 end
        return 6 - Paladin.HPGCount - (Player:BuffStack(S.BlessingofDawnBuff) * 3)
    end
  
    local function MissingAura()
        return (Player:BuffDown(S.DevotionAura) and Player:BuffDown(S.ConcentrationAura) and Player:BuffDown(S.CrusaderAura))
    end
  
    --- ===== CastTargetIf Filter Functions =====
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterJudgment(TargetUnit)
        return TargetUnit:DebuffRemains(S.JudgmentDebuff)
    end

    local function Precombat()
        -- flask
        -- food
        -- augmentation
        -- snapshot_stats
        -- devotion_aura
        if S.DevotionAura:IsReady() and (MissingAura()) then
          if Cast(S.DevotionAura) then return "devotion_aura precombat 6"; end
        end
        -- lights_judgment
        if S.LightsJudgment:IsReady() then
          if Cast(S.LightsJudgment) then return "lights_judgment precombat 8"; end
        end
        -- arcane_torrent
        if S.ArcaneTorrent:IsReady() then
          if Cast(S.ArcaneTorrent) then return "arcane_torrent precombat 10"; end
        end
        -- consecration
        if S.Consecration:IsReady() and Target:IsInMeleeRange(8) then
          if Cast(S.Consecration) then return "consecration precombat 12"; end
        end
        -- variable,name=trinket_sync_slot,value=1,if=trinket.1.has_cooldown&trinket.1.has_stat.any_dps&(!trinket.2.has_stat.any_dps|trinket.1.cooldown.duration>=trinket.2.cooldown.duration)|!trinket.2.has_cooldown
        -- variable,name=trinket_sync_slot,value=2,if=trinket.2.has_cooldown&trinket.2.has_stat.any_dps&(!trinket.1.has_stat.any_dps|trinket.2.cooldown.duration>trinket.1.cooldown.duration)|!trinket.1.has_cooldown
        -- Note: Unable to handle the has_stat trinket conditionals.
        -- Manually added: avengers_shield
        if S.AvengersShield:IsReady() and GetSetting('ashield_logic', {})['as_pull'] then
          if Cast(S.AvengersShield) then return "avengers_shield precombat 10"; end
        end
        -- Manually added: judgment
        if S.Judgment:IsReady() then
          if Cast(S.Judgment) then return "judgment precombat 12"; end
        end
    end

    local function Defensives()
        -- EyeofTyr on Pull
        -- CUSTOM: (EnemiesCount8y > 1 or Target:IsBoss() and Target:IsInRange(8))
        if S.EyeofTyr:IsReady() and Target:IsInRange(8) and IsTanking and HL:CombatTime() <= 3 and (EnemiesCount8y > 1 and not GetSetting('EoT_check', false) or Target:IsBoss() and MainAddon.TargetIsValid()) then
            if Cast(S.EyeofTyr) then
                return "EyeofTyr start pull";
            end
        end

        -- Shield
        if S.ShieldoftheRighteous:IsReady(Player) and (not S.RighteousProtector:IsAvailable() or RP_ICD_Ready()) and (Player:BuffRemains(S.ShieldoftheRighteousBuff) <= GetSetting('sotr_refresh', 2.5)) and Player:AffectingCombat() then
            if Cast(S.ShieldoftheRighteous) then
                return "Shield of the Righteous maintain buff";
            end
        end

        if IsInCombat then
            if GetSetting('EoT_check', false) and S.EyeofTyr:IsReady(Player) and Target:IsInRange(8) and IsTanking and Player:HealthPercentage() <= GetSetting('EoT_spin', 30) then
                if Cast(S.EyeofTyr) then
                    return "EyeofTyr defensives";
                end
            end

            if S.GiftoftheNaaru:IsReady(Player) and Player:HealthPercentage() < 30 then
                if Cast(S.GiftoftheNaaru, true) then
                    return 'Gift oft he Naaru player';
                end
            end

            if Player:DebuffDown(S.ForberanceDebuff, true) then
                if GetSetting('layonhands_self_check', false) and S.LayonHands:IsReady(Player) and Player:RealHealthPercentage() <= GetSetting('layonhands_self_spin', 30) then
                    local delay = GetSetting('layonhands_self_delay', 0.5)
                    if CastAlly(S.LayonHands, Player, nil, nil, nil, nil, nil, nil, delay) then
                        return 'lay on hands defensive player';
                    end
                end

                if GetSetting('DivineShield_check', false) and S.DivineShield:IsReady(Player) and Player:RealHealthPercentage() <= GetSetting('DivineShield_spin', 30) and (not Player:IsInPartyOrRaid() or (Player:IsInPartyOrRaid() and S.FinalStand:IsAvailable())) then
                    if Cast(S.DivineShield) then
                        return "DivineShield defensives";
                    end
                end
            end

            if GetSetting('GuardianOfAncientKings_check', false) and S.GuardianofAncientKings:IsReady(Player) and (Player:RealHealthPercentage() <= GetSetting('GuardianOfAncientKings_spin', 30)) then
                if Cast(S.GuardianofAncientKings) then
                    return "guardian_of_ancient_kings defensive";
                end
            end

            if GetSetting('ArdentDefender_check', false) and S.ArdentDefender:IsReady(Player) and ((Player:RealHealthPercentage() <= GetSetting('ArdentDefender_spin', 30) or Player:NeedsDefensivePvE())) then
                if Cast(S.ArdentDefender) then
                    return "ardent_defender defensive";
                end
            end
        end

        local Should = Player:ShouldUseDefensive(3)
        -- if not Should then
        --     Should = Player:ShouldUseDynamicDefensive(3)
        -- end
    
        if Should then
            if S.WordofGlory:IsReady(Player) and S.FaithintheLight:IsAvailable() and Player:BuffDown(S.FaithintheLightBuff) then
                if CastAlly(S.WordofGlory, Player) then
                    MainAddon.UI:ShowToast("Faith in the Light", "Pre-cast Word of Glory due to danger !", MainAddon.GetTexture(S.WordofGlory))
                    return "word_of_glory Tank Buster";
                end
            end
        end

        if GetSetting('WordofGlory_self_check', false) and S.WordofGlory:IsReady(Player) and (Player:HealthPercentage() <= GetSetting('WordofGlory_self_spin', 30) and not Player:HealingAbsorbed()) then
            if CastAlly(S.WordofGlory, Player) then
                return "word_of_glory defensive";
            end
        end

        --Consecration is actually puggers and should be maintained
        if Player:AffectingCombat() then
            if S.Consecration:IsReady() and (Player:BuffDown(S.ConsecrationBuff) or GetTotemTimeLeft(1) <= Player:GCD() * 2) then
                if Cast(S.Consecration) then
                    return "consecration standard 9"
                end
            end
        end

        if S.ShieldoftheRighteous:IsReady() and Player:BuffRefreshable(S.ShieldoftheRighteousBuff) and ActiveMitigationNeeded then
            if Cast(S.ShieldoftheRighteous) then return "shield_of_the_righteous defensive 14"; end
        end
    end

    local function Cooldowns()
        -- lights_judgment,if=spell_targets.lights_judgment>=2|!raid_event.adds.exists|raid_event.adds.in>75|raid_event.adds.up
        if S.LightsJudgment:IsReady() then
          if Cast(S.LightsJudgment) then return "lights_judgment cooldowns 2"; end
        end
        -- avenging_wrath
        if S.AvengingWrath:IsReady() then
          if Cast(S.AvengingWrath) then return "avenging_wrath cooldowns 4"; end
        end
        -- Manually added: sentinel
        -- Note: Simc has back-end code for Protection Paladin to replace AW with Sentinel when talented.
        if S.Sentinel:IsReady() then
          if Cast(S.Sentinel) then return "sentinel cooldowns 6"; end
        end
        -- moment_of_glory,if=(buff.avenging_wrath.remains<15|(time>10))
        if S.MomentofGlory:IsReady() and (Player:BuffRemains(S.AvengingWrathBuff) < 15 or HL.CombatTime() > 10) then
          if Cast(S.MomentofGlory) then return "moment_of_glory cooldowns 10"; end
        end
        -- divine_toll,if=spell_targets.shield_of_the_righteous>=3
        if S.DivineToll:IsReady() and (EnemiesCount8y >= 3) then
          if Cast(S.DivineToll) then return "divine_toll cooldowns 12"; end
        end
        -- bastion_of_light,if=buff.avenging_wrath.up|cooldown.avenging_wrath.remains<=30
        if S.BastionofLight:IsReady() and (Player:BuffUp(S.AvengingWrathBuff) or S.AvengingWrath:CooldownRemains() <= 30) then
          if Cast(S.BastionofLight) then return "bastion_of_light cooldowns 14"; end
        end
        -- invoke_external_buff,name=power_infusion,if=buff.avenging_wrath.up
        -- Note: Not handling external buffs.
        -- fireblood,if=buff.avenging_wrath.remains>8
        if S.Fireblood:IsReady() and (Player:BuffRemains(S.AvengingWrathBuff) > 8) then
          if Cast(S.Fireblood, true) then return "fireblood cooldowns 16"; end
        end
    end
            
    local function Standard()
        -- judgment,target_if=charges>=2|full_recharge_time<=gcd.max
        if S.Judgment:IsReady() and (S.Judgment:Charges() >= 2 or S.Judgment:FullRechargeTime() <= Player:GCD()) then
            if CastTargetIf(S.Judgment, Enemies30y, "min", EvaluateTargetIfFilterJudgment, nil) then return "judgment standard 2"; end
        end
        -- hammer_of_light,if=buff.hammer_of_light_free.remains<2|buff.shake_the_heavens.remains<1|!buff.shake_the_heavens.up|cooldown.eye_of_tyr.remains<1.5|fight_remains<2
        if S.HammerofLight:IsReady() and (FreeHammerOfLight and FreeHammerOfLightUntil - GetTime() < 2 or Player:BuffUp(S.ShaketheHeavensBuff) and Player:BuffRemains(S.ShaketheHeavensBuff) < 2 or Player:BuffDown(S.ShaketheHeavensBuff) or S.EyeofTyr:CooldownRemains() < 1.5 or BossFightRemains < 2) then
          if Cast(S.HammerofLight) then return "hammer_of_light standard 4"; end
        end
        -- eye_of_tyr,if=(hpg_to_2dawn=5|!talent.of_dusk_and_dawn.enabled)&talent.lights_guidance.enabled
        -- eye_of_tyr,if=(hpg_to_2dawn=1|buff.blessing_of_dawn.stack>0)&talent.lights_guidance.enabled
        -- Note: Combining both lines into one.
        -- Note: Ignoring CDsON if spec'd Templar Hero Tree.
        if S.EyeofTyr:IsReady() and not GetSetting('EoT_check', false) and (S.LightsGuidance:IsAvailable() and ((HPGTo2Dawn() == 5 or not S.OfDuskandDawn:IsAvailable()) or (HPGTo2Dawn() == 1 or Player:BuffStack(S.BlessingofDawnBuff) > 0))) then
          if Cast(S.EyeofTyr) then return "eye_of_tyr standard 6"; end
        end
        -- shield_of_the_righteous,if=!buff.hammer_of_light_ready.up&(buff.luck_of_the_draw.up&((holy_power+judgment_holy_power>=5)|(!talent.righteous_protector.enabled|cooldown.righteous_protector_icd.remains=0)))
        -- shield_of_the_righteous,if=!buff.hammer_of_light_ready.up&set_bonus.thewarwithin_season_2_4pc&((holy_power+judgment_holy_power>5)|(holy_power+judgment_holy_power>=5&cooldown.righteous_protector_icd.remains=0))
        -- shield_of_the_righteous,if=!set_bonus.thewarwithin_season_2_4pc&(!talent.righteous_protector.enabled|cooldown.righteous_protector_icd.remains=0)&!buff.hammer_of_light_ready.up
        local RighteousProtectorICD = 999
        if S.RighteousProtector:IsAvailable() then
            local LastCast = mathmin(S.ShieldoftheRighteous:TimeSinceLastCast(), S.WordofGlory:TimeSinceLastCast())
            RighteousProtectorICD = mathmax(0, 1 - mathmin(S.ShieldoftheRighteous:TimeSinceLastCast(), S.WordofGlory:TimeSinceLastCast()))
        end
        if S.ShieldoftheRighteous:IsReady() and (not S.HammerofLight:IsCastable()) and (
            (Player:BuffUp(S.LuckoftheDrawBuff) and ((Player:HolyPower() + Player:JudgmentPower() >= 5) or (not S.RighteousProtector:IsAvailable() or RighteousProtectorICD == 0))) or
            (Player:HasTier("TWW2", 4) and ((Player:HolyPower() + Player:JudgmentPower() > 5) or (Player:HolyPower() + Player:JudgmentPower() >= 5 and RighteousProtectorICD == 0))) or
            (not Player:HasTier("TWW2", 4) and (not S.RighteousProtector:IsAvailable() or RighteousProtectorICD == 0))
        ) then
            if Cast(S.ShieldoftheRighteous) then return "shield_of_the_righteous standard 8"; end
        end
        -- judgment,target_if=min:debuff.judgment.remains,if=spell_targets.shield_of_the_righteous>3&buff.bulwark_of_righteous_fury.stack>=3&holy_power<3
        if S.Judgment:IsReady() and (EnemiesCount8y > 3 and Player:BuffStack(S.BulwarkofRighteousFuryBuff) >= 3 and Player:HolyPower() < 3) then
          if CastTargetIf(S.Judgment, Enemies30y, "min", EvaluateTargetIfFilterJudgment, nil) then return "judgment standard 10"; end
        end
        -- avengers_shield,if=!buff.bulwark_of_righteous_fury.up&talent.bulwark_of_righteous_fury.enabled&spell_targets.shield_of_the_righteous>=3
        if S.AvengersShield:IsReady() and (Player:BuffDown(S.BulwarkofRighteousFuryBuff) and S.BulwarkofRighteousFury:IsAvailable() and EnemiesCount8y >= 3) 
        and GetSetting('ashield_logic', {})['as_dps'] then
          if Cast(S.AvengersShield) then return "avengers_shield standard 12"; end
        end
        if Player:BuffUp(S.BlessedAssuranceBuff) and EnemiesCount8y < 3 and Player:BuffDown(S.AvengingWrathBuff) then
          -- hammer_of_the_righteous,if=buff.blessed_assurance.up&spell_targets.shield_of_the_righteous<3&!buff.avenging_wrath.up
          if S.HammeroftheRighteous:IsReady() then
            if Cast(S.HammeroftheRighteous) then return "hammer_of_the_righteous standard 14"; end
          end
          -- blessed_hammer,if=buff.blessed_assurance.up&spell_targets.shield_of_the_righteous<3&!buff.avenging_wrath.up
          if S.BlessedHammer:IsReady() then
            if Cast(S.BlessedHammer) then return "blessed_hammer standard 16"; end
          end
        end
        -- crusader_strike,if=buff.blessed_assurance.up&spell_targets.shield_of_the_righteous<2&!buff.avenging_wrath.up
        if S.CrusaderStrike:IsReady() and (Player:BuffUp(S.BlessedAssuranceBuff) and EnemiesCount8y < 2 and Player:BuffDown(S.AvengingWrathBuff)) then
          if Cast(S.CrusaderStrike) then return "crusader_strike standard 18"; end
        end
        -- judgment,target_if=min:debuff.judgment.remains,if=charges>=2|full_recharge_time<=gcd.max
        if S.Judgment:IsReady() and (S.Judgment:Charges() >= 2 or S.Judgment:FullRechargeTime() <= Player:GCD() + 0.25) then
          if CastTargetIf(S.Judgment, Enemies30y, "min", EvaluateTargetIfFilterJudgment, nil) then return "judgment standard 20";  end
        end
        -- consecration,if=buff.divine_guidance.stack=5
        if S.Consecration:IsReady() and (Player:BuffStack(S.DivineGuidanceBuff) == 5) then
          if Cast(S.Consecration) then return "consecration standard 22"; end
        end
        -- holy_armaments,if=next_armament=sacred_weapon&(!buff.sacred_weapon.up|(buff.sacred_weapon.remains<6&!buff.avenging_wrath.up&cooldown.avenging_wrath.remains<=30))
        if S.SacredWeapon:IsReady() and (Player:BuffDown(S.SacredWeaponBuff) or (Player:BuffRemains(S.SacredWeaponBuff) < 6 and Player:BuffDown(S.AvengingWrathBuff) and S.AvengingWrath:CooldownRemains() <= 30)) then
          if Cast(S.SacredWeapon) then return "holy_armaments standard 24"; end
        end
        -- hammer_of_wrath
        if S.HammerofWrath:IsReady() then
          if Cast(S.HammerofWrath) then return "hammer_of_wrath standard 26"; end
        end
        -- divine_toll,if=(!raid_event.adds.exists|raid_event.adds.in>10)
        if S.DivineToll:IsReady() then
          if Cast(S.DivineToll) then return "divine_toll standard 28"; end
        end
        -- avengers_shield,if=talent.refining_fire.enabled&talent.lights_guidance.enabled
        if S.AvengersShield:IsReady() and (S.RefiningFire:IsAvailable())
        and GetSetting('ashield_logic', {})['as_dps'] then
          if Cast(S.AvengersShield) then return "avengers_shield standard 30"; end
        end
        -- judgment,target_if=min:debuff.judgment.remains,if=(buff.avenging_wrath.up&talent.hammer_and_anvil.enabled)
        if S.Judgment:IsReady() and (Player:BuffUp(S.AvengingWrathBuff) and S.HammerandAnvil:IsAvailable()) then
          if CastTargetIf(S.Judgment, Enemies30y, "min", EvaluateTargetIfFilterJudgment, nil) then return "judgment standard 32"; end
        end
        -- holy_armaments,if=next_armament=holy_bulwark&charges=2
        if S.HolyBulwark:IsReady() and (S.HolyBulwark:Charges() == 2) then
          if Cast(S.HolyBulwark) then return "holy_armaments standard 34"; end
        end
        -- judgment,target_if=min:debuff.judgment.remains
        if S.Judgment:IsReady() then
          if CastTargetIf(S.Judgment, Enemies30y, "min", EvaluateTargetIfFilterJudgment, nil) then return "judgment standard 36"; end
        end
        -- avengers_shield,if=!buff.shake_the_heavens.up&talent.shake_the_heavens.enabled
        if S.AvengersShield:IsReady() and (Player:BuffUp(S.ShaketheHeavensBuff) and Player:BuffRemains(S.ShaketheHeavensBuff) < 2 and S.ShaketheHeavens:IsAvailable())
        and GetSetting('ashield_logic', {})['as_dps'] then
          if Cast(S.AvengersShield) then return "avengers_shield standard 38"; end
        end
        if (Player:BuffUp(S.BlessedAssuranceBuff) and EnemiesCount8y < 3) or Player:BuffUp(S.ShaketheHeavensBuff) and Player:BuffRemains(S.ShaketheHeavensBuff) < 2 then
          -- hammer_of_the_righteous,if=(buff.blessed_assurance.up&spell_targets.shield_of_the_righteous<3)|buff.shake_the_heavens.up
          if S.HammeroftheRighteous:IsReady() then
            if Cast(S.HammeroftheRighteous) then return "hammer_of_the_righteous standard 40"; end
          end
          -- blessed_hammer,if=buff.blessed_assurance.up&spell_targets.shield_of_the_righteous<3&!buff.avenging_wrath.up
          if S.BlessedHammer:IsReady() then
            if Cast(S.BlessedHammer) then return "blessed_hammer standard 42"; end
          end
          -- blessed_hammer,if=(buff.blessed_assurance.up&spell_targets.shield_of_the_righteous<3)|buff.shake_the_heavens.up
        end
        -- crusader_strike,if=(buff.blessed_assurance.up&spell_targets.shield_of_the_righteous<2)|buff.shake_the_heavens.up
        if S.CrusaderStrike:IsReady() and ((Player:BuffUp(S.BlessedAssuranceBuff) and EnemiesCount8y < 2) or Player:BuffUp(S.ShaketheHeavensBuff) and Player:BuffRemains(S.ShaketheHeavensBuff) < 2) then
          if Cast(S.CrusaderStrike) then return "crusader_strike standard 44"; end
        end
        -- avengers_shield,if=!talent.lights_guidance.enabled
        if S.AvengersShield:IsReady() and (not S.LightsGuidance:IsAvailable())
        and GetSetting('ashield_logic', {})['as_dps'] then
          if Cast(S.AvengersShield) then return "avengers_shield standard 46"; end
        end
        -- consecration,if=!consecration.up
        if S.Consecration:IsReady() and (Player:BuffDown(S.ConsecrationBuff)) then
          if Cast(S.Consecration) then return "consecration standard 48"; end
        end
        -- eye_of_tyr,if=(talent.inmost_light.enabled&raid_event.adds.in>=45|spell_targets.shield_of_the_righteous>=3)&!talent.lights_deliverance.enabled
        -- Note: Ignoring CDsON if spec'd Templar Hero Tree.
        if S.EyeofTyr:IsReady() and not GetSetting('EoT_check', false) and ((S.InmostLight:IsAvailable() and EnemiesCount8y == 1 or EnemiesCount8y >= 3) and not S.LightsDeliverance:IsAvailable()) then
          if Cast(S.EyeofTyr) then return "eye_of_tyr standard 50"; end
        end
        -- holy_armaments,if=next_armament=holy_bulwark
        if S.HolyBulwark:IsReady() then
          if Cast(S.HolyBulwark) then return "holy_armaments standard 52"; end
        end
        -- blessed_hammer
        if S.BlessedHammer:IsReady() then
          if Cast(S.BlessedHammer) then return "blessed_hammer standard 54"; end
        end
        -- hammer_of_the_righteous
        if S.HammeroftheRighteous:IsReady() then
          if Cast(S.HammeroftheRighteous) then return "hammer_of_the_righteous standard 56"; end
        end
        -- crusader_strike
        if S.CrusaderStrike:IsReady() then
          if Cast(S.CrusaderStrike) then return "crusader_strike standard 58"; end
        end
        -- avengers_shield
        if S.AvengersShield:IsReady()
        and GetSetting('ashield_logic', {})['as_dps'] then
          if Cast(S.AvengersShield) then return "avengers_shield standard 66"; end
        end
        -- eye_of_tyr,if=!talent.lights_deliverance.enabled
        -- Note: Ignoring CDsON if spec'd Templar Hero Tree.
        if S.EyeofTyr:IsReady() and not GetSetting('EoT_check', false) and (not S.LightsDeliverance:IsAvailable()) then
          if Cast(S.EyeofTyr) then return "eye_of_tyr standard 68"; end
        end
        -- arcane_torrent,if=holy_power<5
        if S.ArcaneTorrent:IsReady() and (Player:HolyPower() < 5) then
          if Cast(S.ArcaneTorrent) then return "arcane_torrent standard 76"; end
        end
        -- consecration
        if S.Consecration:IsReady() then
          if Cast(S.Consecration) then return "consecration standard 78"; end
        end
    end
      
    local function Trinkets()
        -- use_item,name=tome_of_lights_devotion,if=buff.inner_resilience.up
        if I.TomeofLightsDevotion:IsEquippedAndReady() and Player:BuffUp(S.InnerResilienceBuff) then
            if Cast(I.TomeofLightsDevotion) then return "tome_of_lights_devotion trinkets 2"; end
        end
        -- use_items,slots=trinket1,if=(variable.trinket_sync_slot=1&(buff.avenging_wrath.up|fight_remains<=40)|(variable.trinket_sync_slot=2&(!trinket.2.cooldown.ready|!buff.avenging_wrath.up))|!variable.trinket_sync_slot)
        -- use_items,slots=trinket2,if=(variable.trinket_sync_slot=2&(buff.avenging_wrath.up|fight_remains<=40)|(variable.trinket_sync_slot=1&(!trinket.1.cooldown.ready|!buff.avenging_wrath.up))|!variable.trinket_sync_slot)
        -- Note: Unable to handle these trinket conditionals. Using a generic fallback instead.
        -- use_items,if=buff.avenging_wrath.up|fight_remains<=40
        if Player:BuffUp(S.AvengingWrathBuff) or FightRemains <= 40 then
          local ItemToUse, ItemSlot, ItemRange = Player:GetUseableItems(OnUseExcludes)
          if ItemToUse then
            if ((ItemSlot == 13 or ItemSlot == 14)) or (ItemSlot ~= 13 and ItemSlot ~= 14) then
              if Cast(ItemToUse) then return "Generic use_items for " .. ItemToUse:Name(); end
            end
          end
        end
    end

    local function MainRotation()
        IsInCombat = Player:AffectingCombat()
        Enemies8y = Player:GetEnemiesInRangeFilter(8)
        Enemies30y = Player:GetEnemiesInRangeFilter(30)
        DivineShieldIsUp = Player:BuffUp(S.DivineShield)
        ArdentDefenderIsUp = Player:BuffUp(S.ArdentDefenderBuff)
        GuardianOfAncientKingsIsUp = Player:BuffUp(S.GuardianofAncientKingsBuff) or Player:BuffUp(S.GiftOfTheGoldenValKyr)

        if (AoEON()) then
            EnemiesCount8y = #Enemies8y
            EnemiesCount30y = #Enemies30y
        else
            EnemiesCount8y = 1
            EnemiesCount30y = 1
        end

        -- MainAddon.UpdateVariable('WoG IsReady', S.WordofGlory:IsReady(Player))
        -- MainAddon.UpdateVariable('WoG IsCastable', S.WordofGlory:IsCastable(Player))
        -- MainAddon.UpdateVariable('HP', Player:HolyPower())

        ActiveMitigationNeeded = Player:ActiveMitigationNeeded()
        IsTanking = Player:IsTankingAoE(8) or Player:IsTanking(Target) or Target:IsDummy()

        -- Calculate fight_remains
        BossFightRemains = HL.BossFightRemains()
        FightRemains = BossFightRemains
        if FightRemains == 11111 then
            FightRemains = HL.FightRemains(Enemies8y, false)
        end
        
        -- Utilities
		local ShouldReturn = Utilities();
		if ShouldReturn then return ShouldReturn; end

        if not IsInCombat then
            if GetSetting('FlashOfLight_check', false) and S.FlashofLight:IsReady(Player) and Player:HealthPercentage() <= GetSetting('FlashOfLight_spin', 30) then
                if CastAlly(S.FlashofLight, Player) then
                    return "flash of light player";
                end
            end
        end

        -- Trinkets
        local shouldReturn = MainAddon.TrinketTanking()
        if shouldReturn then
            return shouldReturn
        end

        -- Burst Potion
        if Target:IsInRange(8) then
            if MainAddon.UsePotion() then
                MainAddon.SetTopColor(1, "Combat Potion")
            end
        end

        if GetSetting('WordofGlory_self_emergency_check', false) and S.WordofGlory:IsReady(Player) and (Player:HealthPercentage() <= GetSetting('WordofGlory_self_emergency_spin', 30) and not Player:HealingAbsorbed()) then
            if CastAlly(S.WordofGlory, Player) then
                return "Emergency WoG";
            end
        end

        if (IsTanking) then
            local ShouldReturn = Defensives();
            if ShouldReturn then
                return ShouldReturn;
            end
        end

        local ShouldReturn = PartyHealing();
        if ShouldReturn then
            return ShouldReturn;
        end
        
        if M.TargetIsValid() then
            if not Player:AffectingCombat() then
                local ShouldReturn = Precombat();
                if ShouldReturn then
                    return ShouldReturn;
                end
            end
            -- auto_attack
            -- Interrupts
            -- call_action_list,name=cooldowns
            local ShouldReturn = Cooldowns();
            if ShouldReturn then
                return ShouldReturn;
            end
            -- call_action_list,name=trinkets
            local ShouldReturn = Trinkets();
            if ShouldReturn then
                return ShouldReturn;
            end
            -- call_action_list,name=standard
            local ShouldReturn = Standard();
            if ShouldReturn then
                return ShouldReturn;
            end
        end
    end

    local function Init()
        C_Timer.After(10, 
            function()
                UpdateAvengersShield()
            end
        )
    end
    M.SetAPL(66, MainRotation, Init)

    local OldIsCastable
    OldIsCastable = HL.AddCoreOverride("Spell.IsCastable",
            function(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                local BaseCheck, Reason = OldIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                if MainAddon.PlayerSpecID() == 66 then
                    local ConsecrationMoving = GetSetting("ConsecrationMoving", false)
                    local EoTMoving = GetSetting("EoTMoving", false)

                    if M.Toggle:GetToggle('HoldingHP') then
                        if self == S.ShieldoftheRighteous and Player:HolyPower() - S.ShieldoftheRighteous:Cost() < 2 then
                            return false, "Holding Holy Power"
                        end
                        if self == S.WordofGlory and Player:HolyPower() - S.WordofGlory:Cost() < 3 then
                            return false, "Holding Holy Power"
                        end
                    end

                    if self == S.ShieldoftheRighteous and S.HammerofLight:IsAvailable() and not S.HammerofLight:IsBlocked() then
                        return false, "Holding Holy Power for Hammer of Light"
                    end

                    if M.Toggle:GetToggle('BuildingSanctification') or BuildingSanctificationBuff() then
                        if self == S.Consecration and Player:BuffStack(S.SanctificationBuff) == VarSanctificationMaxStack then
                            return false, "Holding Sanctification"
                        end
                    end

                    if self == S.Intercession then
						if Player:IsCasting(self) then
							return false, "Intercession is already casting"
						end
					end
                    
                    if self == S.Consecration then
                        if not ConsecrationMoving and Player:IsMovingFor() >= 0.5 then
                            return false, "Player Moving"
                        end
                    end

                    if self == S.EyeofTyr or self == S.HammerofLight then
                        if not EoTMoving and Player:IsMovingFor() >= 0.5 then
                            return false, "Player Moving"
                        end
                    end

                    if self == S.DivineShield and not self:IsQueued() then
                        if ArdentDefenderIsUp or GuardianOfAncientKingsIsUp or DivineShieldIsUp then
                            return false, "Other defensive buff found"
                        end
                    end

                    if self == S.ArdentDefender and not self:IsQueued() then
                        if DivineShieldIsUp or GuardianOfAncientKingsIsUp or ArdentDefenderIsUp then
                            return false, "Other defensive buff found"
                        end
                    end

                    if self == S.GuardianofAncientKings and not self:IsQueued() then
                        if DivineShieldIsUp or ArdentDefenderIsUp or GuardianOfAncientKingsIsUp then
                            return false, "Other defensive buff found"
                        end
                    end

                    if self == S.ShieldoftheRighteous then
                        if GetSetting('WordofGlory_self_emergency_check', false) and S.WordofGlory:IsReady(nil, nil, nil, nil, true) and (Player:HealthPercentage() <= GetSetting('WordofGlory_self_emergency_spin', 30)) then
                            return false, "Pooling Holy Power for Emergency"
                        end
                    end

                    if self == S.ShieldoftheRighteous or self == S.WordofGlory then
                        local autorebith = GetSetting('autorebirth', {})
                        if autorebith['autorebirth_mouseover'] then
                            if S.Intercession:IsReady(MouseOver, nil, nil, nil, nil, nil, true) then
                                if MouseOver:EvaluateRebirth() then
                                    local GetMouseFociCache = GetMouseFoci()
                                    ---@class Frame
                                    local MouseFocus = GetMouseFociCache[1]

                                    local FrameName = MouseFocus and not MouseFocus:IsForbidden() and MouseFocus:GetName() or "None"
                                    if FrameName ~= "WorldFrame" and FrameName ~= "None" then
                                        return false, "Pooling Holy Power for Intercession"
                                    end
                                end
                            end
                        end
                        
                        if autorebith['autorebirth_target'] then
                            if S.Intercession:IsReady(Target, nil, nil, nil, nil, nil, true) then
                                if Target:EvaluateRebirth() then
                                    return false, "Pooling Holy Power for Intercession"
                                end
                            end
                        end
                    end

                    if self == S.RiteofAdjuration then
                        return BaseCheck and Player:BuffDown(S.RiteofAdjurationBuff) and not Player:IsCasting(self)
                    elseif self == S.RiteofSanctification then
                        return BaseCheck and Player:BuffDown(S.RiteofSanctificationBuff) and not Player:IsCasting(self)
                    else
                        return BaseCheck, Reason
                    end
                end
                return BaseCheck, Reason
            end
    , 66);

    local ProtPalBuffUp
    ProtPalBuffUp = HL.AddCoreOverride("Player.BuffUp",
        function(self, ThisSpell, AnyCaster, BypassRecovery)
            local BaseCheck = ProtPalBuffUp(self, ThisSpell, AnyCaster, BypassRecovery)
            if MainAddon.PlayerSpecID() == 66 then
                if ThisSpell == S.AvengingWrathBuff and S.Sentinel:IsAvailable() then
                    return Player:BuffUp(S.SentinelBuff)
                else
                    return BaseCheck
                end
            end
            return BaseCheck
        end
    , 66)

    local ProtPalBuffRemains
    ProtPalBuffRemains = HL.AddCoreOverride("Player.BuffRemains",
        function(self, Spell, AnyCaster, BypassRecovery)
            local BaseCheck = ProtPalBuffRemains(self, Spell, AnyCaster, BypassRecovery)
            if MainAddon.PlayerSpecID() == 66 then
                if Spell == S.AvengingWrathBuff and S.Sentinel:IsAvailable() then
                    return Player:BuffRemains(S.SentinelBuff)
                else
                    return BaseCheck
                end
            end
            return BaseCheck
        end
    , 66)

    local ProtPalCDRemains
    ProtPalCDRemains = HL.AddCoreOverride("Spell.CooldownRemains",
        function(self, BypassRecovery, BypassCD)
            local BaseCheck = ProtPalCDRemains(self, BypassRecovery, BypassCD)
            if MainAddon.PlayerSpecID() == 66 then
                if self == S.AvengingWrath and S.Sentinel:IsAvailable() then
                    return S.Sentinel:CooldownRemains()
                else
                    return BaseCheck
                end
            end
            return BaseCheck
        end
    , 66)

    local ProtPalIsAvail
    ProtPalIsAvail = HL.AddCoreOverride("Spell.IsAvailable",
        function(self, CheckPet)
            local BaseCheck = ProtPalIsAvail(self, CheckPet)
            if MainAddon.PlayerSpecID() == 66 then
                if self == S.AvengingWrath and S.Sentinel:IsAvailable() then
                    return S.Sentinel:IsAvailable()
                else
                    return BaseCheck
                end
            end
            return BaseCheck
        end
    , 66)

    HL.AddCoreOverride("Player.JudgmentPower",
        function(self)
            local JP = 1
            if Player:BuffUp(S.AvengingWrathBuff) or Player:BuffUp(S.SentinelBuff) then
                JP = JP + 1
            end
            if Player:BuffUp(S.BastionofLightBuff) then
                JP = JP + 2
            end
            return JP
        end
    , 66)

    HL:RegisterForCombatEvent(function(...)
        local SourceGUID, _, _, _, UnitPetGUID, _, _, _, SpellID = select(4, ...)
        if SpellID == 387174 then
            FreeHammerOfLight = true
            FreeHammerOfLightUntil = GetTime() + 12
            C_Timer.After(12, function()
                FreeHammerOfLight = false
                FreeHammerOfLightUntil = 0
            end)
        end
    end, "SPELL_CAST_SUCCESS")
end