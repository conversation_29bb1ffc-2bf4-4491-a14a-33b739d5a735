function A_257(...)
    -- Addon
    ---@class MainAddon
    local MainAddon = MainAddon
    local M = MainAddon
    ---@class HealingEngine
    local HealingEngine = MainAddon.HealingEngine
    -- HeroLib
    local HL = HeroLibEx
    ---@class Unit
    local Unit = HL.Unit
    ---@class Unit
    local Player = Unit.Player
    ---@class Unit
    local Target = Unit.Target
    ---@class Unit
    local MouseOver = Unit.MouseOver
    ---@class Unit
    local Focus = Unit.Focus
    ---@class Unit
    local Pet = Unit.Pet
    ---@class Spell
    local Spell = HL.Spell
    ---@class Item
    local Item = HL.Item
    -- HeroRotation
    local Cast = M.Cast
    local CastCycleAlly = M.CastCycleAlly
    local CastTargetIfAlly = M.CastTargetIfAlly
    local CastAlly = M.CastAlly
    local CastMagicAlly = M.CastMagicAlly
    local CastMagic = M.CastMagic
    ---@class Priest
    local Priest = M.Priest
    -- Lua
    local AoEON = M.AoEON
    local IsInGroup = _G['IsInGroup']
    local GetTime = _G['GetTime']
    local GetNumGroupMembers = _G['GetNumGroupMembers']
    
    -- Define S/I for spell and item arrays
    local S = Spell.Priest.Holy
    local I = Item.Priest.Holy

    -- Create table to exclude above trinkets from On Use function
    local OnUseExcludes = {
        I.IridaltheEarthsMaster:ID(),
        -- I.Dreambinder:ID()
    }

    MainAddon.Toggle.Special["ForceDPS"] = {
        Icon = MainAddon.GetTexture(S.HolyFire),
        Name = "Force DPS",
        Description = "This toggle will force DPS.",
        Spec = 257
    }

    MainAddon.Toggle.Special["ForceHealing"] = {
        Icon = MainAddon.GetTexture(S.Heal),
        Name = "Force Healing",
        Description = "This toggle will disable the DPS rotation.",
        Spec = 257
    }

    local GetSetting = MainAddon.Config.GetClassSetting
    local Config_Key = MainAddon.GetClassVariableName()
    local Config_Color = 'FFFFFF'
    local Config_Table = {
        key = Config_Key,
        title = 'Priest - Holy',
        subtitle = '?? ' .. MainAddon.Version,
        width = 600,
        height = 700,
        profiles = true,
        config = {
            { type = 'header', text = '', size = 24, align = 'Center', color = Config_Color },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
            
            { type = 'header', text = 'Single Target Healing', color = Config_Color },
            { type = 'spacer' },
            
            -- Renew
            { type = 'header', text = 'Renew', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'RenewHP', icon = S.Renew:ID(), min = 1, max = 100, default = 70 },
            { type = 'spacer' },
            
            -- Heal
            { type = 'header', text = 'Heal', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'HealHP', icon = S.Heal:ID(), min = 1, max = 100, default = 80 },
            { type = 'spinner', text = 'Lightweaver Threshold (%)', key = 'LWHealHP', icon = S.Heal:ID(), min = 1, max = 100, default = 80 },
            { type = 'spacer' },
            
            -- Flash Heal
            { type = 'header', text = 'Flash Heal', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'FlashHealHP', icon = S.FlashHeal:ID(), min = 1, max = 100, default = 70 },
            { type = 'spacer' },
            
            -- Holy Word: Serenity
            { type = 'header', text = 'Holy Word: Serenity', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'HolyWordSerenityHP', icon = S.HolyWordSerenity:ID(), min = 1, max = 100, default = 80 },
            { type = 'spacer' },
            
            -- Guardian Spirit
            { type = 'header', text = 'Guardian Spirit', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Tank Health Threshold (%)', key = 'GSTanksHP', icon = S.GuardianSpirit:ID(), min = 1, max = 100, default = 35 },
            { type = 'checkspin', text = 'Group Member Threshold (%)', key = 'GSMembersHP', icon = S.GuardianSpirit:ID(), min = 1, max = 100, default_spin = 25, default_check = true },
            { type = 'spinner', text = 'Cast Delay (seconds)', key = 'GSDelay', icon = S.GuardianSpirit:ID(), min = 0, max = 3, default = 0.5},
            { type = 'spacer' },
            
            -- Void Shift
            { type = 'header', text = 'Void Shift', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Self Health Threshold (%)', key = 'VoidShift_Self', icon = S.VoidShift:ID(), min = 1, max = 100, default = 35},
            { type = 'checkspin', text = 'Target Health Threshold (%)', key = 'VoidShift_Members', icon = S.VoidShift:ID(), min = 1, max = 100, default_spin = 25, default_check = true },
            { type = 'spinner', text = 'Minimum Health Difference (%)', key = 'VoidShift_Difference', icon = S.VoidShift:ID(), min = 1, max = 100, default = 50},
            { type = 'spacer' },
            
            { type = 'header', text = 'Group Healing', color = Config_Color },
            { type = 'spacer' },
            
            -- Circle of Healing
            { type = 'header', text = 'Circle of Healing', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'CoH_underX', icon = S.CircleofHealing:ID(), min = 1, max = 100, default = 65 },
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'CoH_underX_val', icon = S.CircleofHealing:ID(), min = 1, max = 100, default = 90 },
            { type = 'spacer' },
            
            -- Prayer of Healing
            { type = 'header', text = 'Prayer of Healing', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'PoH_underX', icon = S.PrayerofHealing:ID(), min = 1, max = 100, default = 20 },
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'PoH_underX_val', icon = S.PrayerofHealing:ID(), min = 1, max = 100, default = 90 },
            { type = 'spacer' },
            
            -- Prayer of Mending
            { type = 'header', text = 'Prayer of Mending', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'PoM_underX', icon = S.PrayerofMending:ID(), min = 1, max = 100, default = 25 },
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'PoM_underX_val', icon = S.PrayerofMending:ID(), min = 1, max = 100, default = 95 },
            { type = 'spinner', text = 'Group Health Threshold (%)', key = 'PrayerofMendingHP', icon = S.PrayerofMending:ID(), min = 1, max = 100, default = 90 },
            { type = 'spacer' },
            
            -- Lightwell
            { type = 'header', text = 'Lightwell', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'Lightwell_underX', icon = S.Lightwell:ID(), min = 1, max = 100, default = 30 },
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'Lightwell_underX_val', icon = S.Lightwell:ID(), min = 1, max = 100, default = 80 },
            { type = 'spinner', text = 'Group Health Threshold (%)', key = 'LightwellHP', icon = S.Lightwell:ID(), min = 1, max = 100, default = 75 },
            { type = 'spacer' },
            
            -- Divine Hymn
            { type = 'header', text = 'Divine Hymn', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'DHHP_underX', icon = S.DivineHymn:ID(), min = 1, max = 100, default = 45 },
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'DHHP_underX_val', icon = S.DivineHymn:ID(), min = 1, max = 100, default = 65 },
            { type = 'spinner', text = 'Group Health Threshold (%)', key = 'DivineHymnHP', icon = S.DivineHymn:ID(), min = 1, max = 100, default = 60 },
            { type = 'spacer' },
            
            -- Holy Word: Sanctify
            { type = 'header', text = 'Holy Word: Sanctify', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'Sanctify_underX', icon = S.HolyWordSanctify:ID(), min = 1, max = 100, default = 35 },
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'Sanctify_underX_val', icon = S.HolyWordSanctify:ID(), min = 1, max = 100, default = 75 },
            { type = 'spinner', text = 'Group Health Threshold (%)', key = 'HolyWordSanctifyHP', icon = S.HolyWordSanctify:ID(), min = 1, max = 100, default = 80 },
            { type = 'dropdown', text = 'Magic Groundspell Placement', key = 'magicgroundspell', icon = S.HolyWordSanctify:ID(), 
                list = {
                    { text = 'Friend', key = 1 },
                    { text = 'Enemy', key = 2 },
                    { text = 'No Magic', key = 3 },
                },
                default = 3
            },
            { type = 'spacer' },
            
            -- Holy Word: Salvation
            { type = 'header', text = 'Holy Word: Salvation', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'Salvation_underX', icon = S.HolyWordSalvation:ID(), min = 1, max = 100, default = 45 },
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'Salvation_underX_val', icon = S.HolyWordSalvation:ID(), min = 1, max = 100, default = 65 },
            { type = 'spinner', text = 'Group Health Threshold (%)', key = 'HolyWordSalvationHP', icon = S.HolyWordSalvation:ID(), min = 1, max = 100, default = 60 },
            { type = 'spacer' },
            
            -- Apotheosis
            { type = 'header', text = 'Apotheosis', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'Apotheosis_underX', icon = S.Apotheosis:ID(), min = 1, max = 100, default = 45 },
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'Apotheosis_underX_val', icon = S.Apotheosis:ID(), min = 1, max = 100, default = 60 },
            { type = 'spinner', text = 'Group Health Threshold (%)', key = 'ApotheosisHP', icon = S.Apotheosis:ID(), min = 1, max = 100, default = 70 },
            { type = 'spacer' },
            
            -- Vampiric Embrace
            { type = 'header', text = 'Vampiric Embrace', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Group Health Threshold (%)', key = 'VE_groupHP', icon = S.VampiricEmbrace:ID(), min = 1, max = 100, default = 70 },
            { type = 'spacer' },
            
            -- Divine Star & Halo
            { type = 'header', text = 'Divine Star & Halo', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkspin', text = 'Divine Star Health Threshold (%)', key = 'star_HP', icon = S.DivineStar:ID(), min = 1, max = 100, default_spin = 80, default_check = true },
            { type = 'checkspin', text = 'Halo Health Threshold (%)', key = 'halo_HP', icon = S.Halo:ID(), min = 1, max = 100, default_spin = 80, default_check = true },
            { type = 'checkbox', text = 'DPS Use Only', icon = S.DivineStar:ID(), default = false, key = 'star_dps_only' },
            { type = 'spacer' },
            
            { type = 'header', text = 'Premonition Talents', color = Config_Color },
            { type = 'spacer' },
            
            -- Premonition of Insight
            { type = 'header', text = 'Premonition of Insight', size = 14, align = 'Left', color = Config_Color },
            { type = 'dropdown', text = 'Usage Mode', key = 'pofinsight', icon = S.PremonitionOfInsight:ID(),
                list = {
                    { text = 'Auto', key = 1 },
                    { text = 'For Healing Only', key = 2 },
                    { text = 'For DPS Only', key = 3 },
                },
                default = 1
            },
            { type = 'spacer' },
            
            -- Premonition of Piety
            { type = 'header', text = 'Premonition of Piety', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'PoPiety_underX', icon = S.PremonitionOfPiety:ID(), min = 1, max = 100, default = 60 },
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'PoPiety_underX_val', icon = S.PremonitionOfPiety:ID(), min = 1, max = 100, default = 75 },
            { type = 'spinner', text = 'Single Target HP Threshold (%)', key = 'PoPietySTHP', icon = S.PremonitionOfPiety:ID(), min = 1, max = 100, default = 45 },
            { type = 'spacer' },
            
            -- Premonition of Clairvoyance
            { type = 'header', text = 'Premonition of Clairvoyance', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Single Target HP Threshold (%)', key = 'PoClairSTHP', icon = S.PremonitionOfClairvoyance:ID(), min = 1, max = 100, default = 70 },
            { type = 'spacer' },
            
            { type = 'header', text = 'Holy Word Management', color = Config_Color },
            { type = 'spacer' },
            
            -- Divine Word
            { type = 'header', text = 'Divine Word', size = 14, align = 'Left', color = Config_Color },
            { type = 'dropdown', text = 'Preferred Holy Word', key = 'dw', list = {
                { text = 'Serenity', key = 'dw_ser' },
                { text = 'Sanctify', key = 'dw_san' },
                { text = 'Chastise', key = 'dw_cha' },
                { text = 'Auto', key = 'dw_auto' },
              },
              default = 'dw_auto',
            },
            { type = 'spacer' },
            
            { type = 'header', text = 'Mana Management', color = Config_Color },
            { type = 'spacer' },
            
            -- Symbol of Hope
            { type = 'header', text = 'Symbol of Hope', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Mana Threshold (%)', key = 'SymbolofHopeMP', icon = S.SymbolofHope:ID(), min = 1, max = 100, default = 50 },
            { type = 'spacer' },
            
            -- Shadowfiend
            { type = 'header', text = 'Shadowfiend', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Mana Threshold (%)', key = 'ShadowfiendMana', icon = S.Shadowfiend:ID(), min = 1, max = 100, default = 70 },
            { type = 'spacer' },
            
            -- Mana Saving
            { type = 'header', text = 'Mana Saving', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkspin', text = 'Enable Below Mana (%)', key = 'SavingMana', min = 1, max = 100, default_spin = 50, default_check = true },
            { type = 'spacer' },
            
            { type = 'header', text = 'DPS Options', color = Config_Color },
            { type = 'spacer' },
            
            -- Power Infusion
            { type = 'header', text = 'Power Infusion', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkbox', text = 'Full Auto Mode', key = 'pi_fullauto', icon = S.PowerInfusion:ID(), default = true },
            { type = 'checkbox', text = 'Show Toast Notification', key = 'pi_fullauto_toast', icon = S.PowerInfusion:ID(), default = true },  
            { type = 'spacer' },
            
            -- Holy Word: Chastise
            { type = 'header', text = 'Holy Word: Chastise', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkbox', text = 'Use For Holy Fire', icon = S.HolyWordChastise:ID(), key = 'Chastise_dps', default = false },
            { type = 'spacer' },
            
            -- Holy Fire
            { type = 'header', text = 'Holy Fire', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkbox', text = "Prioritize Empyreal Blaze's Holy Fire", icon = S.HolyFire:ID(), key = 'highprio_holyfire', default = false },
            { type = 'spacer' },
            
            -- Shadow Word: Pain
            { type = 'header', text = 'Shadow Word: Pain', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = "Maximum Active DoTs", key = 'max_SWP', icon = S.ShadowWordPain:ID(), min = 0, max = 20, default = 5 },
            { type = 'spacer' },
            
            { type = 'header', text = 'Defensives', color = Config_Color },
            { type = 'spacer' },
            
            -- Desperate Prayer
            { type = 'header', text = 'Desperate Prayer', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'DesperatePrayerHP', icon = S.DesperatePrayer:ID(), min = 1, max = 100, default = 60 },
            { type = 'spacer' },
            
            { type = 'header', text = 'Movement & Utility', color = Config_Color },
            { type = 'spacer' },
            
            -- Angelic Feather
            { type = 'header', text = 'Angelic Feather', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkspin', text = 'Movement Threshold (seconds)', icon = S.AngelicFeather:ID(), key = 'angelic', min = 0, max = 15, default_spin = 2, default_check = true },
            { type = 'spacer' },
            
            -- Body and Soul
            { type = 'header', text = 'Body and Soul', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkspin', text = 'Movement Threshold (seconds)', icon = S.BodyAndSoul:ID(), key = 'bsoul', min = 0, max = 15, default_spin = 2, default_check = true },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'ruler' },
            { type = 'spacer' },
        }
    }
    Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
    Config_Table.config = MainAddon.BuildHealingTrinketUI(Config_Table.config, Config_Color)
    Config_Table.config = MainAddon.BuildCombatManaPotionUI(Config_Table.config, "Holy", Config_Color)
    MainAddon.SetConfig(257, Config_Table)

    -- Vars
    local ShouldReturn
    local Tanks, Healers, Members, Damagers, Melees, TargetIfAlly
    Priest.MembersPI = {}
    Priest.MembersPIAuto = {}
    local Enemies12y = {}
    local EnemiesCount12y = 0
    local Enemies40y = {}
    local BossFightRemains = 11111
    local FightRemains = 11111
    local Settings = {}
    local Var = {}
    Var['TargetIsValid'] = false
    Var['CombatMonitor_TimeStamp'] = GetTime()
    Var['CombatMonitor_State'] = false
    Var['LightwellIsLearnt'] = S.Lightwell:IsAvailable()
    Var['LightwellActive'] = false
    

    HL:RegisterForEvent(function()
        Var['LightwellIsLearnt'] = S.Lightwell:IsAvailable()
    end, "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB", "PLAYER_SPECIALIZATION_CHANGED", "PLAYER_ENTERING_WORLD")

    local function combatMonitor()
        if GetTime() - Var['CombatMonitor_TimeStamp'] < 1 then
            return Var['CombatMonitor_State']
        end
        
        if Tanks then
            ---@param TankUnit Unit
            for _, TankUnit in pairs(Tanks) do
                if TankUnit:AffectingCombat() then
                    Var['CombatMonitor_TimeStamp'] = GetTime()
                    Var['CombatMonitor_State'] = true
                    return true
                end
            end
        end

        Var['CombatMonitor_TimeStamp'] = GetTime()
        Var['CombatMonitor_State'] = false
        return false
    end

    HL:RegisterForEvent(function()
        BossFightRemains = 11111
        FightRemains = 11111
    end, "PLAYER_REGEN_ENABLED")

    -- Functions
    local function UpdateVars()
        -- Variables
        Tanks, Healers, Members, Damagers, Melees, _, _, _, TargetIfAlly = HealingEngine:Fetch()
        Var['TargetIsValid'] = M.TargetIsValid()
        Var['IsInCombat'] = Player:AffectingCombat()
        Var['ManaPct'] = Player:ManaPercentage()
        Var['partySize']  = GetNumGroupMembers()
		Var['MembersAmount'] = Var['partySize'] < 5 and 5 or Var['partySize']
        Var['IsInRaid'] = Player:IsInRaid()
        Var['IsInDungeon'] = Player:IsInDungeonArea()
        Settings['piUsage'] = GetSetting("piusage", "piauto")  -- Default to 'piauto' if not set
        

        -- Update Power Infusion candidates
        if Members and #Members > 0 then
            -- Create copies to avoid modifying the original HealingEngine tables
            Priest.MembersPI = {}
            Priest.MembersPIAuto = {}
            
            -- Copy Members table
            for i = 1, #Members do
                Priest.MembersPI[i] = Members[i]
            end
            
            -- Copy Damagers table
            if Damagers then
                for i = 1, #Damagers do
                    Priest.MembersPIAuto[i] = Damagers[i]
                end
            end
            
            Priest.SortMembersPI(Priest.MembersPI, Enemies40y and #Enemies40y or 0)
        end

        -- Under X%
        if Var['MembersAmount'] then
            Settings['CoH_underX'] = (GetSetting('CoH_underX', 30) * Var['MembersAmount']) / 100
            Settings['CoH_underX_val'] = GetSetting('CoH_underX_val', 30)
            Settings['PoH_underX'] = (GetSetting('PoH_underX', 30) * Var['MembersAmount']) / 100
            Settings['PoH_underX_val'] = GetSetting('PoH_underX_val', 30)
            Settings['PoPiety_underX'] = (GetSetting('PoPiety_underX', 30) * Var['MembersAmount']) / 100
            Settings['PoPiety_underX_val'] = GetSetting('PoPiety_underX_val', 30)
            Settings['DHHP_underX'] = (GetSetting('DHHP_underX', 30) * Var['MembersAmount']) / 100
            Settings['DHHP_underX_val'] = GetSetting('DHHP_underX_val', 30)
            Settings['PoM_underX'] = (GetSetting('PoM_underX', 30) * Var['MembersAmount']) / 100
            Settings['PoM_underX_val'] = GetSetting('PoM_underX_val', 30)
            Settings['Salvation_underX'] = (GetSetting('Salvation_underX', 30) * Var['MembersAmount']) / 100
            Settings['Salvation_underX_val'] = GetSetting('Salvation_underX_val', 30)
            Settings['Apotheosis_underX'] = (GetSetting('Apotheosis_underX', 30) * Var['MembersAmount']) / 100
            Settings['Apotheosis_underX_val'] = GetSetting('Apotheosis_underX_val', 30)
            Settings['Sanctify_underX'] = (GetSetting('Sanctify_underX', 30) * Var['MembersAmount']) / 100
            Settings['Sanctify_underX_val'] = GetSetting('Sanctify_underX_val', 30)
            Settings['Lightwell_underX'] = (GetSetting('Lightwell_underX', 30) * Var['MembersAmount']) / 100
            Settings['Lightwell_underX_val'] = GetSetting('Lightwell_underX_val', 30)
        end

        -- Group Health threshold
        Settings['DivineHymnHP'] = GetSetting("DivineHymnHP", 30)
        Settings['PrayerofMendingHP'] = GetSetting("PrayerofMendingHP", 30)
        Settings['HolyWordSanctifyHP'] = GetSetting("HolyWordSanctifyHP", 30)
        Settings['HolyWordSalvationHP'] = GetSetting("HolyWordSalvationHP", 30)
        Settings['ApotheosisHP'] = GetSetting("ApotheosisHP", 30)
        Settings['VE_groupHP'] = GetSetting("VE_groupHP", 30)
        Settings['LightwellHP'] = GetSetting("LightwellHP", 30)

        -- Unit Health threshold
        Settings['GSTanksHP'] = GetSetting('GSTanksHP', 30)
        Settings['GSMembersHP'] = GetSetting('GSMembersHP_spin', 30)
        Settings['GSMembersHPCheck'] = GetSetting('GSMembersHP_check', false)
        Settings['HolyWordSerenityHP'] = GetSetting("HolyWordSerenityHP")
        Settings['RenewHP'] = GetSetting("RenewHP", 30)
        Settings['FlashHealHP'] = GetSetting("FlashHealHP", 30)
        Settings['HealHP'] = GetSetting("HealHP", 30)
        Settings['LWHealHP'] = GetSetting("LWHealHP", 30)
        Settings['VoidShift_Self'] = GetSetting("VoidShift_Self", 30)
        Settings['VoidShift_Members_check'] = GetSetting("VoidShift_Members_check", false)
        Settings['VoidShift_Members_spin'] = GetSetting("VoidShift_Members_spin", 30)
        Settings['VoidShift_Difference'] = GetSetting("VoidShift_Difference", 30)
        Settings['halo_HP_check'] = GetSetting("halo_HP_check", false)
        Settings['halo_HP_spin'] = GetSetting("halo_HP_spin", 30)
        Settings['star_HP_check'] = GetSetting("star_HP_check", false)
        Settings['star_HP_spin'] = GetSetting("star_HP_spin", 30)
        Settings['PoPietySTHP'] = GetSetting("PoPietySTHP", 30)
        Settings['PoClairSTHP'] = GetSetting("PoClairSTHP", 30)

        -- Defensives
        Settings['DesperatePrayerHP'] = GetSetting("DesperatePrayerHP", 30)

        -- Misc
        Settings['SymbolofHopeMP'] = GetSetting("SymbolofHopeMP", 30)
        Settings['ShadowfiendMana'] = GetSetting("ShadowfiendMana", 30)
        Settings['SavingManaValue'] = GetSetting('SavingMana_spin', 30)
        Settings['SavingManaCheck'] = GetSetting('SavingMana_check', false)
        Settings['dw'] = GetSetting('dw')
        Settings['PINSIGHT'] = GetSetting('pofinsight')

        -- Utilities
        Settings['angelic_check'] = GetSetting('angelic_check', false)
        Settings['angelic_spin'] = GetSetting('angelic_spin', 30)
        Settings['bsoul_check'] = GetSetting('bsoul_check', false)
        Settings['bsoul_spin'] = GetSetting('bsoul_spin', 30)
        Settings['magicgroundspell'] = GetSetting('magicgroundspell', 1)

        -- Variables
        Var['AverageHPInRange'] = HealingEngine:MedianHP(true)
        Var['SpiritofRedemption'] = Player:BuffUp(S.SpiritofRedemption)
        Var['AngelicFeatherBuff'] = Player:BuffUp(S.AngelicFeatherBuff)
        Var['BodyAndSoulBuff'] = Player:BuffUp(S.BodyAndSoulBuff)
        Var['Shadowmeld'] = Player:BuffUp(S.Shadowmeld)
        Var['Fade'] = Player:BuffUp(S.Fade)
        Var['LowestHP'], Var['LowestUnit'] = HealingEngine:LowestHP(true, 40)

        -- DPS
        Settings['Chastise_dps'] = GetSetting('Chastise_dps', false)
        Settings['highprio_holyfire'] = GetSetting('highprio_holyfire', false)
        Settings['max_SWP'] = GetSetting('max_SWP', 5)
        Settings['star_dps_only'] = GetSetting('star_dps_only', false)
        Settings['pi_fullauto'] = GetSetting("pi_fullauto", true)
        Priest.PiTargetName_Auto_Enabled = Settings['pi_fullauto']
        Priest.PiTargetName_Auto_Toast = GetSetting("pi_fullauto_toast", true)

        -- Dynamic Variables
        Var['IsManaOK'] = (not Settings['SavingManaCheck'] or Var['ManaPct'] > Settings['SavingManaValue'])
        if not Var['IsInCombat'] then
            Var['IsInCombat'] = combatMonitor()
        end

        if Var['LightwellIsLearnt'] then
            Var['LightwellActive'] = Player:TotemIsActive(135980)
        end
    end

    local function EvaluateTrue()
        return true
    end
    local ExcludeNPCList = {
        -- Spiteful Shades
        [174773] = true
    }

    ---@param TargetUnit Unit
    local function EvaluateSpiteful(TargetUnit)
        return TargetUnit:HealthPercentage()
    end
    ---@param TargetUnit Unit
    local function IsSpiteful(TargetUnit)
        return TargetUnit:NPCID() == 174773 and TargetUnit:TimeToDie() > 2 and Player:IsTanking(TargetUnit)
    end
    ---@param TargetUnit Unit
    local function EvaluateRenew(TargetUnit)
        return TargetUnit:BuffDown(S.Renew) and TargetUnit:HealthPercentage() <= Settings['RenewHP']
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfHP(TargetUnit)
        return TargetUnit:HealthPercentage()
    end
    ---@param TargetUnit Unit
    local function EvaluatePrayerofMending(TargetUnit)
        return TargetUnit:BuffDown(S.PrayerofMendingBuff)
    end
    ---@param TargetUnit Unit
    local function EvaluatePrayerofMendingStacks(TargetUnit)
        return TargetUnit:BuffStack(S.PrayerofMendingBuff) < 8
    end
    ---@param TargetUnit Unit
    local function EvaluatePrayerofMendingOOC(TargetUnit)
        return TargetUnit:BuffDown(S.PrayerofMendingBuff)
    end
    ---@param TargetUnit Unit
    local function EvaluateFlashHeal(TargetUnit)
        return TargetUnit:HealthPercentage() <= Settings['FlashHealHP']
    end
    ---@param TargetUnit Unit
    local function EvaluateHealLightweaver(TargetUnit)
        return Player:BuffStack(S.LightweaverBuff) >= 3 and (TargetUnit:HealthPercentage() <= Settings['LWHealHP'] or TargetUnit:HealthPercentage() <= Settings['HealHP'])
    end
    ---@param TargetUnit Unit
    local function EvaluateHeal(TargetUnit)
        return TargetUnit:HealthPercentage() <= Settings['HealHP']
    end
    ---@param TargetUnit Unit
    local function EvaluateSerinity(TargetUnit)
        return TargetUnit:RealHealthPercentage() <= Settings['HolyWordSerenityHP']
    end
    ---@param TargetUnit Unit
    local function EvaluatePowerWordLife(TargetUnit)
        return TargetUnit:RealHealthPercentage() < 35
    end
    ---@param TargetUnit Unit
    local function EvaluateGSTanks(TargetUnit)
        return TargetUnit:RealHealthPercentage() <= Settings['GSTanksHP']
    end
    ---@param TargetUnit Unit
    local function EvaluateGSMembers(TargetUnit)
        return TargetUnit:RealHealthPercentage() <= Settings['GSMembersHP']
    end
    ---@param TargetUnit Unit
    local function EvaluateSWP(TargetUnit)
        return (TargetUnit:DebuffRefreshable(S.ShadowWordPainDebuff)) and TargetUnit:TimeToDie() > 16
        and not ExcludeNPCList[TargetUnit:NPCID()]
    end
    ---@param TargetUnit Unit
    local function EvaluateSWD(TargetUnit)
        return (TargetUnit:HealthPercentage() < 20)
    end
    ---@param TargetUnit Unit
    local function EvaluatePWSBS(TargetUnit)
        return TargetUnit:IsUnit(Player)
    end
    ---@param TargetUnit Unit
    local function EvaluateVoidShiftTargetIf(TargetUnit)
        return TargetUnit:RealHealthPercentage()
    end
    ---@param TargetUnit Unit
    local function EvaluateVoidShiftSelf(TargetUnit)
        return not TargetUnit:IsUnit(Player) and TargetUnit:RealHealthPercentage() - Player:RealHealthPercentage() >= Settings['VoidShift_Difference']
    end
    ---@param TargetUnit Unit
    local function EvaluateFlashHealPremonition(TargetUnit)
        return TargetUnit:HealthPercentage() <= Settings['PoPietySTHP']
    end
    ---@param TargetUnit Unit
    local function EvaluateVoidShiftMembers(TargetUnit)
        return not TargetUnit:IsUnit(Player) and TargetUnit:RealHealthPercentage() <= Settings['VoidShift_Members_spin'] and Player:RealHealthPercentage() - TargetUnit:RealHealthPercentage() >= Settings['VoidShift_Difference']
    end

    ------ Power Infusion Evaluation Functions ------
    
    ---@param TargetUnit Unit
    -- Basic Power Infusion evaluation
    local function EvaluatePI(TargetUnit)
        return TargetUnit:BuffDown(S.PowerInfusionBuff, true)
        and (not TargetUnit:IsUnit(Player) or not IsInGroup())
        and TargetUnit:CurrentTarget()
    end
    
    ---@param TargetUnit Unit
    -- Power Infusion evaluation with toggle preference
    local function EvaluatePIToggle(TargetUnit)
        return TargetUnit:BuffDown(S.PowerInfusionBuff, true)
        and TargetUnit:Name() == M.Priest.PiTargetName 
        and TargetUnit:CurrentTarget()
    end
    
    ---@param TargetUnit Unit
    local function EvaluatePIAuto(TargetUnit)
        if TargetUnit:Name() ~= Priest.PiTargetName_Auto then
            return false
        end

        if TargetUnit:BuffUp(S.PowerInfusionBuff, true) then
            Priest.PiTargetName_Auto = ""
            return false
        end

        return true
    end

    local function Utilities()
        if S.PowerWordFortitude:IsReady(Player) and (Player:BuffDown(S.PowerWordFortitude, true) or M.GroupBuffMissing(S.PowerWordFortitude)) then
            if Cast(S.PowerWordFortitude) then
                return 'Utilities: Power Word: Fortitude'
            end
        end

        if Var['ManaPct'] <= 30 then
            if S.ArcaneTorrent:IsReady() then
                if Cast(S.ArcaneTorrent) then
                    return "Arcane Torrent";
                end
            end
        end

        if Var['IsInCombat'] and not Var['SpiritofRedemption'] then
            -- Use Symbol of Hope frequently when Desperate Prayer is on cooldown or your allies' defensives are on cooldown
            if S.SymbolofHope:IsReady(Player) and Var['ManaPct'] <= Settings['SymbolofHopeMP'] then
                if Cast(S.SymbolofHope) then
                    return "Symbol of Hope"
                end
            end

            if Var['ManaPct'] <= Settings['ShadowfiendMana'] then
                if S.Shadowfiend:IsReady(Player) then
                    if Cast(S.Shadowfiend) then
                        return 'Shadowfiend'
                    end
                end
            end

            if not Var['Fade'] and not Var['Shadowmeld'] then
                if Player:IsInPartyOrRaid() then
                    if Player:IsTankingAoE(40) then
                        if S.Fade:IsReady(Player) then
                            if Cast(S.Fade) then
                                return 'Fade'
                            end
                        end
                    end
                end
            end
        end

        if not Var['BodyAndSoulBuff'] and not Var['AngelicFeatherBuff'] then
            if Settings['bsoul_check'] and S.BodyAndSoul:IsAvailable() and S.PowerWordShield:IsReady(Player) then
                if Player:IsMovingFor() > Settings['bsoul_spin'] then
                    if CastCycleAlly(S.PowerWordShield, Members, EvaluatePWSBS) then
                        return "Power Word: Shield - Body and Soul"
                    end
                end
            elseif Settings['angelic_check'] and S.AngelicFeather:IsReady(Player) then
                if Player:IsMovingFor() > Settings['angelic_spin'] then
                    if Cast(S.AngelicFeather) then
                        return "Angelic Feather";
                    end
                end
            end
        end

        if not Var['IsInCombat'] then
            if Target:IsDeadOrGhost() and Target:IsInParty() and Target:IsAPlayer() and not Target:IsEnemy() then
                if S.Resurrection:IsReady(Player) then
                    if Cast(S.Resurrection) then
                        return 'Resurrection';
                    end
                end
            end
        end
    end

    local function Items()
        -- if I.IridaltheEarthsMaster:IsEquippedAndReady() and Target:HealthPercentage() < 35 then
        --     if Cast(I.IridaltheEarthsMaster) then return "iridal_the_earths_master"; end
        -- end

        -- if I.Dreambinder:IsEquippedAndReady() then
        --     if MainAddon.SetTopTexture(1, "Weapon On-Use") then return "dreambinder_loom_of_the_great_cycle"; end
        -- end
    end

    local function Defensives()
        if Player:RealHealthPercentage() <= Settings['DesperatePrayerHP'] and S.DesperatePrayer:IsReady(Player) then
            -- Berserking
            if S.Berserking:IsReady() then
                if Cast(S.Berserking, true) then
                    return "Berserking";
                end
            end
            -- Blood Fury
            if S.BloodFury:IsReady() then
                if Cast(S.BloodFury, true) then
                    return "Blood Fury";
                end
            end
            if Cast(S.DesperatePrayer, true) then
                return "Desperate Prayer"
            end
        end

        local Should = Player:ShouldUseDefensive();
        if Should then
           if S.PowerWordShield:IsReady(Player) then
               if CastAlly(S.PowerWordShield, Player) then
                   return "Power Word: Shield - Tank Buster"
               end
           end
        end
    end

    local function HealingSpecial()
		local shouldHeal, isReadyToBeHealed, type = HealingEngine.ShouldHealTargetOrMouseover()
		if shouldHeal and isReadyToBeHealed then
			if Focus:IsInRange(40) then
                -- Flash with Surge of Light Buff
                if Player:BuffUp(S.SurgeofLightBuff) then
                    if S.FlashHeal:IsReady(Focus) then
                        if CastAlly(S.FlashHeal, Focus) then
                            return "Special Healing: Flash Heal x Surge of Light"
                        end
                    end
                end

                -- Prayer of Mending
                if S.PrayerofMending:IsReady(Focus) and EvaluatePrayerofMendingOOC(Focus) then
                    if CastAlly(S.PrayerofMending, Focus) then
                        return "Special Healing: Prayer of Mending"
                    end
                end
                -- Renew
                if S.Renew:IsReady(Focus) and Focus:BuffDown(S.Renew) then
                    if CastAlly(S.Renew, Focus) then
                        return "Special Healing: Renew"
                    end
                end
                -- Flash Heal
                if S.FlashHeal:IsReady(Focus) then
                    if CastAlly(S.FlashHeal, Focus) then
                        return "Special Healing: Flash Heal"
                    end
                end
			end
		elseif not isReadyToBeHealed then
			if type == "MouseOver" then
				MainAddon.SetTopColor(1, "Focus Mouseover")
			elseif type == "Target" then
				MainAddon.SetTopColor(1, "Focus Target")
			end
		end
    end

    local function DamageIncoming()
        if Player:BuffUp(S.ResonantWords) and not (Player:IsCasting(S.FlashHeal) or Player:IsCasting(S.Heal) or Player:IsCasting(S.CircleofHealing)) then
            -- Flash Heal
            if S.FlashHeal:IsReady(Player) then
                if CastTargetIfAlly(S.FlashHeal, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                    return "Damage Incoming: Flash Heal"
                end
            end
        end
        if HealingEngine:MembersUnderPercentage(65) >= 3 then
            if S.HolyWordSerenity:ChargesFractional() <= 1 and S.HolyWordSanctify:ChargesFractional() <= 1 then
                if S.Apotheosis:IsReady(Player) then
                    if Cast(S.Apotheosis) then
                        return "Damage Incoming: Apotheosis"
                    end
                end
            end
        end
        if S.HolyWordSanctify:ChargesFractional() > S.HolyWordSerenity:ChargesFractional() then
            if S.HolyWordSanctify:IsReady(Player) then
                local magicgroundspell_target = Settings['magicgroundspell']

                if magicgroundspell_target == 1 then
                    if #Members > 0 and Var['LowestUnit'] then
                        if CastMagicAlly(S.HolyWordSanctify, Var['LowestUnit'], nil, "34861-Magic") then 
                            return "Damage Incoming: Holy Word Sanctify - Friend"
                        end
                    end
                end

                if magicgroundspell_target == 2 then
                    if Var['TargetIsValid'] then
                        if CastMagic(S.HolyWordSanctify, nil, "34861-Magic") then 
                            return "Damage Incoming: Holy Word Sanctify - Enemy"
                        end
                    end
                end

                if magicgroundspell_target == 3 then
                    if Cast(S.HolyWordSanctify) then
                        return "Damage Incoming: Holy Word Sanctify - Regular"
                    end
                end
            end
        end
        if S.HolyWordSanctify:IsReady(Player) then
            local magicgroundspell_target = Settings['magicgroundspell']

            if magicgroundspell_target == 1 then
                if #Members > 0 and Var['LowestUnit'] then
                    if CastMagicAlly(S.HolyWordSanctify, Var['LowestUnit'], nil, "34861-Magic") then 
                        return "Damage Incoming: Holy Word Sanctify - Friend"
                    end
                end
            end

            if magicgroundspell_target == 2 then
                if Var['TargetIsValid'] then
                    if CastMagic(S.HolyWordSanctify, nil, "34861-Magic") then 
                        return "Damage Incoming: Holy Word Sanctify - Enemy"
                    end
                end
            end

            if magicgroundspell_target == 3 then
                if Cast(S.HolyWordSanctify) then
                    return "Damage Incoming: Holy Word Sanctify - Regular"
                end
            end
        end
    end

    -- Manages Power Infusion targeting and usage
    local function PowerInfusion()
        -- Only proceed if Power Infusion is ready and fight will last long enough
        if not S.PowerInfusion:IsReady(Player) or FightRemains <= 15 then
            return false
        end
        
        if Settings['pi_fullauto'] then
            local piName = Priest.PiTargetName_Auto or ""
            if piName ~= "" then
                local now = GetTime()
                if now - Priest.PILastSuggestionTime < 1.5 then
                    return false
                end

                if CastCycleAlly(S.PowerInfusion, Priest.MembersPIAuto, EvaluatePIAuto) then
                    if Priest.PiTargetName_Auto_Toast then
                        MainAddon.UI:ShowToast("Power Infusion", "Cast on " .. piName, MainAddon.GetTexture(S.PowerInfusion))
                    end
                    return "Cooldowns: Auto Power Infusion on " .. piName
                end
            end
        else
            if Priest.PiTargetName == "None" then
                if CastCycleAlly(S.PowerInfusion, Priest.MembersPI, EvaluatePI) then
                    return "Cooldowns: Power Infusion (Auto Target)"
                end
            else
                if CastCycleAlly(S.PowerInfusion, Priest.MembersPI, EvaluatePIToggle) then
                    return "Cooldowns: Power Infusion on " .. Priest.PiTargetName
                end
            end
        end
    end

    local function HealingCDs()          

        if S.GuardianSpirit:IsReady(Player) then
            local delay = GetSetting('GSDelay', 0.5)
            if CastCycleAlly(S.GuardianSpirit, Tanks, EvaluateGSTanks, nil, true, nil, delay) then
                -- Berserking
                if S.Berserking:IsReady() then
                    if Cast(S.Berserking, true) then
                        return "Berserking";
                    end
                end
                -- Blood Fury
                if S.BloodFury:IsReady() then
                    if Cast(S.BloodFury, true) then
                        return "Blood Fury";
                    end
                end
                return "Guardian Spirit - Tank"
            end
            if Settings['GSMembersHPCheck'] then
                if CastCycleAlly(S.GuardianSpirit, Members, EvaluateGSMembers, nil, true, nil, delay) then
                    -- Berserking
                    if S.Berserking:IsReady() then
                        if Cast(S.Berserking, true) then
                            return "Berserking";
                        end
                    end
                    -- Blood Fury
                    if S.BloodFury:IsReady() then
                        if Cast(S.BloodFury, true) then
                            return "Blood Fury";
                        end
                    end
                    return "Guardian Spirit - Members"
                end
            end
        end

        if not Var['SpiritofRedemption'] then
            if S.VampiricEmbrace:IsReady(Player) then
                if Var['AverageHPInRange'] <= Settings['VE_groupHP'] then
                    -- Berserking
                    if S.Berserking:IsReady() then
                        if Cast(S.Berserking, true) then
                            return "Berserking";
                        end
                    end
                    -- Blood Fury
                    if S.BloodFury:IsReady() then
                        if Cast(S.BloodFury, true) then
                            return "Blood Fury";
                        end
                    end
                    if Cast(S.VampiricEmbrace, true) then
                        return "Vampiric Embrace"
                    end
                end
            end
        end

        if S.VoidShift:IsReady(Player) then
            -- Void Shift Self.
            if Player:RealHealthPercentage() <= Settings['VoidShift_Self'] then
                if CastTargetIfAlly(S.VoidShift, TargetIfAlly, "max", EvaluateVoidShiftTargetIf, EvaluateVoidShiftSelf, true) then
                    return "Void Shift"
                end
            end

            -- Void Shift Member.
            if Settings['VoidShift_Members_check'] then
                if CastTargetIfAlly(S.VoidShift, TargetIfAlly, "min", EvaluateVoidShiftTargetIf, EvaluateVoidShiftMembers, true) then
                    return "Void Shift"
                end
            end
        end

        if S.HolyWordSalvation:IsReady(Player) then
        if HealingEngine:MembersUnderPercentage(Settings['Salvation_underX_val']) >= Settings['Salvation_underX'] then
                -- Berserking
                if S.Berserking:IsReady() then
                    if Cast(S.Berserking, true) then
                        return "Berserking";
                    end
                end
                -- Blood Fury
                if S.BloodFury:IsReady() then
                    if Cast(S.BloodFury, true) then
                        return "Blood Fury";
                    end
                end
                if Cast(S.HolyWordSalvation) then
                    return "Holy Word: Salvation"
                end
            end
            if Var['AverageHPInRange'] <= Settings['HolyWordSalvationHP'] then
                -- Berserking
                if S.Berserking:IsReady() then
                    if Cast(S.Berserking, true) then
                        return "Berserking";
                    end
                end
                -- Blood Fury
                if S.BloodFury:IsReady() then
                    if Cast(S.BloodFury, true) then
                        return "Blood Fury";
                    end
                end
                if Cast(S.HolyWordSalvation) then
                    return "Holy Word: Salvation"
                end
            end
        end

        if not Var['SpiritofRedemption'] then
            if S.Apotheosis:IsReady(Player) then
                if HealingEngine:MembersUnderPercentage(Settings['Apotheosis_underX_val']) >= Settings['Apotheosis_underX'] then
                    -- Berserking
                    if S.Berserking:IsReady() then
                        if Cast(S.Berserking, true) then
                            return "Berserking";
                        end
                    end
                    -- Blood Fury
                    if S.BloodFury:IsReady() then
                        if Cast(S.BloodFury, true) then
                            return "Blood Fury";
                        end
                    end
                    if Cast(S.Apotheosis) then
                        return "Apotheosis"
                    end
                end
                if Var['AverageHPInRange'] <= Settings['ApotheosisHP'] then
                    -- Berserking
                    if S.Berserking:IsReady() then
                        if Cast(S.Berserking, true) then
                            return "Berserking";
                        end
                    end
                    -- Blood Fury
                    if S.BloodFury:IsReady() then
                        if Cast(S.BloodFury, true) then
                            return "Blood Fury";
                        end
                    end
                    if Cast(S.Apotheosis) then
                        return "Apotheosis"
                    end
                end
            end

            if S.DivineHymn:IsReady(Player) then
                if HealingEngine:MembersUnderPercentage(Settings['DHHP_underX_val']) >= Settings['DHHP_underX'] then
                    -- Berserking
                    if S.Berserking:IsReady() then
                        if Cast(S.Berserking, true) then
                            return "Berserking";
                        end
                    end
                    -- Blood Fury
                    if S.BloodFury:IsReady() then
                        if Cast(S.BloodFury, true) then
                            return "Blood Fury";
                        end
                    end
                    if Cast(S.DivineHymn) then
                        return "Divine Hymn"
                    end
                end
                if Var['AverageHPInRange'] <= Settings['DivineHymnHP'] then
                    -- Berserking
                    if S.Berserking:IsReady() then
                        if Cast(S.Berserking, true) then
                            return "Berserking";
                        end
                    end
                    -- Blood Fury
                    if S.BloodFury:IsReady() then
                        if Cast(S.BloodFury, true) then
                            return "Blood Fury";
                        end
                    end
                    if Cast(S.DivineHymn) then
                        return "Divine Hymn"
                    end
                end
            end

            if S.Lightwell:IsReady(Player) then
                if Var['AverageHPInRange'] <= Settings['LightwellHP'] or HealingEngine:MembersUnderPercentage(Settings['Lightwell_underX_val']) >= Settings['Lightwell_underX'] then
                    if Cast(S.Lightwell) then
                        return "Lightwell"
                    end
                end
            end
        end
    end

    local function DivineWord()
        if Player:BuffUp(S.DivineWord) then
            if Settings['dw'] == 'dw_auto' then
                if Player:IsInRaid() then
                    if S.HolyWordSanctify:IsReady(Player) then
                        local magicgroundspell_target = Settings['magicgroundspell']

                        if magicgroundspell_target == 1 then
                            if #Members > 0 and Var['LowestUnit'] then
                                if CastMagicAlly(S.HolyWordSanctify, Var['LowestUnit'], nil, "34861-Magic") then 
                                    return "Holy Word Sanctify x Divine Word - Friend"
                                end
                            end
                        end
        
                        if magicgroundspell_target == 2 then
                            if Var['TargetIsValid'] then
                                if CastMagic(S.HolyWordSanctify, nil, "34861-Magic") then 
                                    return "Holy Word Sanctify x Divine Word - Enemy"
                                end
                            end
                        end
        
                        if magicgroundspell_target == 3 then
                            if Cast(S.HolyWordSanctify) then
                                return "Holy Word Sanctify x Divine Word - Regular"
                            end
                        end
                    end
                else
                    if Var['LowestHP'] > Settings['HolyWordSerenityHP'] then
                        if Var['TargetIsValid'] and S.HolyWordChastise:IsReady(Target) then
                            if Cast(S.HolyWordChastise) then
                                return "Holy Word: Chastise x Divine Word"
                            end
                        elseif Player:BuffRemains(S.DivineWord) <= (Player:GCD() * 1.5) then
                            if S.HolyWordSerenity:IsReady(Player) then
                                if CastTargetIfAlly(S.HolyWordSerenity, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                                    return "Holy Word: Serenity x Divine Word fallback"
                                end
                            else
                                if S.HolyWordSanctify:IsReady(Player) then
                                    local magicgroundspell_target = Settings['magicgroundspell']

                                    if magicgroundspell_target == 1 then
                                        if #Members > 0 and Var['LowestUnit'] then
                                            if CastMagicAlly(S.HolyWordSanctify, Var['LowestUnit'], nil, "34861-Magic") then 
                                                return "Holy Word Sanctify x Divine Word Fallback - Friend"
                                            end
                                        end
                                    end
                    
                                    if magicgroundspell_target == 2 then
                                        if Var['TargetIsValid'] then
                                            if CastMagic(S.HolyWordSanctify, nil, "34861-Magic") then 
                                                return "Holy Word Sanctify x Divine Word Fallback - Enemy"
                                            end
                                        end
                                    end
                    
                                    if magicgroundspell_target == 3 then
                                        if Cast(S.HolyWordSanctify) then
                                            return "Holy Word Sanctify x Divine Word Fallback - Regular"
                                        end
                                    end
                                end
                            end
                        end
                    else
                        if S.HolyWordSerenity:IsReady(Player) then
                            if CastTargetIfAlly(S.HolyWordSerenity, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                                return "Holy Word: Serenity x Divine Word"
                            end
                        end
                    end
                end
            end
            if Settings['dw'] == 'dw_ser' then
                if S.HolyWordSerenity:IsReady(Player) then
                    if CastTargetIfAlly(S.HolyWordSerenity, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                        return "Holy Word: Serenity x Divine Word"
                    end
                end
            end
            if Settings['dw'] == 'dw_san' then
                if S.HolyWordSanctify:IsReady(Player) then
                    local magicgroundspell_target = Settings['magicgroundspell']

                    if magicgroundspell_target == 1 then
                        if #Members > 0 and Var['LowestUnit'] then
                            if CastMagicAlly(S.HolyWordSanctify, Var['LowestUnit'], nil, "34861-Magic") then 
                                return "Holy Word Sanctify x Divine Word - Friend"
                            end
                        end
                    end
    
                    if magicgroundspell_target == 2 then
                        if Var['TargetIsValid'] then
                            if CastMagic(S.HolyWordSanctify, nil, "34861-Magic") then 
                                return "Holy Word Sanctify x Divine Word - Enemy"
                            end
                        end
                    end
    
                    if magicgroundspell_target == 3 then
                        if Cast(S.HolyWordSanctify) then
                            return "Holy Word Sanctify x Divine Word - Regular"
                        end
                    end
                end
            end
            if Settings['dw'] == 'dw_cha' then
                if Var['TargetIsValid'] and S.HolyWordChastise:IsReady(Target) then
                    if Cast(S.HolyWordChastise) then
                        return "Holy Word: Chastise x Divine Word"
                    end
                elseif Player:BuffRemains(S.DivineWord) <= (Player:GCD() * 1.5) then
                    if S.HolyWordSerenity:IsReady(Player) then
                        if CastTargetIfAlly(S.HolyWordSerenity, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                            return "Holy Word: Serenity x Divine Word fallback"
                        end
                    else
                        if S.HolyWordSanctify:IsReady(Player) then
                            local magicgroundspell_target = Settings['magicgroundspell']

                            if magicgroundspell_target == 1 then
                                if #Members > 0 and Var['LowestUnit'] then
                                    if CastMagicAlly(S.HolyWordSanctify, Var['LowestUnit'], nil, "34861-Magic") then 
                                        return "Holy Word Sanctify x Divine Word fallback - Friend"
                                    end
                                end
                            end
            
                            if magicgroundspell_target == 2 then
                                if Var['TargetIsValid'] then
                                    if CastMagic(S.HolyWordSanctify, nil, "34861-Magic") then 
                                        return "Holy Word Sanctify x Divine Word fallback - Enemy"
                                    end
                                end
                            end
            
                            if magicgroundspell_target == 3 then
                                if Cast(S.HolyWordSanctify) then
                                    return "Holy Word Sanctify x Divine Word fallback - Regular"
                                end
                            end
                        end
                    end
                end
            end
        end
        
        if S.DivineWord:IsReady(Player) then
            if Settings['dw'] == 'dw_auto' then
                if Player:IsInRaid() then
                    if S.HolyWordSanctify:IsReady(Player) then
                        if Var['AverageHPInRange'] <= Settings['HolyWordSanctifyHP'] or HealingEngine:MembersUnderPercentage(Settings['Sanctify_underX_val']) >= Settings['Sanctify_underX'] then
                            if Cast(S.DivineWord, true) then
                                return "Divine Word x Holy Word: Sanctify"
                            end
                        end
                    end
                else
                    if Var['LowestHP'] <= Settings['HolyWordSerenityHP'] then
                        if S.HolyWordSerenity:IsReady(Player) then
                            if Cast(S.DivineWord, true) then
                                return "Divine Word x Holy Word: Serenity"
                            end
                        end
                    elseif Var['TargetIsValid'] and S.HolyWordChastise:IsReady(Target) and S.HolyFire:CooldownDown() and Target:TimeToDie() > 10 then
                        if Cast(S.DivineWord, true) then
                            return "Divine Word x Holy Word: Chastise"
                        end
                    end
                end
            end
            if Settings['dw'] == 'dw_ser' then
                if S.HolyWordSerenity:IsReady(Player) then
                    if Var['LowestHP'] <= Settings['HolyWordSerenityHP'] then
                        if Cast(S.DivineWord, true) then
                            return "Divine Word x Holy Word: Serenity"
                        end
                    end
                end
            end
            if Settings['dw'] == 'dw_san' then
                if S.HolyWordSanctify:IsReady(Player) then
                    if Var['AverageHPInRange'] <= Settings['HolyWordSanctifyHP'] or HealingEngine:MembersUnderPercentage(Settings['Sanctify_underX_val']) >= Settings['Sanctify_underX'] then
                        if Cast(S.DivineWord, true) then
                            return "Divine Word x Holy Word: Sanctify"
                        end
                    end
                end
            end
            if Settings['dw'] == 'dw_cha' then
                if Var['TargetIsValid'] and S.HolyWordChastise:IsReady(Target) and S.HolyFire:CooldownDown() and Target:TimeToDie() > 10 then
                    if Cast(S.DivineWord, true) then
                        return "Divine Word x Holy Word: Chastise"
                    end
                end
            end
        end
    end

    local function PremonitionOfPiety() 
        -- PremonitionOfPiety logic
        if S.PremonitionOfPiety:IsReady(Player) then
            -- AoE Healing
            if HealingEngine:MembersUnderPercentage(Settings['PoPiety_underX_val']) >= Settings['PoPiety_underX'] then
                if Cast(S.PremonitionOfPiety, true) then
                    return " treshold reached (AoE)"
                end
            end
            -- Emergency Healing
            if Var['LowestHP'] <= Settings['PoPietySTHP'] then
                if Cast(S.PremonitionOfPiety, true) then
                    return " Emergency treshold reached (ST)"
                end
            end
        end
    end    
    
    local function PremonitionOfPietyReady()
        -- PremonitionOfPiety buff check
        if Player:BuffUp(S.PremonitionOfPiety) then
            -- check if the required number of members are under the health percentage
            if HealingEngine:MembersUnderPercentage(Settings['PoPiety_underX_val']) >= Settings['PoPiety_underX'] then
                -- cast Guardian Spirit on the player if it's ready
                if S.GuardianSpirit:IsReady(Player) then
                    if CastAlly(S.GuardianSpirit, Player) then
                        return "Piety: Guardian Spirit on self"
                    end
                end
                -- if close to capping Holy Word: Serenity
                if Player:BuffUp(S.GuardianSpirit) and S.HolyWordSerenity:ChargesFractional() > 1.7 and S.HolyWordSerenity:IsReady(Player) then
                    if CastAlly(S.HolyWordSerenity, Player) then
                        return "Piety: GA x Holy Word: Serenity"
                    end
                end
                -- if Guardian Spirit is up and our mana is above 50%, spam Flash Heal on ourself
                if Player:BuffUp(S.GuardianSpirit) and Player:ManaPercentage() > 50 then
                    if S.FlashHeal:IsReady(Player) then
                        if CastAlly(S.FlashHeal, Player) then
                            return "Piety: GA x Spamming Flash Heal"
                        end
                    end
                end
                -- if Guardian Spirit is up and our mana is below or equal to 50%, spam Heal on ourself
                if Player:BuffUp(S.GuardianSpirit) and Player:ManaPercentage() <= 50 then
                    if S.Heal:IsReady(Player) then
                        if CastAlly(S.Heal, Player) then
                            return "Piety: GA x Spamming Heal"
                        end
                    end
                end
            end
            
            -- Emergency Healing Part - were at two stacks or close to capping Holy Word: Serenity (ST)
            if Var['LowestHP'] <= Settings['PoPietySTHP'] and S.HolyWordSerenity:ChargesFractional() > 1.7 and S.HolyWordSerenity:IsReady(Player) then
                if CastCycleAlly(S.HolyWordSerenity, Members, EvaluateSerinity) then
                    return "Emergency Healing - HolyWordSerenity"
                end
            end
            -- Emergency Healing Part (ST)
            if Var['LowestHP'] <= Settings['PoPietySTHP'] and S.FlashHeal:IsReady(Player) then
                if CastCycleAlly(S.FlashHeal, Members, EvaluateFlashHealPremonition) then
                    return "Emergency Healing - FlashHeal"
                end
            end
        end
    end      

    local function PremonitionOfClairvoyance()
        -- PremonitionOfClairvoyance logic
        if S.PremonitionOfClairvoyance:IsReady(Player) then
            -- Emergency Healing
            if Var['LowestHP'] <= Settings['PoClairSTHP'] then
                if Cast(S.PremonitionOfClairvoyance, true) then
                    return "Threshold reached (ST)"
                end
            end
        end
    end    

    local function PremonitionOfInsight()
        local PoMCDRemains = S.PrayerofMending:CooldownRemains(Player)
        local HolyFireCDRemains = S.HolyFire:CooldownRemains(Player)

        -- PremonitionOfInsight logic
        if S.PremonitionOfInsight:IsReady(Player) then
            if Settings['PINSIGHT'] == 1 then
                if Var['LowestHP'] < 70 and PoMCDRemains < 2 then
                    if Cast(S.PremonitionOfInsight, true) then
                        return "healing (Auto)"
                    end
                elseif Var['LowestHP'] >= 70 and HolyFireCDRemains < 2 then
                    if Cast(S.PremonitionOfInsight, true) then
                        return "dmg (Auto)"
                    end
                end
            elseif Settings['PINSIGHT'] == 2 then
                if PoMCDRemains < 2 then
                    if Cast(S.PremonitionOfInsight, true) then
                        return "healing"
                    end
                end
            elseif Settings['PINSIGHT'] == 3 then
                if Var['TargetIsValid'] and HolyFireCDRemains < 2 then
                    if Cast(S.PremonitionOfInsight, true) then
                        return "dmg"
                    end
                end
            end
        end
    end    

    local function PremonitionOfInsightReady()
        ---PremonitionOfInsight execute
        if Settings['PINSIGHT'] == 1 then
            if Var['LowestHP'] < 70 then
                if CastTargetIfAlly(S.PrayerofMending, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluatePrayerofMendingStacks) and S.PrayerofMending:IsReady(Player) then
                    return " healing (Auto)"
                end
            elseif Var['LowestHP'] >= 70 and S.HolyFire:IsReady(Player) then
                if Cast(S.HolyFire) then
                    return " dmg (Auto)"
                end
            end
        end
    
        if Settings['PINSIGHT'] == 2 then
            if S.PrayerofMending:IsReady(Player) then
                if CastTargetIfAlly(S.PrayerofMending, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluatePrayerofMendingStacks) then
                    return " healing"
                end
            end
        end
    
        if Settings['PINSIGHT'] == 3 then
            if Var['TargetIsValid'] and S.HolyFire:IsReady() then
                if Cast(S.HolyFire) then
                    return " dmg"
                end
            end
        end
    end    

    local function HealingRotation()
        if Var['IsInCombat'] then
            ShouldReturn = DivineWord()
            if ShouldReturn then
                return ShouldReturn
            end
        end

        -- Power Word: Life
        if S.PowerWordLife:IsReady(Player) then
            if CastCycleAlly(S.PowerWordLife, Members, EvaluatePowerWordLife) then
                return "Power Word: Life"
            end
        end

        -- Cast Prayer of Mending on cooldown on boss fights but abstain on trash packs unless the group is taking constant damage
        if S.PrayerofMending:IsReady(Player) then
            if CastCycleAlly(S.PrayerofMending, Tanks, EvaluatePrayerofMendingStacks) then
                return "Prayer of Mending - Tank"
            end
            if HealingEngine:MembersUnderPercentage(Settings['PoM_underX_val']) >= Settings['PoM_underX'] then
                if CastTargetIfAlly(S.PrayerofMending, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluatePrayerofMendingStacks) then
                    return "Prayer of Mending - HP value reached"
                end
            end
            if Var['AverageHPInRange'] <= Settings['PrayerofMendingHP'] then
                if CastTargetIfAlly(S.PrayerofMending, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluatePrayerofMendingStacks) then
                    return "Prayer of Mending - average HP reached"
                end
            end
            if not Var['IsInCombat'] and Player:IsInPvEActivity() then
                if CastTargetIfAlly(S.PrayerofMending, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluatePrayerofMendingOOC) then
                    return "Prayer of Mending - OOC"
                end
            end
        end

        -- Holy Word: Serenity
        if S.HolyWordSerenity:IsReady(Player) then
            -- Premonition of Solace first if ready
            if S.PremonitionOfSolace:IsReady(Player) then
                if Cast(S.PremonitionOfSolace) then
                    -- Holy Word: Serenity after Premonition of Solace
                    if CastCycleAlly(S.HolyWordSerenity, Members, EvaluateSerinity) then
                        return "Premonition of Solace before Holy Word: Serenity"
                    end
                end
            else
                -- If Premonition of Solace isn't ready, cast Holy Word: Serenity directly.
                if CastCycleAlly(S.HolyWordSerenity, Members, EvaluateSerinity) then
                    return "Holy Word: Serenity"
                end
            end
        end

        -- Halo
        if S.Halo:IsReady(Player) then
            -- check if the 'Divine Star / Halo DPS only' option is enabled
            if Settings['star_dps_only'] then
                -- cast Halo for DPS only if there's a valid target and we're in combat
                if Var['TargetIsValid'] and Var['IsInCombat'] and Target:IsInRange(40) then
                    if Cast(S.Halo) then
                        return "Halo (DPS)"
                    end
                end
            else
                -- original healing logic
                if Settings['halo_HP_check'] and HealingEngine:LowestHP(true, 30) <= Settings['halo_HP_spin'] then
                    if Cast(S.Halo) then
                        return "Halo"
                    end
                end
            end
        end

        if Player:BuffUp(S.LightweaverBuff) then
            if S.Heal:IsReady(Player) then
                if CastCycleAlly(S.Heal, Members, EvaluateHealLightweaver) then
                    return "Heal x Lightweaver"
                end
            end
        end
        
        if Player:BuffUp(S.SurgeofLightBuff) then
            if S.FlashHeal:IsReady(Player) then
                if CastCycleAlly(S.FlashHeal, Members, EvaluateFlashHeal) then
                    return "Flash Heal x Surge of Light"
                end
            end
        end

        if Player:BuffUp(S.SurgeofLightBuff) and Player:BuffRemains(S.SurgeofLightBuff) <= (Player:GCD() * HL.Latency()) * Player:BuffStack(S.SurgeofLightBuff) then
            if S.FlashHeal:IsReady(Player) then
                if CastTargetIfAlly(S.FlashHeal, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                    return "Flash Heal x Surge of Light"
                end
            end
        end

        -- Lightweaver upkeep inside of combat
        if Player:BuffUp(S.LightweaverBuff) and Player:BuffRemains(S.LightweaverBuff) < 3.75 then
            if S.FlashHeal:IsReady(Player) and not Player:IsCasting(S.FlashHeal) then
                if CastTargetIfAlly(S.FlashHeal, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                    return "Lightweaver upkeep"
                end
            end
        end

        -- Cast Holy Word: Sanctify on cooldown for AoE healing
        if S.HolyWordSanctify:IsReady(Player) 
        and (Var['AverageHPInRange'] <= Settings['HolyWordSanctifyHP']
        or HealingEngine:MembersUnderPercentage(Settings['Sanctify_underX_val']) >= Settings['Sanctify_underX']) then
            local magicgroundspell_target = Settings['magicgroundspell']

            if magicgroundspell_target == 1 then
                if #Members > 0 and Var['LowestUnit'] then
                    if CastMagicAlly(S.HolyWordSanctify, Var['LowestUnit'], nil, "34861-Magic") then 
                        return "Holy Word Sanctify - Friend"
                    end
                end
            end

            if magicgroundspell_target == 2 then
                if Var['TargetIsValid'] then
                    if CastMagic(S.HolyWordSanctify, nil, "34861-Magic") then 
                        return "Holy Word Sanctify - Enemy"
                    end
                end
            end

            if magicgroundspell_target == 3 then
                if Cast(S.HolyWordSanctify) then
                    return "Holy Word Sanctify - Regular"
                end
            end
        end

        -- Circle of Healing
        if S.CircleofHealing:IsReady(Player) then
            if HealingEngine:MembersUnderPercentage(Settings['CoH_underX_val']) >= Settings['CoH_underX'] then
                if CastTargetIfAlly(S.CircleofHealing, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                    return "Circle of Healing"
                end
            end
        end

        -- Build stacks of Lightweaver with Flash Heal and spend with Heal
        if S.FlashHeal:IsReady(Player) then
            if CastCycleAlly(S.FlashHeal, Members, EvaluateFlashHeal) then
                return "Flash Heal"
            end
        end

        -- Holy Nova at max stacks as damage comes out for AoE healing.
        if S.HolyNova:IsReady(Player) 
        and (S.Rhapsody:IsAvailable() and Player:BuffStack(S.RhapsodyBuff) >= 20 
        and (Var['IsInCombat'] and Var['LowestHP'] < 90 or Var['LowestHP'] < 70)) then
            if Cast(S.HolyNova) then
                return "Holy Nova"
            end
        end

        -- Divine Star
        if S.DivineStar:IsReady(Player) then
            -- check if the 'Divine Star / Halo DPS only' option is enabled
            if Settings['star_dps_only'] then
                -- cast Divine Star for DPS only if there's a valid target and we're in combat
                if Var['TargetIsValid'] and Var['IsInCombat'] and Target:IsInRange(40) then
                    if Cast(S.DivineStar) then
                        return "Divine Star (DPS)"
                    end
                end
            else
                -- original healing logic
                if Settings['star_HP_check'] and HealingEngine:LowestHP(true, 30) <= Settings['star_HP_spin'] then
                    if Cast(S.DivineStar) then
                        return "Divine Star"
                    end
                end
            end
        end
    
        if S.Heal:IsReady(Player) then
            if CastCycleAlly(S.Heal, Members, EvaluateHeal) then
                return "Heal"
            end
        end

        if Player:IsMoving() then
            if S.Renew:IsReady(Player) then
                if CastCycleAlly(S.Renew, Members, EvaluateRenew) then
                    return "Renew"
                end
            end
        end

        -- Lightweaver upkeep outside of combat
        if not Var['IsInCombat'] and Player:BuffUp(S.LightweaverBuff) and Player:BuffRemains(S.LightweaverBuff) < 5 then
            if S.FlashHeal:IsReady(Player) and not Player:IsCasting(S.FlashHeal) then
                if CastTargetIfAlly(S.FlashHeal, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                    return "Lightweaver upkeep (OOC)"
                end
            end
        end
    end

    local function HealingRotationRaid()
        if Var['IsInCombat'] then
            ShouldReturn = DivineWord()
            if ShouldReturn then
                return ShouldReturn
            end  
        end

        -- Cast Prayer of Mending on cooldown.
        if S.PrayerofMending:IsReady(Player) then
            if CastCycleAlly(S.PrayerofMending, Tanks, EvaluatePrayerofMending) then
                return "Prayer of Mending - Tank"
            end
            if HealingEngine:MembersUnderPercentage(Settings['PoM_underX_val']) >= Settings['PoM_underX'] then
                if CastTargetIfAlly(S.PrayerofMending, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluatePrayerofMendingStacks) then
                    return "Prayer of Mending"
                end
            end
            if Var['AverageHPInRange'] <= Settings['PrayerofMendingHP'] then
                if CastTargetIfAlly(S.PrayerofMending, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluatePrayerofMendingStacks) then
                    return "Prayer of Mending"
                end
            end
            if not Var['IsInCombat'] and Player:IsInPvEActivity() then
                if CastTargetIfAlly(S.PrayerofMending, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluatePrayerofMendingOOC) then
                    return "Prayer of Mending"
                end
            end
        end

        -- Cast Holy Word: Sanctify on cooldown for AoE healing and Holy Word: Serenity for single-target.
        if S.HolyWordSanctify:IsReady(Player) 
        and (Var['AverageHPInRange'] <= Settings['HolyWordSanctifyHP'] 
        or HealingEngine:MembersUnderPercentage(Settings['Sanctify_underX_val']) >= Settings['Sanctify_underX']) then
            local magicgroundspell_target = Settings['magicgroundspell']

            if magicgroundspell_target == 1 then
                if #Members > 0 and Var['LowestUnit'] then
                    if CastMagicAlly(S.HolyWordSanctify, Var['LowestUnit'], nil, "34861-Magic") then 
                        return "Holy Word Sanctify - Friend"
                    end
                end
            end

            if magicgroundspell_target == 2 then
                if Var['TargetIsValid'] then
                    if CastMagic(S.HolyWordSanctify, nil, "34861-Magic") then 
                        return "Holy Word Sanctify - Enemy"
                    end
                end
            end

            if magicgroundspell_target == 3 then
                if Cast(S.HolyWordSanctify) then
                    return "Holy Word Sanctify - Regular"
                end
            end
        end

        -- Cast Circle of Healing on cooldown as raid damage allows.
        if S.CircleofHealing:IsReady(Player) then
            if HealingEngine:MembersUnderPercentage(Settings['CoH_underX_val']) >= Settings['CoH_underX'] then
                if CastTargetIfAlly(S.CircleofHealing, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                    return "Circle of Healing"
                end
            end
        end

        -- Lightweaver upkeep inside of combat
        if Player:BuffUp(S.LightweaverBuff) and Player:BuffRemains(S.LightweaverBuff) < 3.75 then
            if S.FlashHeal:IsReady(Player) and not Player:IsCasting(S.FlashHeal) then
                if CastTargetIfAlly(S.FlashHeal, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                    return "Lightweaver upkeep"
                end
            end
        end

        -- Holy Nova at max stacks as damage comes out for AoE healing.
        if S.HolyNova:IsReady(Player) 
        and (S.Rhapsody:IsAvailable() and Player:BuffStack(S.RhapsodyBuff) >= 20 
        and (Var['IsInCombat'] and Var['LowestHP'] < 90 or Var['LowestHP'] < 70)) then
            if Cast(S.HolyNova) then
                return "Holy Nova"
            end
        end

        -- Cast Divine Star on cooldown
        if S.DivineStar:IsReady(Player) and Settings['halo_HP_check'] and HealingEngine:LowestHP(true, 30) <= Settings['halo_HP_spin'] then
            if Cast(S.DivineStar) then
                return "Divine Star"
            end
        end

        -- Cast Halo on cooldown
        if S.Halo:IsReady(Player) and Settings['star_HP_check'] and HealingEngine:LowestHP(true, 30) <= Settings['star_HP_spin'] then
            if Cast(S.Halo) then
                return "Halo"
            end
        end

        -- Holy Word: Serenity
        if S.HolyWordSerenity:IsReady(Player) then
            if CastCycleAlly(S.HolyWordSerenity, Members, EvaluateSerinity) then
                return "Holy Word: Serenity"
            end
        end

        -- Use Prayer of Healing regularly to reverse raid-wide damage.
        if S.PrayerofHealing:IsReady(Player) then
            if HealingEngine:MembersUnderPercentage(Settings['PoH_underX_val']) >= Settings['PoH_underX'] then
                if CastTargetIfAlly(S.PrayerofHealing, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                    return "Prayer of Healing"
                end
            end
        end

        -- Use Power Word: Life as needed on allies sub-35% health.
        if S.PowerWordLife:IsReady(Player) then
            if CastCycleAlly(S.PowerWordLife, Members, EvaluatePowerWordLife) then
                return "Power Word: Life"
            end
        end

        if Player:BuffUp(S.SurgeofLightBuff) then
            if S.FlashHeal:IsReady(Player) then
                if CastCycleAlly(S.FlashHeal, Members, EvaluateFlashHeal) then
                    return "Flash Heal x Surge of Light"
                end
            end
        end

        if Player:BuffUp(S.SurgeofLightBuff) and Player:BuffRemains(S.SurgeofLightBuff) <= (Player:GCD() * HL.Latency()) * Player:BuffStack(S.SurgeofLightBuff) then
            if S.FlashHeal:IsReady(Player) then
                if CastTargetIfAlly(S.FlashHeal, TargetIfAlly, "min", EvaluateTargetIfHP, EvaluateTrue) then
                    return "Flash Heal x Surge of Light"
                end
            end
        end

        if Player:BuffUp(S.LightweaverBuff) then
            if S.Heal:IsReady(Player) then
                if CastCycleAlly(S.Heal, Members, EvaluateHealLightweaver) then
                    return "Heal x Lightweaver"
                end
            end
        end

        -- Build stacks of Lightweaver with Flash Heal and spend with Heal
        if S.FlashHeal:IsReady(Player) then
            if CastCycleAlly(S.FlashHeal, Members, EvaluateFlashHeal) then
                return "Flash Heal"
            end
        end

        if S.Heal:IsReady(Player) then
            if CastCycleAlly(S.Heal, Members, EvaluateHeal) then
                return "Heal"
            end
        end
        if Player:IsMoving() then
            if S.Renew:IsReady(Player) then
                if CastCycleAlly(S.Renew, Members, EvaluateRenew) then
                    return "Renew"
                end
            end
        end
    end

    local function AmountOfUnitsWithSWP()
        local amountUnits = 0
        ---@param ThisUnit Unit   
        for _, ThisUnit in pairs(Enemies40y) do
            if ThisUnit:DebuffUp(S.ShadowWordPainDebuff) then
                amountUnits = amountUnits + 1
            end
        end
        return amountUnits
    end

    local function DamageRotation()
        -- Cast Holy Fire with Empyreal Blaze and cast Holy Fire until on cooldown.
        if S.HolyFire:IsReady() then
            if Player:BuffUp(S.EmpyrealBuff) then
                if Cast(S.HolyFire) then
                    return "Holy Fire x Empyreal Blaze"
                end
            end
        end

        -- Chastise for procc Holy Fire
        if Settings['Chastise_dps'] then
            if S.EmpyrealBlaze:IsAvailable() then
                if S.HolyFire:CooldownRemains() > 3 and not Player:BuffUp(S.DivineWord) and (not S.DivineWord:IsReady(Player) or Settings['dw'] ~= 'dw_cha' and (Settings['dw'] ~= 'dw_auto' or Player:IsInRaid())) then
                    if S.HolyWordChastise:IsReady(Target) then
                        if Cast(S.HolyWordChastise) then
                            return "Holy Word: Chastise"
                        end
                    end
                end
            end
        end

        -- Use Holy Nova for large quantities of enemies.
        if S.HolyNova:IsReady(Player) and ((EnemiesCount12y >= 1 and Player:BuffStack(S.RhapsodyBuff) >= 20) or (not S.Rhapsody:IsAvailable() and EnemiesCount12y >= 3)) then
            if Cast(S.HolyNova) then
                return "Holy Nova"
            end
        end

        -- Cast Holy Fire then activate Empyreal Blaze and cast Holy Fire until on cooldown.
        if S.HolyFire:IsReady() then
            if Cast(S.HolyFire) then
                return "Holy Fire"
            end
        end

        -- Cast Divine Star on cooldown.
        if S.DivineStar:IsReady() and not Settings['star_HP_check'] then
            if Cast(S.DivineStar) then
                return "Divine Star"
            end
        end

        -- Cast Halo on cooldown.
        if S.Halo:IsReady() and not Settings['halo_HP_check'] then
            if Cast(S.Halo) then
                return "Halo"
            end
        end

        -- Multi-DoT with Shadow Word: Pain
        if Target:DebuffRefreshable(S.ShadowWordPainDebuff) then
            if S.ShadowWordPain:IsReady() then
                if Cast(S.ShadowWordPain) then
                    return "Shadow Word: Pain"
                end
            end
        end

        if S.HolyWordChastise:CooldownRemains(nil, true) and S.VoiceofHarmony:IsAvailable() then
            if S.HolyNova:IsReady(Player) and (EnemiesCount12y >= 3) then
                if Cast(S.HolyNova) then
                    return "Holy Nova - Resets Holy Word Chastise"
                end
            end
        end

        if AmountOfUnitsWithSWP() < Settings['max_SWP'] then
            if S.ShadowWordPain:IsCastable() then
                if M.CastCycle(S.ShadowWordPain, Enemies40y, EvaluateSWP) then
                    return "Shadow Word: Pain - Cycle";
                end
            end
        end

        -- Use Shadow Word: Death to execute enemies.
        if S.ShadowWordDeath:IsCastable() then
            if M.CastCycle(S.ShadowWordDeath, Enemies40y, EvaluateSWD) then
                return "Shadow Word: Death";
            end
        end

        -- Bag Of Tricks
        if S.BagofTricks:IsReady() then
            if Cast(S.BagofTricks) then
                return "Bag Of Tricks";
            end
        end

        -- Arcane Pulse
        if S.ArcanePulse:IsReady() then
            if Cast(S.ArcanePulse) then
                return "Arcane Pulse";
            end
        end

        -- Light's Judgment
        if S.LightsJudgment:IsReady() then
            if Cast(S.LightsJudgment) then
                return "Light's Judgment";
            end
        end

        -- Holy Nova if AoE
        if S.HolyNova:IsReady(Player) and (EnemiesCount12y >= 4) then
            if Cast(S.HolyNova) then
                return "Holy Nova - Filler"
            end
        end

        -- Use Smite
        if S.Smite:IsReady() then
            if Cast(S.Smite) then
                return "Smite - Filler"
            end
        end
    end

    local function APL()
        -- Var Update
        UpdateVars()

        if AoEON() then
            Enemies12y = Player:GetEnemiesInRange(12)
            Enemies40y = Player:GetEnemiesInRange(40)
        else
            Enemies12y = { Target }
            Enemies40y = { Target }
        end
        EnemiesCount12y = #Enemies12y

        if Var['TargetIsValid'] or Var['IsInCombat'] then
            -- Calculate fight_remains
            BossFightRemains = HL.BossFightRemains()
            FightRemains = BossFightRemains
            if FightRemains == 11111 then
                FightRemains = HL.FightRemains(Enemies40y, false)
            end
            Priest.PiFightRemains = FightRemains
        end

        if Player:IsChanneling(S.DivineHymn) or Player:IsChanneling(S.SymbolofHope) then
            return
        end

        -- Trinkets
        if not Var['SpiritofRedemption'] then
            ShouldReturn = MainAddon.TrinketHealing(Members, OnUseExcludes)
            if ShouldReturn then
                return ShouldReturn
            end  
        end

        if Var['IsInCombat'] then
            -- Potion
            if MainAddon.UseManaPotion() then
                MainAddon.SetTopColor(1, "Combat Potion")
            end

            -- Defensives
            if not Var['SpiritofRedemption'] then
                ShouldReturn = Defensives()
                if ShouldReturn then
                    return "Defensives: " .. ShouldReturn
                end
            end

            if Var['TargetIsValid'] then
                ShouldReturn = Items();
                if ShouldReturn then
                    return ShouldReturn;
                end
            end
        end

        if MainAddon.Toggle:GetToggle('ForceDPS') and Var['TargetIsValid'] then
            if Var['TargetIsValid'] and Settings['highprio_holyfire'] and Player:BuffUp(S.EmpyrealBuff) then
                if S.HolyFire:IsReady() then
                    if Cast(S.HolyFire) then
                        return "Holy Fire x Empyreal Blaze - Force DPS"
                    end
                end
            end

            ShouldReturn = DamageRotation();
            if ShouldReturn then
                return "Force DPS: " .. ShouldReturn;
            end
        end

        if Var['IsInCombat'] then
            if not Player:BuffUp(S.PremonitionOfInsight) then
                -- PremonitionOfInsight
                ShouldReturn = PremonitionOfInsight()
                if ShouldReturn then
                    return "PremonitionOfInsight: " .. ShouldReturn
                end
            else
                -- PremonitionOfInsightReady
                ShouldReturn = PremonitionOfInsightReady()
                if ShouldReturn then
                    return "PremonitionOfInsight: " .. ShouldReturn
                end
            end

            if not Player:BuffUp(S.PremonitionOfPiety) then
                -- PremonitionOfPiety
                ShouldReturn = PremonitionOfPiety()
                if ShouldReturn then
                    return "PremonitionOfPiety: " .. ShouldReturn
                end
            else
                -- PremonitionOfPietyReady
                ShouldReturn = PremonitionOfPietyReady()
                if ShouldReturn then
                    return "PremonitionOfPiety: " .. ShouldReturn
                end
            end

            -- PremonitionOfClairvoyance
            if not Player:BuffUp(S.PremonitionOfClairvoyance) then
                ShouldReturn = PremonitionOfClairvoyance()
                if ShouldReturn then
                    return "PremonitionOfClairvoyance: " .. ShouldReturn
                end
            end

            -- Power Infusion
            local ShouldReturn = PowerInfusion()
            if ShouldReturn then
                return "PIn: " .. ShouldReturn
            end

            -- Healing CDs
            ShouldReturn = HealingCDs()
            if ShouldReturn then
                return "Healing CDs: " .. ShouldReturn
            end
        end

        -- Damage Incoming
        local Reason, SpellID = MainAddon:DamageIncoming()
        if Reason == "SOON" then
            MainAddon.UI:ShowToast(MainAddon.GetSpellInfo(SpellID), "Predicting major incoming damage.", MainAddon.GetTexture(Spell(SpellID)), 10)

            if HealingEngine:MembersUnderPercentage(90) >= 1 then
                ShouldReturn = DamageIncoming();
                if ShouldReturn then
                    return ShouldReturn;
                end
            end
        end      

        -- Healing Special
        ShouldReturn = HealingSpecial();
        if ShouldReturn then
            return ShouldReturn;
        end
        
        if Var['TargetIsValid'] and Settings['highprio_holyfire'] and Player:BuffUp(S.EmpyrealBuff) then
            if S.HolyFire:IsReady() then
                if Cast(S.HolyFire) then
                    return "Holy Fire x Empyreal Blaze - High prio"
                end
            end
        end            

        if Player:IsInRaid() then
            -- Healing Rotation
            ShouldReturn = HealingRotationRaid()
            if ShouldReturn then
                return "Healing Rotation: " .. ShouldReturn
            end
        else
            -- Healing Rotation
            ShouldReturn = HealingRotation()
            if ShouldReturn then
                return "Healing Rotation: " .. ShouldReturn
            end
        end

        -- Utilities
        ShouldReturn = Utilities()
        if ShouldReturn then
            return "Utilities: " .. ShouldReturn
        end

        if not Var['SpiritofRedemption'] then
            if Var['TargetIsValid'] and Var['IsManaOK'] and not MainAddon.Toggle:GetToggle('ForceHealing') then
                -- Damage Rotation
                ShouldReturn = DamageRotation()
                if ShouldReturn then
                    return "Damage Rotation: " .. ShouldReturn
                end
            end
        end        
    end

    local function Init()
    end
    M.SetAPL(257, APL, Init)

    local OldIsCastable
    OldIsCastable = HL.AddCoreOverride("Spell.IsCastable",
        function(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
            if MainAddon.PlayerSpecID() == 257 then
                if self == S.HolyWordSalvation or self == S.Apotheosis then
                    if Player:IsCasting(S.DivineHymn) then
                        return false, "Divine Hymn"
                    end
                end

                if self == S.HolyNova then
                    if not Var['IsInCombat'] and not Player:IsInDungeonArea() then
                        return false, "Not in combat"
                    end
                end

                if self == S.DominateMind then
                    if Player:IsCasting(S.DominateMind) then
                        return false, "Already casting it."
                    end
                end

                if self == S.HolyWordSanctify then
                    if S.HolyWordSanctify:TimeSinceLastCast() < 1 then
                        return false, "Already casted."
                    end
                end

                if self == S.Halo then
                    if Player:IsCasting(S.Halo) then
                        return false, "Already casting it."
                    end
                end

                if self == S.Fade then
                    if MainAddon.SpecialCase_FreedomBlacklist() then
                        return false, "Freedom Blacklist"
                    end
                end
            end
            local BaseCheck, Reason = OldIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
            return BaseCheck, Reason
        end,
    257);
end