---@class MainAddon
local MainAddon = MainAddon
-- HeroLib
local HL = HeroLibEx
---@class HeroCache
local Cache = HeroCache;
---@class Spell
local Spell = HL.Spell
local CreateSpell = MainAddon.CreateSpell
local CreateMultiSpell = MainAddon.CreateMultiSpell
---@class Item
local Item = HL.Item
local GetUnitName = _G["GetUnitName"]
local UnitInParty = _G["UnitInParty"]
local UnitInRaid = _G["UnitInRaid"]
local UnitCanAttack = _G['UnitCanAttack']
local MergeTableByKey = HL.Utils.MergeTableByKey

MainAddon.Paladin = {}
MainAddon.Paladin.WhitelistUnitFreedom = {}
---@class Paladin
local Paladin = MainAddon.Paladin

if not Spell.Paladin then
    Spell.Paladin = {}
end

---@class PLCustomTable
Spell.Paladin.Custom = {
    BloodFury = CreateMultiSpell(20572, 33702, 33697),
    AuraMastery = CreateSpell(31821),
    TurnEvil = CreateSpell(10326),
    DivineProtection = CreateMultiSpell(403876, 498),
    Redemption = CreateSpell(7328),
    RadiantGlory = CreateSpell(458359),
    BlindingLight = CreateSpell(115750),
    Intercession = CreateSpell(391054),
    Repentance = CreateSpell(20066),
    CleanseToxins = CreateSpell(213644),
    SenseUndead = CreateSpell(5502),
    ForberanceDebuff = CreateSpell(25771),
    FinalStand = CreateSpell(204077),
    GiftOfTheGoldenValKyr = CreateSpell(378279),
    BladeofVengeance = CreateSpell(403826),
    VengefulWrath = CreateSpell(406835),
    BlessingofAnsheBuff = CreateSpell(445206),
    FaithintheLight = CreateSpell(379043),
    FaithintheLightBuff = CreateSpell(379041),

    -- Blessings
    BlessingOfFreedom = CreateSpell(1044),
    BlessingOfProtection = CreateSpell(1022),
    BlessingOfSacrifice = CreateSpell(6940),
    BlessingOfSpellwarding = CreateSpell(204018),

    --PvP
    BlessingofSanctuary = CreateSpell(210256),
    -- PvP Protection
    GuardianoftheForgottenQueen = CreateSpell(228049),
    Inquisition = CreateSpell(207028),
    ShieldofVirtue = CreateSpell(215652),
}

---@class PLCommonsTable
Spell.Paladin.Commons = {
    -- Racials
    AncestralCall                         = CreateSpell(274738),
    ArcanePulse                           = CreateSpell(260364),
    ArcaneTorrent                         = CreateSpell(50613),
    BagofTricks                           = CreateSpell(312411),
    Berserking                            = CreateSpell(26297),
    BloodFury                             = CreateSpell(20572),
    Fireblood                             = CreateSpell(265221),
    GiftoftheNaaru                        = CreateSpell(59542),
    LightsJudgment                        = CreateSpell(255647),
    -- Abilities
    Consecration                          = CreateSpell(26573),
    CrusaderStrike                        = CreateSpell(35395),
    DivineShield                          = CreateSpell(642),
    DivineSteed                           = CreateSpell(190784),
    FlashofLight                          = CreateSpell(19750),
    HammerofJustice                       = CreateSpell(853),
    HandofReckoning                       = CreateSpell(62124),
    Judgment                              = CreateSpell(20271),
    Rebuke                                = CreateSpell(96231),
    ShieldoftheRighteous                  = CreateSpell(53600),
    WordofGlory                           = CreateSpell(85673),
    -- Talents
    AvengingWrath                         = CreateSpell(31884),
    HammerofWrath                         = CreateSpell(24275),
    HolyAvenger                           = CreateSpell(105809),
    LayonHands                            = CreateMultiSpell(633, 471195),
    OfDuskandDawn                         = CreateSpell(409441),
    VengefulWrath                         = CreateSpell(406835),
    ZealotsParagon                        = CreateSpell(391142),
    -- Auras
    ConcentrationAura                     = CreateSpell(317920),
    CrusaderAura                          = CreateSpell(32223),
    DevotionAura                          = CreateSpell(465),
    RetributionAura                       = CreateSpell(183435),
    -- Buffs
    AvengingWrathBuff                     = CreateSpell(31884),
    BlessingofDawnBuff                    = CreateSpell(385127),
    BlessingofDuskBuff                    = CreateSpell(385126),
    ConsecrationBuff                      = CreateSpell(188370),
    DivinePurposeBuff                     = CreateSpell(223819),
    HolyAvengerBuff                       = CreateSpell(105809),
    ShieldoftheRighteousBuff              = CreateSpell(132403),
    -- Debuffs
    ConsecrationDebuff                    = CreateSpell(204242),
    JudgmentDebuff                        = CreateSpell(197277),
    MarkofFyralathDebuff                  = CreateSpell(414532),
    -- Pool
    Pool                                  = CreateSpell(999910),
}
  
---@class HeraldoftheSunTable
Spell.Paladin.HeraldoftheSun = {
    -- Talents
    BlessingofAnshe                       = CreateSpell(445200),
    -- Buffs
    BlessingofAnsheHolyBuff               = CreateSpell(445204),
    BlessingofAnsheRetBuff                = CreateSpell(445206),
}
  
---@class LightsmithTable
Spell.Paladin.Lightsmith = {
  -- Abilities
  HolyBulwark                          = CreateSpell(432459),
  SacredWeapon                         = CreateSpell(432472),
  -- Talents
  BlessedAssurance                     = CreateSpell(433015),
  HammerandAnvil                       = CreateSpell(433718),
  HolyArmaments                        = CreateSpell(432459),
  RiteofAdjuration                     = CreateSpell(433583),
  RiteofSanctification                 = CreateSpell(433568),
  -- Buffs
  BlessedAssuranceBuff                 = CreateSpell(433019),
  DivineGuidanceBuff                   = CreateSpell(433106),
  RiteofAdjurationBuff                 = CreateSpell(433584),
  RiteofSanctificationBuff             = CreateSpell(433550),
  SacredWeaponBuff                     = CreateSpell(432502),
}
  
---@class TemplarTable
Spell.Paladin.Templar = {
    -- Talents
    HammerofLight                        = CreateSpell(427453),
    Hammerfall                           = CreateSpell(432463),
    LightsDeliverance                    = CreateSpell(425518),
    LightsGuidance                       = CreateSpell(427445),
    ShaketheHeavens                      = CreateSpell(431533),
    -- Buffs
    LightsDeliveranceBuff                = CreateSpell(433674),
    ShaketheHeavensBuff                  = CreateSpell(431536),
}

---@class PLProtectionTable
Spell.Paladin.Protection = {
    -- Abilities
    Judgment                              = CreateSpell(275779),
    -- Talents
    ArdentDefender                        = CreateSpell(31850),
    AvengersShield                        = CreateSpell(31935),
    BastionofLight                        = CreateSpell(378974),
    BlessedHammer                         = CreateSpell(204019),
    BulwarkofRighteousFury                = CreateSpell(386653),
    CrusadersJudgment                     = CreateSpell(204023),
    DivineToll                            = CreateSpell(375576),
    EyeofTyr                              = CreateSpell(387174),
    GuardianofAncientKings                = CreateMultiSpell(86659,212641),
    HammeroftheRighteous                  = CreateSpell(53595),
    InmostLight                           = CreateSpell(405757),
    MomentofGlory                         = CreateSpell(327193),
    Redoubt                               = CreateSpell(280373),
    RefiningFire                          = CreateSpell(469883),
    RighteousProtector                    = CreateSpell(204074),
    Sentinel                              = CreateSpell(389539),
    -- Buffs
    ArdentDefenderBuff                    = CreateSpell(31850),
    BastionofLightBuff                    = CreateSpell(378974),
    BulwarkofRighteousFuryBuff            = CreateSpell(386652),
    GuardianofAncientKingsBuff            = CreateMultiSpell(86659,212641),
    InnerResilienceBuff                   = CreateSpell(450706), -- Tome of Light's Devotion buff
    LuckoftheDrawBuff                     = CreateSpell(1218114), -- TWW S2 Tier
    MomentofGloryBuff                     = CreateSpell(327193),
    RedoubtBuff                           = CreateSpell(280375),
    SanctificationBuff                    = CreateSpell(424616), -- T31, 2pc
    SanctificationEmpowerBuff             = CreateSpell(424622), -- T31, 2pc
    SentinelBuff                          = CreateSpell(389539),
    ShiningLightFreeBuff                  = CreateSpell(327510),
}
---@class PLCustomTable
Spell.Paladin.Protection = MergeTableByKey(Spell.Paladin.Protection, Spell.Paladin.Custom)
---@class PLCommonsTable
Spell.Paladin.Protection = MergeTableByKey(Spell.Paladin.Protection, Spell.Paladin.Commons, true)
---@class HeraldoftheSunTable
Spell.Paladin.Protection = MergeTableByKey(Spell.Paladin.Protection, Spell.Paladin.HeraldoftheSun)
---@class LightsmithTable
Spell.Paladin.Protection = MergeTableByKey(Spell.Paladin.Protection, Spell.Paladin.Lightsmith)
---@class TemplarTable
Spell.Paladin.Protection = MergeTableByKey(Spell.Paladin.Protection, Spell.Paladin.Templar)

---@class PLRetributionTable
Spell.Paladin.Retribution = {    
  -- Abilities
  TemplarsVerdict                       = CreateSpell(85256),
  -- Talents
  AshestoDust                           = CreateSpell(383300),
  BladeofJustice                        = CreateSpell(184575),
  BladeofVengeance                      = CreateSpell(403826),
  BladeofWrath                          = CreateSpell(231832),
  BlessedChampion                       = CreateSpell(403010),
  BoundlessJudgment                     = CreateSpell(405278),
  Crusade                               = CreateSpell(231895),
  CrusadingStrikes                      = CreateSpell(404542),
  DivineArbiter                         = CreateSpell(404306),
  DivineAuxiliary                       = CreateSpell(406158),
  DivineHammer                          = CreateSpell(198034),
  DivineResonance                       = CreateSpell(384027),
  DivineStorm                           = CreateSpell(53385),
  DivineToll                            = CreateSpell(375576),
  EmpyreanLegacy                        = CreateSpell(387170),
  EmpyreanPower                         = CreateSpell(326732),
  ExecutionSentence                     = CreateSpell(343527),
  ExecutionersWill                      = CreateSpell(406940),
  ExecutionersWrath                     = CreateSpell(387196),
  Exorcism                              = CreateSpell(383185),
  Expurgation                           = CreateSpell(383344),
  FinalReckoning                        = CreateSpell(343721),
  FinalVerdict                          = CreateSpell(383328),
  FiresofJustice                        = CreateSpell(203316),
  HolyBlade                             = CreateSpell(383342),
  HolyFlames                            = CreateSpell(406545),
  Jurisdiction                          = CreateSpell(402971),
  JusticarsVengeance                    = CreateSpell(215661),
  RadiantDecree                         = CreateSpell(383469),
  RadiantDecreeTalent                   = CreateSpell(384052),
  RadiantGlory                          = CreateSpell(458359),
  RighteousVerdict                      = CreateSpell(267610),
  ShieldofVengeance                     = CreateSpell(184662),
  TempestoftheLightbringer              = CreateSpell(383396),
  TemplarSlash                          = CreateSpell(406647),
  TemplarStrike                         = CreateSpell(407480),
  VanguardofJustice                     = CreateSpell(406545),
  VanguardsMomentum                     = CreateSpell(383314),
  WakeofAshes                           = CreateSpell(255937),
  Zeal                                  = CreateSpell(269569),
  -- Buffs
  CrusadeBuff                           = CreateSpell(231895),
  DivineArbiterBuff                     = CreateSpell(406975),
  DivineHammerBuff                      = CreateSpell(198034),
  DivineResonanceBuff                   = CreateSpell(384029),
  EchoesofWrathBuff                     = CreateSpell(423590), -- T31, 4pc
  EmpyreanLegacyBuff                    = CreateSpell(387178),
  EmpyreanPowerBuff                     = CreateSpell(326733),
  -- Tier Set Bonuses (TWW)
  WinningStreakBuff                     = CreateSpell(1216828), -- TWW S2 2pc Buff
  AllInBuff                             = CreateSpell(1216837), -- TWW S2 4pc Buff
  -- Debuffs
  ExecutionSentenceDebuff               = CreateSpell(343527),
  ExpurgationDebuff                     = CreateSpell(383346),
}
---@class PLCustomTable
Spell.Paladin.Retribution = MergeTableByKey(Spell.Paladin.Retribution, Spell.Paladin.Custom)
---@class PLCommonsTable
Spell.Paladin.Retribution = MergeTableByKey(Spell.Paladin.Retribution, Spell.Paladin.Commons, true)
---@class HeraldoftheSunTable
Spell.Paladin.Retribution = MergeTableByKey(Spell.Paladin.Retribution, Spell.Paladin.HeraldoftheSun)
---@class LightsmithTable
Spell.Paladin.Retribution = MergeTableByKey(Spell.Paladin.Retribution, Spell.Paladin.Lightsmith)
---@class TemplarTable
Spell.Paladin.Retribution = MergeTableByKey(Spell.Paladin.Retribution, Spell.Paladin.Templar)

---@class PLHolyTable
Spell.Paladin.Holy = {
    -- Racials
    AncestralCall = CreateSpell(274738),
    ArcanePulse = CreateSpell(260364),
    ArcaneTorrent = CreateSpell(155145),
    BagofTricks = CreateSpell(312411),
    Berserking = CreateSpell(26297),
    BloodFury = CreateSpell(20572),
    Fireblood = CreateSpell(265221),
    GiftoftheNaaru = CreateSpell(59542),
    LightsJudgment = CreateSpell(255647),

    -- Common
    AvengingWrath = CreateSpell(31884),
    BlessingOfFreedom = CreateSpell(1044),
    BlessingOfProtection = CreateSpell(1022),
    BlessingOfSacrifice = CreateSpell(6940),
    ConcentrationAura = CreateSpell(317920),
    Consecration = CreateSpell(26573),
    CrusaderAura = CreateSpell(32223),
    CrusaderStrike = CreateSpell(35395),
    DevotionAura = CreateSpell(465),
    DivineShield = CreateSpell(642),
    DivineSteed = CreateSpell(190784),
    FlashOfLight = CreateSpell(19750),
    HammerOfJustice = CreateSpell(853),
    HammerOfWrath = CreateSpell(24275),
    HandOfReckoning = CreateSpell(62124),
    Judgment = CreateSpell(275773),
    RetributionAura = CreateSpell(183435),
    SenseUndead = CreateSpell(5502),
    ShieldOfTheRighteous = CreateSpell(415091),
    WordOfGlory = CreateSpell(85673),
    Absolution = CreateSpell(212056),
    BeaconOfLight = CreateSpell(53563),
    Cleanse = CreateSpell(4987),
    DivineFavor = CreateSpell(210294),
    DivineToll = CreateSpell(375576),
    HolyLight = CreateSpell(82326),
    HolyShock = CreateSpell(20473),
    LightOfDawn = CreateSpell(85222),
    LightOfTheMartyr = CreateSpell(183998), --OTHER
    JudgmentDebuff = CreateSpell(197277),

    -- Paladin
    ImprovedCleanse = CreateSpell(393024),
    HallowedGround = CreateSpell(377043),
    AurasOfTheResolute = CreateSpell(385633),
    AurasOfSwiftVengeance = CreateSpell(385639),
    FistOfJustice = CreateSpell(234299),
    Rebuke = CreateSpell(96231),
    Cavalier = CreateSpell(230332),
    SacrificeOfTheJust = CreateSpell(384820), --Multi
    Recompense = CreateSpell(384914), --Multi
    JudgmentOfLight = CreateSpell(183778),
    HolyAegis = CreateSpell(385515),
    GoldenPath = CreateSpell(377128),
    SealOfMercy = CreateSpell(384897),
    SealOfClarity = CreateSpell(384815),
    Afterimage = CreateSpell(385414),
    UnbreakableSpirit = CreateSpell(114154),
    ImprovedBlessingOfProtection = CreateSpell(384909),
    DivinePurpose = CreateSpell(223817), --Multi
    HolyAvenger = CreateSpell(105809), --Multi
    SealOfAlacrity = CreateSpell(385425),
    SealOfMight = CreateSpell(385450),
    AspirationOfDivinity = CreateSpell(385416),
    SealOfOrder = CreateSpell(385129),
    OfDuskAndDawn = CreateSpell(385125),
    ZealotSParagon = CreateSpell(391142),
    SealOfTheCrusader = CreateSpell(385728),
    Obduracy = CreateSpell(385427),
    Incandescence = CreateSpell(385464), --Multi
    TouchOfLight = CreateSpell(385349), --Multi
    SealOfReprisal = CreateSpell(377053),
    SeasonedWarhorse = CreateSpell(376996), --Multi
    SealOfTheTemplar = CreateSpell(377016), --Multi
    GreaterJudgment = CreateSpell(231644),

    -- Holy
    HolyArmementBulwak = CreateSpell(432459),
    HolyArmementSacredWeapon = CreateSpell(432472),
    HolyArmementBulwakBuff = CreateSpell(432496),
    HolyArmementSacredWeaponBuff = CreateSpell(432502),
    Dawnlight = CreateSpell(431377), -- BETA
    EternalFlame = CreateSpell(156322), -- BETA
    HandOfDivinity = CreateSpell(414273),
    BeaconOfFaith = CreateSpell(156910), --Multi
    BeaconOfVirtue = CreateSpell(200025), --Multi
    EchoingBlessings = CreateSpell(387801),
    ImbuedInfusions = CreateSpell(392961),
    BarrierOfFaith = CreateSpell(148039),
    MaraadSDyingBreath = CreateSpell(388018),
    UntemperedDedication = CreateSpell(387814),
    RuleOfLaw = CreateSpell(214202),
    SavedByTheLight = CreateSpell(157047),
    UnendingLight = CreateSpell(387998), --Multi
    BestowFaith = CreateSpell(223306), --Multi
    UnwaveringSpirit = CreateSpell(392911), --Multi
    ProtectionOfTyr = CreateSpell(200430), --Multi
    MomentOfCompassion = CreateSpell(387786), --Multi
    ResplendentLight = CreateSpell(392902), --Multi
    Illumination = CreateSpell(387993), --Multi
    DivineInsight = CreateSpell(392914), --Multi
    TirionSDevotion = CreateSpell(392928),
    RadiantOnslaught = CreateSpell(231667),
    EmpyrealWard = CreateSpell(387791),
    ShiningSavior = CreateSpell(388005),
    LightSHammer = CreateSpell(114158), --Multi
    HolyPrism = CreateSpell(114165), --Multi
    DivineRevelations = CreateSpell(387808),
    CommandingLight = CreateSpell(387781),
    Veneration = CreateSpell(392938),
    BreakingDawn = CreateSpell(387879),
    SecondSunrise = CreateSpell(200482),
    AvengingCrusader = CreateSpell(216331), --Multi
    DivineGlimpse = CreateSpell(387805),
    TowerOfRadiance = CreateSpell(231642),
    BoundlessSalvation = CreateSpell(392951),
    TyrSDeliverance = CreateSpell(200652),
    PowerOfTheSilverHand = CreateSpell(200474),
    RelentlessInquisitor = CreateSpell(383388),
    InflorescenceOfTheSunwell = CreateSpell(392907), --Multi
    EmpyreanLegacy = CreateSpell(387170), --Multi
    Awakening = CreateSpell(248033),
    BlessingOfSummer = CreateSpell(388007),
    CrusaderSMight = CreateSpell(196926),
    GlimmerOfLight = CreateSpell(287269),
    DivineResonance = CreateSpell(387893),

    --Buffs
    EmpyreanLegacyBuff = CreateSpell(387178),
    BeaconOfFaithBuff = CreateSpell(156910),
    BeaconOfLightBuff = CreateSpell(53563),
    DivinePurposeBuff = CreateSpell(223819),
    GlimmerBuff = CreateSpell(287280),
    OfDuskandDawn = CreateSpell(385125),
    UnendingLightBuff = CreateSpell(394709),
    DawnBuff = CreateSpell(385127),
    DuskBuff = CreateSpell(385126),
    BlessingOfAutumn = CreateSpell(388010),
    BlessingOfWinter = CreateSpell(388011),
    BlessingOfSpring = CreateSpell(388013),
    VenerationBuff = CreateSpell(392939),
    ShiningRighteousnessBuff = CreateSpell(414444),
    InfusionOfLightBuff = CreateSpell(54149),
    ConsecrationBuff = CreateSpell(188370),

    --Debuffs
    ForberanceDebuff = CreateSpell(25771)
}
---@class PLCustomTable
Spell.Paladin.Holy = MergeTableByKey(Spell.Paladin.Holy, Spell.Paladin.Custom)
---@class PLCommonsTable
Spell.Paladin.Holy = MergeTableByKey(Spell.Paladin.Holy, Spell.Paladin.Commons, true)
---@class HeraldoftheSunTable
Spell.Paladin.Holy = MergeTableByKey(Spell.Paladin.Holy, Spell.Paladin.HeraldoftheSun)
---@class LightsmithTable
Spell.Paladin.Holy = MergeTableByKey(Spell.Paladin.Holy, Spell.Paladin.Lightsmith)
---@class TemplarTable
Spell.Paladin.Holy = MergeTableByKey(Spell.Paladin.Holy, Spell.Paladin.Templar)

-- Items
if not Item.Paladin then
    Item.Paladin = {}
end

---@class PLCommonsItemTable
Item.Paladin.Commons = {
    -- TWW Items
    BestinSlotsMelee                      = Item(232526, {16}),
}

---@class PLProtectionItemTable
Item.Paladin.Protection = {
    TomeofLightsDevotion                  = Item(219309, {13, 14}),

    -- Custom
    TreemouthFesteringSplinter            = Item(193652, { 13, 14 }),
    DecorationofFlame                     = Item(194299, { 13, 14 }),
    WardofFacelessIre                     = Item(203714, { 13, 14 }),
    EnduringDreadplate                    = Item(202616, { 13, 14 }),
    GranythsEnduringScale                 = Item(212757, { 13, 14 }),
    FyrakksTaintedRageheart               = Item(207174, { 13, 14 }),
    ShadowmoonInsignia                    = Item(150526, { 13, 14 }),
}
---@class PLCommonsItemTable
Item.Paladin.Protection = MergeTableByKey(Item.Paladin.Protection, Item.Paladin.Commons)

---@class PLRetributionItemTable
Item.Paladin.Retribution = {
    -- Potion
    PotionofSpectralIntellect = Item(171273),
    PotionofSpectralStrength = Item(171275),
    AlgetharPuzzleBox = Item(193701, { 13, 14 }),
    -- Other On-Use Items
    GaveloftheFirstArbiter = Item(189862),
    ShadowedRazingAnnihilator = Item(205046),
    Fyralath = Item(206448, { 16 }),
}
---@class PLCommonsItemTable
Item.Paladin.Retribution = MergeTableByKey(Item.Paladin.Retribution, Item.Paladin.Commons)

---@class PLHolyItemTable
Item.Paladin.Holy = {
    -- Potion
    PotionofSpectralIntellect = Item(171273),
    PotionofSpectralStrength = Item(171275),
    -- Trinkets
    AspirantsBadgeCosmic = Item(186906, { 13, 14 }),
    AspirantsBadgeSinful = Item(175884, { 13, 14 }),
    AspirantsBadgeUnchained = Item(185161, { 13, 14 }),
    ChainsofDomination = Item(188252, { 13, 14 }),
    DarkmoonDeckVoracity = Item(173087, { 13, 14 }),
    DreadfireVessel = Item(184030, { 13, 14 }),
    EarthbreakersImpact = Item(188264, { 13, 14 }),
    FaultyCountermeasure = Item(137539, { 13, 14 }),
    GiantOrnamentalPearl = Item(137369, { 13, 14 }),
    GladiatorsBadgeCosmic = Item(186866, { 13, 14 }),
    GladiatorsBadgeSinful = Item(175921, { 13, 14 }),
    GladiatorsBadgeUnchained = Item(185197, { 13, 14 }),
    GrimCodex = Item(178811, { 13, 14 }),
    HeartoftheSwarm = Item(188255, { 13, 14 }),
    InscrutableQuantumDevice = Item(179350, { 13, 14 }),
    MacabreSheetMusic = Item(184024, { 13, 14 }),
    MemoryofPastSins = Item(184025, { 13, 14 }),
    OverwhelmingPowerCrystal = Item(179342, { 13, 14 }),
    SalvagedFusionAmplifier = Item(186432, { 13, 14 }),
    ScarsofFraternalStrife = Item(188253, { 13, 14 }),
    SkulkersWing = Item(184016, { 13, 14 }),
    SpareMeatHook = Item(178751, { 13, 14 }),
    TheFirstSigil = Item(188271, { 13, 14 }),
    WindscarWhetstone = Item(137486, { 13, 14 }),
    -- Other On-Use Items
    GaveloftheFirstArbiter = Item(189862),
    ShadowedRazingAnnihilator = Item(205046),
    Fyralath = Item(206448, { 16 }),
}
---@class PLCommonsItemTable
Item.Paladin.Holy = MergeTableByKey(Item.Paladin.Holy, Item.Paladin.Commons)


Spell.Paladin.Protection.HandofReckoning:SetGeneric(PALADIN_PROTECTION_SPECID, "Generic1")
Spell.Paladin.Protection.AvengersShield:SetGeneric(PALADIN_PROTECTION_SPECID, "Generic2")
Spell.Paladin.Protection.Judgment:SetGeneric(PALADIN_PROTECTION_SPECID, "Generic3")
Spell.Paladin.Protection.HammerofJustice:SetGeneric(PALADIN_PROTECTION_SPECID, "Generic4")
Spell.Paladin.Protection.Repentance:SetGeneric(PALADIN_PROTECTION_SPECID, "Generic5")
Spell.Paladin.Protection.TurnEvil:SetGeneric(PALADIN_PROTECTION_SPECID, "Generic6")

Spell.Paladin.Protection.HandofReckoning.offGCD = true
Spell.Paladin.Protection.ShieldoftheRighteous.offGCD = true
Spell.Paladin.Protection.BlindingLight.MeleeRange = 10
Spell.Paladin.Protection.Consecration.MeleeRange = 8
Spell.Paladin.Protection.BlessedHammer.MeleeRange = 10
Spell.Paladin.Protection.AvengingWrath.MeleeRange = 8
Spell.Paladin.Protection.ShieldoftheRighteous.MeleeRange = 5
Spell.Paladin.Protection.Rebuke.IsFocusTarget[PALADIN_PROTECTION_SPECID] = true
Spell.Paladin.Protection.HammerofJustice.IsFocusTarget[PALADIN_PROTECTION_SPECID] = true
Spell.Paladin.Protection.Judgment.IsFocusTarget[PALADIN_PROTECTION_SPECID] = true
Spell.Paladin.Protection.HandofReckoning.IsFocusTarget[PALADIN_PROTECTION_SPECID] = true
Spell.Paladin.Protection.AvengersShield.IsFocusTarget[PALADIN_PROTECTION_SPECID] = true


Spell.Paladin.Retribution.HammerofJustice:SetGeneric(PALADIN_RETRIBUTION_SPECID, "Generic1")
Spell.Paladin.Retribution.Repentance:SetGeneric(PALADIN_RETRIBUTION_SPECID, "Generic2")
Spell.Paladin.Retribution.TurnEvil:SetGeneric(PALADIN_RETRIBUTION_SPECID, "Generic3")

Spell.Paladin.Retribution.AvengingWrath.offGCD = true
Spell.Paladin.Retribution.Crusade.offGCD = true
Spell.Paladin.Retribution.ShieldoftheRighteous.offGCD = true
Spell.Paladin.Retribution.DivineStorm.MeleeRange = 8
Spell.Paladin.Retribution.Consecration.MeleeRange = 8
Spell.Paladin.Retribution.FinalReckoning.MeleeRange = 8
Spell.Paladin.Retribution.WakeofAshes.MeleeRange = 10
Spell.Paladin.Retribution.BlindingLight.MeleeRange = 10
Spell.Paladin.Retribution.AvengingWrath.MeleeRange = 8
Spell.Paladin.Retribution.Rebuke.IsFocusTarget[PALADIN_RETRIBUTION_SPECID] = true
Spell.Paladin.Retribution.HammerofJustice.IsFocusTarget[PALADIN_RETRIBUTION_SPECID] = true

Spell.Paladin.Holy.LightSHammer.Range = 40
Spell.Paladin.Holy.Consecration.MeleeRange = 8
Spell.Paladin.Holy.BlindingLight.MeleeRange = 10
Spell.Paladin.Holy.ShieldOfTheRighteous.MeleeRange = 5
Spell.Paladin.Holy.HammerOfWrath:SetGeneric(PALADIN_HOLY_SPECID, "Generic1")
Spell.Paladin.Holy.HammerOfJustice:SetGeneric(PALADIN_HOLY_SPECID, "Generic2")
Spell.Paladin.Holy.Repentance:SetGeneric(PALADIN_HOLY_SPECID, "Generic3")
Spell.Paladin.Holy.TurnEvil:SetGeneric(PALADIN_HOLY_SPECID, "Generic4")
Spell.Paladin.Holy.RuleOfLaw.offGCD = true
Spell.Paladin.Holy.Rebuke.IsFocusTarget[PALADIN_HOLY_SPECID] = true
Spell.Paladin.Holy.HammerOfJustice.IsFocusTarget[PALADIN_HOLY_SPECID] = true


Paladin.HPGCount = 0
Paladin.DivineHammerActive = false

--- ============================ CONTENT ============================
--- ===== HPGTo2Dawn Tracker =====
local Spec = Cache.Persistent.Player.Spec[1]
Paladin.HPGCount = 0
HL:RegisterForSelfCombatEvent(
  function (...)
    if Spec == 66 then
      Paladin.HPGCount = Paladin.HPGCount + 1
    end
  end
, "SPELL_ENERGIZE")

HL:RegisterForSelfCombatEvent(
  function (...)
    local SpellID = select(12, ...)
    if SpellID == 385127 then
      Paladin.HPGCount = 0
    elseif SpellID == 198034 then -- Divine Hammer
      Paladin.DivineHammerActive = true
    end
  end
, "SPELL_AURA_APPLIED", "SPELL_AURA_APPLIED_DOSE")

HL:RegisterForSelfCombatEvent(
  function (...)
    local SpellID = select(12, ...)
    if SpellID == 198034 then -- Divine Hammer
      Paladin.DivineHammerActive = false
    end
  end
  , "SPELL_AURA_REMOVED"
)

local Player = HeroLibEx.Unit.Player
if Player:Class() == "PALADIN" then

    -- Paladins have a talent that improve the dispels to get more spell schools. This is a bit of a hack to get the correct dispel flag.
    if Spell.Paladin.Holy.ImprovedCleanse:IsAvailable() then
        MainAddon.CONST.DispelList.Cleanse.DispelFlag = MainAddon.CONST.DispelFlag.Magic + MainAddon.CONST.DispelFlag.Disease + MainAddon.CONST.DispelFlag.Poison
    else
        MainAddon.CONST.DispelList.Cleanse.DispelFlag = MainAddon.CONST.DispelFlag.Magic
    end

    HL:RegisterForEvent(function()
        if Spell.Paladin.Holy.ImprovedCleanse:IsAvailable() then
            MainAddon.CONST.DispelList.Cleanse.DispelFlag = MainAddon.CONST.DispelFlag.Magic + MainAddon.CONST.DispelFlag.Disease + MainAddon.CONST.DispelFlag.Poison
        else
            MainAddon.CONST.DispelList.Cleanse.DispelFlag = MainAddon.CONST.DispelFlag.Magic
        end
    end, "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")

    -- Blessing of Freedom right-click menu
    Paladin.WhitelistUnitFreedom = {}

    ---@param rootDescription Menu
    local function RightClick(_, rootDescription, contextData)
        if Cache.Persistent.Player.Class[2] ~= "PALADIN" then
            return
        end

        local unit = contextData.unit
        local unitName = contextData.name

        if not unit or UnitCanAttack(Player:ID(), unit) then
            return
        end
    
        local unitFound = false
        for i, v in pairs(Paladin.WhitelistUnitFreedom) do
            if v == unitName then
                unitFound = true
                break
            end
        end
    
        local SetUnset = (not Paladin.WhitelistUnitFreedom or #Paladin.WhitelistUnitFreedom == 0) and "Set" or unitFound and "Unset" or "Set"
        local colorCode = unitFound and "|cffff0000" or "|cff00ff00"

        rootDescription:CreateButton("|T135968:24|t" .. " " .. colorCode .. SetUnset .. " Blessing of Freedom" .. "|r", function()
            if not Paladin.WhitelistUnitFreedom or #Paladin.WhitelistUnitFreedom == 0 then
                table.insert(Paladin.WhitelistUnitFreedom, unitName)
                MainAddon:Print("Blessing of Freedom on: " .. unitName .. " ", true, 2)
            else
                if unitFound then
                    for i, v in pairs(Paladin.WhitelistUnitFreedom) do
                        if v == unitName then
                            table.remove(Paladin.WhitelistUnitFreedom, i)
                            break
                        end
                    end
                    MainAddon:Print("Blessing of Freedom on: " .. unitName .. " ", false, 2)
                else
                    table.insert(Paladin.WhitelistUnitFreedom, unitName)
                    MainAddon:Print("Blessing of Freedom on: " .. unitName .. " ", true, 2)
                end
            end
        end)
    end
    Menu.ModifyMenu("MENU_UNIT_SELF", RightClick);
    Menu.ModifyMenu("MENU_UNIT_PLAYER", RightClick);
    Menu.ModifyMenu("MENU_UNIT_TARGET", RightClick);
    Menu.ModifyMenu("MENU_UNIT_FOCUS", RightClick);
    Menu.ModifyMenu("MENU_UNIT_RAID_PLAYER", RightClick);
    Menu.ModifyMenu("MENU_UNIT_PARTY", RightClick);
end