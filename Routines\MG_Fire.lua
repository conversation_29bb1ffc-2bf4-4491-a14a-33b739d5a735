function A_63(...)
    -- Addon
    ---@class MainAddon
    local MainAddon = MainAddon
    local M = MainAddon
    -- HeroLib
    local HL = HeroLibEx
    ---@class Unit
    local Unit = HL.Unit
    ---@class Unit
    local Player = Unit.Player
    ---@class Unit
    local Target = Unit.Target
    ---@class Unit
    local Focus = Unit.Focus
    ---@class Spell
    local Spell = HL.Spell
    ---@class Item
    local Item = HL.Item
    -- HeroRotation
    local CastMagic = M.CastMagic
    local CastCycle = M.CastCycle
    ---@class Unit
    local MouseOver = Unit.MouseOver
    local AoEON = M.AoEON
    -- LUAs
    local pairs = pairs
    local max = _G['math'].max
    local GetMouseFoci = _G['GetMouseFoci']
    local C_Timer = _G['C_Timer']
    local GetTime = _G['GetTime']
    local IsFalling = _G['IsFalling']
    local IsCurrentSpell = _G['C_Spell']['IsCurrentSpell']
    local IsInGroup = _G['IsInGroup']
    local IsInRaid = _G['IsInRaid']
    local num = M.num
    
    -- Define S/I for spell and item arrays
    local S = Spell.Mage.Fire
    local I = Item.Mage.Fire
  
    -- Toggle Setting
    MainAddon.Toggle.Special["HoldingSKB"] = {
        Icon = MainAddon.GetTexture(S.SunKingsBlessingBuff),
        Name = "Holding Sun King's Blessing",
        Description = "Holding Sun King's Blessing Buff.",
        Spec = 63
    }
  
    local GetSetting = MainAddon.Config.GetClassSetting
    local Config_Key = MainAddon.GetClassVariableName()
    local Config_Color = '3FC7EB'
    local Config_Table = {
        key = Config_Key,
        title = 'Mage - Fire',
        subtitle = '?? ' .. MainAddon.Version,
        width = 600,
        height = 700,
        profiles = true,
        config = {
          { type = 'header', text = '', size = 24, align = 'Center', color = Config_Color },
          { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
          { type = "header", text = '\"You must be ready to burn yourself in your own flame;\nhow could you rise anew if you have not first become ashes?\"', size = 16, align = "center", color = 'F37735' },
          { type = "header", text = "?? x yuno", size = 12, align = "center", color = '4C4C4C' },
          { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
          { type = 'header', text = 'DPS', color = Config_Color },
          { type = 'checkbox', text = ' Disable dispel and low priority kicks while in Combustion', icon = S.Combustion:ID(), key = 'combnodispel', default = true },
        -- removed for now, will be reintroduced later
        --   { type = 'checkbox', text = 'Opener sequence', key = 'opener_seq', default = false },
          { type = 'checkbox', text = ' Snipe Scorch via Focus', icon = S.Scorch:ID(), key = 'snipe_scorch_f', default = true },
          { type = 'checkbox', text = ' Focus Target as fallback', icon = S.Scorch:ID(), key = 'focus_target_fallback', default = false },
          { type = 'checkbox', text = " Offensive Dragon's Breath (fish for procs)", icon = S.DragonsBreath:ID(), key = 'db_fish', default = false },
          { type = 'checkspin', text = ' Flamestrike custom target count', key = 'FSaoecount', icon = S.Flamestrike:ID(), min = 0, max = 10, default_spin = 5, default_check = false },
          { type = 'checkbox', text = ' Mirror Image prepull', icon = S.MirrorImage:ID(), key = 'mirrorimage_prepull', default = false },
        -- removed for now, will be reintroduced later and probably automated with the opener
        --   { type = 'dropdown', text = ' Hardcast Pyroblast Opener', key = 'pyro_open', multiselect = true, icon = S.Pyroblast:ID(), list = { { text = 'Boss', key = 1 }, { text = 'Elite', key = 2 }, { text = 'Normal', key = 3 }, { text = 'Dummy', key = 4 } }, default = { 1, 4 } },
          { type = 'dropdown',
          text = ' Shifting Power priority', key = 'spusage',
          icon = S.ShiftingPower:ID(),
          list = {
            { text = 'Combustion', key = 'spcomb' },
            { text = 'Not wasting PF/IB', key = 'spstacks' },
          },
          default = 'spcomb',
          },
          { type = 'spinner', text = ' Shifting Power - Stand still threshold', key = 'SPMovingValue', icon = S.ShiftingPower:ID(), min = 0, max = 10, default = 0.75 },
          { type = 'spacer' },
          { type = 'checkspin', text = ' Combustion - Pooling (Fire Blast Stacks)', key = 'fb_pooling_stacks', icon = S.FireBlast:ID(), min = 0, max = 3, default_spin = 1.2, default_check = true },
          { type = 'checkspin', text = ' Combustion - Pooling (Timer, below threshold)', key = 'fb_pooling_combu_timer', icon = S.FireBlast:ID(), min = 0, max = 120, default_spin = 6, default_check = true },
          { type = 'checkspin', text = ' Combustion - Pooling (Phoenix Flames Stacks)', key = 'pf_pooling_stacks', icon = S.PhoenixFlames:ID(), min = 0, max = 3, default_spin = 1.2, default_check = true },
          { type = 'checkspin', text = ' Combustion - Pooling (Timer, below threshold)', key = 'pf_pooling_combu_timer', icon = S.PhoenixFlames:ID(), min = 0, max = 120, default_spin = 8, default_check = true },
          { type = 'spacer' },
          { type = 'checkbox', text = ' Sun King\'s Blessing - Pooling', icon = S.SunKingsBlessingBuff:ID(), key = 'pool_for_skb', default = false },
          { type = 'checkspin', text = ' Sun King\'s Blessing - Time To Die (seconds threshold)', key = 'skb_aoe_ttd', icon = S.SunKingsBlessing:ID(), min = 0, max = 200, default_spin = 10, default_check = false },
          { type = 'spacer' },
          { type = 'header', text = 'Defensives', color = Config_Color },
          { type = 'checkspin', text = ' Blazing Barrier', key = 'BB', icon = S.BlazingBarrier:ID(), min = 1, max = 100, default_spin = 55, default_check = true },
          { type = 'checkspin', text = ' Mass Barrier - Average Group health threshold', key = 'MassBB', icon = S.MassBarrier:ID(), min = 1, max = 100, default_spin = 70, default_check = true },
          { type = 'checkspin', text = ' Mirror Image - Defensive', key = 'mi_defensive', icon = S.MirrorImage:ID(), min = 1, max = 100, default_spin = 40, default_check = false },
          { type = 'checkspin', text = ' Ice Cold / Ice Block', key = 'ICIB', icon = S.IceBlock:ID(), min = 1, max = 100, default_spin = 35, default_check = true },
          { type = 'spacer' },
          { type = 'header', text = 'Utilities', color = Config_Color },
          { type = 'checkbox', text = ' Mirror Image - Aggro', icon = S.MirrorImage:ID(), key = 'mi_aggro', default = true },
          { type = 'checkbox', text = ' Greater Invisibility - Aggro', icon = S.GreaterInvisibility:ID(), key = 'gi_aggro', default = true },
          { type = 'dropdown', text = ' Ice Floes', key = 'if', multiselect = true, icon = S.IceFloes:ID(), list = { { text = 'Shifting Power', key = 1 }, { text = 'Pyroblast', key = 2 }, { text = 'Flamestrike', key = 3 } }, default = { 1, 2, 3 } },
          { type = 'dropdown', text = ' Arcane Intellect', key = 'int', icon = S.ArcaneIntellect:ID(), multiselect = true, list = { { text = 'Self', key = 'int_self' }, { text = 'Friends', key = 'int_friends' } }, default = { 'int_self', 'int_friends' } },
          { type = 'checkbox', text = ' Toast message about Shifting Power', icon = S.ShiftingPower:ID(), key = 'toast_SP', default = true },
          { type = 'spinner', text = ' Shifting Power Toast message reset timer (sec)', icon = S.ShiftingPower:ID(), key = 'toast_SP_reset', min = 0, max = 60, default = 15 },
          { type = 'checkbox', text = "Display number of enemies detected", key = 'infotext', default = true },
          { type = 'spacer' },
          { type = 'checkbox', text = "Cast Groundspell only when MouseOver enemies or tank.", key = 'MOOption', default = false },
          { type = 'checkbox', text = ' Magic Groundspell - Flamestrike', icon = S.Flamestrike:ID(), key = 'magicgroundspell_flamestrike', default = false },
          { type = 'checkbox', text = ' Magic Groundspell - Meteor', icon = S.Meteor:ID(), key = 'magicgroundspell_meteor', default = false },
          { type = 'spacer' },
          { type = 'spacer' },
          { type = 'spacer' },
          { type = 'spacer' },
          { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
        }        
    }
    Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
    Config_Table.config = MainAddon.BuildDPSTrinketUI(Config_Table.config, Config_Color)
    Config_Table.config = MainAddon.BuildCombatPotionUI(Config_Table.config, "Fire", Config_Color)
    M.SetConfig(63, Config_Table)
  
    --- ===== Custom start =====
    local ShouldReturn
    local vars = {}
    vars['CombustionDown'] = true 
    vars['CombustionUp'] = false
    vars['CombustionRemains'] = 0
    vars['ShowingGroundSpell'] = false
    vars['CombustionCondition'] = false
    vars['AoECondition'] = 999
    vars['PyroFS'] = S.Pyroblast
    vars['Bolt'] = S.Fireball
    vars['Reason'] = "ST"
    vars['opener'] = true
    vars['CombatTime'] = 0
    vars['FocusIsValid'] = false
    vars['ShouldUseShiftingPower'] = false
    vars['CanCastShiftingPower'] = false
    vars['splash_init_time'] = 0
    local settings = {}
    -- Additional Settings
    settings['combnodispel'] = true
    -- Opener and Prepull stuff
    settings['opener_seq'] = false
    settings['mirrorimage_prepull'] = false
    -- Fire Blast pooling settings
    settings['fb_pooling_stacks_c'] = true      -- Checkbox: Pooling enabled
    settings['fb_pooling_stacks_s'] = 1.2      -- Spin value: Stacks threshold
    settings['fb_pooling_combu_timer_c'] = true -- Checkbox: Combustion timer pooling enabled
    settings['fb_pooling_combu_timer_s'] = 6   -- Spin value: Combustion timer threshold
    -- Phoenix Flames pooling settings
    settings['pf_pooling_stacks_c'] = true      -- Checkbox: Pooling enabled
    settings['pf_pooling_stacks_s'] = 1.2       -- Spin value: Stacks threshold
    settings['pf_pooling_combu_timer_c'] = true -- Checkbox: Combustion timer pooling enabled
    settings['pf_pooling_combu_timer_s'] = 8   -- Spin value: Combustion timer threshold
    -- Pooling for Sun King's Blessing
    settings['pool_for_skb'] = false
    -- Sun King's Blessing - Time To Die setting
    settings['skb_aoe_ttd_c'] = false
    settings['skb_aoe_ttd_s'] = 8
    -- Flamestrike target count setting
    settings['FSaoecount_c'] = false              -- Checkbox: setting enabled
    settings['FSaoecount_s'] = 5               -- Spin value: Target count
    --- ===== Custom end =====

    --- ===== Filter and Condition Functions start =====
    --- ScorchExecute function to check if Scorch should be casted
    ---@param ThisUnit Unit
    local function ScorchExecute(ThisUnit)
        return ThisUnit:HealthPercentage() < 30
    end

    --- Improved Scorch
    ---@param ThisUnit Unit
     local function EvaluateImprovedScorch(ThisUnit)
        return ((ThisUnit:DebuffRemains(S.ImprovedScorchDebuff) <= 2) or (ThisUnit:DebuffStack(S.ImprovedScorchDebuff) < 2)) and (ThisUnit:HealthPercentage() < 30 or Player:BuffUp(S.HeatShimmerBuff))
    end
    --- ===== Filter and Condition Functions end =====

    local function Cast(Object, OffGCD, DisplayStyle, OutofRange, CustomTime, ignoreChannel, TextureSlot)
        if Object == S.Scorch then
            if OffGCD == "execute" and not vars['CombustionUp'] then
                if GetSetting('snipe_scorch_f', true) then
                    if ScorchExecute(Target) then
                        if M.Cast(Object) then
                            return true
                        end
                    end
                    if vars['FocusIsValid'] then
                        if MainAddon.SetTopTexture(6, "2948-Focus") then
                            return true
                        end
                    end
                else
                    if ScorchExecute(Target) then
                        if M.Cast(Object) then
                            return true
                        end
                    end
                end
            else
                if M.Cast(Object) then
                    return true
                end
            end
        elseif Object == S.Flamestrike then
            local magicgroundspell_flamestrike = GetSetting('magicgroundspell_flamestrike', false)
            -- If MOOption is enabled and no valid mouseover target, try to use Pyroblast instead
            if GetSetting('MOOption', false) and not vars['MOCheck'] then
                -- If we have Hot Streak or Hyperthermia, use Pyroblast instead of Flamestrike
                if Player:BuffUp(S.HotStreakBuff) or Player:BuffUp(S.HyperthermiaBuff) then
                    -- Use M.Cast directly here to bypass the groundspell check
                    if M.Cast(S.Pyroblast) then
                        return true
                    end
                end
                -- Return false to continue with the rotation if we couldn't cast Pyroblast
                return false
            else
                if CastMagic(S.Flamestrike, OffGCD, "2120-Magic", magicgroundspell_flamestrike) then
                    return true
                end
            end
        elseif Object == S.Meteor then
            local magicgroundspell_meteor = GetSetting('magicgroundspell_meteor', false)
            -- Skip Meteor if MOOption is enabled and no valid mouseover target
            if GetSetting('MOOption', false) and not vars['MOCheck'] then
                -- Just return false to allow the rotation to continue
                return false
            else
                if CastMagic(S.Meteor, OffGCD, "153561-Magic", magicgroundspell_meteor) then
                    return true
                end
            end
        else
            if M.Cast(Object, OffGCD, DisplayStyle, OutofRange, CustomTime, ignoreChannel, TextureSlot) then
                return true
            end
        end
    end   

    local function Defensives()
        local DefensiveUp = Player:BuffUp(S.BlazingBarrier) or Player:BuffUp(S.IceCold) or Player:BuffUp(S.MirrorImageBuff) or Player:BuffUp(S.IceBlock)
        if not DefensiveUp then
            if GetSetting('BB_check', true) then
                if S.BlazingBarrier:IsReady(Player) and Player:HealthPercentage() <= GetSetting('BB_spin', 30) then
                    if Cast(S.BlazingBarrier) then
                        return "Blazing Barrier";
                    end
                end
            end

            if vars['CombatTime'] > 0.25 then
                if GetSetting('ICIB_check', false) and Player:HealthPercentage() <= GetSetting('ICIB_spin', 35) and Player:DebuffDown(S.HypothermiaDebuff) then
                    if S.IceCold:IsReady(Player) then
                        if Cast(S.IceCold) then
                            return "Ice Cold - HP"
                        end
                    end

                    if S.IceBlock:IsReady(Player) then
                        if Cast(S.IceBlock) then
                            return "Ice Block - HP"
                        end
                    end
                end
        
                if S.MirrorImage:IsReady(Player) and GetSetting('mi_defensive_check', false) and Player:HealthPercentage() <= GetSetting('mi_defensive_spin', 40) then
                    if Cast(S.MirrorImage) then
                        return "Mirror Image - HP"
                    end
                end
            end
        end

        if vars['CombatTime'] > 0.25 then
            if Player:IsInDungeonArea() or Player:IsInRaidArea() then
                if S.MassBarrier:IsReady(Player) and GetSetting('MassBB_check', false) and MainAddon.HealingEngine:MedianHP() <= GetSetting('MassBB_spin', 30) then
                    if Cast(S.MassBarrier) then
                        return "Mass Barrier"
                    end
                end
            end
        end
    end
  
    local function Utilities()
        local int = GetSetting('int', {})
        if S.ArcaneIntellect:IsReady(Player) and (int['int_self'] and Player:BuffDown(S.ArcaneIntellect, true) or int['int_friends'] and M.GroupBuffMissing(S.ArcaneIntellect)) then
            if Cast(S.ArcaneIntellect) then
                return "arcane_intellect precombat 2";
            end
        end
    
        if Player:IsTankingAoE(40) then
            if GetSetting('mi_aggro', true) then
                if S.MirrorImage:IsReady(Player) and not Player:PrevGCD(1, S.GreaterInvisibility) and Player:BuffDown(S.GreaterInvisibilityBuff) then
                    if Cast(S.MirrorImage) then
                        return 'Mirror Image'
                    end
                end
            end
            if GetSetting('gi_aggro', true) then
                if S.GreaterInvisibility:IsReady(Player) and not Player:PrevGCD(1, S.MirrorImage) and (IsInGroup() or IsInRaid()) and not Player:BuffUp(S.MirrorImageBuff) then
                    if Cast(S.GreaterInvisibility) then
                        return 'Greater Invisibility'
                    end
                end
            end
        end 
    end
  
    -- Create table to exclude above trinkets from On Use function
    local OnUseExcludes = {
        I.HyperthreadWristwraps:ID()
    }
    -- Trinket Item Objects
    local Equip = Player:GetEquipment()
    local Trinket1 = Equip[13] and Item(Equip[13]) or Item(0)
    local Trinket2 = Equip[14] and Item(Equip[14]) or Item(0)
  
    -- Enemy variables
    local EnemiesCount8ySplash,EnemiesCount10ySplash,EnemiesCount16ySplash
    local EnemiesCount10yMelee, EnemiesCount40y
    local Enemies8ySplash, Enemies10yMelee, Enemies40y
    local UnitsWithIgniteCount
    local BossFightRemains = 11111
    local FightRemains = 11111

    local function GetBestUnitSniping()
        local BestUnit = nil

        if ScorchExecute(Target) then
            BestUnit = Target
            return BestUnit
        end
        
        if vars['FocusIsValid'] then
            BestUnit = Focus
            return BestUnit
        end
        
        for _, CycleUnit in pairs(Enemies40y) do
            if CycleUnit:GUID() ~= Target:GUID() then
                if ScorchExecute(CycleUnit) then
                    BestUnit = CycleUnit
                end
            end
        end
        return BestUnit
    end

    local function VarUpdate()
        vars['FocusIsValid'] = Focus:GUID() and Player:CanAttack(Focus) and Focus:IsSpellInRange(S.Scorch) and ScorchExecute(Focus)
        vars['CombustionUp'] = Player:BuffUp(S.CombustionBuff)
        vars['CombustionRemains'] = Player:BuffRemains(S.CombustionBuff)
        vars['CombatTime'] = HL.CombatTime()
        vars['CanCastShiftingPower'] = Player:BuffDown(S.HyperthermiaBuff) and S.ArcanePhoenix:TimeSinceLastAppliedOnPlayer() > 17

        settings['combnodispel'] = GetSetting('combnodispel', true)
        settings['opener_seq'] = GetSetting('opener_seq', false)
        settings['mirrorimage_prepull'] = GetSetting('mirrorimage_prepull', false)
        settings['fb_pooling_stacks_c'] = GetSetting('fb_pooling_stacks_check', true)
        settings['fb_pooling_stacks_s'] = GetSetting('fb_pooling_stacks_spin', 1.4)
        settings['pool_for_skb'] = GetSetting('pool_for_skb', false)        
        settings['fb_pooling_combu_timer_c'] = GetSetting('fb_pooling_combu_timer_check', true)
        settings['fb_pooling_combu_timer_s'] = GetSetting('fb_pooling_combu_timer_spin', 10)
        settings['skb_aoe_ttd_c'] = GetSetting('skb_aoe_ttd_check', false)
        settings['skb_aoe_ttd_s'] = GetSetting('skb_aoe_ttd_spin', 10)
        settings['pf_pooling_stacks_c'] = GetSetting('pf_pooling_stacks_check', true)
        settings['pf_pooling_stacks_s'] = GetSetting('pf_pooling_stacks_spin', 1.4)
        settings['pf_pooling_combu_timer_c'] = GetSetting('pf_pooling_combu_timer_check', true)
        settings['pf_pooling_combu_timer_s'] = GetSetting('pf_pooling_combu_timer_spin', 10)
        settings['FSaoecount_c'] = GetSetting('FSaoecount_check', false)
        settings['FSaoecount_s'] = GetSetting('FSaoecount_spin', 5)

        -- Set conditions for FS
        if AoEON() and (S.FlamePatch:IsAvailable() or S.Quickflame:IsAvailable()) then
            -- Sunfury: always use Flamestrike at 5+ targets
            if S.SpellfireSpheres:IsAvailable() then  
                vars['AoECondition'] = 5
            -- Frostfire: check pack time-to-die (TTD)
            elseif S.FrostfireBolt:IsAvailable() then  
                local packTTD = Player:GetEnemiesRangeTTD(40)
                if packTTD < 40 then
                    vars['AoECondition'] = 4  -- use Flamestrike at 4+ targets if pack dies in under 40 seconds
                else
                    vars['AoECondition'] = 5  -- otherwise, use Flamestrike at 5+ targets
                end
            else
                vars['AoECondition'] = 4  -- leveing or whatever
            end
        else
            vars['AoECondition'] = 999  -- AoE mode off or required groundspell not available
        end
        
        -- Apply custom user setting if enabled
        if settings['FSaoecount_c'] then
            vars['AoECondition'] = settings['FSaoecount_s']
        end
        -- If Sun Kings Blessing is ready override count regardless of user settings. This is because  hardcast Flamestrike breaks the rotation ("Already casted this Ground spell" issue bc of MainAddon.LastDisplayed.GreenCircleTime)
        if S.SunKingsBlessing:IsAvailable() and (Player:BuffStack(S.SunKingsBlessingBuff) >= 9 or Player:BuffUp(S.FuryoftheSunKingBuff)) then
            vars['AoECondition'] = 999
        end

        -- Use FS or PB according to targetcount
        if EnemiesCount8ySplash >= vars['AoECondition'] then
            vars['PyroFS'] = S.Flamestrike
            vars['Reason'] = "AoE: "
        else
            vars['PyroFS'] = S.Pyroblast
            vars['Reason'] = "ST: "
        end
    end

    local function EvaluateGroundSpellMagic()
        if vars['PyroFS'] == S.Pyroblast then
            return true
        end
        return false
    end

    HL:RegisterForEvent(function()
      Equip = Player:GetEquipment()
      Trinket1 = Equip[13] and Item(Equip[13]) or Item(0)
      Trinket2 = Equip[14] and Item(Equip[14]) or Item(0)
    end, "PLAYER_EQUIPMENT_CHANGED")
  
    HL:RegisterForEvent(function()
      S.Pyroblast:RegisterInFlight()
      S.Fireball:RegisterInFlight()
      S.FrostfireBolt:RegisterInFlight()
      S.FrostfireBolt:RegisterInFlightEffect(468655)
      S.Meteor:RegisterInFlightEffect(351140)
      S.Meteor:RegisterInFlight()
      S.PhoenixFlames:RegisterInFlightEffect(257542)
      S.PhoenixFlames:RegisterInFlight()
      S.Pyroblast:RegisterInFlight(S.CombustionBuff)
      S.Fireball:RegisterInFlight(S.CombustionBuff)
      S.FrostfireBolt:RegisterInFlight(S.CombustionBuff)
      vars['Bolt'] = S.FrostfireBolt:IsAvailable() and S.FrostfireBolt or S.Fireball
    end, "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")
    S.Pyroblast:RegisterInFlight()
    S.Fireball:RegisterInFlight()
    S.FrostfireBolt:RegisterInFlight()
    S.FrostfireBolt:RegisterInFlightEffect(468655)
    S.Meteor:RegisterInFlightEffect(351140)
    S.Meteor:RegisterInFlight()
    S.PhoenixFlames:RegisterInFlightEffect(257542)
    S.PhoenixFlames:RegisterInFlight()
    S.Pyroblast:RegisterInFlight(S.CombustionBuff)
    S.Fireball:RegisterInFlight(S.CombustionBuff)
    S.FrostfireBolt:RegisterInFlight(S.CombustionBuff)
    
    HL:RegisterForEvent(function()
        vars['opener'] = true
        BossFightRemains = 11111
        FightRemains = 11111
    end, "PLAYER_REGEN_ENABLED")
    
    HL:RegisterForEvent(function()
  
    end, "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")
    
    local function FirestarterActive()
      return (S.Firestarter:IsAvailable() and (Target:HealthPercentage() > 90))
    end
                  
    local function FreeCastAvailable()
        local FSInFlight = FirestarterActive() and (num(S.Pyroblast:InFlight() and S.Pyroblast:InFlightRemains() > -2 ) + num(vars['Bolt']:InFlight() and vars['Bolt']:InFlightRemains() > -2)) or 0
        FSInFlight = FSInFlight + num(S.PhoenixFlames:InFlight() and S.PhoenixFlames:InFlightRemains() > -2)
        
        -- Hot Streak Buff
        if Player:BuffUp(S.HotStreakBuff) then
            return true, "Hot Streak Buff"
        end
        
        -- Hyperthermia Buff (unless Feel the Burn is about to expire and we have Fire Blast charges)
        if Player:BuffUp(S.HyperthermiaBuff) then
            if S.FeeltheBurn:IsAvailable() and Player:BuffRemains(S.FeeltheBurnBuff) < 1.5 and S.FireBlast:ChargesFractional() > 0 then
                return false, "Need to maintain Feel the Burn"
            end
            return true, "Hyperthermia Buff"
        end
        
        -- Heating Up Buff with recent Fire Blast
        if Player:BuffUp(S.HeatingUpBuff) then
            if S.FireBlast:TimeSinceLastCast() < 0.2 then
                return true, "Heating Up Buff with recent Fire Blast"
            end

            if S.Scorch:TimeSinceLastCast() < 0.2 then
                return true, "Heating Up Buff with recent Scorch"
            end
        end
        
        
        if Player:BuffUp(S.HeatingUpBuff) then
            -- Scorch
            if ((vars['CombustionUp'] or ScorchExecute(Target) or ScorchExecute(MouseOver) or ScorchExecute(Focus)) 
            and (Player:IsCasting(S.Scorch) or IsCurrentSpell(S.Scorch:ID()))) then
                return true, "Scorch casting"
            end

            -- Firestarter Active or Combustion Up with Fireball casting
            if (FirestarterActive() or vars['CombustionUp']) and (Player:IsCasting(vars['Bolt']) or IsCurrentSpell(vars['Bolt']:ID()) or FSInFlight > 0) then
                return true, "Firestarter Active or Combustion Up with Fireball casting"
            end

            -- Phoenix Flames casting
            if IsCurrentSpell(S.PhoenixFlames:ID()) then
                return true, "Phoenix Flames casting"
            end
        end

        -- If the player has both the Fury of the Sun King and Heating Up buffs, and is currently casting Pyroblast or Flamestrike,
        -- then consider that the player is actively in a Pyro/FS casting state.
        if Player:BuffUp(S.FuryoftheSunKingBuff) and Player:BuffUp(S.HeatingUpBuff) and (Player:IsCasting(S.Pyroblast) or IsCurrentSpell(S.Pyroblast:ID()) or Player:IsCasting(S.Flamestrike) or IsCurrentSpell(S.Flamestrike:ID())) then
            return true, "Pyro/FS Casting"
        end

        if Player:IsCasting() then
            local castStart = Player:CastStart()
            local timeSinceCastStarted = GetTime() - castStart

            if Player:IsCasting(vars['Bolt']) and S.FireBlast:TimeSinceLastDisplay() < timeSinceCastStarted then
                return true, "Fireblast clipped"
            end
        end
        
        return false, "No Free Cast Available"
    end
  
    local function HotStreakInFlight()
        local Count = 0
        if vars['Bolt']:InFlight() and vars['Bolt']:InFlightRemains() > -2 
        or S.PhoenixFlames:InFlight() and S.PhoenixFlames:InFlightRemains() > -2 
        or S.Pyroblast:InFlight() and S.Pyroblast:InFlightRemains() > -2 then
            Count = Count + 1
        end
  
        if Player:IsCasting(vars['Bolt']) or Player:IsCasting(S.Pyroblast) or Player:IsCasting(S.Flamestrike) then
            Count = Count + 1
        end
        return Count
    end
  
    local function UnitsWithIgnite(enemies)
        local WithIgnite = 0
        ---@param CycleUnit Unit
        for _, CycleUnit in pairs(enemies) do
            if CycleUnit:DebuffUp(S.IgniteDebuff) then
                WithIgnite = WithIgnite + 1
            end
        end
        return WithIgnite
    end
    
    local function PrePullTimer()
        local PullTimer = M.CombinedPullTimer()
        if PullTimer < 7 then
            -- mirror_image
            if settings['mirrorimage_prepull'] and S.MirrorImage:IsReady() then
                if Cast(S.MirrorImage) and Target:TimeToDie() > 15 then
                    return "mirror image";
                end
            end
            -- flamestrike,if=active_enemies>=variable.hot_streak_flamestrike
            -- Note: Can't calculate enemies in Precombat
            -- pyroblast
            if vars['PyroFS']:IsReady() and not Player:IsCasting(vars['PyroFS']) and not IsCurrentSpell(vars['PyroFS']:ID()) and vars['PyroFS']:CastTime() >= PullTimer then
                if Cast(vars['PyroFS']) then
                    return "Pyro/FS";
                end
            end

            if (Player:IsCasting(vars['Bolt']) or Player:IsCasting(S.Scorch)) and Player:CastRemains() < 1 and S.Combustion:IsReady() then
                if Cast(S.Combustion, nil, nil, nil, nil, true) then
                    return "Combustion"
                end
            end
        end
    end

    local function Precombat()
        if (Player:IsCasting(vars['Bolt']) or Player:IsCasting(S.Scorch)) and Player:CastRemains() < 1 and S.Combustion:IsReady() then
            if Cast(S.Combustion, nil, nil, nil, nil, true) then
                return "Combustion"
            end
        end
        
        if S.Scorch:IsReady() and S.Scorch:TimeSinceLastCast() > 0.5 then
            if Cast(S.Scorch) then
                return "Scorch";
            end
        else
            if vars['Bolt']:IsReady() and vars['Bolt']:TimeSinceLastCast() > 0.5 then
                if Cast(vars['Bolt']) then
                    return "Fireball";
                end
            end
        end
    end

    -- Removed for now, will be reintroduced later
    -- local function Opener()
    --     local PullTimer = M.CombinedPullTimer()
    --     if settings['mirrorimage_prepull'] and S.MirrorImage:IsReady() and PullTimer < 4 then
    --         if Cast(S.MirrorImage) then
    --             return "Mirror Image"
    --         end
    --     end
    
    --     if vars['PyroFS']:IsReady() and Player:AffectingCombat() then
    --         if Player:BuffUp(S.HotStreakBuff) then
    --             if Cast(vars['PyroFS']) then
    --                 return "Instant-cast Pyro/FS (Hot Streak)"
    --             end

    --             if Player:BuffUp(S.HyperthermiaBuff) then
    --                 if Cast(vars['PyroFS']) then
    --                     return "Pyro/FS (Hyperthermia)"
    --                 end
    --             end
    --         end
    --     end

    --     if not Player:AffectingCombat() then
    --         if not Player:BuffUp(S.HotStreakBuff) then
    --             if vars['PyroFS']:IsReady() and not Player:IsCasting(vars['PyroFS']) and not IsCurrentSpell(vars['PyroFS']:ID()) then --and (Player:BuffUp(S.FlameAccelerantBuff) and PullTimer < 2 or PullTimer < 3) then
    --                 if Cast(vars['PyroFS']) then
    --                     return "Hard-cast Pyroblast"
    --                 end
    --             end
    --         else
    --             if vars['Bolt']:IsReady() then
    --                 if Cast(vars['Bolt']) then
    --                     return "Fireball"
    --                 end
    --             end
    --         end
    --     end
    
    --     if Player:IsCasting(vars['PyroFS']) and Player:CastRemains() < 0.5 then
    --         if S.PhoenixFlames:IsReady(nil, nil, true) then
    --             if Cast(S.PhoenixFlames) then
    --                 return "Phoenix Flames"
    --             end
    --         end
    --     end
        
    --     if S.FireBlast:IsReady(nil, nil, true) and S.ShiftingPower:IsReady() then
    --         if Cast(S.FireBlast, nil, nil, nil, nil, true) then
    --             return "Fire Blast"
    --         end
    --     end
    
    --     if S.FireBlast:IsReady(nil, nil, true) and Player:IsChanneling(S.ShiftingPower) then
    --         if Cast(S.FireBlast, nil, nil, nil, nil, true) then
    --             return "Fire Blast during Shifting Power"
    --         end
    --     end

    --     if S.ShiftingPower:IsReady() and not FreeCastAvailable() and not vars['CombustionUp'] and S.FireBlast:Charges() == 0 then
    --         if Cast(S.ShiftingPower) then
    --             return "Shifting Power"
    --         end
    --     end
        
    --     if S.PhoenixFlames:IsReady() and S.ShiftingPower:CooldownRemains(nil, true) > 0 then
    --         if Cast(S.PhoenixFlames) then
    --             return "Phoenix Flames"
    --         end
    --     end
        
    --     if S.FireBlast:IsReady(nil, nil, true) and Player:IsCasting(vars['Bolt']) then
    --         if Cast(S.FireBlast, nil, nil, nil, nil, true) then
    --             return "Fire Blast during Fireball"
    --         end
    --     end

    --     if vars['Bolt']:IsReady() then
    --         if Cast(vars['Bolt']) then
    --             return "Fireball"
    --         end
    --     end
    -- end

    local function SunfuryCombustion()
        -- Meteor inside Combustion:
        if S.Meteor:IsReady() and vars['CombustionRemains'] >= S.Meteor:TravelTime() then
            if Cast(S.Meteor) then
                return "meteor 2"
            end
        end
    
        -- -- no trinkets in here yet, placeholder
        -- -- Trinket usage during Combustion:
        -- if I.TRINKET:IsEquippedAndReady() and (Player:BuffRemains(S.CombustionBuff) > 10 or BossFightRemains < 25) then
        --     if Cast(I.TRINKET) then
        --         return "trinket 4"
        --     end
        -- end
    
        -- Offensive racials:
        -- Cast Blood Fury
        if S.BloodFury:IsReady() then
            if Cast(S.BloodFury) then
                return "blood_fury 6"
            end
        end
    
        -- Cast Berserking
        if S.Berserking:IsReady() then
            if Cast(S.Berserking) then
                return "berserking 8"
            end
        end
    
        -- Cast Fireblood
        if S.Fireblood:IsReady() then
            if Cast(S.Fireblood) then
                return "fireblood 10"
            end
        end
    
        -- Cast Ancestral Call
        if S.AncestralCall:IsReady() then
            if Cast(S.AncestralCall) then
                return "ancestral_call 12"
            end
        end
    
        -- Pyroblast (Pyro/FS) casting:
        if vars['PyroFS']:IsReady() then
            -- If Hot Streak is active, cast Pyroblast for Hot Streak.
            if Player:BuffUp(S.HotStreakBuff) then
                if Cast(vars['PyroFS']) then
                    return "pyroblast (hot_streak) 14"
                end
            end
            -- If Hyperthermia is active, cast Pyroblast for Hyperthermia.
            if Player:BuffUp(S.HyperthermiaBuff) then
                if Cast(vars['PyroFS']) then
                    return "pyroblast (hyperthermia) 16"
                end
            end
            -- If Fury of the Sun King is active, cast Pyroblast for Sun King's Blessing.
            if Player:BuffUp(S.FuryoftheSunKingBuff) then
                if Cast(vars['PyroFS']) then
                    return "pyroblast (sun_kings_blessing) 18"
                end
            end
        end
    
        -- Cast Phoenix Flames when ready and if the Flames Fury buff is active.
        if S.PhoenixFlames:IsReady() and Player:BuffUp(S.FlamesFuryBuff) then
            if Cast(S.PhoenixFlames) then
                return "phoenix_flames 20"
            end
        end
    
        -- Improved Scorch usage:
        if S.Scorch:IsReady() and S.ImprovedScorch:IsAvailable() and EvaluateImprovedScorch(Target) then
            if Cast(S.Scorch) then
                return "improved_scorch 22"
            end
        end        
    
        -- Cast Fire Blast:
        if S.FireBlast:IsReady(nil, nil, true) then
            if Cast(S.FireBlast, nil, nil, nil, nil, true) then
                return "fire_blast 24"
            end
        end
    
        -- Fallback Phoenix Flames:
        if S.PhoenixFlames:IsReady() then
            if Cast(S.PhoenixFlames) then
                return "phoenix_flames 28"
            end
        end
    
        -- Dragon's Breath usage:
        if S.DragonsBreath:IsReady() and 
           vars['CombustionRemains'] > 0 and vars['CombustionRemains'] <= 1 and not Player:BuffUp(S.HotStreakBuff) and 
           EnemiesCount10yMelee >= 1 and GetSetting('db_fish', false) then
             if Cast(S.DragonsBreath) then
                 return "dragons_breath 30"
             end
        end         
    
        -- Cast Fireball
        if vars['Bolt']:IsReady() and Player:BuffUp(S.FlameAccelerantBuff) then
            if Cast(vars['Bolt']) then
                return "fireball 32"
            end
        end
    
        -- Cast Scorch
        if S.Scorch:IsReady() then
            if Cast(S.Scorch) then
                return "scorch 34"
            end
        end
    end    

    local function FrostfireCombustion()
        -- Meteor inside Combustion:
        if S.Meteor:IsReady() and vars['CombustionRemains'] >= S.Meteor:TravelTime() then
            if Cast(S.Meteor) then
                return "meteor 2"
            end
        end
    
        -- no trinkets in here yet, placeholder
        -- -- Trinket usage during Combustion:
        -- if I.TRINKET:IsEquippedAndReady() and (Player:BuffRemains(S.CombustionBuff) > 10 or BossFightRemains < 25) then
        --     if Cast(I.TRINKET) then
        --         return "trinket 4"
        --     end
        -- end
    
        -- Blood Fury:
        if S.BloodFury:IsReady() then
            if Cast(S.BloodFury) then
                return "blood_fury 6"
            end
        end
    
        -- Berserking:
        if S.Berserking:IsReady() then
            if Cast(S.Berserking) then
                return "berserking 8"
            end
        end
    
        -- Fireblood:
        if S.Fireblood:IsReady() then
            if Cast(S.Fireblood) then
                return "fireblood 10"
            end
        end
    
        -- Ancestral Call:
        if S.AncestralCall:IsReady() then
            if Cast(S.AncestralCall) then
                return "ancestral_call 12"
            end
        end
    
        -- Pyro/FS casting:
        if vars['PyroFS']:IsReady() then
            if Player:BuffUp(S.HotStreakBuff) then
                if Cast(vars['PyroFS']) then
                    return "pyroblast (hot_streak) 14"
                end
            end
            if Player:BuffUp(S.HyperthermiaBuff) then
                if Cast(vars['PyroFS']) then
                    return "pyroblast (hyperthermia) 16"
                end
            end
            if Player:BuffUp(S.FuryoftheSunKingBuff) then
                if Cast(vars['PyroFS']) then
                    return "pyroblast (sun_kings_blessing) 18"
                end
            end
        end
    
        -- Cast Fire Blast:
        if S.FireBlast:IsReady(nil, nil, true) then
            if Cast(S.FireBlast, nil, nil, nil, nil, true) then
                return "fire_blast 20"
            end
        end
    
        -- Phoenix Flames with FF/EF:
        if S.PhoenixFlames:IsReady() then
            if Player:BuffUp(S.FlamesFuryBuff) or Player:BuffUp(S.ExcessFrostBuff) then
                if Cast(S.PhoenixFlames) then
                    return "phoenix_flames (ff_ef) 22"
                end
            end
        end
    
        -- Frostfire Bolt (Empowerment):
        if S.FrostfireBolt:IsAvailable() and Player:BuffUp(S.FrostfireEmpowermentBuff) and vars['Bolt']:IsReady() then
            if Cast(vars['Bolt']) then
                return "frostfire_bolt (empowerment) 24"
            end
        end
    
        -- Improved Scorch usage:
        if S.Scorch:IsReady() and S.ImprovedScorch:IsAvailable() and EvaluateImprovedScorch(Target) then
            if Cast(S.Scorch) then
                return "improved_scorch 26"
            end
        end     
    
        -- Fallback Phoenix Flames:
        if S.PhoenixFlames:IsReady() then
            if Cast(S.PhoenixFlames) then
                return "phoenix_flames 28"
            end
        end
    
        -- Dragon's Breath usage:
        if S.DragonsBreath:IsReady() and vars['CombustionRemains'] > 0 and vars['CombustionRemains'] <= 1 and not Player:BuffUp(S.HotStreakBuff) and EnemiesCount10yMelee >= 1 and GetSetting('db_fish', false) then
            if Cast(S.DragonsBreath) then
                return "dragons_breath 30"
            end
        end
    
        -- Fireball (FlameAccelerant inside Combustion):
        if vars['Bolt']:IsReady() and Player:BuffUp(S.FlameAccelerantBuff) then
            if Cast(vars['Bolt']) then
                return "fireball (flameaccelerant) 32"
            end
        end
    
        -- Scorch:
        if S.Scorch:IsReady() then
            if Cast(S.Scorch) then
                return "scorch 34"
            end
        end
    end    

    local function SunfuryRotation()
        -- High Priority Shifting Power:
        if GetSetting('spusage', 'spcomb') == 'spcomb' and S.ShiftingPower:CooldownUp(nil, true) and not FreeCastAvailable() and not vars['CombustionUp'] and vars['CanCastShiftingPower'] and S.Combustion:CooldownRemains(nil, true) ~= 0 and S.FireBlast:Charges() <= 2 then
            vars['ShouldUseShiftingPower'] = true
        end

        -- Meteor when Combustion is not ready:
        if S.Meteor:IsReady() and S.Combustion:CooldownRemains() > 45 and not S.UnleashedInferno:IsAvailable() then
            if Cast(S.Meteor) then
                return "meteor_combustion_not_ready 2"
            end
        end
    
        -- Pyro/FS casting with Fury of the Sun King:
        if vars['PyroFS']:IsReady() then
            if Player:BuffUp(S.FuryoftheSunKingBuff) and S.Combustion:CooldownRemains() >= 1.5 then
                if Cast(vars['PyroFS']) then
                    return "pyroblast (sun_kings_blessing) 4"
                end
            end
    
            -- Pyro/FS casting with Hot Streak:
            if Player:BuffUp(S.HotStreakBuff) then
                if Cast(vars['PyroFS']) then
                    return "pyroblast (hot_streak) 6"
                end
            end
    
            -- Pyro/FS casting with Hyperthermia:
            if Player:BuffUp(S.HyperthermiaBuff) then
                if Cast(vars['PyroFS']) then
                    return "pyroblast (hyperthermia) 8"
                end
            end
        end
    
        -- Entering Combustion via Scorch:
        if S.Combustion:IsReady() and Player:BuffDown(S.CombustionBuff, nil, true) then
            if (Player:IsCasting(vars['Bolt']) or Player:IsCasting(S.Scorch)) and Player:CastRemains() < 0.6 then
                if Cast(S.Combustion, nil, nil, nil, nil, true) then
                    return "enter_comb 12"
                end
            end

            if Cast(S.Scorch) then
                return "enter_comb_scorch 10"
            end    
        end
    
        -- Low Priority Shifting Power:
        if GetSetting('spusage', 'spcomb') == 'spstacks' and S.ShiftingPower:CooldownUp(nil, true) and not FreeCastAvailable() and not vars['CombustionUp'] and vars['CanCastShiftingPower'] and S.Combustion:CooldownRemains(nil, true) ~= 0 and S.FireBlast:Charges() == 0 then
            vars['ShouldUseShiftingPower'] = true
        end
    
        -- Improved Scorch usage:
        if S.Scorch:IsReady() and S.ImprovedScorch:IsAvailable() and EvaluateImprovedScorch(Target) then
            if S.Scorch:IsReady() then
                if Cast(S.Scorch, "execute") then
                    return "improved_scorch 18"
                end
            end
        end
    
        -- Fire Blast and Phoenix Flames when Fury of the Sun King is NOT active:
        if not Player:BuffUp(S.FuryoftheSunKingBuff) then
            if S.FireBlast:IsReady(nil, nil, true) then
                if Player:BuffUp(S.HeatingUpBuff) then
                    if Cast(S.FireBlast, nil, nil, nil, nil, true) then
                        return "fire_blast (heating_up) 20"
                    end
                end
                if S.FireBlast:ChargesFractional() > 2.4 and S.FireBlast:TimeSinceLastCast() > 1.5 then
                    if Cast(S.FireBlast, nil, nil, nil, nil, true) then
                        return "fire_blast (capping) 22"
                    end
                end
            end
    
            if S.PhoenixFlames:IsReady() then
                if Player:BuffUp(S.HeatingUpBuff) then
                    if Cast(S.PhoenixFlames) then
                        return "phoenix_flames (heating_up) 24"
                    end
                end
            end
        end

        -- Phoenix Flames to generate Heating Up:
        if S.PhoenixFlames:IsReady() and S.PhoenixFlames:TimeSinceLastCast() >= 1.5 then
            if Cast(S.PhoenixFlames) then
                return "phoenix_flames (generate_heating_up) 28"
            end
        end
    
        -- Dragon's Breath for Heating Up:
        if S.DragonsBreath:IsReady() and S.AlexstraszasFury:IsAvailable() and EnemiesCount10yMelee >= 1 and GetSetting('db_fish', false) then
            if Player:BuffUp(S.HeatingUpBuff) then
                if Cast(S.DragonsBreath) then
                    return "dragons_breath (heating_up) 26"
                end
            end
        end
    
        -- Scorch (Filler below 30% HP):
        if S.Scorch:IsReady() then
            if ((GetSetting('snipe_scorch_f', true) and vars['FocusIsValid'])) then
                if Cast(S.Scorch, "execute") then
                    return "scorch (filler_below_30hp) 30"
                end
            end
        end
    
        -- Scorch (Filler below 30% HP):
        if S.Scorch:IsReady() and Target:HealthPercentage() < 30 then
            if Cast(S.Scorch) then
                return "scorch (filler_below_30hp) 32"
            end
        end
    
        -- Fireball as filler:
        if vars['Bolt']:IsReady() then
            if Cast(vars['Bolt']) then
                return "fireball (filler) 32"
            end
        end
    
        -- Additional Filler when Moving:
        if Player:IsMoving() then
            if S.DragonsBreath:IsReady() and S.AlexstraszasFury:IsAvailable() and EnemiesCount10yMelee >= 1 and GetSetting('db_fish', false) then
                if Player:BuffUp(S.HeatingUpBuff) then
                    if Cast(S.DragonsBreath) then
                        return "dragons_breath (filler) 34"
                    end
                end
            end
    
            if S.Scorch:IsReady() and Target:HealthPercentage() < 30 then
                if Cast(S.Scorch) then
                    return "scorch (moving_lt_30hp) 36"
                end
            end
    
            if S.DragonsBreath:IsReady() and not S.AlexstraszasFury:IsAvailable() and EnemiesCount10yMelee >= 1 and GetSetting('db_fish', false) then
                if Player:BuffUp(S.HeatingUpBuff) then
                    if Cast(S.DragonsBreath) then
                        return "dragons_breath (filler_wo_alex) 38"
                    end
                end
            end
    
            if S.Scorch:IsReady() then
                if Cast(S.Scorch) then
                    return "scorch (moving) 40"
                end
            end
        end
    end    
  
    local function FrostfireRotation()
        -- High Priority Shifting Power:
        if GetSetting('spusage', 'spcomb') == 'spcomb' and S.ShiftingPower:CooldownUp(nil, true) and not FreeCastAvailable() and not vars['CombustionUp'] and vars['CanCastShiftingPower'] and S.Combustion:CooldownRemains(nil, true) ~= 0 and S.FireBlast:Charges() <= 2 then
            vars['ShouldUseShiftingPower'] = true
        end
    
        -- Entering Combustion via Scorch:
        if S.Combustion:IsReady() and Player:BuffDown(S.CombustionBuff, nil, true) then    
            if (Player:IsCasting(vars['Bolt']) or Player:IsCasting(S.Scorch)) and Player:CastRemains() < 0.6 then
                if Cast(S.Combustion, nil, nil, nil, nil, true) then
                    return "enter_comb 8"
                end
            end

            if Cast(S.Scorch) then
                return "enter_comb_scorch 4"
            end
        end
    
        -- Pyro/FS Casting:
        if vars['PyroFS']:IsReady() then
            if Player:BuffUp(S.FuryoftheSunKingBuff) and S.Combustion:CooldownRemains() >= 4 then
                if Cast(vars['PyroFS']) then
                    return "pyroblast (sun_kings_blessing) 10"
                end
            end
    
            if Player:BuffUp(S.HotStreakBuff) then
                if Cast(vars['PyroFS']) then
                    return "pyroblast (hot_streak) 12"
                end
            end
    
            if Player:BuffUp(S.HyperthermiaBuff) then
                if Cast(vars['PyroFS']) then
                    return "pyroblast (hyperthermia) 14"
                end
            end
        end
    
        -- Meteor (Combustion not ready):
        if S.Meteor:IsReady() and S.Combustion:CooldownRemains() > 45 and not S.UnleashedInferno:IsAvailable() then
            if Cast(S.Meteor) then
                return "meteor (combustion_not_ready) 16"
            end
        end
    
        -- Improved Scorch usage:
        if S.Scorch:IsReady() and S.ImprovedScorch:IsAvailable() and EvaluateImprovedScorch(Target) then
            if S.Scorch:IsReady() then
                if Cast(S.Scorch, "execute") then
                    return "improved_scorch 18"
                end
            end
        end
    
        -- Fire Blast and Phoenix Flames (when Fury of the Sun King is NOT active):
        if not Player:BuffUp(S.FuryoftheSunKingBuff) then
            if S.FireBlast:IsReady(nil, nil, true) then
                if Player:BuffUp(S.HeatingUpBuff) then
                    if Cast(S.FireBlast, nil, nil, nil, nil, true) then
                        return "fire_blast (heating_up) 20"
                    end
                end
                if S.FireBlast:ChargesFractional() > 2.4 and S.FireBlast:TimeSinceLastCast() > 1.5 then
                    if Cast(S.FireBlast, nil, nil, nil, nil, true) then
                        return "fire_blast (capping) 22"
                    end
                end
            end
    
            if S.PhoenixFlames:IsReady() and S.PhoenixFlames:ChargesFractional() >= 0.8 then
                if Player:BuffUp(S.HeatingUpBuff) then
                    if Cast(S.PhoenixFlames) then
                        return "phoenix_flames (heating_up) 24"
                    end
                end
            end
        end
    
        -- Phoenix Flames with FF/EF:
        if S.PhoenixFlames:IsReady() then
            if Player:BuffUp(S.FlamesFuryBuff) or Player:BuffUp(S.ExcessFrostBuff) then
                if Cast(S.PhoenixFlames) then
                    return "phoenix_flames (ff_ef) 26"
                end
            end
        end

        -- Low Priority Shifting Power:
        if GetSetting('spusage', 'spcomb') == 'spstacks' and S.ShiftingPower:CooldownUp(nil, true) and not FreeCastAvailable() and not vars['CombustionUp'] and vars['CanCastShiftingPower'] and S.Combustion:CooldownRemains(nil, true) ~= 0 and S.FireBlast:Charges() == 0 then
            vars['ShouldUseShiftingPower'] = true
        end
    
        -- Frostfire Bolt (Empowerment):
        if S.FrostfireBolt:IsAvailable() and Player:BuffUp(S.FrostfireEmpowermentBuff) and vars['Bolt']:IsReady() then
            if Cast(vars['Bolt']) then
                return "frostfire_bolt (empowerment) 28"
            end
        end
    
        -- Dragon's Breath (Heating Up):
        if S.DragonsBreath:IsReady() and S.AlexstraszasFury:IsAvailable() and EnemiesCount10yMelee >= 1 and GetSetting('db_fish', false) then
            if Player:BuffUp(S.HeatingUpBuff) then
                if Cast(S.DragonsBreath) then
                    return "dragons_breath (heating_up) 30"
                end
            end
        end
    
        -- Phoenix Flames (generate Heating Up):
        if S.PhoenixFlames:IsReady() and S.PhoenixFlames:TimeSinceLastCast() >= 1.5 then
            if Cast(S.PhoenixFlames) then
                return "phoenix_flames (generate_heating_up) 32"
            end
        end
    
        -- Scorch (Filler below 30% HP):
        if S.Scorch:IsReady() then
            if ((GetSetting('snipe_scorch_f', true) and vars['FocusIsValid'])) then
                if Cast(S.Scorch, "execute") then
                    return "scorch (filler_below_30hp) 34"
                end
            end
        end
    
        -- Scorch (Filler below 30% HP):
        if S.Scorch:IsReady() and Target:HealthPercentage() < 30 then
            if Cast(S.Scorch) then
                return "scorch (filler_below_30hp) 36"
            end
        end
    
        -- Frostfire Bolt (Filler):
        if vars['Bolt']:IsReady() then
            if Cast(vars['Bolt']) then
                return "frostfire_bolt (filler) 38"
            end
        end
    
        -- Additional Filler when Moving:
        if Player:IsMoving() then
            if S.DragonsBreath:IsReady() and S.AlexstraszasFury:IsAvailable() and EnemiesCount10yMelee >= 1 and GetSetting('db_fish', false) then
                if Player:BuffUp(S.HeatingUpBuff) then
                    if Cast(S.DragonsBreath) then
                        return "dragons_breath (filler) 40"
                    end
                end
            end
    
            if S.Scorch:IsReady() and Target:HealthPercentage() < 30 then
                if Cast(S.Scorch) then
                    return "scorch (moving_lt_30hp) 42"
                end
            end
    
            if S.DragonsBreath:IsReady() and not S.AlexstraszasFury:IsAvailable() and EnemiesCount10yMelee >= 1 and GetSetting('db_fish', false) then
                if Player:BuffUp(S.HeatingUpBuff) then
                    if Cast(S.DragonsBreath) then
                        return "dragons_breath (filler_wo_alex) 44"
                    end
                end
            end
    
            if S.Scorch:IsReady() then
                if Cast(S.Scorch) then
                    return "scorch (moving) 46"
                end
            end
        end
    end    
  
    local function ImmuneRotation()
        if S.Combustion:CooldownRemains(nil, true) > 0 then
            if vars['PyroFS']:IsReady() then
                if Player:BuffUp(S.HotStreakBuff) or Player:BuffUp(S.HyperthermiaBuff) or Player:BuffUp(S.FuryoftheSunKingBuff) then
                    if Cast(vars['PyroFS']) then
                        return "pyroblast"
                    end
                end
            end

            if S.FireBlast:IsReady(nil, nil, true) and S.FireBlast:ChargesFractional() > 2.4 and S.FireBlast:TimeSinceLastCast() > 1.5 then
                if Cast(S.FireBlast, nil, nil, nil, nil, true) then
                    return "fire blast (capping)"
                end
            end

            if S.Scorch:IsReady() and Target:HealthPercentage() < 30 then
                if Cast(S.Scorch) then
                    return "scorch (<30% hp)"
                end
            end

            if vars['Bolt']:IsReady() then
                if Cast(vars['Bolt']) then
                    return "fireball"
                end
            end

            if S.Scorch:IsReady() then
                if Cast(S.Scorch) then
                    return "scorch"
                end
            end
        else
            if vars['PyroFS']:IsReady() then
                if Player:BuffUp(S.HyperthermiaBuff) or Player:BuffUp(S.FuryoftheSunKingBuff) then
                    if Cast(vars['PyroFS']) then
                        return "pyroblast"
                    end
                end
            end

            if S.Scorch:IsReady() and Target:HealthPercentage() < 30 then
                if Cast(S.Scorch) then
                    return "scorch (<30% hp)"
                end
            end

            if vars['Bolt']:IsReady() then
                if Cast(vars['Bolt']) then
                    return "fireball"
                end
            end

            if S.Scorch:IsReady() then
                if Cast(S.Scorch) then
                    return "scorch"
                end
            end
        end
    end

    --- ======= ACTION LISTS =======
    local function APL()
        Enemies8ySplash = Target:GetEnemiesInSplashRange(8)
        Enemies10yMelee = Player:GetEnemiesInRange(10)

        if AoEON() then
            EnemiesCount8ySplash = Target:GetEnemiesInSplashRangeCount(8)
            EnemiesCount10ySplash = Target:GetEnemiesInSplashRangeCount(10)
            EnemiesCount16ySplash = Target:GetEnemiesInSplashRangeCount(16)
            EnemiesCount10yMelee = #Enemies10yMelee
            Enemies40y = Player:GetEnemiesInRange(40)
        else            
            EnemiesCount8ySplash = 1
            EnemiesCount10ySplash = 1
            EnemiesCount16ySplash = 1
            EnemiesCount10yMelee = 1
            Enemies40y = { Target }
        end
        EnemiesCount40y = #Enemies40y

        if AoEON() and EnemiesCount8ySplash < 3 
        and (GetTime() - vars['splash_init_time'] > 8 or GetTime() - HL.SplashEnemies.Tracker_LastUpdate > HL.CombatTime())
        then
            EnemiesCount8ySplash = EnemiesCount40y
            EnemiesCount10ySplash = EnemiesCount40y
            EnemiesCount16ySplash = EnemiesCount40y
        end
        if GetSetting('infotext', true) then
            MainAddon.InfoText = EnemiesCount8ySplash
        else
            MainAddon.InfoText = nil
        end

        VarUpdate()

        if GetSetting('if', {})[2] and Player:IsCasting(vars['PyroFS']) and Player:BuffRemains(S.IceFloes) < 3.5 or GetSetting('if', {})[1] and Player:IsChanneling(S.ShiftingPower) and Player:BuffRemains(S.IceFloes) < 4.5 then
            if S.IceFloes:IsReady(Player, nil, true, true) then
                if Cast(S.IceFloes, true, nil, nil, nil, true) then
                    return "Ice Floes"; 
                end
            end
        end

        if vars['ShouldUseShiftingPower'] then
            if S.ShiftingPower:CooldownUp() and not S.ShiftingPower:IsBlocked() and vars['CanCastShiftingPower'] 
            and (not Player:IsMoving() or S.IceFloes:IsAvailable() and not S.IceFloes:IsBlocked()) then
                if Player:BuffUp(S.IceFloes) and S.ShiftingPower:IsReady(Player) and Player:BuffRemains(S.IceFloes) > 4 then
                    if Cast(S.ShiftingPower) then
                        return "Shifting Power x Ice Floes";
                    end
                end
        
                if GetSetting('if', {})[1] and S.IceFloes:IsReady(Player, nil, true, true) and Player:BuffRemains(S.IceFloes) < 4.5 then
                    if Cast(S.IceFloes, true, nil, nil, nil, true) then
                        return "Ice Floes x Shifting Power"; 
                    end
                else
                    if S.ShiftingPower:IsReady(Player) then
                        if Cast(S.ShiftingPower) then
                            return "Shifting Power (normal)";
                        end
                    else
                        vars['ShouldUseShiftingPower'] = false
                    end
                end

                return "Waiting for Ice Floes"
            else
                if GetSetting('toast_SP', true) then
                    if S.ShiftingPower:IsBlocked() 
                    or Player:IsMoving() and not S.IceFloes:IsAvailable() 
                    or S.ShiftingPower:CooldownUp(nil, true) and not MainAddon.CDsON() then
                        MainAddon.UI:ShowToast("Fire Mage", "Shifting Power should be used now!", MainAddon.GetTexture(S.ShiftingPower), GetSetting('toast_SP_reset', 15))
                    end
                end
                vars['ShouldUseShiftingPower'] = false
            end
        end

        -- MainAddon.UpdateVariable("Scorch IsCastable", S.Scorch:IsCastable())
        -- MainAddon.UpdateVariable("Scorch IsReady", S.Scorch:IsReady())
        -- MainAddon.UpdateVariable("HotStreakBuff", Player:BuffUp(S.HotStreakBuff))
        -- MainAddon.UpdateVariable("Heating Up Buff", Player:BuffUp(S.HeatingUpBuff))
        -- MainAddon.UpdateVariable("Free Cast Available", FreeCastAvailable())
        -- MainAddon.UpdateVariable("Hot Streak In Flight", HotStreakInFlight())
        -- MainAddon.UpdateVariable("Bolt InFlight", vars['Bolt']:InFlight())
        -- MainAddon.UpdateVariable("Phoenix Flames", S.PhoenixFlames:IsReady())
        -- MainAddon.UpdateVariable("Combu Up", vars['CombustionUp'])
        -- MainAddon.UpdateVariable("1", FreeCastAvailable())
        -- MainAddon.UpdateVariable("3", Player:PrevOffGCD(1, S.FireBlast))
        -- MainAddon.UpdateVariable("InFlight: ", S.Pyroblast:InFlight())
        -- MainAddon.UpdateVariable("InFlightRemains: ", S.Pyroblast:InFlightRemains())

        if vars['CombatTime'] > 0.25
            and (Player:IsCasting(S.Scorch) or (Player:IsCasting(vars['Bolt']) and (vars['CombustionRemains'] > 0 or vars['PyroFS'] == S.Flamestrike)))
            and Player:CastRemains() > 1
            and Player:BuffUp(S.HotStreakBuff) then
             MainAddon.SetTopColor(1, "Stop Casting")
         end

        -- Defensives
        ShouldReturn = Defensives()
        if ShouldReturn then
            return ShouldReturn
        end

        -- Utilities
        ShouldReturn = Utilities()
        if ShouldReturn then
            return ShouldReturn
        end

        -- if vars['CombatTime'] < 2 then
        --     ShouldReturn = PrePullTimer()
        --     if ShouldReturn then
        --         return ShouldReturn
        --     end
        -- end

        if M.TargetIsValid() or Player:AffectingCombat() then
            -- Check how many units have ignite
            --UnitsWithIgniteCount = UnitsWithIgnite(Enemies8ySplash)

            if GetSetting('snipe_scorch_f', true) and S.Scorch:IsAvailable() then
                local TargetGUID = Target:GUID()
                local FocusGUID = Focus:GUID()
                local GetBestUnitSnipingUnit = GetBestUnitSniping()
                
                if GetBestUnitSnipingUnit then
                    if FocusGUID ~= GetBestUnitSnipingUnit:GUID() and TargetGUID ~= GetBestUnitSnipingUnit:GUID() then
                        MainAddon.Nameplate.AddIcon(GetBestUnitSnipingUnit, Spell(351172), true)
                        if MouseOver:Exists() and MouseOver:GUID() == GetBestUnitSnipingUnit:GUID() then
                            MainAddon.SetTopColor(1, "Focus Mouseover")
                            return
                        end
                    end

                    if TargetGUID == GetBestUnitSnipingUnit:GUID() and FocusGUID ~= TargetGUID then
                        MainAddon.SetTopColor(1, "Focus Target")
                    end
                end

                if GetSetting('focus_target_fallback', false) then
                    if not GetBestUnitSnipingUnit and MainAddon.TargetIsValid() and not Focus:Exists() then
                        MainAddon.SetTopColor(1, "Focus Target")
                    end
                end
            end

            -- Calculate fight_remains
            BossFightRemains = HL.BossFightRemains()
            FightRemains = BossFightRemains
            if FightRemains == 11111 then
                FightRemains = HL.FightRemains(Enemies8ySplash, false)
            end
        end

        vars['MOCheck'] = (MouseOver:IsEnemy() or MouseOver:IsATank() or MouseOver:IsAMelee())
        
        if MainAddon.TargetIsValid() then 
            if MainAddon.UsePotion() then
                MainAddon.SetTopColor(1, "Combat Potion")
            end

            -- Trinkets
            local shouldReturn = MainAddon.TrinketDPS()
            if shouldReturn then
                return shouldReturn
            end  
        
            if vars['CombustionUp'] and S.Combustion:TimeSinceLastCast() < 10 then
                ---@class Item
                local ItemToUse = Player:GetUseableItems(OnUseExcludes)
                if ItemToUse then
                    if Cast(ItemToUse) then
                        return "Generic use_items for " .. ItemToUse:Name();
                    end
                end

                if I.HyperthreadWristwraps:IsEquippedAndReady() and S.FireBlast:Charges() == 0 then
                    if Cast(I.HyperthreadWristwraps) then return "hyperthread_wristwraps"; end
                end
            end
        
            if not Player:AffectingCombat() then
                ShouldReturn = Precombat();
                if ShouldReturn then 
                    return "Precombat: " .. ShouldReturn
                end
            end            

            if vars['CombustionUp'] then
                if S.SpellfireSpheres:IsAvailable() then
                    ShouldReturn = SunfuryCombustion()
                    if ShouldReturn then
                        return "[SF][COMB]: " .. ShouldReturn
                    end
                else
                    ShouldReturn = FrostfireCombustion()
                    if ShouldReturn then
                        return "[FF][COMB]: " .. ShouldReturn
                    end
                end
            else
                if S.SpellfireSpheres:IsAvailable() then
                    ShouldReturn = SunfuryRotation()
                    if ShouldReturn then
                        return "[SF]: " .. ShouldReturn
                    end
                else
                    ShouldReturn = FrostfireRotation()
                    if ShouldReturn then
                        return "[FF]: " .. ShouldReturn
                    end
                end
            end
        end

        -- Check for immune target
        if Player:AffectingCombat() and Target:HasPvEImmunityCached() then
            ShouldReturn = ImmuneRotation()
            if ShouldReturn then
                return "[IMMUNE]: " .. ShouldReturn
            end
        end
    end

    local function Init()
    end
    MainAddon.SetAPL(63, APL, Init)
  
    -- Register event handler for Shifting Power
    HL:RegisterForEvent(function(_, unitID, _, spellID)
        if unitID == "player" then
            if spellID == S.ShiftingPower:ID() then
                vars['CanCastShiftingPower'] = true
            end
        end
    end, "UNIT_SPELLCAST_INTERRUPTED", "UNIT_SPELLCAST_SUCCEEDED", "UNIT_SPELLCAST_SENT")

    local FireOldSpellIsReady
    FireOldSpellIsReady = HL.AddCoreOverride("Spell.IsReady",
        function(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
            if GetSetting('combnodispel', true) then
                if (self == S.BlazingBarrier or self == S.MassBarrier or self == S.Supernova or self == S.DragonsBreath) then
                    if Player:BuffUp(S.CombustionBuff) then
                        return false, "Disabled due to Combustion"
                    end
                end
            end
            if GetSetting('MOOption', false) then
                if self == S.Flamestrike or self == S.Meteor then
                    if not vars['MOCheck'] or vars['CombatTime'] < 1 then
                        -- Special case: If we're trying to cast Flamestrike, we have Hot Streak or Hyperthermia,
                        -- and vars['PyroFS'] is set to Flamestrike, still return true
                        -- to allow the Cast function to handle the fallback to Pyroblast
                        if self == S.Flamestrike and vars['PyroFS'] == S.Flamestrike and 
                           (Player:BuffUp(S.HotStreakBuff) or Player:BuffUp(S.HyperthermiaBuff)) then
                            return true, "Allow fallback to Pyroblast"
                        end
                        return false, "MOCheck is false"
                    end
                end
            end

            local BaseCheck = FireOldSpellIsReady(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
            return BaseCheck
        end
    , 63)

    local OldIsCastable
    OldIsCastable = HL.AddCoreOverride("Spell.IsCastable",
        function(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                if MainAddon.PlayerSpecID() == 63 then
                ignoreMovement = false

                if Player:BuffUp(S.IceFloes) then
                    ignoreMovement = true
                end

                if self == S.ShiftingPower then
                    if Player:BuffUp(S.CombustionBuff) or not vars['CanCastShiftingPower'] then
                        return false, 'No SP during comb/hyper'
                    end
                end

                if self == S.Scorch then
                    ignoreMovement = true
                    if FreeCastAvailable() then
                        return false, "Is Casting"
                    end
                end

                if self:CastTime() > 0 then
                    if FreeCastAvailable() then
                        return false, "Waiting fs/pyro 0"
                    end
                end

                if (self == S.Pyroblast or self == S.Flamestrike) and Player:BuffUp(S.HotStreakBuff) then
                    ignoreMovement = true
                end

                if self == S.ShiftingPower then
                    if Player:IsStandingStillFor() < GetSetting('SPMovingValue', 0.75) and Player:BuffRemains(S.IceFloes) < 4 then
                        return false, 'Is Not Standing Still'
                    end

                    if GetSetting('SPMovingValue', 1.5) == 0 and GetSetting('if', {})[1] then
                        ignoreMovement = true
                    end
                end

                if self == S.Flamestrike then
                    if self:CastTime() > 0 then
                        if Player:BuffUp(S.FuryoftheSunKingBuff) and (Player:IsCasting(S.Pyroblast) or IsCurrentSpell(S.Pyroblast:ID()) or Player:IsCasting(S.Flamestrike) or IsCurrentSpell(S.Flamestrike:ID())) then
                            return false, "Already entering SKB"
                        end
                    end
                end

                if self == S.Pyroblast or self == S.Flamestrike then
                    if Player:AffectingCombat() then
                        if self:CastTime() > 0 then
                            -- Toggle Pool SKB
                            if Player:BuffUp(S.FuryoftheSunKingBuff) and M.Toggle:GetToggle('HoldingSKB') then
                                return false, "Holding SKB Toggle - Toggle"
                            end

                            if settings['skb_aoe_ttd_c'] then
                                if Player:GetEnemiesRangeTTD(40) < settings['skb_aoe_ttd_s'] and not Target:IsInBossList() then
                                    return false, "Holding SKB Toggle - TTD"
                                end
                            end

                            if Player:BuffUp(S.FuryoftheSunKingBuff) and (Player:IsCasting(S.Pyroblast) or IsCurrentSpell(S.Pyroblast:ID()) or Player:IsCasting(S.Flamestrike) or IsCurrentSpell(S.Flamestrike:ID())) then
                                return false, "Already entering SKB"
                            end

                            if Player:BuffRemains(S.CombustionBuff) > 2 and not Player:BuffUp(S.FuryoftheSunKingBuff) 
                            or Player:BuffDown(S.CombustionBuff) and not Player:BuffUp(S.FuryoftheSunKingBuff) then
                                return false, "Waiting fs/pyro 1"
                            end
                        end
                    end
                end

                if self == S.FireBlast then

                    -- To make sure we dont open with Fire Blast when Combustion is ready
                    if S.Combustion:IsReady() and vars['CombatTime'] < 0.3 then
                        return false, "not in combat yet"
                    end

                    -- Allow Fire Blast during Hyperthermia if Feel the Burn buff is about to expire
                    if Player:BuffUp(S.HyperthermiaBuff) and S.FeeltheBurn:IsAvailable() and Player:BuffRemains(S.FeeltheBurnBuff) < 1.5 then
                        return true, "Maintain Feel the Burn during Hyperthermia"
                    end

                    if settings['pool_for_skb'] then
                        if Player:BuffStack(S.SunKingsBlessingBuff) >= 8 or Player:BuffUp(S.FuryoftheSunKingBuff) then
                            if S.FireBlast:Charges() <= 1 then
                                return false, "Pooling Fire Blast for Sun King's Blessing"
                            end
                        end
                    end
                
                    if settings['fb_pooling_stacks_c'] and not vars['CombustionUp'] then
                        if S.FireBlast:ChargesFractional() <= settings['fb_pooling_stacks_s'] then
                            if settings['fb_pooling_combu_timer_c'] then
                                if S.Combustion:CooldownRemains() <= settings['fb_pooling_combu_timer_s'] then
                                    -- Prevent using Fire Blast when Heating Up is active during the pooling period
                                    if Player:BuffUp(S.HeatingUpBuff) then
                                        return false, "Pooling Fire Blast for Combustion (even with Heating Up)"
                                    end
                                    return false, "Pooling Fire Blast for Combustion"
                                end
                            end
                        end
                    end
                
                    if Player:BuffUp(S.HotStreakBuff) then
                        return false, "Waiting fs/pyro 2"
                    end
                
                    if FreeCastAvailable() then
                        return false, "Waiting fs/pyro 2.5"
                    end
                end

                if self == S.PhoenixFlames then

                    -- To make sure we dont open with Phoenix Flames when Combustion is ready
                    if S.Combustion:IsReady() and vars['CombatTime'] < 1 then
                        return false, "not in combat yet"
                    end

                    if settings['pool_for_skb'] then
                        if Player:BuffStack(S.SunKingsBlessingBuff) >= 8 or Player:BuffUp(S.FuryoftheSunKingBuff) then
                            if S.PhoenixFlames:Charges() <= 1 then
                                return false, "Pooling Phoenix Flames for Sun King's Blessing"
                            end
                        end
                    end

                    -- Prevent using Phoenix Flames when Hot Streak is active or about to proc
                    if Player:BuffUp(S.HotStreakBuff) or (Player:BuffUp(S.HeatingUpBuff) and S.FireBlast:TimeSinceLastCast() < 0.3) then
                        return false, "Hot Streak active or about to proc - Use Pyroblast first"
                    end
                
                    if settings['pf_pooling_stacks_c'] and not vars['CombustionUp'] then
                        if S.PhoenixFlames:ChargesFractional() <= settings['pf_pooling_stacks_s'] then
                            if settings['pf_pooling_combu_timer_c'] then
                                if S.Combustion:CooldownRemains() <= settings['pf_pooling_combu_timer_s'] then
                                    -- Prevent using Phoenix Flames when Heating Up is active during the pooling period
                                    if Player:BuffUp(S.HeatingUpBuff) then
                                        return false, "Pooling Phoenix Flames for Combustion (even with Heating Up)"
                                    end
                                    return false, "Pooling Phoenix Flames for Combustion"
                                end
                            end
                        end
                    end
                
                    if Player:BuffUp(S.HotStreakBuff) then
                        return false, "Waiting fs/pyro 2"
                    end
                
                    if FreeCastAvailable() then
                        return false, "Waiting fs/pyro 2.5"
                    end
                end

                if self:CastTime() > 0 and self ~= vars['Bolt'] then
                    if ((FreeCastAvailable() or Player:BuffUp(S.HotStreakBuff) or HotStreakInFlight() > 0 and (FirestarterActive() or Player:BuffUp(S.CombustionBuff))) 
                    and ((self ~= S.Pyroblast and self ~= S.Flamestrike) or Player:BuffUp(S.FuryoftheSunKingBuff))) then
                        return false, "Waiting fs/pyro 3"
                    end
                end

                if self == S.GreaterInvisibility or self == S.Invisibility then
                    if not MainAddon.safeVanish() then
                        return false, "Not safe to vanish"
                    end
                end
            end

            local BaseCheck, Reason = OldIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
            return BaseCheck, Reason
        end,
    63);
  

    local OldIsCastableQueue
    OldIsCastableQueue = HL.AddCoreOverride("Spell.IsCastableQueue",
        function(self, ignoreMovement)
            if MainAddon.PlayerSpecID() == 63 then
                if Player:BuffUp(S.IceFloes) then
                    ignoreMovement = true
                end

                if self == S.Scorch then
                    ignoreMovement = true
                end

                if (self == S.Pyroblast or self == S.Flamestrike) and Player:BuffUp(S.HotStreakBuff) then
                    ignoreMovement = true
                end
            end
            local BaseCheck, Reason = OldIsCastableQueue(self, ignoreMovement)
            return BaseCheck, Reason
        end,
    63);

      
    local OldIsMoving
    OldIsMoving = HL.AddCoreOverride("Player.IsMoving",
        function(self)
            if IsFalling() then
                return true, "Falling/Jumping"
            end
            local BaseCheck, Reason = OldIsMoving(self)
            return BaseCheck, Reason
        end,
    63);
  
    local FirePlayerBuffUp
    FirePlayerBuffUp = HL.AddCoreOverride("Player.BuffUp",
        function (self, ThisSpell, AnyCaster, Offset)
            local BaseCheck = FirePlayerBuffUp(self, ThisSpell, AnyCaster, Offset)
            if MainAddon.PlayerSpecID() == 63 then
                if ThisSpell == S.FlameAccelerantBuff then
                    if Player:BuffRemains(S.FlameAccelerantBuff) > 0 then
                        if Player:IsCasting(vars['Bolt']) or Player:IsCasting(S.Pyroblast) or Player:IsCasting(S.Flamestrike) then
                            return false
                        end
                    end
                end

                if ThisSpell == S.HotStreakBuff then
                    if Player:BuffRemains(S.HeatingUpBuff) > 0 then
                        if S.FireBlast:TimeSinceLastCast() < 0.3 then
                            return true
                        end
                    end
                end

                if ThisSpell == S.CombustionBuff then
                    if Player:BuffRemains(S.FuryoftheSunKingBuff) > 0 and (Player:IsCasting(S.Pyroblast) or Player:IsCasting(S.Flamestrike)) then
                        return true
                    end
                end
        
                if ThisSpell == S.HeatingUpBuff then
                    if Player:IsCasting(S.Scorch) and (ScorchExecute(Target) or GetSetting('snipe_scorch_f', true) and vars['FocusIsValid']) then
                        return true
                    end
                    -- "Predictive" Heating Up buff for SKB Pyroblast casts...
                    return BaseCheck or (Player:IsCasting(S.Pyroblast) or Player:IsCasting(S.Flamestrike)) and Player:BuffRemains(S.FuryoftheSunKingBuff) > 0
                else
                    return BaseCheck
                end
            end
            return BaseCheck
        end
    , 63)
  
    local FireOldPlayerAffectingCombat
    FireOldPlayerAffectingCombat = HL.AddCoreOverride("Player.AffectingCombat",
        function (self)
            return FireOldPlayerAffectingCombat(self)
            or Player:IsCasting(S.Pyroblast)
            or Player:IsCasting(vars['Bolt'])
            or Player:IsCasting(S.Scorch)
        end
    , 63)

    HL:RegisterForEvent(function(...)
        local _, subevent, _, sourceGUID, _, _, _, _, destName, _, _, spellId, spellName = CombatLogGetCurrentEventInfo()
        if sourceGUID == Player:GUID() then
          if (subevent == "SPELL_CAST_SUCCESS") then
            if spellId == 108853 then
                if Target:DebuffUp(S.IgniteDebuff) then
                    vars['splash_init_time'] = GetTime()
                end
            end

            if spellId == 2120 or spellId == 257541 then
                vars['splash_init_time'] = GetTime()
            end
          end
        end
    end, "COMBAT_LOG_EVENT_UNFILTERED")

    HL:RegisterForEvent(function(_, ThisUnit)
        if ThisUnit == "player" then
            vars['splash_init_time'] = 0
        end
    end, "UNIT_TARGET")
end