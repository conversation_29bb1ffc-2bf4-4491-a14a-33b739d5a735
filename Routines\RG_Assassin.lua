function A_259()
    -- HR UPDATE: ?? (??/??/??)
    -- REMEMBER: GetSetting('dm_logic') == 'strict' or 'tolerant'
    -- REMEMBER: MainAddon.Toggle:GetToggle('BurstAoE') for Cast(S.Envenom)
    ---@class MainAddon
    local MainAddon = MainAddon
    ---@class MainAddon
    local M = MainAddon
    -- HeroLib
    local HL = HeroLibEx
    ---@class Unit
    local Unit = HL.Unit
    ---@class Unit
    local Player = Unit.Player
    ---@class Unit
    local Target = Unit.Target
    ---@class Unit
    local Focus = Unit.Focus
    ---@class Unit
    local MouseOver = Unit.MouseOver
    ---@class Spell
    local Spell = HL.Spell
    ---@class Item
    local Item = HL.Item
    -- HeroRotation
    local Cast = M.Cast
    local CastPooling = M.CastPooling
    --local CastLeftNameplate = M.CastLeftNameplate
    local AoEON = M.AoEON
    -- Lua
    local pairs = pairs
    local mathfloor = math.floor
    local GetTime = _G['GetTime']
    local fastrandom = _G['fastrandom']
    local BoolToInt = M.num
    local ValueIsInArray = HL.Utils.ValueIsInArray
    local mathmax = math.max
    local mathmin = math.min
    local num = M.num
    local Delay = _G['C_Timer'].After

    local Rogue = M.Rogue

    local S = Spell.Rogue.Assassination
    local I = Item.Rogue.Assassination

    local OnUseExcludeTrinkets = {
        I.BottledFlayedwingToxin:ID(),
        I.ImperfectAscendancySerum:ID(),
        I.TreacherousTransmitter:ID(),
        I.MadQueensMandate:ID(),
        I.JunkmaestrosMegaMagnet:ID()
    }

    -- Toggle Setting
    MainAddon.Toggle.Special["Funneling"] = {
        Icon = MainAddon.GetTexture(S.Rupture),
        Name = "Funneling",
        Description = "Funneling.",
        Spec = 259
    }

    MainAddon.Toggle.Special["BurstAoE"] = {
        Icon = MainAddon.GetTexture(S.Envenom),
        Name = "Burst AoE",
        Description = "Burst AoE.",
        Spec = 259
    }

    local GetSetting = MainAddon.Config.GetClassSetting
    local Config_Key = MainAddon.GetClassVariableName()
    local Config_Color = 'FFF468'
    local Config_Table = {
        key = Config_Key,
        title = 'Rogue - Assassination',
        subtitle = '?? ' .. MainAddon.Version,
        width = 600,
        height = 700,
        profiles = true,
        config = {
            { type = 'header', text = '', size = 18, align = 'Center', color = Config_Color},
            { type = 'spacer' },{ type = 'ruler' },{ type = 'spacer' },
            { type = 'header', text = 'DPS',  color = Config_Color },
            { type = 'dropdown',
              text = 'Spread DoTs via Focus', key = 'spread_f',
              multiselect = true,
              list = {
                  { text = 'Rupture', key = 1 },
                  { text = 'Garrote', key = 2 },
              },
              default = {
                1,
                2
              },
            },
            { type = 'checkbox', text = ' Vanish DPS', icon = 1856, key = 'VanishDPS', default = true},
            { type = 'checkbox', text = ' Smooth CDs', icon = 360194, key = 'smooth_deathmark', default = true},
            { type = 'dropdown',
              text = 'Vanish Settings', key = 'VanishMode',
              multiselect = true,
              list = {
                  { text = 'Boss', key = 'VanishMode_Boss' },
                  { text = 'Trash', key = 'VanishMode_Trash' },
                  { text = 'Dummy', key = 'VanishMode_Dummy' },
                  { text = 'PvP', key = 'VanishMode_PvP' },
                  { text = 'Solo', key = 'VanishMode_Solo' }
              },
              default = {
                  "VanishMode_Boss",
                  "VanishMode_Trash",
                  'VanishMode_Dummy',
                  'VanishMode_PvP'
              },
            },
            { type = 'checkbox', text = " Vanish to re-apply Deathstalker's Mark", icon = S.Vanish:ID(), key = 'vanish_for_deathstalkers_mark', default = true},
            { type = 'spacer' },
            { type = 'dropdown',
                text = 'Funneling', key = 'hybrid_rotation',
                list = {
                    { text = 'Bosses', key = 'hybrid_rotation_boss' },
                    { text = 'Toggle', key = 'hybrid_rotation_toggle' },
                    { text = 'Always', key = 'hybrid_rotation_always' },
                    { text = 'Never', key = 'hybrid_rotation_never' },
                },
                default = 'hybrid_rotation_toggle',
            },
            { type = 'spinner', text = ' Indiscriminate Carnage desired targets (default: 2)', key = 'carnageusage', icon = S.IndiscriminateCarnage:ID(), min = 1, max = 10, default = 2 },
            -- { type = 'dropdown',
            --     icon = S.Deathmark:ID(),
            --     text = ' Deathmark logic', key = 'dm_logic',
            --     list = {
            --         { text = 'Strict (APL)', key = 'strict' },
            --         { text = 'Tolerant (Custom)', key = 'tolerant' },
            --     },
            --     default = 'tolerant',
            -- },
            { type = 'spacer' },
            { type = 'spinner', text = " Rupture Count Threshold", icon = S.Rupture:ID(), key = 'RuptureCountThreshold', min = 0, max = 40, default = 5 },
            { type = 'spinner', text = " Garrote Count Threshold", icon = S.Garrote:ID(), key = 'GarroteCountThreshold', min = 0, max = 40, default = 5 },
            -- { type = 'checkbox', text = ' Disabled Rupture spread while stealthed', icon = S.Rupture:ID(), key = 'NoLeftNameplatewhenICupRupture', default = false },
            -- { type = 'checkbox', text = ' Disabled Garrote spread while stealthed', icon = S.Garrote:ID(), key = 'NoLeftNameplatewhenICupGarrote', default = false },
            { type = 'spacer' },
            { type = 'header', text = 'Defensives',  color = Config_Color },
            { type = 'checkspin', text = ' Crimson Vial - Raid', icon = 185311, key = 'CrimsonVial', min = 1, max = 100, default_spin = 20, default_check = true },
            { type = 'checkspin', text = ' Crimson Vial - Dungeon', icon = 185311, key = 'PartyCrimsonVial', min = 1, max = 100, default_spin = 65, default_check = true },
            { type = 'checkspin', text = ' Crimson Vial - Solo', icon = 185311, key = 'SoloCrimsonVial', min = 1, max = 100, default_spin = 75, default_check = true },
            { type = 'spinner', text = ' Smart Feint above key level', key = 'smart_feint_above_key_level', icon = S.Feint:ID(), min = 1, max = 40, default = 2},
            { type = 'checkbox', text = ' Evasion when aggro/tanking', icon = S.Evasion:ID(), default = true, key = 'evasion_aggro' },
            { type = 'spacer' },
            { type = 'header', text = 'Utilities',  color = Config_Color },
            { type = 'checkbox', text = ' Stealth when enemies around', icon = 1784, default = true, key = 'autostealth' },
            { type = 'checkbox', text = ' Shiv to Purge', icon = 5938, default = false, key = 'shiv_purge' },
            { type = 'spacer' },
            { type = 'checkbox', text = ' Tricks Of The Trade', icon = 57934, key = 'ToT', default = true },
            { type = 'spacer' },
            { type = 'dropdown',
              width = 150,
              text = 'Lethal Poison', key = 'lethalpoison',
              list = {
                { text = 'Deadly', key = 'lethalpoison_deadly' },
                { text = 'Instant', key = 'lethalpoison_instant' },
                { text = 'Wound', key = 'lethalpoison_wound' },
                { text = 'Amplifying', key = 'lethalpoison_ampli' },
                { text = 'None', key = 'lethalpoison_none' },
              },
              default = 'lethalpoison_none',
            },
            { type = 'dropdown',
              width = 150,
              text = 'Non-Lethal Poison', key = 'nonlethalpoison',
              list = {
                { text = 'Crippling', key = 'nonlethalpoison_crippling' },
                { text = 'Numbing / Atrophic', key = 'nonlethalpoison_numbing' },
                { text = 'None', key = 'nonlethalpoison_none' },
              },
              default = 'nonlethalpoison_none',
            },
            { type = 'dropdown',
                width = 150,
                text = 'Dragon-Tempered Blades - Lethal Poison', key = 'dtb_lethalpoison',
                list = {
                { text = 'Deadly', key = 'dtb_lethalpoison_deadly' },
                { text = 'Instant', key = 'dtb_lethalpoison_instant' },
                { text = 'Wound', key = 'dtb_lethalpoison_wound' },
                { text = 'Amplifying', key = 'dtb_lethalpoison_ampli' },
                { text = 'None', key = 'dtb_lethalpoison_none' },
                },
                default = 'dtb_lethalpoison_none',
            },
            { type = 'dropdown',
                width = 150,
                text = 'Dragon-Tempered Blades - Non-Lethal Poison', key = 'dtb_nonlethalpoison',
                list = {
                { text = 'Crippling', key = 'dtb_nonlethalpoison_crippling' },
                { text = 'Numbing / Atrophic', key = 'dtb_nonlethalpoison_numbing' },
                { text = 'None', key = 'dtb_nonlethalpoison_none' },
                },
                default = 'dtb_nonlethalpoison_none',
            },
            { type = 'spacer' },
            { type = 'checkspin', text = ' Shroud Pull Timer', icon = 114018, key = 'ShroudUsage', min = 1, max = 12, default_spin = 6, default_check = false },
            { type = 'spacer' },
            { type = 'dropdown',
                text = "Re-Target Deathmark's unit", key = 'retarget_DM',
                list = {
                { text = 'MouseOver', key = 1 },
                { text = 'Auto', key = 2 },
                { text = 'None', key = 3 },
                },
                default = 3,
            },
            { type = 'spacer' },
            { type = 'header', text = 'PvP', color = Config_Color },
            { type = 'dropdown',
              text = 'Opener', key = 'PvPOpener',
              multiselect = false,
              list = {
                  { text = 'Cheap Shot', key = 'PvPOpenerCS' },
                  { text = 'Ambush', key = 'PvPOpenerAmbush' },
                  { text = 'None', key = 'PvPOpenerNone' },
              },
              default = 'PvPOpenerCS',
            },
            { type = 'spacer' },
            { type = 'header', text = 'WeakAuras', color = Config_Color },
            { type = 'button', text = 'Import Funneling Tracker', width = 150, callback = function()
                if not _G['WeakAuras'] then
                    return false
                end
                _G['WeakAuras'].Import("!WA:2!1r1xVTrrq8AzPI4euLAqriGkDkiL)OkcbAtruufYN15KuCCcNTBkqHZ7D7C(wY5Dp2)Ke33WcH458rWsWt8KfFc4bE(ueVYl5Jq)eWC7zf1c07HBNz2zMDNF)MzR1UX4g0g0FEnPWOZyCqEWG(D2RRV8SdssuGUMCYcH5eECQqEOGX1rT8723pi3LYu5zKj9HZ0Hjc5yIompuZgdl0QLVuSrPfJl9yqoLOHa4eGRVKyqRenl(igvN61evLKyntWv3lqPjsTtucJZuPoE4I2zQwYgncKQBSQCH4F6PNKdrvNWLktKn19mjjSZMh2QzV(H963mOFXRgTlqY0Px5ZHsa9jO3H(D6mVk(qBUWtwBufVr(FNy42RJ76B44IFzIysM7(egVjLIMFGBhwupTjA9vAgdwBV)D2CRv24(7aAR66FvODTlzm8c5aJ9Q8yTZsC3FZ(IrJYGYORKwFT2gohqwz0AB4QtbUR15YpjOnsURwAaRnitb)NDtiO1QT50QDDSsoovOGk)M7oy)MDd)Ib(d8d9FeYQEgeSJkjvqgeNcXhhzQ4nowgkNPQCilBpQYzgfIqOUpcBYD97Cy7bDo3Wxqno53SKnpb6xPVVGc)21YFN)3gMkHGY(g6ZW2lqYjzpcPAe()HPNkK0JKK8PhTqWlrW1Z3HLjftC9ez0ziTgaJk7DE7LlACbQgrIpEe2uZPEzcc9xp3OGqow1Y0injdl(vJuiYMb3kySjtZw(wddIZik1Qf3vEuZGoh06Zl2E5GGd2zG)qB42Th6HTPa600USrPA)Se0RIpDyEJRCjKWPHimfNE5lQJr97lvS9Q9xA4DxQkNKmeJg6vU7YfBF)Hdrz2tbu2zAzz2dvEqKsyKXqeBCUqQVqLsOItFC1Cz98LQgmBlr(X(l)2hbKJBAKe193ARWDnLiAylbIuIt5UhkfJKGs5A9yM0cCLSONgzLZvqwIDiV465V3lF(oxcXSscQ(8NZPZVQLDwSGtz25zNlWHD8gEqELQ87WbowYKIRptlIpPINFRA)0VScJM)M9g49b92RB4vzkCRpCrj)LvL87ET5jL1zpTKOj1F2Zv)LLruVwb((DdIfzc5dRvF3pBwd7x9pYWOZnBBM067F4t3A)pjcVJjSroNt4SY3Ie87v86T)dGOkZnWhPtFLIgECbhUGIGvPh2NjKkOS8utlDT8mlEI3yCIUDXxJbu8KIVP4BlcX1B8VTmVQsAvD3QvR(m8DiGAFe8hFT5mEfkJh0YZrXyW3oOom)2VCQGobhnzXH6uKwtrwEsdvW2BENn)4gN8xp(Fo")
            end },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
        }
    }
    Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
    Config_Table.config = MainAddon.BuildDPSTrinketUI(Config_Table.config, Config_Color)
    Config_Table.config = MainAddon.BuildCombatPotionUI(Config_Table.config, "Assassination", Config_Color)
    MainAddon.SetConfig(259, Config_Table)

    -- Custom var
    -- Define a table to hold the state variables
    local state = {
        spread_f_rupture = GetSetting('spread_f', {})[1] or false,
        spread_f_garrote = GetSetting('spread_f', {})[2] or false,
        
        InCombat = Player:AffectingCombat(),
        InDungeon = Player:IsInDungeonArea(),
        InRaid = Player:IsInRaidArea(),
        DungeonSlice = Player:IsInParty() and Player:IsInDungeonArea() and not Player:IsInRaid(),
        ---@type Unit
        Tank = Rogue.GetTank(),
        carnageusage = GetSetting('carnageusage', 2),
        ReTarget = nil,


        BleedTickTime = 1, 
        ExsanguinatedBleedTickTime = 1,
        ComboPoints = 0,
        ComboPointsDeficit = 5,
        RuptureThreshold = 2,
        CrimsonTempestThreshold = 2,
        GarroteThreshold = 5.4,
        RuptureDMGThreshold = 3,
        GarroteDMGThreshold = 3,
        PriorityRotation = false,
        EffectiveCPSpend = 0,
        ActualComboPoints = 0,
        EnergyIncoming = 0,
        RuptureCountThreshold = GetSetting('RuptureCountThreshold', 5),
        GarroteCountThreshold = GetSetting('GarroteCountThreshold', 5),

        PoisonedBleeds = 0,

        EnergyRegenCombined = 50,
        EnergyTimeToMaxCombined = 10,

        EnergyRegenSaturated = false,
        AvoidTea = false,
        CDSoon = false,
        NotPooling = false,
        ScentSaturated = false,
        ScentEffectiveMaxStacks = 0,

        SingleTarget = true,
        InCooldowns = false,
        UpperLimitEnergy = false
    }

    local random_number = ((fastrandom() * 3.5 - fastrandom() * 4.6) + 0.5)
    local TimeCripplingPoisonCasted = GetTime()
    local TimeDeadlyPoisonCasted = GetTime()
    local TimeInstantPoisonCasted = GetTime()
    local TimeAmplifyingPoisonCasted = GetTime()
    local TimeNumbingPoisonCasted = GetTime()
    local TimeWoundPoisonCasted = GetTime()
    local TimeAtrophicPoisonCasted = GetTime()
    local futureEnemies = {}
    local futureEnemiesCount = 0 
    local timeSinceLastDSMark = 0

    -- Custom func
    local function CastLeftNameplate(ThisUnit, ThisSpell)
        if ThisUnit == Target then
            if Cast(ThisSpell) then
                return true
            end
        end

        if ThisSpell == S.Rupture and state.spread_f_rupture  
        or ThisSpell == S.Garrote and state.spread_f_garrote then
            if Focus:GUID() and Player:CanAttack(Focus) and Focus:IsSpellInRange(S.Rupture) 
            and Focus:DebuffRefreshable(ThisSpell, ThisSpell == S.Rupture and state.RuptureThreshold or state.GarroteThreshold) then
                if ThisSpell == S.Rupture then
                    if MainAddon.SetTopTexture(6, "1943-Focus") then
                        return true
                    end
                end
                if ThisSpell == S.Garrote then
                    if MainAddon.SetTopTexture(6, "703-Focus") then
                        return true
                    end
                end                
            end
        else
            M.CastLeftNameplate(ThisUnit, ThisSpell)
        end
    end

    -- Is the current unit valid during cycle?
    ---@param ThisUnit Unit
    local function UnitIsCycleValid(ThisUnit, BestUnitTTD, TimeToDieOffset)
        return ThisUnit:IsSpellInRange(S.Mutilate) and (not BestUnitTTD or ThisUnit:FilteredTimeToDie(">", BestUnitTTD, TimeToDieOffset));
    end

    local function Vanish_DPS_Condition ()
        local VanishMode = GetSetting('VanishMode', {})
  
        if Target:IsDummy() and VanishMode["VanishMode_Dummy"] then
            return true
        end

        if Player:InPvP() and VanishMode["VanishMode_PvP"] then
            return true
        end
  
        if not Player:IsInPvEActivity() and not Player:IsInDelve() and not VanishMode["VanishMode_Solo"] then
            return false, "Not in PvE Activity"
        end
  
        if Player:InBossEncounter() and VanishMode["VanishMode_Boss"] then
            return true
        end
  
        if VanishMode["VanishMode_Trash"] then
            return true
        end
  
        return false
    end

    -- Rotation Variables
    local ShouldReturn
    --local BleedTickTime, ExsanguinatedBleedTickTime = 2 * Player:SpellHaste(), 1 * Player:SpellHaste()
    --local ComboPoints, ComboPointsDeficit, ActualComboPoints
    --local RuptureThreshold, GarroteThreshold, CrimsonTempestThreshold, RuptureDMGThreshold, GarroteDMGThreshold, RuptureDurationThreshold, RuptureTickTime, GarroteTickTime
    --local PriorityRotation
    --local RuptureCountThreshold, GarroteCountThreshold
    --local AvoidTea, CDSoon, NotPooling, PoisonedBleeds, EnergyRegenCombined, EnergyTimeToMaxCombined, EnergyRegenSaturated, SingleTarget, ScentSaturated
    local TrinketSyncSlot = 0
    ---@type Item
    local TrinketItem1, TrinketItem2
    -- local EnergyIncoming = 0
    -- local EffectiveCPSpend
    -- local DungeonSlice
    -- local InRaid

    -- Equipment
    local VarTrinketFailures = 0
    local function SetTrinketVariables ()
        local T1, T2 = Player:GetTrinketData(OnUseExcludeTrinkets)

        -- If we don't have trinket items, try again in 5 seconds.
        if VarTrinketFailures < 5 and ((T1.ID == 0 or T2.ID == 0) or (T1.SpellID > 0 and not T1.Usable or T2.SpellID > 0 and not T2.Usable)) then
            VarTrinketFailures = VarTrinketFailures + 1
            Delay(5, function()
            SetTrinketVariables()
            end
            )
            return
        end

        TrinketItem1 = T1.Object
        TrinketItem2 = T2.Object

        -- actions.precombat+=/variable,name=trinket_sync_slot,value=1,if=trinket.1.has_stat.any_dps&(!trinket.2.has_stat.any_dps|trinket.1.cooldown.duration>=trinket.2.cooldown.duration)&!trinket.2.is.treacherous_transmitter|trinket.1.is.treacherous_transmitter
        -- actions.precombat+=/variable,name=trinket_sync_slot,value=2,if=trinket.2.has_stat.any_dps&(!trinket.1.has_stat.any_dps|trinket.2.cooldown.duration>trinket.1.cooldown.duration)&!trinket.1.is.treacherous_transmitter|trinket.2.is.treacherous_transmitter
        if TrinketItem1:HasStatAnyDps() and (not TrinketItem2:HasStatAnyDps() or T1.Cooldown >= T2.Cooldown) and T2.ID ~= I.TreacherousTransmitter:ID() or T1.ID == I.TreacherousTransmitter:ID() then
            TrinketSyncSlot = 1
        elseif TrinketItem2:HasStatAnyDps() and (not TrinketItem1:HasStatAnyDps() or T2.Cooldown > T1.Cooldown) and T1.ID ~= I.TreacherousTransmitter:ID() or T2.ID == I.TreacherousTransmitter:ID() then
            TrinketSyncSlot = 2
        else
            TrinketSyncSlot = 0
        end
    end
    SetTrinketVariables()

    HL:RegisterForEvent(function()
        VarTrinketFailures = 0
        SetTrinketVariables()
    end, "PLAYER_EQUIPMENT_CHANGED")

        -- Spells Damage
        S.Envenom:RegisterDamageFormula(
        -- Envenom DMG Formula:
        --  AP * CP * Env_APCoef * Aura_M * ToxicB_M * DS_M * Mastery_M * Versa_M
        function ()
            return
            -- Attack Power
            Player:AttackPowerDamageMod() *
            -- Combo Points
            state.ComboPoints *
            -- Envenom AP Coef
            0.22 *
            -- Aura Multiplier (SpellID: 137037)
            1.0 *
            -- Shiv Multiplier
            (Target:DebuffUp(S.ShivDebuff) and 1.3 or 1) *
            -- Deeper Stratagem Multiplier
            (S.DeeperStratagem:IsAvailable() and 1.05 or 1) *
            -- Mastery Finisher Multiplier
            (1 + Player:MasteryPct()/100) *
            -- Versatility Damage Multiplier
            (1 + Player:VersatilityDmgPct() / 100)
        end
        )
        S.Mutilate:RegisterDamageFormula(
        function ()
            return
            -- Attack Power (MH Factor + OH Factor)
            (Player:AttackPowerDamageMod() + Player:AttackPowerDamageMod(true)) *
            -- Mutilate Coefficient
            0.485 *
            -- Aura Multiplier (SpellID: 137037)
            1.0 *
            -- Versatility Damage Multiplier
            (1 + Player:VersatilityDmgPct()/100)
        end
        )

    -- Master Assassin Remains Check
    local function MasterAssassinAuraUp()
        return Player:BuffRemains(S.MasterAssassinBuff) == 9999
    end
    local function MasterAssassinRemains ()
        -- Currently stealthed (i.e. Aura)
        if MasterAssassinAuraUp() then
            if S.Subterfuge:TalentRank() == 2 then 
                return Player:GCDRemains() + 10 
            end
            return Player:GCDRemains() + 3
        end
        -- Broke stealth recently (i.e. Buff)
        return Player:BuffRemains(S.MasterAssassinBuff, nil, true)
    end
    
    -- Improved Garrote Remains Check
    local function ImprovedGarroteRemains ()
        -- Currently stealthed (i.e. Aura)
        if Player:BuffUp(S.ImprovedGarroteAura) then
        return Player:GCDRemains() + 3
        end
        -- Broke stealth recently (i.e. Buff)
        return Player:BuffRemains(S.ImprovedGarroteBuff, nil, true)
    end
    
    -- Indiscriminate Carnage Remains Check
    local function IndiscriminateCarnageRemains ()
        -- Currently stealthed (i.e. Aura)
        if Player:BuffUp(S.IndiscriminateCarnageAura) then
            return Player:GCDRemains() + 10
        end
        -- Broke stealth recently (i.e. Buff)
        return Player:BuffRemains(S.IndiscriminateCarnageBuff, nil, true)
    end

    --- ======= HELPERS =======
    -- Check if the Priority Rotation variable should be set
    local NeutralFunnelMobs = {
        -- The Dawnbreaker
        [2662] = {
            [225601] = true, -- Mereldar Citizen
        }
    }
    
    local function ForceFunnel()
        local InstanceID = Player:InstanceID()
        if not NeutralFunnelMobs[InstanceID] then
            return false
        end

        local Enemies = Player:GetEnemiesInRangeUnfilter(10, "combatimmune")
        for _, ThisEnemy in ipairs(Enemies) do
            if NeutralFunnelMobs[InstanceID][ThisEnemy:NPCID()] then
                return true
            end
        end
        return false -- 
    end

    local function UsePriorityRotation() 
        if ForceFunnel() then
            return true
        end
        
        local hybrid_rotation = GetSetting('hybrid_rotation', 'hybrid_rotation_toggle')
        if state.MeleeEnemies10yCount < 2 then
            return false
        elseif hybrid_rotation == 'hybrid_rotation_always' then
            return true
        elseif hybrid_rotation == 'hybrid_rotation_boss' and Target:IsInBossList() then
            return true
        elseif hybrid_rotation == 'hybrid_rotation_toggle' then
            if MainAddon.Toggle:GetToggle('Funneling') then 
                return true 
            end 
        end
        return false
    end
        
    -- actions+=/variable,name=in_cooldowns,value=dot.kingsbane.ticking|debuff.shiv.up
    local function InCooldowns()
        return Target:DebuffUp(S.Kingsbane) or Target:DebuffUp(S.ShivDebuff)
    end
    -- actions+=/variable,name=clip_envenom,value=buff.envenom.up&buff.envenom.remains<=1 note. added .5 for delay
    local function ClipEnvenom()
        return Player:BuffUp(S.Envenom) and Player:BuffRemains(S.Envenom) <= 1
    end
    -- actions+=/variable,name=upper_limit_energy,value=energy.pct>=(80-10*talent.vicious_venoms.rank-30*talent.amplifying_poison)
    local function UpperLimitEnergy()
        return Player:EnergyPercentage() >= (80 - 10 * S.ViciousVenoms:TalentRank() - 30 * num(S.AmplifyingPoison:IsAvailable()))
    end
    -- actions+=/variable,name=cd_soon,value=talent.kingsbane&cooldown.kingsbane.remains<3&!cooldown.kingsbane.ready
    local function CDSoonVar()
        return S.Kingsbane:IsAvailable() and S.Kingsbane:CooldownRemains(nil, true) < 3 and not S.Kingsbane:IsCastable()
    end
    -- actions+=/variable,name=not_pooling,value=variable.in_cooldowns|buff.darkest_night.up|variable.upper_limit_energy|fight_remains<=20
    local function NotPoolingVar()
        if state.InCooldowns or Player:BuffUp(S.DarkestNightBuff) or state.UpperLimitEnergy or (HL.BossFilteredFightRemains("<=", 20) and state.InRaid) or Target:TimeToDie() <= 5 then
            return true
        end
        return false
    end
    
    
    -- actions.dot=variable,name=scent_effective_max_stacks,value=(spell_targets.fan_of_knives*talent.scent_of_blood.rank*2)>?20
    -- actions.dot+=/variable,name=scent_saturation,value=buff.scent_of_blood.stack>=variable.scent_effective_max_stacks
    local function ScentEffectiveMaxStacks()
        return mathmin(20, S.ScentOfBlood:TalentRank() * 2 * state.MeleeEnemies10yCount)
    end
    
    local function ScentSaturatedVar()
        if not S.ScentOfBlood:IsAvailable() then
            return true
        end
        return Player:BuffStack(S.ScentOfBloodBuff) >= state.ScentEffectiveMaxStacks
    end
    
    -- Custom Override for Handling 4pc Pandemics
    local function IsDebuffRefreshable(TargetUnit, Spell, PandemicThreshold)
        local PandemicThreshold = PandemicThreshold or Spell:PandemicThreshold()
        return TargetUnit:DebuffRefreshable(Spell, PandemicThreshold)
    end

    -- Handle CastLeftNameplate Suggestions for DoT Spells
    ---@param DoTSpell Spell
    local function SuggestCycleDoT(DoTSpell, DoTEvaluation, DoTMinTTD, Enemies)
        -- Custom
        if MainAddon.Toggle:GetToggle('BurstAoE') or S.Kingsbane:TimeSinceLastCast() < 14 or S.Deathmark:TimeSinceLastCast() < 16 then
            return
        end

        -- Custom (need feedback)
        if state.InRaid then
            if Target:Exists() and DoTEvaluation(Target) then
                CastLeftNameplate(Target, DoTSpell)
                return
            end
            --return
        end

        -- Prefer melee cycle units
        local BestUnit, BestUnitTTD = nil, DoTMinTTD
        local TargetGUID = Target:GUID()
        ---@param CycleUnit Unit
        for _, CycleUnit in pairs(Enemies) do
            if UnitIsCycleValid(CycleUnit, BestUnitTTD, -CycleUnit:DebuffRemains(DoTSpell))
            and DoTEvaluation(CycleUnit) then
                BestUnit, BestUnitTTD = CycleUnit, CycleUnit:TimeToDie()
            end
        end
        if BestUnit then
            if CastLeftNameplate(BestUnit, DoTSpell) then return true end
        -- Check ranged units next, if the RangedMultiDoT option is enabled
        else
            BestUnit, BestUnitTTD = nil, DoTMinTTD
            ---@param CycleUnit Unit
            for _, CycleUnit in pairs(state.MeleeEnemies5y) do
                if UnitIsCycleValid(CycleUnit, BestUnitTTD, -CycleUnit:DebuffRemains(DoTSpell))
                and DoTEvaluation(CycleUnit) then
                    BestUnit, BestUnitTTD = CycleUnit, CycleUnit:TimeToDie()
                end
            end
            if BestUnit then
                if CastLeftNameplate(BestUnit, DoTSpell) then return true end
            end
        end
    end

    -- Target If handler
    -- Mode is "min", "max", or "first"
    -- ModeEval the target_if condition (function with a target as param)
    -- IfEval the condition on the resulting target (function with a target as param)
    local function CheckTargetIfTarget(Mode, ModeEvaluation, IfEvaluation)
        -- First mode: Only check target if necessary
        local TargetsModeValue = ModeEvaluation(Target)
        if Mode == "first" and TargetsModeValue ~= 0 then
            return Target
        end

        -- Custom
        if state.InRaid then
            if Target:Exists() and IfEvaluation(Target) then
                return Target
            end
            --return nil
        end
        
        ---@type Unit
        local BestUnit, BestValue = nil, 0
        local function RunTargetIfCycler(Enemies)
            ---@param CycleUnit Unit
            for _, CycleUnit in pairs(Enemies) do
                local ValueForUnit = ModeEvaluation(CycleUnit)
                if not BestUnit and Mode == "first" then
                    if ValueForUnit ~= 0 then
                    BestUnit, BestValue = CycleUnit, ValueForUnit
                    end
                elseif Mode == "min" then
                    if not BestUnit or ValueForUnit < BestValue then
                    BestUnit, BestValue = CycleUnit, ValueForUnit
                    end
                elseif Mode == "max" then
                    if not BestUnit or ValueForUnit > BestValue then
                    BestUnit, BestValue = CycleUnit, ValueForUnit
                    end
                end
                -- Same mode value, prefer longer TTD
                if BestUnit and ValueForUnit == BestValue and CycleUnit:TimeToDie() > BestUnit:TimeToDie() then
                    BestUnit, BestValue = CycleUnit, ValueForUnit
                end
            end
        end
        
        -- Prefer melee cycle units over ranged
        RunTargetIfCycler(state.MeleeEnemies5y)
        -- Prefer current target if equal mode value results to prevent "flickering"
        if BestUnit and BestValue == TargetsModeValue and IfEvaluation(Target) then
            return Target
        end
        if BestUnit and IfEvaluation(BestUnit) then
            return BestUnit
        end
        return nil
    end
      
    local baseDamageMap = {
        [584] = 2414811,
        [587] = 2489600,
        [590] = 2566685,
        [593] = 2646114,
        [597] = 2755894,
        [600] = 2841156,
        [603] = 2929054,
        [606] = 3019638,
        [610] = 3144769,
        [613] = 3241983,
        [616] = 3342181,
        [619] = 3445456,
        [623] = 3588099,
        [626] = 3698923,
        [629] = 3813138,
        [632] = 3930865,
        [636] = 4093467,
        [639] = 4219779
    }
    -- Functions for calculating trinket damage
    local function GetMadQueensBaseDamage()
        -- Get the item level of Mad Queen's Mandate
        local itemLevel = I.MadQueensMandate:Level()
        return baseDamageMap[itemLevel] or 0
    end
    
    local function CalculateMadQueensDamage()
        local currentHealth = Target:Health()
        local maxHealth = Target:MaxHealth()
    
        if currentHealth == 0 or not currentHealth then return 0 end
        
        -- Get base damage based on item level
        local baseDamage = GetMadQueensBaseDamage()
        -- Calculate damage scaling with missing health
        local healthFactor = 1 + (math.min((maxHealth - currentHealth) / maxHealth, 0.5) / 2) -- 1% per 2% missing health, capped at 50%
        return baseDamage * healthFactor
    end

    -- =================== Start of Second Custom Functions ===================
    local function CheckTimeToDieUnits()
        futureEnemies = {}
        futureEnemiesCount = 0
    
        for i, ThisUnit in pairs(state.MeleeEnemies10y) do
            if ThisUnit:TimeToDie() > 60 then
                table.insert(futureEnemies, ThisUnit)
                futureEnemiesCount = futureEnemiesCount + 1
            end
        end
    end

    local function Poisons()
        local PoisonRefreshTime = state.InCombat and 2 or 1200
        local lethalPoison = GetSetting('lethalpoison', 'lethalpoison_none')
        local nonlethalPoison = GetSetting('nonlethalpoison', 'nonlethalpoison_none')
        local dtb_lethalPoison = GetSetting('dtb_lethalpoison', 'dtb_lethalpoison_none')
        local dtb_nonlethalPoison = GetSetting('dtb_nonlethalpoison', 'dtb_nonlethalpoison_none')

        -- Lethal Poison
        if lethalPoison ~= 'lethalpoison_none' then
            if lethalPoison == 'lethalpoison_deadly' then
                if Player:BuffRemains(S.DeadlyPoison) < PoisonRefreshTime and GetTime() - TimeDeadlyPoisonCasted > 2 then
                    if Cast(S.DeadlyPoison) then
                        return 'Deadly Poison'
                    end
                end
            end
            if lethalPoison == 'lethalpoison_instant' then
                if Player:BuffRemains(S.InstantPoison) < PoisonRefreshTime and GetTime() - TimeInstantPoisonCasted > 2 then
                    if Cast(S.InstantPoison) then
                        return 'Instant Poison'
                    end
                end
            end
            if lethalPoison == 'lethalpoison_wound' then
                if Player:BuffRemains(S.WoundPoison) < PoisonRefreshTime and GetTime() - TimeWoundPoisonCasted > 2 then
                    if Cast(S.WoundPoison) then
                        return 'Wound Poison'
                    end
                end
            end
            if lethalPoison == 'lethalpoison_ampli' then
                if S.AmplifyingPoison:IsAvailable() then
                    if Player:BuffRemains(S.AmplifyingPoison) < PoisonRefreshTime and GetTime() - TimeAmplifyingPoisonCasted > 2 then
                        if Cast(S.AmplifyingPoison) then
                            return 'Amplifying Poison'
                        end
                    end
                end
            end
        end
        if not state.InCombat then
            -- Non-Lethal Poison
            if nonlethalPoison ~= 'nonlethalpoison_none' then
                if nonlethalPoison == 'nonlethalpoison_crippling' then
                    if Player:BuffRemains(S.CripplingPoison) < PoisonRefreshTime and GetTime() - TimeCripplingPoisonCasted > 2 then
                        if Cast(S.CripplingPoison) then
                            return 'Crippling Poison'
                        end
                    end
                end
                if nonlethalPoison == 'nonlethalpoison_numbing' then
                    if S.NumbingPoison:IsAvailable() and Player:BuffRemains(S.NumbingPoison) < PoisonRefreshTime and GetTime() - TimeNumbingPoisonCasted > 2 then
                        if Cast(S.NumbingPoison) then
                            return 'Numbing Poison'
                        end
                    end
                    if S.AtrophicPoison:IsAvailable() and Player:BuffRemains(S.AtrophicPoison) < PoisonRefreshTime and GetTime() - TimeAtrophicPoisonCasted > 2 then
                        if Cast(S.AtrophicPoison) then
                            return 'Atrophic Poison'
                        end
                    end
                end
            end
        end

        if S.DragonTemperedBlades:IsAvailable() then
            -- Dragon-Tempered Blades - Lethal Poison
            if dtb_lethalPoison ~= 'dtb_lethalpoison_none' then
                if dtb_lethalPoison == 'dtb_lethalpoison_deadly' then
                    if Player:BuffRemains(S.DeadlyPoison) < PoisonRefreshTime and GetTime() - TimeDeadlyPoisonCasted > 2  then
                        if Cast(S.DeadlyPoison) then
                            return 'DtB Deadly Poison'
                        end
                    end
                end
                if dtb_lethalPoison == 'dtb_lethalpoison_instant' then
                    if Player:BuffRemains(S.InstantPoison) < PoisonRefreshTime and GetTime() - TimeInstantPoisonCasted > 2 then
                        if Cast(S.InstantPoison) then
                            return 'DtB Instant Poison'
                        end
                    end
                end
                if dtb_lethalPoison == 'dtb_lethalpoison_wound' then
                    if Player:BuffRemains(S.WoundPoison) < PoisonRefreshTime and GetTime() - TimeWoundPoisonCasted > 2 then
                        if Cast(S.WoundPoison) then
                            return 'DtB Wound Poison'
                        end
                    end
                end
                if dtb_lethalPoison == 'dtb_lethalpoison_ampli' then
                    if S.AmplifyingPoison:IsAvailable() then
                        if Player:BuffRemains(S.AmplifyingPoison) < PoisonRefreshTime and GetTime() - TimeAmplifyingPoisonCasted > 2 then
                            if Cast(S.AmplifyingPoison) then
                                return 'DtB Amplifying Poison'
                            end
                        end
                    end
                end
            end
            if not state.InCombat then
                -- Dragon-Tempered Blades - Non-Lethal Poison
                if dtb_nonlethalPoison ~= 'dtb_nonlethalpoison_none' then
                    if dtb_nonlethalPoison == 'dtb_nonlethalpoison_crippling' and nonlethalPoison ~= 'nonlethalpoison_crippling' then
                        if Player:BuffRemains(S.CripplingPoison) < PoisonRefreshTime and GetTime() - TimeCripplingPoisonCasted > 2 then
                            if Cast(S.CripplingPoison) then
                                return 'DtB Crippling Poison'
                            end
                        end
                    end
                    if dtb_nonlethalPoison == 'dtb_nonlethalpoison_numbing' and nonlethalPoison ~= 'nonlethalpoison_numbing' then
                        if S.NumbingPoison:IsAvailable() and Player:BuffRemains(S.NumbingPoison) < PoisonRefreshTime and GetTime() - TimeNumbingPoisonCasted > 2 then
                            if Cast(S.NumbingPoison) then
                                return 'DtB Numbing Poison'
                            end
                        end
                        if S.AtrophicPoison:IsAvailable() and Player:BuffRemains(S.AtrophicPoison) < PoisonRefreshTime and GetTime() - TimeAtrophicPoisonCasted > 2 then
                            if Cast(S.AtrophicPoison) then
                                return 'DtB Atrophic Poison'
                            end
                        end
                    end
                end
            end
        end
    end

    local function evaluateVial()
        if Player:BuffDown(S.CrimsonVial) and S.CrimsonVial:IsReady(Player) then
            if state.InRaid then
                if GetSetting('CrimsonVial_check', false) and Player:HealthPercentage() <= GetSetting('CrimsonVial_spin', 30) then
                    return true
                end
            elseif state.InDungeon then
                if GetSetting('PartyCrimsonVial_check', false) and Player:HealthPercentage() <= GetSetting('PartyCrimsonVial_spin', 30) then
                    return true
                end
            else
                if GetSetting('SoloCrimsonVial_check', false) and Player:HealthPercentage() <= GetSetting('SoloCrimsonVial_spin', 30) then
                    return true
                end
            end
        end
        return false
    end

    local function Defensives()
        if Player:MythicDifficulty() >= GetSetting('smart_feint_above_key_level', 2) or not state.InDungeon then
            if Player:ShouldFeint() and Player:BuffDown(S.Feint) and Player:BuffDown(S.CloakofShadows) and S.Feint:IsReady(Player) then
                if Cast(S.Feint) then
                    MainAddon.UI:ShowToast("Feint", "Dangerous situation detected !", MainAddon.GetTexture(S.Feint))
                    return "Feint"
                end
            end
        end

        if GetSetting('evasion_aggro', true) and Player:IsInParty() then
            if Player:IsTankingAoEUnfiltered(10) then
                if S.Evasion:IsReady(Player) then
                    if Cast(S.Evasion) then
                        return 'Evasion'
                    end
                end
            end
        end
    end

    local function PvPOpener()
        -- Opener
        local PvPOpenerSetting = GetSetting('PvPOpener', 'PvPOpenerNone')
        if PvPOpenerSetting == "PvPOpenerCS" then
            if S.CheapShot:IsReady() then
                if Cast(S.CheapShot) then
                    return 'PvP Cheap Shot'
                end
            else
                return
            end
        end
        if PvPOpenerSetting == "PvPOpenerAmbush" then
            if S.Ambush:IsReady() then
                if Cast(S.Ambush) then
                    return 'PvP Ambush 2'
                end
            else
                return
            end
        end
        if PvPOpenerSetting == "PvPOpenerNone" then
            return
        end
    end

    ---@param CycleUnit Unit
    local function EvaluateDeathmark(CycleUnit)
        return CycleUnit:DebuffUp(S.Deathmark) 
    end

    local function UnitsWithDM()
        if Target:DebuffUp(S.Deathmark) then
            return 0
        end
        if S.Deathmark:CooldownUp() then
            return 0
        end
        local Amount = 0
        for _, CycleUnit in pairs(state.MeleeEnemies10y) do
            if EvaluateDeathmark(CycleUnit) then
                Amount = Amount + 1
            end
        end
        return Amount
    end

    local function GetBestUnitSniping()
        local BestUnit = nil

        if Player:StealthUp(true, false) then
            for _, CycleUnit in pairs(state.MeleeEnemies5y_Sorted) do
                if CycleUnit:GUID() ~= Target:GUID() 
                and ((state.ActualComboPoints >= state.EffectiveCPSpend and not Focus:DebuffRefreshable(S.Rupture) 
                or state.ActualComboPoints < state.EffectiveCPSpend and not Focus:DebuffRefreshable(S.Garrote) 
                or not Focus:Exists() 
                or not Focus:IsSpellInRange(S.Rupture)) 
                and state.spread_f_rupture or not state.spread_f_rupture and state.spread_f_garrote) then
                    if CycleUnit:DebuffRefreshable(S.Garrote) and state.spread_f_garrote and CycleUnit:DebuffRefreshable(S.Rupture) and state.spread_f_rupture then
                        if CycleUnit:TimeToDie() > 8 then
                            BestUnit = CycleUnit
                        end
                    end

                    if not BestUnit then
                        if CycleUnit:DebuffRefreshable(S.Rupture) and state.spread_f_rupture then
                            if CycleUnit:TimeToDie() > 8 then
                                BestUnit = CycleUnit
                            end
                        end
                    end

                    if not BestUnit then
                        if CycleUnit:DebuffRefreshable(S.Garrote) and (not Focus:DebuffRefreshable(S.Garrote) or not Focus:Exists()) and state.spread_f_garrote then
                            if CycleUnit:TimeToDie() > 8 then
                                BestUnit = CycleUnit
                            end
                        end
                    end
                end
            end
        else
            for _, CycleUnit in pairs(state.MeleeEnemies5y) do
                if CycleUnit:GUID() ~= Target:GUID() 
                and ((not Focus:DebuffRefreshable(S.Rupture) or not Focus:Exists() or not Focus:IsSpellInRange(S.Rupture)) and state.spread_f_rupture or not state.spread_f_rupture and state.spread_f_garrote) then
                    if CycleUnit:DebuffRefreshable(S.Garrote) and state.spread_f_garrote and CycleUnit:DebuffRefreshable(S.Rupture) and state.spread_f_rupture then
                        if CycleUnit:TimeToDie() > 8 then
                            BestUnit = CycleUnit
                        end
                    end

                    if not BestUnit then
                        if CycleUnit:DebuffRefreshable(S.Rupture) and state.spread_f_rupture then
                            if CycleUnit:TimeToDie() > 8 then
                                BestUnit = CycleUnit
                            end
                        end
                    end

                    if not BestUnit then
                        if CycleUnit:DebuffRefreshable(S.Garrote) and (not Focus:DebuffRefreshable(S.Garrote) or not Focus:Exists()) and state.spread_f_garrote then
                            if CycleUnit:TimeToDie() > 8 then
                                BestUnit = CycleUnit
                            end
                        end
                    end
                end
            end
        end
        return BestUnit
    end
    -- =================== End of Second Custom Functions ==================
    --- ======= ACTION LISTS =======
    -- # Stealthed
    -- # Stealthed
    local function Stealthed (ReturnSpellOnly, ForceStealth)
        -- actions.stealthed=pool_resource,for_next=1

        -- # Apply Deathstalkers Mark if it has fallen off
        -- actions.stealthed+=/ambush,if=!debuff.deathstalkers_mark.up&talent.deathstalkers_mark&combo_points<variable.effective_spend_cp
        -- &(dot.rupture.ticking|variable.single_target|!talent.subterfuge)
        if (S.Ambush:IsReady() or (ForceStealth and Player:BuffDown(S.DarkestNightBuff))) and Target:DebuffDown(S.DeathStalkersMarkDebuff) and S.DeathStalkersMark:IsAvailable()
        and state.ComboPoints < state.EffectiveCPSpend and (Target:DebuffUp(S.Rupture) or state.SingleTarget or not S.Subterfuge:IsAvailable() ) then
            if ReturnSpellOnly then
                return S.Ambush
            else
                if Cast(S.Ambush) then
                    return "Cast Ambush Stealthed"
                end
            end
        end
    
        -- # Make sure to have Shiv up during Kingsbane as a final check
        --actions.stealthed+=/shiv,if=talent.kingsbane&dot.kingsbane.ticking&dot.kingsbane.remains<8&(!debuff.shiv.up&debuff.shiv.remains<1)&buff.envenom.up
        if S.Kingsbane:IsAvailable() and not ForceStealth and Player:BuffUp(S.Envenom) then
            if S.Shiv:IsReady(nil, nil, nil, nil, nil, nil, true) and Target:DebuffUp(S.Kingsbane) and Target:DebuffRemains(S.Kingsbane) < 8
            and (Target:DebuffDown(S.ShivDebuff) and Target:DebuffRemains(S.ShivDebuff) < 1) then
                if ReturnSpellOnly then
                    return S.Shiv
                else
                    if Cast(S.Shiv) then
                        return "Cast Shiv (Stealth Kingsbane)"
                    end
                end
            end
        end
    
        -- actions.stealthed+=/envenom,if=effective_combo_points>=variable.effective_spend_cp&dot.kingsbane.ticking
        -- &buff.envenom.remains<=3&(debuff.deathstalkers_mark.up|buff.cold_blood.up|buff.darkest_night.up&combo_points=7)
        -- actions.stealthed+=/envenom,if=effective_combo_points>=variable.effective_spend_cp&buff.master_assassin_aura.up
        -- &variable.single_target&(debuff.deathstalkers_mark.up|buff.cold_blood.up|buff.darkest_night.up&combo_points=7)
        if S.Envenom:IsReady(nil, nil, nil, nil, nil, nil, true) then
            if state.ComboPoints >= state.EffectiveCPSpend and (Target:DebuffUp(S.DeathStalkersMarkDebuff) or Player:BuffUp(S.ColdBlood)
            or Player:BuffUp(S.DarkestNightBuff) and state.ComboPoints == 7) then
                if Target:DebuffUp(S.Kingsbane) and Player:BuffRemains(S.Envenom) <= 3 then
                    if ReturnSpellOnly then
                        return S.Envenom
                    else
                        if Cast(S.Envenom) then
                            return "Cast Envenom (Stealth Kingsbane)"
                        end
                    end
                end
                if state.SingleTarget and MasterAssassinAuraUp() then
                    if ReturnSpellOnly then
                        return S.Envenom
                    else
                        if Cast(S.Envenom) then
                            return "Cast Envenom (Master Assassin)"
                        end
                    end
                end
            end
        end
    
        -- # Rupture during Indiscriminate Carnage
        -- actions.stealthed+=/rupture,target_if=effective_combo_points>=variable.effective_spend_cp&buff.indiscriminate_carnage.up
        -- &refreshable&(!variable.regen_saturated|!variable.scent_saturation|!dot.rupture.ticking)&target.time_to_die>15
        if (S.Rupture:IsCastable() or ForceStealth)
        and (S.Rupture:AuraActiveCount() < state.RuptureCountThreshold or state.RuptureCountThreshold == 0) then
            local function RuptureTargetIfFunc(TargetUnit)
                return TargetUnit:DebuffRemains(S.Rupture)
            end
            local function RuptureIfFunc(TargetUnit)
                return state.ComboPoints >= state.EffectiveCPSpend and (Player:BuffUp(S.IndiscriminateCarnageBuff) or ForceStealth) and TargetUnit:DebuffRefreshable(S.Rupture)
                and (not state.EnergyRegenSaturated or not state.ScentSaturated or TargetUnit:DebuffDown(S.Rupture))
                and Target:TimeToDie() > 15
            end
            if AoEON() then
                local TargetIfUnit = CheckTargetIfTarget("min", RuptureTargetIfFunc, RuptureIfFunc)
                if TargetIfUnit and TargetIfUnit:GUID() ~= Target:GUID() then
                    if ReturnSpellOnly then
                        return S.Rupture
                    else
                        if (IndiscriminateCarnageRemains() > 0 or ForceStealth) then
                            if Cast(S.Rupture) then
                                return "Cast Rupture (Stealth Indiscriminate Carnage)"
                            end
                        else
                            if CastLeftNameplate(TargetIfUnit, S.Rupture) then
                                return "Cast Rupture (Stealth)"
                            end
                        end
                    end
                end
            end
            if RuptureIfFunc(Target) then
                if ReturnSpellOnly then
                    return S.Rupture
                else
                    if Cast(S.Rupture) then
                        return "Cast Rupture (Stealth Indiscriminate Carnage)"
                    end
                end
            end
        end
    
        -- # Improved Garrote: Apply or Refresh with buffed Garrotes, accounting for Indiscriminate Carnage
        -- actions.stealthed+=/garrote,target_if=min:remains,if=stealthed.improved_garrote&(remains<12|pmultiplier<=1|(buff.indiscriminate_carnage.up
        -- &active_dot.garrote<spell_targets.fan_of_knives))&!variable.single_target&target.time_to_die-remains>2&combo_points.deficit>2-buff.darkest_night.up*2
        if ((S.Garrote:IsReady(nil, nil, nil, nil, nil, nil, true) and ImprovedGarroteRemains() > 0) or ForceStealth)
        and (S.Garrote:AuraActiveCount() < state.GarroteCountThreshold or state.GarroteCountThreshold == 0) then
            local function GarroteTargetIfFunc(TargetUnit)
                return TargetUnit:DebuffRemains(S.Garrote)
            end
            local function GarroteIfFunc(TargetUnit)
                return (TargetUnit:PMultiplier(S.Garrote) <= 1 or TargetUnit:DebuffRemains(S.Garrote) < 12
                or ((IndiscriminateCarnageRemains() > 0 or ForceStealth) and S.Garrote:AuraActiveCount() < state.MeleeEnemies10yCount)) and not state.SingleTarget
                and (TargetUnit:FilteredTimeToDie(">", 2, -TargetUnit:DebuffRemains(S.Garrote)) or TargetUnit:TimeToDieIsNotValid())
                and state.ComboPointsDeficit > 2 - (num(Player:BuffUp(S.DarkestNightBuff)) * 2)
                and Rogue.CanDoTUnit(TargetUnit, state.GarroteDMGThreshold)
            end
            if AoEON() then
                local TargetIfUnit = CheckTargetIfTarget("min", GarroteTargetIfFunc, GarroteIfFunc)
                if TargetIfUnit and TargetIfUnit:GUID() ~= Target:GUID() then
                    if ReturnSpellOnly then
                        return S.Garrote
                    else
                        if (IndiscriminateCarnageRemains() > 0 or ForceStealth) then
                            if Cast(S.Garrote) then
                                return "Cast Garrote (Improved Garrote Carnage)"
                            end
                        else
                            if CastLeftNameplate(TargetIfUnit, S.Garrote) then
                                return "Cast Garrote (Stealth)"
                            end
                        end
                    end
                end
            end
            if GarroteIfFunc(Target) then
                if ReturnSpellOnly then
                    return S.Garrote
                else
                    if Cast(S.Garrote) then
                        return "Cast Garrote (Improved Garrote)"
                    end
                end
            end
            -- actions.stealthed+=/garrote,if=stealthed.improved_garrote&(pmultiplier<=1|refreshable)&combo_points.deficit>=1+2*talent.shrouded_suffocation
            if state.ComboPointsDeficit >= (1 + 2 * num(S.ShroudedSuffocation:IsAvailable())) and (Target:PMultiplier(S.Garrote) <= 1 or IsDebuffRefreshable(Target, S.Garrote)) then
                if ReturnSpellOnly then
                    return S.Garrote
                else
                    if Cast(S.Garrote) then
                        return "Cast Garrote (Improved Garrote Low CP)"
                    end
                end
            end
        end
    end

    -- # Stealth Macros
    -- This returns a table with the original Stealth spell and the result of the Stealthed action list as if the applicable buff was present
    ---@param StealthSpell Spell
    local function StealthMacro (StealthSpell)
        -- Handle StealthMacro GUI options
        -- If false, just suggest them as off-GCD and bail out of the macro functionality
        if StealthSpell:ID() == S.Vanish:ID() then
            if Cast(S.Vanish) then return "Cast Vanish" end
            return false
        elseif StealthSpell:ID() == S.Shadowmeld:ID() then
            if Cast(S.Shadowmeld) then return "Cast Shadowmeld" end
            return false
        end

        return false
    end


    -- # Cooldowns
    local function CDs ()
        -- Wait on Deathmark for Garrote with MA and check for Kingsbane
        -- actions.cds=variable,name=deathmark_ma_condition,value=!talent.master_assassin.enabled|dot.garrote.ticking
        -- actions.cds+=/variable,name=deathmark_kingsbane_condition,value=cooldown.kingsbane.remains<=2&buff.envenom.up
        
        local DeathmarkMACondition = not S.MasterAssassin:IsAvailable() or Target:DebuffUp(S.Garrote)
        --local DeathmarkKingsbaneCondition = S.Kingsbane:CooldownRemains() <= 2 and Player:BuffUp(S.Envenom)
        
        -- Deathmark to be used if not stealthed, Rupture is up, and all other talent conditions are satisfied
        local DeathmarkKingsbaneCondition = (not S.Kingsbane:IsAvailable() or S.Kingsbane:CooldownRemains() <= 2) and (Player:BuffUp(S.Envenom) or state.MeleeEnemies10yCount > 1 or Player:HeroTreeID() == 53 and not S.Kingsbane:IsAvailable())
        local DeathmarkCondition = false

        if GetSetting('smooth_deathmark', false) then 
            DeathmarkCondition = Target:DebuffUp(S.Rupture) and DeathmarkKingsbaneCondition and Target:DebuffDown(S.Deathmark) and DeathmarkMACondition
        else
            DeathmarkCondition = Target:DebuffUp(S.Rupture) and (DeathmarkKingsbaneCondition or state.MeleeEnemies10yCount > 1
            and Player:BuffRemains(S.SliceandDice) > 5 or S.Kingsbane:IsAvailable() and Target:DebuffUp(S.CrimsonTempest))
            and Target:DebuffDown(S.Deathmark) and DeathmarkMACondition
        end
      
        -- actions.cds+=/call_action_list,name=items
        -- actions.items=variable,name=base_trinket_condition,value=dot.rupture.ticking&cooldown.deathmark.remains<2|dot.deathmark.ticking|fight_remains<=22
          -- actions.items+=/use_item,name=treacherous_transmitter,use_off_gcd=1,if=variable.base_trinket_condition
          if I.TreacherousTransmitter:IsEquippedAndReady() then
            if (Target:DebuffUp(S.Rupture) and S.Deathmark:CooldownRemains() <= 2 or Target:DebuffUp(S.Deathmark) or (HL.BossFilteredFightRemains("<", 22) and state.InRaid)) then
              if Cast(I.TreacherousTransmitter) then return "Treacherous Transmitter"; end
            end
          end
          -- actions.items+=/use_item,name=mad_queens_mandate,if=cooldown.deathmark.remains>=30&!dot.deathmark.ticking|fight_remains<=3
          if I.MadQueensMandate:IsEquippedAndReady() then
            if (S.Deathmark:CooldownRemains() >= 30 and Target:DebuffDown(S.Deathmark) or HL.BossFilteredFightRemains("<=", 3)) then
                if Cast(I.MadQueensMandate) then
                    return "Mad Queen's Mandate";
                end
            end
          end
          -- Reset Check 
          if I.MadQueensMandate:IsEquippedAndReady() then
            local calculatedDamage = CalculateMadQueensDamage()
            -- Only cast the trinket if the calculated damage exceeds the target's current health
            if calculatedDamage >= Target:Health() and not Target:IsDummy() then
                if Cast(I.MadQueensMandate) then
                    return "Mad Queen's Mandate";
                end
            end
          end

            -- actions.items+=/use_item,name=junkmaestros_mega_magnet,if=cooldown.deathmark.remains>=30&!dot.deathmark.ticking
            -- &!debuff.shiv.up&(!talent.deathstalkers_mark|buff.lingering_darkness.up&buff.junkmaestros_mega_magnet.stack>5)|fight_remains<=10
            if I.JunkmaestrosMegaMagnet:IsEquippedAndReady() and Player:BuffUp(S.JunkmaestrosBuff) then
                if S.Deathmark:CooldownRemains() >= 30 and not Target:DebuffUp(S.Deathmark) and Player:BuffDown(S.ShivDebuff)
                and (not S.DeathStalkersMark:IsAvailable()
                or Player:BuffUp(S.LingeringDarknessBuff) and Player:BuffStack(S.JunkmaestrosBuff) > 5) or HL.BossFilteredFightRemains("<=", 10) then
                    if Cast(I.JunkmaestrosMegaMagnet) then
                        return "Junkmaestros Mega Magnet";
                    end
                end
            end

          -- actions.items+=/use_item,name=imperfect_ascendancy_serum,use_off_gcd=1,if=variable.base_trinket_condition
          if I.ImperfectAscendancySerum:IsEquippedAndReady() then
            if (Target:DebuffUp(S.Rupture) and S.Deathmark:CooldownRemains() <= 2 or (HL.BossFilteredFightRemains("<", 22) and state.InRaid)) then
              if Cast(I.ImperfectAscendancySerum) then return "Imperfect Ascendancy Serum"; end
            end
          end
      
          -- actions.items+=/use_items,slots=trinket1,if=(variable.trinket_sync_slot=1&(debuff.deathmark.up|fight_remains<=20)|(variable.trinket_sync_slot=2&(!trinket.2.cooldown.ready&dot.kingsbane.ticking|!debuff.deathmark.up&cooldown.deathmark.remains>20&dot.kingsbane.ticking))|!variable.trinket_sync_slot)
          -- actions.items+=/use_items,slots=trinket2,if=(variable.trinket_sync_slot=2&(debuff.deathmark.up|fight_remains<=20)|(variable.trinket_sync_slot=1&(!trinket.1.cooldown.ready|!debuff.deathmark.up&cooldown.deathmark.remains>20))|!variable.trinket_sync_slot)
          if TrinketItem1:IsReady() then
            if not Player:IsItemBlacklisted(TrinketItem1) and not ValueIsInArray(OnUseExcludeTrinkets, TrinketItem1:ID())
              and (TrinketSyncSlot == 1 and (S.Deathmark:AnyDebuffUp() or (HL.BossFilteredFightRemains("<", 20) and state.InRaid)) or (TrinketSyncSlot == 2 and (not TrinketItem2:IsReady() and Target:DebuffUp(S.Kingsbane) or not S.Deathmark:AnyDebuffUp() and S.Deathmark:CooldownRemains() > 20 and Target:DebuffUp(S.Kingsbane))) or TrinketSyncSlot == 0) then
              if Cast(TrinketItem1) then
               return "Trinket 1";
              end
            end
          end
      
          if TrinketItem2:IsReady() then
            if not Player:IsItemBlacklisted(TrinketItem2) and not ValueIsInArray(OnUseExcludeTrinkets, TrinketItem2:ID())
              and (TrinketSyncSlot == 2 and (S.Deathmark:AnyDebuffUp() or (HL.BossFilteredFightRemains("<", 20) and state.InRaid)) or (TrinketSyncSlot == 1 and (not TrinketItem1:IsReady() and Target:DebuffUp(S.Kingsbane) or not S.Deathmark:AnyDebuffUp() and S.Deathmark:CooldownRemains() > 20 and Target:DebuffUp(S.Kingsbane))) or TrinketSyncSlot == 0) then
              if Cast(TrinketItem2) then
                return "Trinket 2";
              end
            end
          end
      
        -- actions.cds+=/invoke_external_buff,name=power_infusion,if=dot.deathmark.ticking
        -- Note: We don't handle external buffs.
      
        -- actions.cds+=/deathmark,if=(variable.deathmark_condition&target.time_to_die>=10)|fight_remains<=20
        if S.Deathmark:IsReady() then
            if (DeathmarkCondition and Target:TimeToDie() >= 10) or (HL.BossFilteredFightRemains("<=", 20) and state.InRaid) then
                if Cast(S.Deathmark) then 
                    return "Cast Deathmark" 
                end
            end
        end
        
        -- Base conditions for Shiv included in CD section
        -- # Check for Applicable Shiv usage
        -- actions.cds+=/call_action_list,name=shiv
        -- actions.shiv=variable,name=shiv_condition,value=!debuff.shiv.up&dot.garrote.ticking&dot.rupture.ticking extra check to fulfill 100% uptime on Shiv during dmg windows
        -- actions.shiv=variable,name=shiv_condition,value=!debuff.shiv.up&dot.garrote.ticking&dot.rupture.ticking&spell_targets.fan_of_knives<=5
        local ShivCondition = (Target:DebuffDown(S.ShivDebuff) or (Target:DebuffRemains(S.ShivDebuff) < 1.5 and Target:DebuffUp(S.ShivDebuff))) and Target:DebuffUp(S.Garrote) and Target:DebuffUp(S.Rupture) and state.MeleeEnemies10yCount <= 5
  
        -- actions.shiv+=/variable,name=shiv_kingsbane_condition,value=talent.kingsbane&buff.envenom.up&variable.shiv_condition
        local ShivKingsbaneCondition = S.Kingsbane:IsAvailable() and Player:BuffUp(S.Envenom) and ShivCondition
        
        if S.Shiv:IsReady(nil, nil, nil, nil, nil, nil, true) then
          local FightRemains = HL.BossFilteredFightRemains("<=", S.Shiv:Charges() * 8)
            -- # Shiv for aoe with Arterial Precision
            -- actions.shiv+=/shiv,if=talent.arterial_precision&!debuff.shiv.up&dot.garrote.ticking&dot.rupture.ticking
            -- &spell_targets.fan_of_knives>=4&dot.crimson_tempest.ticking&(target.health.pct<=35
            -- &talent.zoldyck_recipe|cooldown.shiv.charges_fractional>=1.9)
            if S.ArterialPrecision:IsAvailable() and Target:DebuffDown(S.ShivDebuff) and Target:DebuffUp(S.Garrote)
            and Target:DebuffUp(S.Rupture) and state.MeleeEnemies10yCount >= 4 and S.CrimsonTempest:AnyDebuffUp()
            and (Target:HealthPercentage() <= 35 and S.ZoldyckRecipe:IsAvailable() or S.Shiv:ChargesFractional() >= 1.9) then
                if CastPooling(S.Shiv, Player:EnergyTimeToX(S.Shiv:Cost())) then
                    return "Cast Shiv (Arterial Precision)"
                end
            end
            -- # Shiv cases for Kingsbane
            -- actions.shiv+=/shiv,if=!talent.lightweight_shiv.enabled&variable.shiv_kingsbane_condition
            -- &(dot.kingsbane.ticking&dot.kingsbane.remains<8|!dot.kingsbane.ticking&cooldown.kingsbane.remains>=20)
            -- &(!talent.crimson_tempest.enabled|variable.single_target|dot.crimson_tempest.ticking)
            if not S.LightweightShiv:IsAvailable() then
                if ShivKingsbaneCondition
                and (Target:DebuffUp(S.Kingsbane) and Target:DebuffRemains(S.Kingsbane) < 8 or not Target:DebuffUp(S.Kingsbane) and S.Kingsbane:CooldownRemains() >= 20 and not S.LingeringDarkness:IsAvailable()) -- Custom: and not S.LingeringDarkness:IsAvailable()
                and (not S.CrimsonTempest:IsAvailable() or state.SingleTarget or Target:DebuffUp(S.CrimsonTempest)) then
                    if CastPooling(S.Shiv, Player:EnergyTimeToX(S.Shiv:Cost())) then
                        return "Cast Shiv (Kingsbane)"
                    end
                end
            end

            -- # Shiv for big Darkest Night Envenom during Lingering Darkness
            -- actions.shiv+=/shiv,if=buff.darkest_night.up&combo_points>=variable.effective_spend_cp&buff.lingering_darkness.up
            if Player:BuffUp(S.DarkestNightBuff) and state.ComboPoints >= state.EffectiveCPSpend and Player:BuffUp(S.LingeringDarknessBuff) then
                if CastPooling(S.Shiv, Player:EnergyTimeToX(S.Shiv:Cost())) then
                    return "Cast Shiv Darkest Night, Lingering Darkness"
                end
            end

            -- actions.shiv+=/shiv,if=talent.lightweight_shiv.enabled&variable.shiv_kingsbane_condition&(dot.kingsbane.ticking
            -- &dot.kingsbane.remains<8|cooldown.kingsbane.remains<=1&cooldown.shiv.charges_fractional>=1.7)
            if S.LightweightShiv:IsAvailable() then
                if ShivKingsbaneCondition and (Target:DebuffUp(S.Kingsbane) and Target:DebuffRemains(S.Kingsbane) < 8 or S.Kingsbane:CooldownRemains() <= 1
                and S.Shiv:ChargesFractional() >= 1.7) then
                    if CastPooling(S.Shiv, Player:EnergyTimeToX(S.Shiv:Cost())) then
                        return "Cast Shiv (Kingsbane Lightweight)"
                    end
                end
            end

            -- actions.shiv+=/shiv,if=talent.arterial_precision&!debuff.shiv.up&dot.garrote.ticking&dot.rupture.ticking&debuff.deathmark.up
            if S.ArterialPrecision:IsAvailable() and Target:DebuffDown(S.ShivDebuff) and Target:DebuffUp(S.Rupture) and S.Deathmark:AnyDebuffUp() then
                if CastPooling(S.Shiv, Player:EnergyTimeToX(S.Shiv:Cost())) then
                    return "Cast Shiv (Arterial Precision Deathmark)"
                end
            end

            -- # Fallback if no special cases apply
            -- actions.shiv+=/shiv,if=!debuff.deathmark.up&!talent.kingsbane&variable.shiv_condition&(dot.crimson_tempest.ticking|talent.amplifying_poison)
            -- &(((talent.lightweight_shiv+1)-cooldown.shiv.charges_fractional)*30<cooldown.deathmark.remains)&raid_event.adds.in>20
            if not S.Deathmark:AnyDebuffUp() and not S.Kingsbane:IsAvailable() and ShivCondition and (Target:DebuffUp(S.CrimsonTempest) or S.AmplifyingPoison:IsAvailable())
            and (((num(S.LightweightShiv:IsAvailable()) + 1) - S.Shiv:ChargesFractional()) * 30 < S.Deathmark:CooldownRemains()) then
                if CastPooling(S.Shiv, Player:EnergyTimeToX(S.Shiv:Cost())) then
                    return "Cast Shiv 1"
                end
            end

            -- actions.shiv+=/shiv,if=!talent.kingsbane&!talent.arterial_precision&variable.shiv_condition
            -- &(!talent.crimson_tempest.enabled|variable.single_target|dot.crimson_tempest.ticking)
            if not S.Kingsbane:IsAvailable() and not S.ArterialPrecision:IsAvailable() and ShivCondition
            and (not S.CrimsonTempest:IsAvailable() or state.SingleTarget or Target:DebuffUp(S.CrimsonTempest)) then
                if CastPooling(S.Shiv, Player:EnergyTimeToX(S.Shiv:Cost())) then
                    return "Cast Shiv 2"
                end
            end
        end
      
        -- # Cold Blood for Edge Case or Envenoms during shiv
        -- actions.cds+=/cold_blood,use_off_gcd=1,if=(buff.fatebound_coin_tails.stack>0&buff.fatebound_coin_heads.stack>0)|debuff.shiv.up&(cooldown.deathmark.remains>50|!talent.inevitabile_end&effective_combo_points>=variable.effective_spend_cp) Note: !buff.edge_case.up does not exist
        if ((S.ColdBlood:IsReady() or S.ColdBloodIE:IsReady()) and (not Player:BuffUp(S.ColdBlood) and not Player:BuffUp(S.ColdBloodIE))) and ((Player:BuffStack(S.FateboundCoinTails) > 0 and Player:BuffStack(S.FateboundCoinHeads) > 0) or Target:DebuffUp(S.ShivDebuff) and (S.Deathmark:CooldownRemains() > 50 or not S.InevitabileEnd:IsAvailable() and state.ComboPoints >= state.EffectiveCPSpend)) then
          if Cast(S.ColdBlood) then return "Cast Cold Blood" end
        end
        
        -- actions.cds+=/kingsbane,if=(debuff.shiv.up|cooldown.shiv.remains<6)&buff.envenom.up&(cooldown.deathmark.remains>=45|dot.deathmark.ticking)|fight_remains<=15 Note: based on TC Channel 45 Sec instead of 50; Added DS check so you may use KB alone even when DM is ready. Added Target:DebuffUp(S.Deathmark) so you may always use KB once DM is on the target (Env updatime may be lost due to pooling or mechanics)
        -- actions.cds+=/kingsbane,if=(debuff.shiv.up|cooldown.shiv.remains<6)&(buff.envenom.up|spell_targets.fan_of_knives>1)&(cooldown.deathmark.remains>=50|dot.deathmark.ticking)|fight_remains<=15
        if S.Kingsbane:IsReady(nil, nil, nil, nil, nil, nil, true) then
                if GetSetting('smooth_deathmark', false) then 
                    if S.Deathmark:CooldownRemains(nil, true) >= 40 or Target:DebuffUp(S.Deathmark) then
                        if futureEnemiesCount < state.MeleeEnemies10yCount then
                            if (Target:DebuffUp(S.ShivDebuff) or S.Shiv:CooldownRemains() < 6) and (Player:BuffUp(S.Envenom) or state.MeleeEnemies10yCount > 1) or (HL.BossFilteredFightRemains("<=", 15) and state.InRaid) then
                                if Cast(S.Kingsbane) then return "Cast Kingsbane" end
                            end
                        end
                        if futureEnemiesCount == state.MeleeEnemies10yCount then 
                            if (Target:DebuffUp(S.ShivDebuff) or S.Shiv:CooldownRemains() < 6) and (Player:BuffUp(S.Envenom) or Target:DebuffUp(S.Deathmark)) or (HL.BossFilteredFightRemains("<=", 15) and state.InRaid) then
                                if Cast(S.Kingsbane) then return "Cast Kingsbane" end
                            end
                        end
                    end
                else
                    if (Target:DebuffUp(S.ShivDebuff) or S.Shiv:CooldownRemains() < 6) and (Player:BuffUp(S.Envenom) or state.MeleeEnemies10yCount > 1) and (S.Deathmark:CooldownRemains() >= 45 or Target:DebuffUp(S.Deathmark)) or (HL.BossFilteredFightRemains("<=", 15) and state.InRaid) then
                        if Cast(S.Kingsbane) then return "Cast Kingsbane" 
                    end
                end
            end
        end
      
        -- actions.cds+=/thistle_tea,if=!buff.thistle_tea.up&debuff.shiv.remains>=6|!buff.thistle_tea.up&dot.kingsbane.ticking&dot.kingsbane.remains<=6|!buff.thistle_tea.up&fight_remains<=cooldown.thistle_tea.charges*6
        if S.ThistleTea:IsReady(nil, nil, nil, nil, nil, true) then
            if Player:BuffDown(S.ThistleTea) 
            and ((Target:DebuffRemains(S.ShivDebuff) >= 6)
            or (Target:DebuffUp(S.Kingsbane) and Target:DebuffRemains(S.Kingsbane) <= 6)
            or (HL.BossFilteredFightRemains("<", S.ThistleTea:Charges() * 6) and state.InRaid)) then 
                if Cast(S.ThistleTea, true) then return "Cast Thistle Tea" end
            end
        end
      
        -- MiscCDs here
        -- actions.cds+=/call_action_list,name=misc_cds
      
        -- Racials
        if S.Deathmark:AnyDebuffUp() then
          -- actions.misc_cds+=/blood_fury,if=debuff.deathmark.up
          if S.BloodFury:IsReady() then
            if Cast(S.BloodFury) then return "Cast Blood Fury" end
          end
          -- actions.misc_cds+=/berserking,if=debuff.deathmark.up
          if S.Berserking:IsReady() then
            if Cast(S.Berserking) then return "Cast Berserking" end
          end
          -- actions.misc_cds+=/fireblood,if=debuff.deathmark.up
          if S.Fireblood:IsReady() then
            if Cast(S.Fireblood) then return "Cast Fireblood" end
          end
          -- actions.misc_cds+=/ancestral_call,if=(!talent.kingsbane&debuff.deathmark.up&debuff.shiv.up)|(talent.kingsbane&debuff.deathmark.up&dot.kingsbane.ticking&dot.kingsbane.remains<8)
          if S.AncestralCall:IsReady(nil, nil, nil, nil, nil, true) then
            if (not S.Kingsbane:IsAvailable() and Target:DebuffUp(S.ShivDebuff))
              or (Target:DebuffUp(S.Kingsbane) and Target:DebuffRemains(S.Kingsbane) < 16) then
              if Cast(S.AncestralCall) then return "Cast Ancestral Call" end
            end
          end
        end
      
        -- # Vanish Handling here
        -- local function Vanish ()
        -- actions.cds+=/call_action_list,name=vanish,if=!stealthed.all&master_assassin_remains=0
        if not Player:StealthUp(true, true) and MasterAssassinRemains() <= 0 then

            -- Custom Vanish if Player:HeroTreeID() == 53 and Deathstalker's Mark is NOT up and timeSinceLastDSMark is superior to TimeSinceLastCast of Envenom 
            if GetSetting("vanish_for_deathstalkers_mark", true) then
                if S.Vanish:IsReady(nil, nil, nil, nil, nil, nil, true) 
                and state.TargetInMeleeRange and Player:HeroTreeID() == 53 
                and Player:BuffDown(S.DarkestNightBuff) and Target:DebuffDown(S.DeathStalkersMarkDebuff) 
                and GetTime() - timeSinceLastDSMark > S.Envenom:TimeSinceLastCast() and S.Envenom:TimeSinceLastCast() > 1 and HL.CombatTime() > 1 then
                    if CastPooling(S.Vanish, Player:EnergyTimeToX(S.Ambush:Cost())) then return "Cast Vanish (Custom)" end
                end
            end


            -- # Don't Vanish if deathstalker's mark isn't up and we're at a finish condition
            if Target:BuffDown(S.DeathStalkersMarkDebuff) and state.ComboPoints >= state.EffectiveCPSpend then
                return
            end
      
          -- # Vanish to fish for Fateful Ending if possible
          -- actions.vanish+=/vanish,if=!buff.fatebound_lucky_coin.up&effective_combo_points>=variable.effective_spend_cp&(buff.fatebound_coin_tails.stack>=5|buff.fatebound_coin_heads.stack>=5)
          if S.Vanish:IsReady() and Player:BuffDown(S.FateboundLuckyCoin) and state.ComboPoints >= state.EffectiveCPSpend 
          and (Player:BuffStack(S.FateboundCoinTails) >= 5 or Player:BuffStack(S.FateboundCoinHeads) >= 5) then
            ShouldReturn = StealthMacro(S.Vanish)
            if ShouldReturn then return "Cast Vanish (Fateful Ending Fish)" end
          end
      
          -- # Vanish to spread Garrote during Deathmark without Indiscriminate Carnage
          -- actions.vanish+=/vanish,if=!talent.master_assassin&!talent.indiscriminate_carnage&talent.improved_garrote&cooldown.garrote.up&(dot.garrote.pmultiplier<=1|dot.garrote.refreshable)&(debuff.deathmark.up|cooldown.deathmark.remains<4)&combo_points.deficit>=(spell_targets.fan_of_knives>?4)
        
          if S.Vanish:IsReady() and not S.MasterAssassin:IsAvailable() and not S.IndiscriminateCarnage:IsAvailable() 
          and S.ImprovedGarrote:IsAvailable() and S.Garrote:CooldownUp() and (Target:PMultiplier(S.Garrote) <= 1 
          or IsDebuffRefreshable(Target, S.Garrote, state.GarroteThreshold)) and (Target:DebuffUp(S.Deathmark) or S.Deathmark:CooldownRemains() < 4) 
          and state.ComboPointsDeficit >= mathmin(state.MeleeEnemies10yCount, 4) then
            ShouldReturn = StealthMacro(S.Vanish)
            if ShouldReturn then return "Cast Vanish Garrote Deathmark (No Carnage)" end
          end
      
          -- # Vanish for cleaving Garrotes with Indiscriminate Carnage
          -- actions.vanish+=/vanish,if=talent.indiscriminate_carnage&talent.improved_garrote&cooldown.garrote.up&(dot.garrote.pmultiplier<=1|dot.garrote.refreshable)&spell_targets.fan_of_knives>2&(target.time_to_die-remains>15|raid_event.adds.in>20)
        
          if S.Vanish:IsReady() and S.IndiscriminateCarnage:IsAvailable() 
          and S.ImprovedGarrote:IsAvailable() and S.Garrote:CooldownUp() and (Target:PMultiplier(S.Garrote) <= 1 
          or IsDebuffRefreshable(Target, S.Garrote, state.GarroteThreshold)) and state.MeleeEnemies10yCount > 2 and Target:TimeToDie() > 15 then
            ShouldReturn = StealthMacro(S.Vanish)
            if ShouldReturn then return "Cast Vanish (Garrote Carnage)" end
          end
      
          -- # Vanish fallback for Master Assassin
          --actions.vanish+=/vanish,if=talent.master_assassin&debuff.deathmark.up&dot.kingsbane.remains<=6+3*talent.subterfuge.rank
          --BypassRecovery cause we only care about the crit buff here

          if S.Vanish:IsReady(Player, nil, nil, nil, nil, true) and S.MasterAssassin:IsAvailable() 
          and Target:DebuffUp(S.Deathmark) 
          and (Target:DebuffUp(S.Kingsbane) or not S.Kingsbane:IsAvailable())
          and Target:DebuffRemains(S.Kingsbane, nil, true) <= 6 + 3 * S.Subterfuge:TalentRank() then
            ShouldReturn = StealthMacro(S.Vanish)
            if ShouldReturn then return "Cast Vanish (Master Assassin)" end
          end
      
            -- # Vanish fallback for Improved Garrote during Deathmark if no add waves are expected
            --actions.vanish+=/vanish,if=talent.improved_garrote&cooldown.garrote.up
            -- &(dot.garrote.pmultiplier<=1|dot.garrote.refreshable)
            -- &(debuff.deathmark.up|cooldown.deathmark.remains<4)&raid_event.adds.in>30
            if S.Vanish:IsReady() and S.ImprovedGarrote:IsAvailable() and S.Garrote:CooldownUp()
            and (Target:PMultiplier(S.Garrote) <= 1 or IsDebuffRefreshable(Target, S.Garrote, state.GarroteThreshold))
            and (Target:DebuffUp(S.Deathmark) or S.Deathmark:CooldownRemains() < 4) then
            ShouldReturn = StealthMacro(S.Vanish)
                if ShouldReturn then
                    return "Cast Vanish (Improved Garrote during Deathmark)"
                end
            end
        end
    end
    
    -- # Core damage over time abilities used everywhere
    local function CoreDot()
        -- Maintain Garrote
        -- actions.core_dot=garrote,if=combo_points.deficit>=1&(pmultiplier<=1)&refreshable&target.time_to_die-remains>12
        if S.Garrote:IsReady(nil, nil, nil, nil, nil, nil, true) and state.ComboPointsDeficit >= 1 and Target:PMultiplier(S.Garrote) <= 1 
          and IsDebuffRefreshable(Target, S.Garrote, state.GarroteThreshold)
          and (Target:FilteredTimeToDie(">", 12, -Target:DebuffRemains(S.Garrote)) or Target:TimeToDieIsNotValid()) then
            if CastPooling(S.Garrote) then return "Cast Garrote (Core)" end
        end
        
        -- Maintain Rupture unless darkest night is up
        -- actions.core_dot+=/rupture,if=combo_points>=variable.effective_spend_cp&(pmultiplier<=1)&refreshable&target.time_to_die-remains>(4+(talent.dashing_scoundrel*5)+(variable.regen_saturated*6))&(!buff.darkest_night.up|talent.caustic_spatter&!debuff.caustic_spatter.up)
        if S.Rupture:IsReady(nil, nil, nil, nil, nil, nil, true) and state.ActualComboPoints >= state.EffectiveCPSpend and Target:PMultiplier(S.Rupture) <= 1 
          and IsDebuffRefreshable(Target, S.Rupture, state.RuptureThreshold) and (Player:BuffDown(S.DarkestNightBuff) or S.CausticSpatter:IsAvailable() and Target:DebuffDown(S.CausticSpatterDebuff)) then
          local RuptureDurationThreshold = 4 + (S.DashingScoundrel:IsAvailable() and 5 or 0) + (state.EnergyRegenSaturated and 6 or 0)
          if Target:FilteredTimeToDie(">", RuptureDurationThreshold, -Target:DebuffRemains(S.Rupture)) or Target:TimeToDieIsNotValid() then
            if CastPooling(S.Rupture) then return "Cast Rupture (Core)" end
          end
        end
        
        -- Maintain Crimson Tempest unless it would remove a stronger cast
        -- actions.core_dot+=/crimson_tempest,if=combo_points>=variable.effective_spend_cp&refreshable&pmultiplier<=persistent_multiplier&!buff.darkest_night.up&!talent.amplifying_poison
        if S.CrimsonTempest:IsReady(nil, nil, nil, nil, nil, nil, true) and state.ActualComboPoints >= state.EffectiveCPSpend 
            and IsDebuffRefreshable(Target, S.CrimsonTempest, state.CrimsonTempestThreshold)
            and Player:BuffDown(S.DarkestNightBuff) and not S.AmplifyingPoison:IsAvailable() then
            if CastPooling(S.CrimsonTempest) then return "Cast Crimson Tempest (Core)" end
        end
        
        return false
    end

        
    -- # AoE Damage over time abilities
    local function AoeDot ()
        -- Helper Variable to check basic finisher conditions
        -- actions.aoe_dot=variable,name=dot_finisher_condition,value=combo_points>=variable.effective_spend_cp
        local DotFinisherCondition = state.ActualComboPoints >= state.EffectiveCPSpend
                
        -- # Garrote upkeep in AoE to reach energy saturation
        -- actions.aoe_dot+=/garrote,cycle_targets=1,if=combo_points.deficit>=1&pmultiplier<=1&refreshable&!variable.regen_saturated&target.time_to_die-remains>12
        if S.Garrote:IsCastable() and state.ComboPointsDeficit >= 1 then
          local function Evaluate_Garrote_Target(TargetUnit)
            return IsDebuffRefreshable(TargetUnit, S.Garrote, state.GarroteThreshold) 
                 and TargetUnit:PMultiplier(S.Garrote) <= 1
                 and not state.EnergyRegenSaturated
                 and (TargetUnit:FilteredTimeToDie(">", 12, -TargetUnit:DebuffRemains(S.Garrote)) or TargetUnit:TimeToDieIsNotValid())
          end
          if AoEON() then
            if SuggestCycleDoT(S.Garrote, Evaluate_Garrote_Target, 12, state.MeleeEnemies5y) then return "Garrote (AoE)" end
          end
        end
      
        -- # Rupture upkeep in AoE to reach energy/scent saturation or to spread for damage
        -- actions.aoe_dot+=/rupture,cycle_targets=1,if=variable.dot_finisher_condition&refreshable&(!dot.kingsbane.ticking|buff.cold_blood.up)&(!variable.regen_saturated&(talent.scent_of_blood.rank=2|talent.scent_of_blood.rank<=1&(buff.indiscriminate_carnage.up|target.time_to_die-remains>15)))&target.time_to_die>(7+(talent.dashing_scoundrel*5)+(variable.regen_saturated*6))&!buff.darkest_night.up
        if S.Rupture:IsCastable() and DotFinisherCondition and Player:BuffDown(S.DarkestNightBuff) then
          local function Evaluate_Rupture_Target(TargetUnit)
            return IsDebuffRefreshable(TargetUnit, S.Rupture, state.RuptureThreshold)
             and (not TargetUnit:DebuffUp(S.Kingsbane) or (Player:BuffUp(S.ColdBlood) or Player:BuffUp(S.ColdBloodIE)))
             and (not state.EnergyRegenSaturated and (S.ScentOfBlood:TalentRank() == 2 or S.ScentOfBlood:TalentRank() <= 1 and (IndiscriminateCarnageRemains() > 0.5 or (TargetUnit:FilteredTimeToDie(">", 15, -TargetUnit:DebuffRemains(S.Rupture)) or TargetUnit:TimeToDieIsNotValid()))))
             and (TargetUnit:FilteredTimeToDie(">", (7 + (S.DashingScoundrel:TalentRank() * 5) + (state.EnergyRegenSaturated and 6 or 0)), -TargetUnit:DebuffRemains(S.Rupture)) or TargetUnit:TimeToDieIsNotValid())
          end
          -- AoE and cycle logic
          if AoEON() then
              if SuggestCycleDoT(S.Rupture, Evaluate_Rupture_Target, 15, state.MeleeEnemies5y) then return "Rupture (AoE 1)" end
          end
        end
        
        -- actions.aoe_dot+=/rupture,cycle_targets=1,if=variable.dot_finisher_condition&refreshable&(!dot.kingsbane.ticking|buff.cold_blood.up)&variable.regen_saturated&target.time_to_die>(7+(talent.dashing_scoundrel*5)+(variable.regen_saturated*6))&!buff.darkest_night.up
        if S.Rupture:IsCastable() and DotFinisherCondition and Player:BuffDown(S.DarkestNightBuff) then
          local function Evaluate_Rupture_Target(TargetUnit)
            return IsDebuffRefreshable(TargetUnit, S.Rupture, state.RuptureThreshold)
              and (not TargetUnit:DebuffUp(S.Kingsbane) or (Player:BuffUp(S.ColdBlood) or Player:BuffUp(S.ColdBloodIE)))
              and state.EnergyRegenSaturated
              and (TargetUnit:FilteredTimeToDie(">", (7 + (S.DashingScoundrel:TalentRank() * 5) + (state.EnergyRegenSaturated and 6 or 0)), -TargetUnit:DebuffRemains(S.Rupture)) or TargetUnit:TimeToDieIsNotValid())
          end
          -- AoE and cycle logic
          if AoEON() then
              if SuggestCycleDoT(S.Rupture, Evaluate_Rupture_Target, 15, state.MeleeEnemies5y) then return "Rupture (AoE 2)" end
          end
        end
        
        -- # Garrote as a special generator for the last CP before a finisher for edge case handling
        -- actions.aoe_dot+=/garrote,if=refreshable&combo_points.deficit>=1&(pmultiplier<=1|remains<=tick_time&spell_targets.fan_of_knives>=3)&(remains<=tick_time*2&spell_targets.fan_of_knives>=3)&(target.time_to_die-remains)>4&master_assassin_remains=0
        if S.Garrote:IsReady() and IsDebuffRefreshable(Target, S.Garrote, state.GarroteThreshold) 
          and state.ComboPointsDeficit >= 1 
          and MasterAssassinRemains() <= 0
          and (Target:PMultiplier(S.Garrote) <= 1 or Target:DebuffRemains(S.Garrote) <= state.BleedTickTime and state.MeleeEnemies10yCount >= 3)
          and (Target:DebuffRemains(S.Garrote) <= state.BleedTickTime * 2 and state.MeleeEnemies10yCount >= 3)
          and (Target:FilteredTimeToDie(">", 4, -Target:DebuffRemains(S.Garrote)) or Target:TimeToDieIsNotValid()) then
          if Cast(S.Garrote) then return "Garrote (Fallback AoE)" end
        end
      
        -- # Crimson Tempest on 2+ Targets
        -- actions.aoe_dot+=/crimson_tempest,target_if=min:remains,if=spell_targets>=2&variable.dot_finisher_condition&refreshable&target.time_to_die-remains>6
        if AoEON() and S.CrimsonTempest:IsCastable() and state.MeleeEnemies10yCount >= 2 and DotFinisherCondition then
            local function EvaluateCrimsonTempestTarget(TargetUnit)
              return TargetUnit:DebuffRemains(S.CrimsonTempest)
            end
            local function CrimsonTempestIfFunc(TargetUnit)
              return IsDebuffRefreshable(TargetUnit, S.CrimsonTempest, state.CrimsonTempestThreshold)
                   and (TargetUnit:FilteredTimeToDie(">", 6, -TargetUnit:DebuffRemains(S.CrimsonTempest)) or TargetUnit:TimeToDieIsNotValid())  
            end
            if AoEON() then
              local BestUnit = CheckTargetIfTarget("min", EvaluateCrimsonTempestTarget, CrimsonTempestIfFunc)
              if BestUnit then
                if CastPooling(S.CrimsonTempest, Player:EnergyTimeToX(S.CrimsonTempest:Cost())) then return "Cast Crimson Tempest (AoE)" end
              end
            end
          end

        return false
    end
    
    -- # Direct damage abilities
    local function Direct ()
        -- Envenom at applicable cp if not pooling, capped on amplifying poison stacks, on an animacharged CP, or in aoe.
        -- actions.direct=envenom,if=!buff.darkest_night.up&combo_points>=variable.effective_spend_cp&(variable.not_pooling|debuff.amplifying_poison.stack>=20|!variable.single_target)
        if S.Envenom:IsReady(nil, nil, nil, nil, nil, nil, true) and Player:BuffDown(S.DarkestNightBuff) 
          and state.ActualComboPoints >= state.EffectiveCPSpend 
          and (state.NotPooling or (Target:DebuffStack(S.AmplifyingPoisonDebuff) + Target:DebuffStack(S.AmplifyingPoisonDebuffDeathmark)) >= 20 or not state.SingleTarget) 
          and Player:BuffDown(Rogue.VanishBuffSpell()) then
            if CastPooling(S.Envenom, Player:EnergyTimeToX(S.Envenom:Cost())) then return "Cast Envenom (Main)" end
        end
    
        -- Special Envenom handling for Darkest Night
        -- actions.direct+=/envenom,if=buff.darkest_night.up&effective_combo_points>=cp_max_spend
        if S.Envenom:IsReady(nil, nil, nil, nil, nil, nil, true) and Player:BuffUp(S.DarkestNightBuff) and state.ComboPoints >= Rogue.CPMaxSpend() then
            if CastPooling(S.Envenom, Player:EnergyTimeToX(S.Envenom:Cost())) then return "Cast Envenom (Darkest Night)" end
        end
     
        -- Check if we should be using a filler
        -- actions.direct+=/variable,name=use_filler,value=combo_points<=variable.effective_spend_cp&!variable.cd_soon|variable.not_pooling|!variable.single_target
        local UseFiller = state.ComboPoints <= state.EffectiveCPSpend and not state.CDSoon or state.NotPooling or not state.SingleTarget
      
        -- Filler target count check for FoK
        -- actions.direct+=/variable,name=fok_target_count,value=(buff.clear_the_witnesses.up&(spell_targets.fan_of_knives>=2-(buff.lingering_darkness.up|!talent.vicious_venoms)))|(spell_targets.fan_of_knives>=3-(talent.momentum_of_despair&talent.thrown_precision)+talent.vicious_venoms+talent.blindside)
        local FoKTargetCount = (Player:BuffUp(S.ClearTheWitnessesBuff) and (state.MeleeEnemies10yCount >= 2 - num(Player:BuffUp(S.LingeringDarknessBuff) or not S.ViciousVenoms:IsAvailable()))) 
                            or (state.MeleeEnemies10yCount >= 3 - num(S.MomentumOfDespair:IsAvailable() and S.ThrownPrecision:IsAvailable()) + num(S.ViciousVenoms:IsAvailable()) + num(S.Blindside:IsAvailable()))
        
        -- Maintain Caustic Spatter
        -- actions.direct+=/variable,name=use_caustic_filler,value=talent.caustic_spatter&dot.rupture.ticking&(!debuff.caustic_spatter.up|debuff.caustic_spatter.remains<=2)&combo_points.deficit>=1&!variable.single_target
        local UseCausticFiller = S.CausticSpatter:IsAvailable() and Target:DebuffUp(S.Rupture) 
                              and (Target:DebuffDown(S.CausticSpatterDebuff) or Target:DebuffRemains(S.CausticSpatterDebuff) <= 2) 
                              and state.ComboPointsDeficit >= 1 
                              and not state.SingleTarget
                              
        -- actions.direct+=/mutilate,if=variable.use_caustic_filler
        if UseCausticFiller and S.Mutilate:IsReady(nil, nil, nil, nil, nil, nil, true) then
            if CastPooling(S.Mutilate, Player:EnergyTimeToX(S.Mutilate:Cost())) then return "Cast Mutilate (Caustic)" end
        end
        
        -- actions.direct+=/ambush,if=variable.use_caustic_filler
        if UseCausticFiller and (S.Ambush:IsReady() or S.AmbushOverride:IsReady()) and (Player:StealthUp(true, true) or Player:BuffUp(S.BlindsideBuff)) then
            if CastPooling(S.Ambush, Player:EnergyTimeToX(S.Ambush:Cost())) then return "Cast Ambush (Caustic)" end
        end

        -- Fan of Knives at 6cp for Darkest Night
        -- actions.direct+=/fan_of_knives,if=buff.darkest_night.up&combo_points=6&(!talent.vicious_venoms|spell_targets.fan_of_knives>=2)
        if S.FanofKnives:IsReady(nil, nil, nil, nil, nil, nil, true) 
          and AoEON()
          and Player:BuffUp(S.DarkestNightBuff) 
          and state.ComboPoints == 6 
          and (not S.ViciousVenoms:IsAvailable() or state.MeleeEnemies10yCount >= 2) then
            if CastPooling(S.FanofKnives, Player:EnergyTimeToX(S.FanofKnives:Cost())) then return "Cast Fan of Knives (Darkest Night)" end
        end
        
        -- Fan of Knives at 3+ targets, accounting for various edge cases
        -- actions.direct+=/fan_of_knives,if=variable.use_filler&!priority_rotation&variable.fok_target_count
        if S.FanofKnives:IsReady(nil, nil, nil, nil, nil, nil, true) 
          and UseFiller 
          and AoEON()
          and not state.PriorityRotation 
          and FoKTargetCount then
            if CastPooling(S.FanofKnives, Player:EnergyTimeToX(S.FanofKnives:Cost())) then return "Cast Fan of Knives (AoE)" end
        end
    
        -- Ambush on Blindside/Subterfuge. Do not use Ambush from stealth during Kingsbane & Deathmark.
        -- actions.direct+=/ambush,if=variable.use_filler&(buff.blindside.up|stealthed.rogue)&(!dot.kingsbane.ticking|debuff.deathmark.down|buff.blindside.up)
        if (S.Ambush:IsReady() or S.AmbushOverride:IsReady()) 
          and UseFiller 
          and (Player:BuffUp(S.BlindsideBuff) or Player:StealthUp(true, false))
          and (Target:DebuffDown(S.Kingsbane) or Target:DebuffDown(S.Deathmark) or Player:BuffUp(S.BlindsideBuff)) then
            if CastPooling(S.Ambush, Player:EnergyTimeToX(S.Ambush:Cost())) then return "Cast Ambush" end
        end
    
        -- Tab-Mutilate to apply Deadly Poison at 2 targets if not using Fan of Knives
        -- actions.direct+=/mutilate,target_if=!dot.deadly_poison_dot.ticking&!debuff.amplifying_poison.up,if=variable.use_filler&spell_targets.fan_of_knives=2
        if S.Mutilate:IsReady() and UseFiller and state.MeleeEnemies10yCount == 2 then
            local TargetGUID = Target:GUID()
            for _, CycleUnit in pairs(state.MeleeEnemies5y) do
                if CycleUnit:GUID() ~= TargetGUID 
                  and CycleUnit:DebuffDown(S.DeadlyPoisonDebuff, true) 
                  and CycleUnit:DebuffDown(S.AmplifyingPoisonDebuff, true) then
                    CastLeftNameplate(CycleUnit, S.Mutilate)
                    break
                end
            end
        end

        -- Fallback Mutilate
        -- actions.direct+=/mutilate,if=variable.use_filler
        if S.Mutilate:IsReady() and UseFiller then
            if Cast(S.Mutilate) then return "Cast Mutilate" end
        end
    
        return false
    end



    -- Enemies Update Function
    local function updateEnemies()
        state.MeleeRange = 5
        state.AoERange = 10
        state.TargetInMeleeRange = Target:IsInMeleeRange(state.MeleeRange)
        state.TargetInAoERange = Target:IsInMeleeRange(state.AoERange)
        if AoEON() then
            state.Enemies30y = Player:GetEnemiesInRange(30)
            state.MeleeEnemies10y = Player:GetEnemiesInMeleeRange(state.AoERange)
            state.MeleeEnemies10yCount = #state.MeleeEnemies10y
            state.MeleeEnemies5y = Player:GetEnemiesInMeleeRange(state.MeleeRange)
            state.MeleeEnemies5y_Sorted = Player:GetEnemiesInMeleeRangeSortedAsc(state.MeleeRange)
        else
            state.Enemies30y = {}
            state.MeleeEnemies10y = {}
            state.MeleeEnemies10yCount = 1
            state.MeleeEnemies5y = {}
            state.MeleeEnemies5y_Sorted = {}
        end
    end

    -- Rotation Variables Update Function
    local function updateRotationVars()
        state.spread_f_rupture = GetSetting('spread_f', {})[1] or false
        state.spread_f_garrote = GetSetting('spread_f', {})[2] or false

        state.InCombat = Player:AffectingCombat()
        state.InDungeon = Player:IsInDungeonArea()
        state.InRaid = Player:IsInRaidArea()
        state.DungeonSlice = Player:IsInParty() and Player:IsInDungeonArea() and not Player:IsInRaid()
        state.Tank = Rogue.GetTank()
        state.carnageusage = GetSetting('carnageusage', 2)

        state.BleedTickTime, state.ExsanguinatedBleedTickTime = 2 * Player:SpellHaste(), 1 * Player:SpellHaste()
        state.ComboPoints = Rogue.EffectiveComboPoints(Player:ComboPoints())
        state.ComboPointsDeficit = Player:ComboPointsMax() - state.ComboPoints
        state.RuptureThreshold = ((4 + state.ComboPoints * 4) * 0.3) + Player:GCDRemains()
        state.CrimsonTempestThreshold = ((4 + state.ComboPoints * 2) * 0.3) + Player:GCDRemains()
        state.GarroteThreshold = 5.4 + Player:GCDRemains()
        state.RuptureDMGThreshold = S.Envenom:Damage() * 3
        state.GarroteDMGThreshold = S.Mutilate:Damage() * 3
        state.PriorityRotation = UsePriorityRotation()
        state.EffectiveCPSpend = mathmax(Player:ComboPointsMax()-2, 5 * num(S.HandOfFate:IsAvailable()))
        state.ActualComboPoints = Player:ComboPoints()
        state.EnergyIncoming = 0
        state.RuptureCountThreshold = GetSetting('RuptureCountThreshold', 5)
        state.GarroteCountThreshold = GetSetting('GarroteCountThreshold', 5)

        state.PoisonedBleeds = Rogue.PoisonedBleeds()

        state.EnergyRegenCombined = Player:EnergyRegen() + state.PoisonedBleeds * 6 / (2 * Player:SpellHaste())
        state.EnergyTimeToMaxCombined = Player:EnergyDeficit() / state.EnergyRegenCombined

        if S.VenomousWounds:IsAvailable() then
            ---@param ThisUnit Unit
            for _, ThisUnit in pairs(state.Enemies30y) do
                if ThisUnit:DebuffUp(S.Rupture) and ThisUnit:TimeToDie() <= 20 and ThisUnit:DebuffRemains(S.Rupture) > ThisUnit:TimeToDie() then
                    state.EnergyIncoming = state.EnergyIncoming + 1
                end
            end
        end
        
        -- Combined Energy Regen needed to saturate
        -- actions+=/variable,name=regen_saturated,value=energy.regen_combined>30
        state.EnergyRegenSaturated = state.EnergyRegenCombined > 30
        
        -- Check upper bounds of energy to begin spending
        -- actions+=/variable,name=upper_limit_energy,value=energy.pct>=(80-10*talent.vicious_venoms.rank-30*talent.amplifying_poison)
        state.UpperLimitEnergy = UpperLimitEnergy()
        
        -- Checking for cooldowns soon
        -- actions+=/variable,name=cd_soon,value=talent.kingsbane&cooldown.kingsbane.remains<3&!cooldown.kingsbane.ready
        state.CDSoon = CDSoonVar()
        
        -- Pooling Condition all together
        -- actions+=/variable,name=not_pooling,value=variable.in_cooldowns|buff.darkest_night.up|variable.upper_limit_energy|fight_remains<=20
        state.NotPooling = NotPoolingVar()
        
        -- Check what the maximum Scent of Blood stacks is currently
        -- actions+=/variable,name=scent_effective_max_stacks,value=(spell_targets.fan_of_knives*talent.scent_of_blood.rank*2)>?20
        state.ScentEffectiveMaxStacks = ScentEffectiveMaxStacks()
        
        -- We are Scent Saturated when our stack count is hitting the maximum
        -- actions+=/variable,name=scent_saturation,value=buff.scent_of_blood.stack>=variable.scent_effective_max_stacks
        state.ScentSaturated = ScentSaturatedVar()
        
        -- Conditional to check if there is only one enemy
        -- actions+=/variable,name=single_target,value=spell_targets.fan_of_knives<2
        state.SingleTarget = state.MeleeEnemies10yCount < 2
        
        -- Combined Energy Regen needed to saturate
        -- actions+=/variable,name=in_cooldowns,value=dot.kingsbane.ticking|debuff.shiv.up
        state.InCooldowns = InCooldowns()
    end

    --- ======= MAIN =======
    local function APL ()
        -- Update the state
        updateEnemies()
        updateRotationVars()

        if S.Deathmark:CooldownRemains(nil, true) < 2 then
            CheckTimeToDieUnits()
        end
        
        -- ToT Macro Update
        if GetSetting('ToT', false) then
            if not Rogue.CanToT and Rogue.CanToTTime + 3 < GetTime() then
                Rogue.CanToT = true
            end
        end

        -- Burst Potion
        if state.TargetInMeleeRange then
            if MainAddon.UsePotion() then
                MainAddon.SetTopColor(1, "Combat Potion")
            end
        end

        -- Defensives
        ShouldReturn = Defensives();
        if ShouldReturn then
            return ShouldReturn;
        end

        -- Crimson Vial
        if evaluateVial() then
            ShouldReturn = Rogue.CrimsonVial()
            if ShouldReturn then
                return ShouldReturn
            end
        end

        -- Poisons
        if not Player:IsMoving() and not Player:IsCasting() then
            ShouldReturn = Poisons()
            if ShouldReturn then
                return ShouldReturn
            end
        end

        -- Out of Combat
        if not Player:AffectingCombat() then
            -- actions=stealth
            if not Player:BuffUp(Rogue.VanishBuffSpell()) and not Player:IsCasting()
            and (#Player:GetEnemiesInRangeUnfilter(40, "combatimmune") > 0 and GetSetting('autostealth', false) or state.InRaid or state.InDungeon or Player:IsInInstancedPvP() or Player:CanAttack(Target)) then
                ShouldReturn = Rogue.Stealth(Rogue.StealthSpell())
                if ShouldReturn then return ShouldReturn end
            end
            
            -- Shroud
            if GetSetting('ShroudUsage_check', false) and not state.InDungeon then
                if S.ShroudOfConcealment:IsReady(Player) and M.CombinedPullTimer() > 0 and GetSetting('ShroudUsage_spin', 3) - random_number >= M.CombinedPullTimer() then
                    if Cast(S.ShroudOfConcealment) then
                        return "Shroud"
                    end
                end
            end
            -- Opener
            if M.TargetIsValid() then
                -- actions.precombat+=/slice_and_dice,precombat_seconds=1
                if not Player:BuffUp(S.SliceandDice) then
                    if S.SliceandDice:IsReady() and state.ComboPoints >= 2 then
                        if Cast(S.SliceandDice) then return "Cast Slice and Dice" end
                    end
                end
            end

            -- PvP
            if Player:InPvP() then
                -- Opener
                ShouldReturn = PvPOpener()
                if ShouldReturn then
                    return ShouldReturn
                end
                return
            end
        end

        -- Tricks Of The Trade
        if GetSetting('ToT', false) and Rogue.CanToT and Player:BuffDown(S.TricksOfTheTradeBuffShort) and Player:BuffDown(S.TricksOfTheTradeBuffLong) then
            if state.InDungeon then
                if Rogue.HasMacro and state.Tank ~= nil then
                    if (MainAddon.CombatTimeForRogues(state.Tank) > 0 and MainAddon.CombatTimeForRogues(state.Tank) < 4
                    or (MainAddon.CombatTimeForRogues(state.Tank) == 0 and ((Player:CanAttack(Target) and Target:IsInRange(10) and not Target:IsDeadOrGhost()) or #Player:GetEnemiesInRangeUnfilter(20, "combatimmune") > 0))
                    or Player:IsTankingAoE(30, 1)
                    or (Player:BuffDown(S.TricksOfTheTradeBuffLong) and S.SoTricky:IsAvailable()))
                    and Target:NPCID() ~= 174773 and S.TricksoftheTrade:IsReady(state.Tank) and state.Tank:IsInRange(40) and state.Tank:CanBeToT() 
                    and M.Timers:AddDelay("ToT", 1, 0.5) then
                        if not state.Tank:IsDeadOrGhost() then
                            if Cast(S.TricksoftheTrade, true) then
                                return "TricksOfTheTrade"
                            end
                        end
                    end
                end
            end
        end

        -- Re-Target Ghostly Strike
        if GetSetting('retarget_DM', 3) ~= 3 then
            if S.Deathmark:IsAvailable() and UnitsWithDM() > 0 then
                for i, ThisUnit in pairs(state.MeleeEnemies10y) do
                    if EvaluateDeathmark(ThisUnit) then
                        MainAddon.Nameplate.AddIcon(ThisUnit, S.Deathmark, true)
                        if not state.ReTarget then
                            state.ReTarget = GetTime() - 0.15
                        end
                    end
                end
                if GetSetting('retarget_DM', 3) == 1 then
                    if MouseOver:Exists() and MouseOver:DebuffUp(S.Deathmark) then
                        MainAddon.SetTopColor(1, "Target Mouseover")
                    end
                end
                if GetSetting('retarget_DM', 3) == 2 then
                    if state.ReTarget and state.ReTarget < GetTime() - 0.15 then
                        MainAddon.SetTopColor(1, "Target Enemy")
                        state.ReTarget = GetTime()
                    end  
                end
            else
                state.ReTarget = nil
            end
        end
        
        -- Bottled Flayedwing Toxin
        if I.BottledFlayedwingToxin:IsEquippedAndReady() and Player:BuffDown(S.FlayedwingToxin) then
            if Cast(I.BottledFlayedwingToxin) then
                return "Bottle Of Flayedwing Toxin";
            end
        end

        -- In Combat
        if M.TargetIsValid() then
            if state.spread_f_rupture  
            or state.spread_f_garrote then
                local GetBestUnitSnipingUnit = GetBestUnitSniping()
                if GetBestUnitSnipingUnit then
                    if Focus:GUID() ~= GetBestUnitSnipingUnit:GUID() and Target:GUID() ~= GetBestUnitSnipingUnit:GUID() then
                        MainAddon.Nameplate.AddIcon(GetBestUnitSnipingUnit, Spell(351172), true)
                        if MouseOver:Exists() and MouseOver:GUID() == GetBestUnitSnipingUnit:GUID() then
                            MainAddon.SetTopColor(1, "Focus Mouseover")
                            return
                        end
                    end
                end
            end

            -- Trinkets
            local shouldReturn = MainAddon.TrinketDPS()
            if shouldReturn then
                return shouldReturn
            end  

            -- TODO: Make this match the updated code version
            -- actions+=/cycling_variable,name=energy_incoming,op=add,value=dot.rupture.remains>target.time_to_die&target.time_to_die<=20&talent.venomous_wounds

            -- actions+=/variable,name=regen_saturated,value=(energy.regen_combined>35)|(variable.energy_incoming>=1&talent.caustic_spatter)

            -- actions+=/variable,name=single_target,value=spell_targets.fan_of_knives<2

            --[[
            if GetSetting('dm_logic', 'strict') == "tolerant" then
                if (S.Deathmark:CooldownRemains() >= 50 or Target:DebuffUp(S.Deathmark)) then
                    if S.Kingsbane:IsReady() 
                    and (Target:DebuffUp(S.ShivDebuff) or S.Shiv:CooldownRemains() < 6) and Player:BuffUp(S.Envenom) then
                        if Cast(S.Kingsbane) then return "tolerant Kingsbane" end
                    end

                    if Player:StealthUp(true, false) or ImprovedGarroteRemains() > 0 or MasterAssassinRemains() > 0 then
                        if S.Kingsbane:IsAvailable() and Player:BuffUp(S.Envenom) then
                            if S.Shiv:IsReady() and (Target:DebuffUp(S.Kingsbane) or S.Kingsbane:CooldownUp()) and Target:DebuffDown(S.ShivDebuff) then
                                if Cast(S.Shiv) then return "tolerant Shiv (Stealth Kingsbane)" end
                            end
                            if S.Kingsbane:IsReady() and Player:BuffRemains(S.ShadowDanceBuff) >= 2 then
                                if Cast(S.Kingsbane) then return "tolerant Kingsbane (Dance)" end
                            end
                        end
                    end
                end

                local DeathmarkCondition = not Player:StealthUp(true, false) and Target:DebuffUp(S.Rupture) and Player:BuffUp(S.Envenom) and not S.Deathmark:AnyDebuffUp()
                and (not S.MasterAssassin:IsAvailable() or Target:DebuffUp(S.Garrote))
                and (not S.Kingsbane:IsAvailable() or S.Kingsbane:CooldownRemains() <= 2)

                if S.Deathmark:IsReady() and (DeathmarkCondition or HL.BossFilteredFightRemains("<=", 20)) then
                    if Cast(S.Deathmark) then return "tolerant Deathmark" end
                end
            end
            ]]

            -- actions+=/call_action_list,name=stealthed,if=stealthed.rogue|stealthed.improved_garrote|master_assassin_remains>0
            if Player:StealthUp(true, false) or ImprovedGarroteRemains() > 0 or MasterAssassinRemains() > 0 then
                ShouldReturn = Stealthed()
                if ShouldReturn then return ShouldReturn .. " (Stealthed)" end
            end
        
            -- actions+=/call_action_list,name=cds
            ShouldReturn = CDs()
            if ShouldReturn then return ShouldReturn end
        
            if state.TargetInAoERange then
                --- !!!! ---
                -- Special fallback Poisoned Knife Out of Range [EnergyCap] or [PoisonRefresh]
                -- Only if we are about to cap energy, not stealthed, and completely out of range
                --- !!!! ---
                if S.PoisonedKnife:IsReady() and Target:IsInRange(30) and not Player:StealthUp(true, true)
                and state.MeleeEnemies10yCount == 0 and Player:EnergyTimeToMax() <= Player:GCD() * 1.5 then
                    if Cast(S.PoisonedKnife) then return "Cast Poisoned Knife" end
                end
            end
        
            -- actions+=/call_action_list,name=core_dot
            ShouldReturn = CoreDot()
            if ShouldReturn then return ShouldReturn end
            -- actions+=/call_action_list,name=aoe_dot,if=!variable.single_target
            if AoEON() and not state.SingleTarget then
                ShouldReturn = AoeDot()
                if ShouldReturn then return ShouldReturn end
            end
        
            -- actions+=/call_action_list,name=direct
            ShouldReturn = Direct()
            if ShouldReturn then return ShouldReturn end
        
            -- actions+=/arcane_torrent,if=energy.deficit>=15+energy.regen_combined
            if S.ArcaneTorrent:IsReady() and Player:EnergyDeficit() >= 15 + state.EnergyRegenCombined then
                if Cast(S.ArcaneTorrent) then return "Cast Arcane Torrent" end
            end
            -- actions+=/arcane_pulse
            if S.ArcanePulse:IsReady() then
                if Cast(S.ArcanePulse) then return "Cast Arcane Pulse" end
            end
            -- actions+=/lights_judgment
            if S.LightsJudgment:IsReady() then
                if Cast(S.LightsJudgment) then return "Cast Lights Judgment" end
            end
            -- actions+=/bag_of_tricks
            if S.BagofTricks:IsReady() then
                if Cast(S.BagofTricks) then return "Cast Bag of Tricks" end
            end
            -- Trick to take in consideration the Recovery Setting
            if S.Mutilate:IsReady() or S.Ambush:IsReady() or S.AmbushOverride:IsReady() then
                if Cast(S.PoolEnergy) then return "Normal Pooling" end
            end

            -- Pool if nothing else to do
            if state.EnergyTimeToMaxCombined > 0 then
                return "Pooling Energy"
            end
            
            return "Waiting for resources"
        end
    end

    local function Init()
        S.Deathmark:RegisterAuraTracking()
        S.Garrote:RegisterAuraTracking()
        S.CrimsonTempest:RegisterAuraTracking()
        S.Rupture:RegisterAuraTracking()
        S.Kingsbane:RegisterAuraTracking()
        S.Shiv:RegisterAuraTracking()
    end
    M.SetAPL(259, APL, Init)


    -- function Unit:TimeToDieIsNotValid(MinSamples)
    --     return self:TimeToDie(MinSamples) >= 7777
    -- end
    local OldTimeToDieIsNotValid
    OldTimeToDieIsNotValid = HL.AddCoreOverride("Unit.TimeToDieIsNotValid",
            function(self, MinSamples)
                if MainAddon.PlayerSpecID() == 259 then
                    if self:IsBoss() then
                        return true
                    end
                end

                local BaseCheck = OldTimeToDieIsNotValid(self, MinSamples)
                return BaseCheck
            end
    , 259);


    local OldIsCastable
    OldIsCastable = HL.AddCoreOverride("Spell.IsCastable",
            function(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                if MainAddon.PlayerSpecID() == 259 then
                    if self == S.Vanish then
                        if not GetSetting('VanishDPS', false) or not Vanish_DPS_Condition() or not MainAddon.safeVanish() then
                            return false, "Not usable by custom conditions"
                        end
                    end

                    if self == S.Shadowmeld then
                        if not GetSetting('ShadowmeldDPS', false) or not Vanish_DPS_Condition() or not MainAddon.safeVanish() then
                            return false, "Not usable by custom conditions"
                        end
                    end
        
                    if self == S.IndiscriminateCarnage then
                        if state.MeleeEnemies10yCount < state.carnageusage then
                            return false, "Not usable by custom conditions"
                        end
                    end

                    if self == S.PoisonedKnife then
                        if not Player:AffectingCombat() then
                            return false, "Not usable by custom conditions"
                        end
                    end

                    if self == S.FanofKnives then
                        if not Player:AffectingCombat() or Player:StealthUp(false, true) then
                            return false, "Not usable by custom conditions"
                        end

                        if (not Target:IsSpellInRange(S.Mutilate) and GetTime() - Player:PrevGCDTime() < 0.15) then
                            return false, "Not usable by custom conditions"
                        end
                    end
                end

                local BaseCheck, Reason = OldIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                return BaseCheck, Reason
            end
    , 259);

    -- Prevent double Poison cast
    HL:RegisterForEvent(function(arg1, arg2, arg3, arg4, arg5)
        if arg2 == "player" then
            if arg5 == 3408 then
                TimeCripplingPoisonCasted = GetTime()
            end
            if arg5 == 2823 then
                TimeDeadlyPoisonCasted = GetTime()
            end
            if arg5 == 8679 then
                TimeWoundPoisonCasted = GetTime()
            end
            if arg5 == 381637 then
                TimeAtrophicPoisonCasted = GetTime()
            end
            if arg5 == 5761 then
                TimeNumbingPoisonCasted = GetTime()
            end
            if arg5 == 381664 then
                TimeAmplifyingPoisonCasted = GetTime()
            end
            if arg5 == 315584 then
                TimeInstantPoisonCasted = GetTime()
            end
        end
    end, "UNIT_SPELLCAST_SENT")

    HL:RegisterForSelfCombatEvent(
    function(_, _, _, _, _, _, _, _, _, _, _, SpellID)
        if SpellID == 457129 then
            timeSinceLastDSMark = GetTime()
        end
    end, "SPELL_AURA_APPLIED", "SPELL_AURA_REFRESH", "SPELL_AURA_APPLIED_DOSE")
end
