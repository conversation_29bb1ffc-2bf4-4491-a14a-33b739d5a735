function A_268(...)
	-- YUNO: updated on 11.06.25
	-- Core addon references
	---@class MainAddon
	local MainAddon = MainAddon
	---@class MainAddon
	local M = MainAddon
	
	-- HeroLib
	local HL = HeroLibEx
	--- @class Unit
	local Unit = HL.Unit
	---@class Unit
	local Player = Unit.Player
	---@class Unit
	local Target = Unit.Target
	--- @class Spell
	local Spell = HL.Spell
	local CreateSpell = M.CreateSpell
	--- @class Item
	local Item = HL.Item
	local Cast = M.Cast
	
	-- Lua
	local tableremove = table.remove
	local tableinsert = table.insert
	local tContains = _G['tContains']
	local GetTime = _G['GetTime']
	local C_TimerAfter = _G['C_Timer']['After']
	local mathmin = math.min
	local mathmax = math.max

    ---@class Monk
	local Monk = M.Monk
	
	local S = Spell.Monk.Brewmaster
    local I = Item.Monk.Brewmaster

	local GetSetting = MainAddon.Config.GetClassSetting
	local Config_Key = MainAddon.GetClassVariableName()
	local Config_Color = "00FF98"
	local Config_Table = {
		key = Config_Key,
		movable = true,
		title = "Monk - Brewmaster",
		subtitle = '?? ' .. MainAddon.Version,
		width = 600,
		height = 700,
		profiles = true,
		config = {
			-- Header Section
			{ type = "header", text = "", size = 24, align = "Center", color = Config_Color },
			{ type = "spacer" },
			{ type = "ruler" },
			{ type = "spacer" },
			{ type = "header", text = "\"Every good story needs a hero!\"", size = 16, align = "center", color = Config_Color },
			{ type = "header", text = "?? x yuno", size = 12, align = "center", color = '4C4C4C' },
			{ type = 'spacer' }, 
			{ type = 'ruler' }, 
			{ type = 'spacer' },
			
			-- General Settings
			{ type = "header", text = "General", color = Config_Color },
			{ type = 'checkbox', text = " Enable Mythic+ logics", icon = S.MightyOxKick:ID(), default = true, key = 'dungeonlogics' },
			{ type = 'checkbox', text = " Use Breath of Fire prepull to improve aggro generation", icon = S.BreathofFire:ID(), default = true, key = 'bofprepull' },
			{ type = "dropdown", text = " Touch of Death", key = "tod", multiselect = true, icon = S.TouchofDeath:ID(), list = { { text = "Boss", key = 1 }, { text = "Elite", key = 2 }, { text = "Normal", key = 3 } }, default = { 1, 2, 3 } },
			{ type = "dropdown", text = " Breath of Fire usage", key = "breathoffire_usage", multiselect = true, icon = S.BreathofFire:ID(), list = { { text = "Offensive", key = "offensive" }, { text = "Defensive", key = "defensive" } }, default = { "offensive", "defensive" } },
			{ type = "dropdown", text = " Niuzao usage", key = "niuzao_usage", multiselect = true, icon = S.InvokeNiuzao:ID(), list = { { text = "Offensive", key = "offensive" }, { text = "Defensive", key = "defensive" } }, default = { "offensive", "defensive" } },
			{ type = "spacer" },
			
			-- Defensive Settings
			{ type = "header", text = "Defensives", color = Config_Color },
			{ type = "dropdown", text = " How to handle Defensives", key = "howtodef", multiselect = false, icon = S.InnerStrengthBuff:ID(), list = { { text = "Auto", key = 'auto' }, { text = "With sliders below", key = 'sliders' } }, default = 'auto' },
			
			-- Health/Survival Settings
			{ type = "checkspin", text = " Vivacious Vivification for self healing", key = "vivaciousself", icon = S.Vivify:ID(), min = 1, max = 100, default_spin = 60, default_check = true },
			{ type = "checkspin", text = " Fortifying Brew", key = "fortbrew", icon = S.FortifyingBrew:ID(), min = 1, max = 100, default_spin = 38, default_check = true },
			{ type = "checkspin", text = " Dampen Harm", key = "dampemharm", icon = S.DampenHarm:ID(), min = 1, max = 100, default_spin = 28, default_check = true },
			{ type = "checkspin", text = " Diffuse Magic", key = "diffuse", icon = S.DiffuseMagic:ID(), min = 1, max = 100, default_spin = 40, default_check = false },
			
			-- Expel Harm Settings
			{ type = "checkspin", text = " Expel Harm - Orbs", key = "expelharm", icon = S.ExpelHarm:ID(), min = 1, max = 6, default_spin = 4, default_check = true },
			{ type = "checkspin", text = " Expel Harm - HP%", key = "expelharmhp", icon = S.ExpelHarm:ID(), min = 1, max = 100, default_spin = 70, default_check = true },
			
			-- Brew Management Settings
			{ type = 'checkspin', text = ' Purifying Brew - 1st Charge - Stagger % threshold', icon = S.PurifyingBrew:ID(), key = 'purybrew_one', min = 1, max = 250, default_spin = 5, default_check = true, step = 1 },
			{ type = 'checkspin', text = ' Purifying Brew - 2nd Charge - Stagger % threshold', icon = S.PurifyingBrew:ID(), key = 'purybrew_two', min = 20, max = 250, default_spin = 50, default_check = true, step = 1 },
			{ type = 'checkbox', text = " Purifying Brew - Don't cap on Charges", icon = S.PurifyingBrew:ID(), default = true, key = 'purybrew_cap' },
			{ type = "spinner", text = " Celestial Brew - Purified Chi Stacks", key = "purifiedchistack", icon = S.CelestialBrew:ID(), default = 4, min = 1, max = 6, step = 1 },
			
			-- Advanced Defensive Settings
			{ type = "checkspin", text = " Defensive Niuzao - Stagger % threshold", key = "niuzao_stagger", icon = S.InvokeNiuzao:ID(), min = 1, max = 100, default_spin = 40, default_check = true },
			{ type = 'checkbox', text = " Use Ring of Peace on danger", icon = S.RingofPeace:ID(), default = true, key = 'rop_danger' },
			
			-- Footer Spacing
			{ type = 'spacer' },
			{ type = 'spacer' },
			{ type = 'spacer' },
			{ type = 'spacer' },
			{ type = 'spacer' }, 
			{ type = 'ruler' }, 
			{ type = 'spacer' },
		}
	}
	-- Build configuration UI elements
	Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
	Config_Table.config = MainAddon.BuildTankingTrinketUI(Config_Table.config, Config_Color)
	Config_Table.config = MainAddon.BuildCombatPotionUI(Config_Table.config, "Brewmaster", Config_Color)
	MainAddon.SetConfig(268, Config_Table)

	-- Rotation state variables
	local CombatTime = 0
	local TargetIsValid = false
	local ShouldReturn

	-- Stagger tracking system
	local StaggerSpellID = 115069
	local StaggerDoTID = 124255
	local BobandWeave = CreateSpell(280515)
	local StaggerFull = 0
	local StaggerDamage = {}
	local IncomingDamage = {}

	-- - ======= MAIN =======
	local OnUseExcludes = {
        193652,-- Treemouth's Festering Splinter
        207174,-- Fyrakk's Tainted Rageheart
        212757,-- Granyth's Enduring Scale
        202616,-- Enduring Dreadplate
        203714,-- Ward of Faceless Ire
        194299,-- Decoration of Flame
        150526,-- Shadowmoon Insignia
        193634,-- Burgeoning Seed
        212450,-- Swarmlord's Authority
	}

	--- ===== Rotation Variables =====
	local Enemies5y
	local EnemiesCount5
	local IsTanking

	local BossFightRemains = 11111
    local FightRemains = 11111

    -- Reset variables after combat
    HL:RegisterForEvent(function()
        BossFightRemains = 11111
        FightRemains = 11111
    end, "PLAYER_REGEN_ENABLED")

	-- YUNO: START: Store Vitality (MasterofHarmony)
	-- Initialize StoredVitality variable
	StoredVitality = StoredVitality or 0

	-- Function to handle combat log events
	local function OnCombatLogEvent()
		local _, eventType, _, sourceGUID, _, _, _, destGUID, _, _, _, spellID, _, _, amount, overhealing = CombatLogGetCurrentEventInfo()
	
		-- Only process events caused by the player
		if sourceGUID == UnitGUID("player") then
			-- Damage Events
			if eventType == "SWING_DAMAGE" or eventType == "SPELL_DAMAGE" or eventType == "RANGE_DAMAGE" then
				-- Add 15% of damage dealt to StoredVitality
				StoredVitality = StoredVitality + ((amount or 0) * 0.15)
			end
	
			-- Healing Events
			if eventType == "SPELL_HEAL" or eventType == "SPELL_PERIODIC_HEAL" then
				local effectiveHealing = (amount or 0) - (overhealing or 0)
				-- Add 7% of effective healing to StoredVitality
				StoredVitality = StoredVitality + (effectiveHealing * 0.07)
			end
	
			-- Cap StoredVitality at player's maximum health
			local maxHealth = UnitHealthMax("player")
			if StoredVitality > maxHealth then
				StoredVitality = maxHealth
			end
		end
	
		-- Reset StoredVitality when Celestial Brew is cast
		if eventType == "SPELL_CAST_SUCCESS" and spellID == S.CelestialBrew:ID() and sourceGUID == UnitGUID("player") then
			StoredVitality = 0
		end
	end
	
	-- Register the combat log event handler
	local frame = CreateFrame("Frame")
	frame:RegisterEvent("COMBAT_LOG_EVENT_UNFILTERED")
	frame:SetScript("OnEvent", OnCombatLogEvent)
	-- YUNO: END: Store Vitality (MasterofHarmony)

	---@param Totem Spell
	---@param ReturnTime boolean?
	local function TotemFinder(Totem, ReturnTime)
		for i = 1, 6 do
			local TotemActive, TotemName, StartTime, Duration = Player:GetTotemInfo(i)
			
			-- Check if this totem matches what we're looking for
			local isBlackOxStatue = Totem:ID() == 115315 and TotemName == "Black Ox Statue"
			local isNameMatch = Totem:Name() == TotemName
			
			if isBlackOxStatue or isNameMatch then
				if ReturnTime then
					return mathmax(Duration - (GetTime() - StartTime), 0)
				else
					return true
				end
			end
		end
		
		-- No matching totem found
		return ReturnTime and 0 or false
	end

	local function RegisterStaggerFullAbsorb(Amount)
		local staggerDuration = 10 + (BobandWeave:IsAvailable() and 3 or 0)
		StaggerFull = StaggerFull + Amount
		C_Timer.After(staggerDuration, function() 
			StaggerFull = StaggerFull - Amount
		end)
	end

	local function RegisterStaggerDamageTaken(Amount)
		local maxStaggerEntries = 10
		
		-- Remove oldest entry if at capacity
		if #StaggerDamage >= maxStaggerEntries then
			tableremove(StaggerDamage, maxStaggerEntries)
		end
		
		-- Add new damage to front of table
		tableinsert(StaggerDamage, 1, Amount)
	end

	local function RegisterIncomingDamageTaken(Amount)
		local damageWindowSeconds = 6
		local currentTime = GetTime()
		local cutoffTime = currentTime - damageWindowSeconds
		
		-- Remove expired damage entries
		while #IncomingDamage > 0 and IncomingDamage[#IncomingDamage][1] < cutoffTime do
			tableremove(IncomingDamage, #IncomingDamage)
		end
		
		-- Add new damage entry with timestamp
		tableinsert(IncomingDamage, 1, {currentTime, Amount})
	end
	
	-- Returns the total amount of damage currently stored in the stagger pool
	function Player:StaggerFull()
		return StaggerFull
	end

	-- Returns total damage from the last X stagger ticks
	function Player:StaggerLastTickDamage(Count)
		local TickDamage = 0
		local ActualCount = mathmin(Count, #StaggerDamage)
		
		for i = 1, ActualCount do
			TickDamage = TickDamage + StaggerDamage[i]
		end
		
		return TickDamage
	end

	-- Returns total incoming damage taken within the specified time window (in milliseconds)
	function Player:IncomingDamageTaken(Milliseconds)
		local DamageTaken = 0
		local TimeOffset = Milliseconds / 1000
		local CutoffTime = GetTime() - TimeOffset
		
		for i = 1, #IncomingDamage do
			if IncomingDamage[i][1] > CutoffTime then
				DamageTaken = DamageTaken + IncomingDamage[i][2]
			end
		end
		
		return DamageTaken
	end

	HL:RegisterForCombatEvent(
		function(...)
			local args = {...}
			local DestGUID, SpellID, Amount
			
			-- Handle spell damage absorb (23 arguments)
			if #args == 23 then
				DestGUID, SpellID, Amount = select(8, ...), select(12, ...), select(15, ...)
			-- Handle melee absorb (fewer arguments)
			else
				DestGUID, SpellID, Amount = select(8, ...), select(9, ...), select(12, ...)
			end
			
			if DestGUID == Player:GUID() and SpellID == StaggerSpellID and Amount then
				RegisterStaggerFullAbsorb(Amount)
			end
		end,
		"SPELL_ABSORBED"
	)

	HL:RegisterForCombatEvent(
		function(...)
			local DestGUID, _, _, _, SpellID, _, _, Amount = select(8, ...)
			
			if MainAddon.PlayerSpecID() == 268 and DestGUID == Player:GUID() then
				-- Track Stagger damage
				if SpellID == StaggerDoTID and Amount and Amount > 0 then
					RegisterStaggerDamageTaken(Amount)
				-- Track other incoming damage
				elseif Amount and Amount > 0 then
					RegisterIncomingDamageTaken(Amount)
				end
			end
		end,
		"SWING_DAMAGE",
		"SPELL_DAMAGE",
		"SPELL_PERIODIC_DAMAGE"
	)

	-- Reset variables after combat
	HL:RegisterForEvent(function()
		BossFightRemains = 11111
		FightRemains = 11111
	end, "PLAYER_REGEN_ENABLED")

	-- Clear damage tracking tables when leaving combat
	HL:RegisterForEvent(
		function()
			if #StaggerDamage > 0 then
				for i = 0, #StaggerDamage do
					StaggerDamage[i] = nil
				end
			end
			if #IncomingDamage > 0 then
				for i = 0, #IncomingDamage do
					IncomingDamage[i] = nil
				end
			end
		end,
		"PLAYER_REGEN_ENABLED"
	)

	-- Niuzao tracking variables
	local ImpNiuzao = CreateSpell(322740)
	local Stomp = CreateSpell(227291)
	Monk.NiuzaoGUID = 0
	Monk.LastNiuzaoStomp = 0

	-- Track Niuzao summon
	HL:RegisterForCombatEvent(
		function(...)
			local SourceGUID, _, _, _, DestGUID, _, _, _, SpellID = select(4, ...)
			if SourceGUID == Player:GUID() and SpellID == 132578 then
				Monk.NiuzaoGUID = DestGUID
			end
		end,
		"SPELL_SUMMON"
	)

	-- Track Niuzao stomp
	HL:RegisterForCombatEvent(
		function(...)
			local SourceGUID, _, _, _, _, _, _, _, SpellID = select(4, ...)
			if SourceGUID == Monk.NiuzaoGUID and SpellID == 227291 then
				Monk.LastNiuzaoStomp = GetTime()
			end
		end,
		"SPELL_DAMAGE"
	)

	-- Reset Niuzao GUID when it dies
	HL:RegisterForCombatEvent(
		function(...)
			local DestGUID = select(8, ...)
			if DestGUID == Monk.NiuzaoGUID then
				Monk.NiuzaoGUID = 0
			end
		end,
		"UNIT_DIED"
	)

	local function Utilities()
		-- Resurrect dead party members
		if Target:IsDeadOrGhost() 
			and Target:IsInParty() 
			and Target:IsAPlayer() 
			and not Target:IsEnemy() 
			and S.Resuscitate:IsReady(Player) 
		then
			if Cast(S.Resuscitate) then
				return "Resuscitate"
			end
		end
	end

	-- Touch of Death Logic
	---@param TargetedUnit Unit
	local function EvaluateToD(TargetedUnit)
		local targetHealth = TargetedUnit:Health()
		local playerHealth = Player:Health()
		local targetHealthPercentage = TargetedUnit:HealthPercentage()
		local classification = TargetedUnit:Classification()
		local todSettings = GetSetting("tod", {})
		
		-- Check if target can be killed by Touch of Death
		local canKill = targetHealth <= playerHealth or (S.ImpTouchofDeath:IsAvailable() and targetHealthPercentage <= 15)
		
		-- Boss units
		if TargetedUnit:IsInBossList() or classification == "worldboss" then
			if not todSettings[1] then
				return false, "Boss toggle disabled"
			end
			return canKill, canKill and "Boss can be killed" or "Boss above kill threshold"
		end
		
		-- Elite units
		if classification == "rareelite" or classification == "elite" then
			if not todSettings[2] then
				return false, "Elite toggle disabled"
			end
			return canKill, canKill and "Elite can be killed" or "Elite above kill threshold"
		end
		
		-- Normal units
		if classification == "normal" or classification == "rare" then
			if not todSettings[3] then
				return false, "Normal toggle disabled"
			end
			return canKill, canKill and "Normal can be killed" or "Normal above kill threshold"
		end
		
		return false, "Invalid target classification"
	end

	-- Damage tracking system for defensive decisions
	local MYTHIC_PLUS_EXCLUDED_SPELLS = {
		240443, -- Burst (Bursting)
		240559, -- Grievous Wound (Grievous)
		209858, -- Necrotic Wound (Necrotic)
		240447, -- Quake (Quaking)
		315161  -- Eye of Corruption
	}

	local DAMAGE_TRACKING_WINDOW = 2 -- seconds
	local damageEvents = {}

	local function LastDMGTaken()
		local total = 0
		for _, event in ipairs(damageEvents) do
			total = total + event.damage
		end
		return total
	end

	local function RegisterDMG(timestamp, subevent, hideCaster, sourceGUID, sourceName, sourceFlags, sourceRaidFlags, destGUID, destName, destFlags, destRaidFlags, ...)
		-- Only track damage to the player
		if destGUID ~= UnitGUID("player") then
			return
		end

		-- Skip environmental damage
		if subevent:find("ENVIRONMENTAL") then
			return
		end

		-- Only process damage and missed events
		if not (subevent:find("DAMAGE") or subevent:find("MISSED")) then
			return
		end

		local damageTaken = 0
		local currentTime = GetTime()

		if subevent:find("DAMAGE") and not subevent:find("DURABILITY") then
			if subevent:find("SWING") then
				-- Melee damage
				damageTaken = select(1, ...)
				local absorbed = select(6, ...)
				if absorbed then
					damageTaken = damageTaken + absorbed
				end
			else
				-- Spell damage
				local spellId = select(1, ...)
				if not tContains(MYTHIC_PLUS_EXCLUDED_SPELLS, spellId) then
					damageTaken = select(4, ...)
					local absorbed = select(9, ...)
					if absorbed then
						damageTaken = damageTaken + absorbed
					end
				end
			end
		elseif subevent:find("MISSED") then
			if subevent:find("SWING") then
				-- Melee absorb
				if select(1, ...) == 'ABSORB' then
					damageTaken = select(3, ...)
				end
			else
				-- Spell absorb
				if select(4, ...) == 'ABSORB' then
					local spellId = select(1, ...)
					if not tContains(MYTHIC_PLUS_EXCLUDED_SPELLS, spellId) then
						damageTaken = select(6, ...)
					end
				end
			end
		end

		-- Record the damage event
		if damageTaken and damageTaken > 0 then
			tableinsert(damageEvents, {
				time = currentTime,
				damage = damageTaken
			})

			-- Schedule cleanup after tracking window expires
			C_TimerAfter(DAMAGE_TRACKING_WINDOW, function()
				tableremove(damageEvents, 1)
			end)
		end
	end

	-- Register damage tracking for multiple event types
	HL:RegisterForCombatEvent(RegisterDMG, "SWING_DAMAGE", "SWING_MISSED", "SPELL_DAMAGE", "SPELL_PERIODIC_DAMAGE", "RANGE_DAMAGE", "RANGE_MISSED", "SPELL_MISSED")

	-- Check if Blackout Combo is not talented or buff is inactive
	local function BlackoutComboBuffDown()
		return not S.BlackoutCombo:IsAvailable() or Player:BuffDown(S.BlackoutComboBuff)
	end

	
	local function Defensives()
		-- Purifying Brew
		if S.PurifyingBrew:IsReady(Player) and not Player:BuffUp(S.BlackoutComboBuff) then
			local staggerPct = Player:StaggerPercentage()
			local charges = S.PurifyingBrew:Charges()
			local timeSinceLastCast = S.PurifyingBrew:TimeSinceLastCast()
			
			-- Prevent capping charges
			if charges == 2 and staggerPct >= 2 and GetSetting("purybrew_cap", true) then
				if Cast(S.PurifyingBrew) then return "Purifying Brew - Prevent capping charges" end
			end

			-- Emergency purification on red stagger
			if staggerPct >= 60 then
				if Cast(S.PurifyingBrew) then return "Purifying Brew - Red Stagger" end
			end
	
			-- Core usage logic after cast cooldown
			if timeSinceLastCast >= 3 then
				-- Heavy stagger with imminent death check
				if Player:DebuffUp(S.HeavyStagger) then
					if LastDMGTaken() >= Player:Health() * 0.55 then
						if Cast(S.PurifyingBrew) then return "Purifying Brew - Heavy Stagger + high damage" end
					end
					if Cast(S.PurifyingBrew) then return "Purifying Brew - Heavy Stagger" end
				end
	
				local isSliderMode = GetSetting("howtodef", "auto") == "sliders"
				local purifiedChiExpiring = Player:BuffUp(S.PurifiedChiBuff) and Player:BuffRemains(S.PurifiedChiBuff) <= 2
				
				-- First charge usage
				if charges == 2 then
					if isSliderMode then
						if GetSetting("purybrew_one_check", true) and staggerPct >= GetSetting("purybrew_one_spin", 5) then
							if Cast(S.PurifyingBrew) then return "Purifying Brew - 1st Charge threshold" end
						end
					else -- auto mode
						if staggerPct >= 35 or purifiedChiExpiring then
							if Cast(S.PurifyingBrew) then return "Purifying Brew - 1st Charge auto" end
						end
					end
				end
			
				-- Second charge usage
				if charges == 1 then
					if isSliderMode then
						if GetSetting("purybrew_two_check", true) and staggerPct >= GetSetting("purybrew_two_spin", 50) then
							if Cast(S.PurifyingBrew) then return "Purifying Brew - 2nd Charge threshold" end
						end
					else -- auto mode
						local lowHpHighStagger = staggerPct >= 40 and Player:HealthPercentage() < 45
						if staggerPct >= 60 or purifiedChiExpiring or lowHpHighStagger then
							if Cast(S.PurifyingBrew) then return "Purifying Brew - 2nd Charge auto" end
						end
					end
				end
			end
		end

		-- Fortifying Brew
		if S.FortifyingBrew:IsReady(Player) then
			local playerHp = Player:HealthPercentage()
			local isSliderMode = GetSetting("howtodef", "auto") == "sliders"
			
			if isSliderMode then
				if GetSetting("fortbrew_check", true) and playerHp <= GetSetting("fortbrew_spin", 38) then
					if Cast(S.FortifyingBrew) then return "Fortifying Brew - HP Threshold" end
				end
			else -- auto mode
				local noDampenOrNotReady = not S.DampenHarm:IsAvailable() or 
					(not S.DampenHarm:IsReady(Player) and not Player:BuffUp(S.DampenHarmBuff))
				
				if playerHp <= 35 and noDampenOrNotReady then
					if Cast(S.FortifyingBrew) then return "Fortifying Brew - Auto Mode" end
				end
			end
		end

		-- Expel Harm
		if S.ExpelHarm:IsReady(Player, 8) then
			local count = S.ExpelHarm:Count()
			local playerHp = Player:HealthPercentage()
			local isSliderMode = GetSetting("howtodef", "auto") == "sliders"
			
			if isSliderMode then
				if count >= GetSetting("expelharm_spin", 4) and playerHp <= GetSetting("expelharmhp_spin", 70) then
					if Cast(S.ExpelHarm) then return "Expel Harm - Orb & HP Threshold" end
				end
			else -- auto mode
				local shouldCast = (count >= 4 and playerHp <= 80) or 
					(count == 3 and playerHp <= 60) or 
					(count <= 3 and playerHp <= 25)
				
				if shouldCast then
					if Cast(S.ExpelHarm) then return "Expel Harm - Auto Mode" end
				end
			end
		end

		-- Celestial Brew
		if S.CelestialBrew:IsReady(Player) then
			local isSliderMode = GetSetting("howtodef", "auto") == "sliders"
			local blackoutComboDown = BlackoutComboBuffDown()
			local purifiedChiStacks = Player:BuffStack(S.PurifiedChiBuff)
			local highIncomingDamage = Player:IncomingDamageTaken(2500) > Player:MaxHealth() * 0.25
			
			if isSliderMode then
				if blackoutComboDown and highIncomingDamage and Player:StaggerLastTickDamage(5) > Player:MaxHealth() * 0.25 then
					if Cast(S.CelestialBrew) then return "Celestial Brew - Incoming Stagger Damage" end
				elseif Player:NeedsDefensivePvE() then
					if Cast(S.CelestialBrew) then return "Celestial Brew - Critical Moment" end
				elseif S.PurifyingBrew:Charges() < 2 and purifiedChiStacks >= GetSetting("purifiedchistack", 4) then
					if Cast(S.CelestialBrew) then return "Celestial Brew - Purified Chi Stacks" end
				end
			else -- auto mode
				if blackoutComboDown and highIncomingDamage and Player:StaggerPercentage() > 40 then
					if Cast(S.CelestialBrew) then return "Celestial Brew - Auto Stagger" end
				elseif Player:NeedsDefensivePvE() then
					if Cast(S.CelestialBrew) then return "Celestial Brew - Auto Critical" end
				else
					local shouldCastForStacks = (S.PurifyingBrew:Charges() == 0 and purifiedChiStacks >= 5) or 
						purifiedChiStacks >= 6
					
					if shouldCastForStacks then
						if Cast(S.CelestialBrew) then return "Celestial Brew - Purified Chi Auto" end
					end
				end
			end
		end

		-- Dampen Harm
		if S.DampenHarm:IsReady(Player) then
			local playerHp = Player:HealthPercentage()
			local isSliderMode = GetSetting("howtodef", "auto") == "sliders"
			
			if isSliderMode then
				if GetSetting("dampemharm_check", true) and playerHp <= GetSetting("dampemharm_spin", 28) then
					if Cast(S.DampenHarm) then return "Dampen Harm - HP Threshold" end
				end
			else -- auto mode
				local noFortOrNotReady = not S.FortifyingBrew:IsAvailable() or 
					(not S.FortifyingBrew:IsReady(Player) and not Player:BuffUp(S.FortifyingBrewBuff))
				
				if playerHp <= 45 and noFortOrNotReady then
					if Cast(S.DampenHarm) then return "Dampen Harm - Auto Mode" end
				end
			end
		end

		-- Diffuse Magic (sliders only)
		if GetSetting("howtodef", "auto") == "sliders" and GetSetting("diffuse_check", false) and 
		   S.DiffuseMagic:IsReady(Player) and Player:HealthPercentage() <= GetSetting("diffuse_spin", 40) then
			if Cast(S.DiffuseMagic) then return "Diffuse Magic - HP Threshold" end
		end

		-- Ring of Peace emergency
		if GetSetting("rop_danger", true) and not Player:InBossEncounter() and Player:IsInDungeonArea() then
			local noDefensivesReady = (not S.FortifyingBrew:IsReady(Player) and not Player:BuffUp(S.FortifyingBrewBuff)) and
				(not S.DampenHarm:IsReady(Player) and not Player:BuffUp(S.DampenHarmBuff)) and
				(not S.CelestialBrew:IsReady(Player) and not Player:BuffUp(S.CelestialBrewBuff)) and
				S.PurifyingBrew:Charges() == 0
			
			if noDefensivesReady and S.RingofPeace:IsReady() then
				local playerHp = Player:HealthPercentage()
				local staggerPct = Player:StaggerPercentage()
				local emergencyCondition = playerHp < 25 or 
					(staggerPct > 70 and playerHp < 35) or 
					(staggerPct > 90 and playerHp < 40)
				
				if emergencyCondition then
					if Cast(S.RingofPeace, Player) then
						MainAddon.UI:ShowToast("Ring of Peace", "We are in Danger, start kiting!", MainAddon.GetTexture(S.RingofPeace))
						return "Ring of Peace - Emergency Kiting"
					end
				end
			end
		end

		-- Vivify emergency self-heal
		if S.Vivify:IsReady(Player) and Player:BuffUp(S.VivaciousVivification) then
			local playerHp = Player:HealthPercentage()
			local isSliderMode = GetSetting("howtodef", "auto") == "sliders"
			
			if isSliderMode then
				if playerHp <= GetSetting("vivaciousself_spin", 60) and Cast(S.Vivify) then
					return "Vivify - HP Threshold"
				end
			else -- auto mode
				local combatTime = HL.CombatTime()
				local shouldHeal = (playerHp < 69 and combatTime <= 8) or (playerHp < 48 and combatTime > 8)
				
				if shouldHeal and Cast(S.Vivify) then
					return "Vivify - Auto Self-Heal"
				end
			end
		end
	end
	
	local function Precombat()
		-- Rushing Jade Wind for aggro generation
		if S.RushingJadeWind:IsReady(Player) and Target:IsInRange(20) and Player:BuffRemains(S.RushingJadeWindBuff) < 1 then
			if Cast(S.RushingJadeWind) then 
				return "Rushing Jade Wind (aggro generation)" 
			end
		end
		
		-- Breath of Fire prepull for aggro generation
		if GetSetting('bofprepull', true) and S.BreathofFire:IsReady(nil, 25) and not Player:BuffUp(S.RushingJadeWindBuff) then
			if Cast(S.BreathofFire) then
				return "Breath of Fire (aggro generation)"
			end
		end
		
		-- Chi abilities for pulling
		if S.ChiBurst:IsReady() then
			if Cast(S.ChiBurst) then 
				return "Chi Burst - Pull" 
			end
		end
		
		if S.ChiWave:IsReady() then
			if Cast(S.ChiWave) then 
				return "Chi Wave - Pull" 
			end
		end
		
		-- Keg Smash pull with 2 charges
		if S.KegSmash:IsReady() and S.KegSmash:Charges() == 2 then 
			if Cast(S.KegSmash) then 
				return "Keg Smash - Pull" 
			end
		end
	end

	local function ItemActions()
		local usableItem = Player:GetUseableItems(OnUseExcludes)
		if usableItem and Cast(usableItem) then
			return "Using " .. usableItem:Name()
		end
	end
	  
	local function RaceActions()
		-- Racial abilities for offensive cooldowns
		local racialAbilities = {
			{S.BloodFury, "Blood Fury"},
			{S.Berserking, "Berserking"},
			{S.ArcaneTorrent, "Arcane Torrent"},
			{S.LightsJudgment, "Light's Judgment"},
			{S.Fireblood, "Fireblood"},
			{S.AncestralCall, "Ancestral Call"},
			{S.BagofTricks, "Bag of Tricks"}
		}
		
		for _, ability in ipairs(racialAbilities) do
			local spell, spellName = ability[1], ability[2]
			if spell:IsReady() and Cast(spell) then
				return spellName
			end
		end
	end

	local function SingleTarget()
		-- Aggro recovery: KegSmash if player has no aggro on current pull's mobs
		if not Target:IsDummy() and not IsTanking and S.KegSmash:IsReady() then
			if Cast(S.KegSmash) then 
				return "Keg Smash - Building Aggro"
			end
		end

		-- Aggro recovery: Spinning Crane Kick if no KegSmash charges and player has no aggro on current pull's mobs
		if not Target:IsDummy() and not IsTanking and S.KegSmash:Charges() <= 2 and S.SpinningCraneKick:IsReady() and Player:BuffUp(S.CharredPassionsBuff) then
			if Cast(S.SpinningCraneKick) then 
				return "Spinning Crane Kick - Building Aggro"
			end
		end

		-- Black Ox Brew resource management
		if S.BlackOxBrew:IsReady() then
			local hasFlurryBuff = S.FlurryStrikes:IsAvailable() and Player:BuffUp(S.WisdomoftheWallFlurryBuff)
			local shadoPanCondition = hasFlurryBuff and Player:Energy() < 50 and S.PurifyingBrew:ChargesFractional() < 0.65 and S.CelestialBrew:ChargesFractional() < 0.85
			local standardCondition = Player:Energy() < 40 and S.PurifyingBrew:Charges() == 0 and S.CelestialBrew:Charges() == 0
			
			if shadoPanCondition then
				if Cast(S.BlackOxBrew) then 
					return "Black Ox Brew - Shado-Pan Flurry"
				end
			elseif standardCondition then
				if Cast(S.BlackOxBrew) then 
					return "Black Ox Brew - Refresh"
				end
			end
		end

		-- Use Breath of Fire to gain Charred Passions if Opportunistic Strike is active (4pc bonus)
		if S.BreathofFire:IsReady() and Player:BuffUp(S.OpportunisticStrikeBuff) and Player:BuffDown(S.CharredPassionsBuff) then
			if Cast(S.BreathofFire) then
				return "Breath of Fire - With Opportunistic Strike"
			end
		end

		-- Weapons of Order priority spenders
		if Target:DebuffUp(S.WeaponsofOrderDebuff) then
			-- Spend Keg Smash charges ASAP
			if S.KegSmash:IsReady() and Target:IsInRange(8) then
				if Cast(S.KegSmash) then
					return "Keg Smash - WoO"
				end
			end
	
			-- Use Niuzao in offensive mode
			if S.InvokeNiuzao:IsReady() and GetSetting("niuzao_usage", "defensive") == "offensive" then
				if Cast(S.InvokeNiuzao) then
					return "Invoke Niuzao - WoO"
				end
			end
		end

		-- Blackout Kick to get Blackout Combo buff
		if S.BlackoutKick:IsReady() and Player:BuffDown(S.BlackoutComboBuff) and S.BlackoutCombo:IsAvailable() then
			if Cast(S.BlackoutKick) then
				return "Blackout Kick - For Blackout Combo"
			end
		end

		-- Chi-Burst (Master of Harmony)
		if S.ChiBurst:IsReady() and S.AspectOfHarmony:IsAvailable() then
			if Cast(S.ChiBurst) then 
				return "Chi Burst - Master of Harmony"
			end
		end

		-- Blackout Combo consumption logic
		if Player:BuffUp(S.BlackoutComboBuff) then
			local hasPressAdvantage = S.PresstheAdvantage:IsAvailable()
			local pressStacks = Player:BuffStack(S.PresstheAdvantageBuff)
			
			if not hasPressAdvantage then
				-- No Press the Advantage → Tiger Palm to dump Blackout Combo
				if S.TigerPalm:IsReady() then
					if Cast(S.TigerPalm) then
						return "Tiger Palm - Consume Blackout Combo"
					end
				end
			elseif hasPressAdvantage then
				-- Press the Advantage up → spend at 10 stacks on Rising Sun Kick (≤3 targets)
				if S.RisingSunKick:IsReady() and pressStacks == 10 and EnemiesCount5 <= 3 then
					if Cast(S.RisingSunKick) then
						return "Rising Sun Kick - Consume Blackout Combo at 10 stacks"
					end
				-- Otherwise dump Blackout Combo with Tiger Palm
				elseif S.TigerPalm:IsReady() then
					if Cast(S.TigerPalm) then
						return "Tiger Palm - Consume Blackout Combo"
					end
				end
			end
		end

		-- Use Keg Smash (avoid casting at 10 stacks of Press the Advantage unless defensive need)
		if S.KegSmash:IsReady() and S.KegSmash:ChargesFractional() >= 1.8 then
			local hasPressAdvantage = S.PresstheAdvantage:IsAvailable()
			local pressStacks = Player:BuffStack(S.PresstheAdvantageBuff)
			local lowHealth = Player:HealthPercentage() <= 50
			
			if not hasPressAdvantage or pressStacks < 10 or (pressStacks == 10 and lowHealth) then
				if Cast(S.KegSmash) then 
					return "Keg Smash"
				end
			end
		end

		-- Breath of Fire
		local breathSettings = GetSetting("breathoffire_usage", {})
		local breathOffensive = breathSettings["offensive"]
		local breathDefensive = breathSettings["defensive"]
		local needsCharredPassions = Player:BuffRemains(S.CharredPassionsBuff) <= 2.65
		local breathDebuffExpiring = Target:DebuffRemains(S.BreathofFireDotDebuff) <= 2.65
		local canUseWithBlackoutCombo = Player:BuffDown(S.BlackoutComboBuff) or EnemiesCount5 > 8
		
		if S.BreathofFire:IsReady() then
			-- Offensive Breath of Fire
			if breathOffensive and needsCharredPassions and canUseWithBlackoutCombo then
				if Cast(S.BreathofFire) then
					return "Breath of Fire - Offensive Setting"
				end
			end
			-- Defensive Breath of Fire
			if breathDefensive and breathDebuffExpiring and canUseWithBlackoutCombo and Target:DebuffUp(S.KegSmashDebuff) then
				if Cast(S.BreathofFire) then
					return "Breath of Fire - Defensive Setting"
				end
			end
		end

		-- Celestial Brew for offensive usage (Aspect of Harmony)
		if S.CelestialBrew:IsReady() and S.AspectOfHarmony:IsAvailable() and not Player:BuffUp(S.AspectOfHarmonyBuffSpending) and Player:HealthPercentage() <= 90 then
			local nearTwoCharges = S.CelestialBrew:ChargesFractional() >= 1.95
			local vitalityNearMax = StoredVitality >= (Player:MaxHealth() * 0.95)
			
			if nearTwoCharges or vitalityNearMax then
				if Cast(S.CelestialBrew) then 
					return "Celestial Brew - Offensive Usage"
				end
			end
		end

		-- Use Weapons of Order
		if S.WeaponsofOrder:IsReady(Player) and CombatTime > 4 and Player:IsStandingStillFor() > 1 then
			if Cast(S.WeaponsofOrder) then 
				return "Weapons of Order"
			end
		end

		-- Use Rising Sun Kick 
		if S.RisingSunKick:IsReady() then
			if Cast(S.RisingSunKick) then 
				return "Rising Sun Kick"
			end
		end

		-- Use Chi Burst if playing Shado-Pan
		if S.ChiBurst:IsReady() and S.FlurryStrikes:IsAvailable() then
			if Cast(S.ChiBurst) then 
				return "Chi Burst"
			end
		end

		-- Use Exploding Keg
		if S.ExplodingKeg:IsReady() then
			if Cast(S.ExplodingKeg, Player) then 
				return "Exploding Keg"
			end
		end

		-- Use extra charge of Purifying Brew to trigger Special Delivery if taking minimal damage
		if S.PurifyingBrew:IsReady() and S.PurifyingBrew:Charges() == 2 and Player:StaggerPercentage() >= 2 and Player:HealthPercentage() <= 90 then
			if Cast(S.PurifyingBrew) then 
				return "Purifying Brew - For Special Delivery"
			end
		end

		-- Refresh Rushing Jade Wind
		if S.RushingJadeWind:IsReady() and Player:BuffRemains(S.RushingJadeWindBuff) < 2 then
			if Cast(S.RushingJadeWind) then 
				return "Rushing Jade Wind - Refresh"
			end
		end

		-- Use the last charge of Keg Smash
		if S.KegSmash:IsReady() then
			if Cast(S.KegSmash) then 
				return "Keg Smash"
			end
		end

		-- Tiger Palm filler (ensure we can still Keg Smash)
		if S.TigerPalm:IsReady() and Player:Energy() > 35 and S.KegSmash:CooldownRemains() > 1.25 then
			if Cast(S.TigerPalm) then 
				return "Tiger Palm - Filler"
			end
		end
	end

	local function AoE()
		-- Black Ox Statue Management
		if GetSetting('dungeonlogics', true) then
			-- Summon Black Ox Statue
			if S.SummonBlackOxStatue:IsReady() and not TotemFinder(S.SummonBlackOxStatue) and Player:IsStandingStillFor() > 0.65 then
				if Cast(S.SummonBlackOxStatue) then
					return "Summon Black Ox Statue"
				end
			end

			-- Taunt statue when losing aggro
			if S.Provoke:IsReady() and TotemFinder(S.SummonBlackOxStatue) and not Target:IsDummy() and not IsTanking then
				if Cast(S.Provoke, "Provoke Statue") then
					return "Taunt with Provoke"
				end
			end
		end

		-- Aggro recovery with Keg Smash
		if not Target:IsDummy() and not IsTanking and S.KegSmash:IsReady() then
			if Cast(S.KegSmash) then 
				return "Keg Smash - Aggro Recovery"
			end
		end
		
		-- Aggro recovery with Spinning Crane Kick
		if not Target:IsDummy() and not IsTanking and S.KegSmash:Charges() <= 2 and S.SpinningCraneKick:IsReady() and Player:BuffUp(S.CharredPassionsBuff) then
			if Cast(S.SpinningCraneKick) then 
				return "Spinning Crane Kick - Aggro Recovery"
			end
		end

		-- Black Ox Brew resource management
		if S.BlackOxBrew:IsReady() then
			local hasFlurryBuff = S.FlurryStrikes:IsAvailable() and Player:BuffUp(S.WisdomoftheWallFlurryBuff)
			local shadoPanCondition = hasFlurryBuff and Player:Energy() < 50 and S.PurifyingBrew:ChargesFractional() < 0.65 and S.CelestialBrew:ChargesFractional() < 0.85
			local standardCondition = Player:Energy() < 40 and S.PurifyingBrew:Charges() == 0 and S.CelestialBrew:Charges() == 0
			
			if shadoPanCondition then
				if Cast(S.BlackOxBrew) then 
					return "Black Ox Brew - Shado-Pan Flurry"
				end
			elseif standardCondition then
				if Cast(S.BlackOxBrew) then 
					return "Black Ox Brew - Resource Management"
				end
			end
		end

		-- Weapons of Order priority actions
		if Target:DebuffUp(S.WeaponsofOrderDebuff) then
			-- Spend Keg Smash charges ASAP
			if S.KegSmash:IsReady() and Target:IsInRange(8) then
				if Cast(S.KegSmash) then
					return "Keg Smash - Weapons of Order"
				end
			end

			-- Use Niuzao in offensive mode
			if S.InvokeNiuzao:IsReady() and GetSetting("niuzao_usage", "defensive") == "offensive" then
				if Cast(S.InvokeNiuzao) then
					return "Invoke Niuzao - Weapons of Order"
				end
			end
		end

		-- Breath of Fire with Opportunistic Strike (4pc bonus)
		if S.BreathofFire:IsReady() and Player:BuffUp(S.OpportunisticStrikeBuff) and Player:BuffDown(S.CharredPassionsBuff) then
			if Cast(S.BreathofFire) then
				return "Breath of Fire - Opportunistic Strike"
			end
		end

		-- Blackout Kick for Blackout Combo buff
		if S.BlackoutKick:IsReady() and Player:BuffDown(S.BlackoutComboBuff) and S.BlackoutCombo:IsAvailable() then
			if Cast(S.BlackoutKick) then
				return "Blackout Kick - Blackout Combo Setup"
			end
		end

		-- Chi-Burst for Master of Harmony
		if S.ChiBurst:IsReady() and S.AspectOfHarmony:IsAvailable() then
			if Cast(S.ChiBurst) then 
				return "Chi Burst - Master of Harmony"
			end
		end

		-- Blackout Combo consumption logic
		if Player:BuffUp(S.BlackoutComboBuff) then
			local hasPressAdvantage = S.PresstheAdvantage:IsAvailable()
			local pressStacks = Player:BuffStack(S.PresstheAdvantageBuff)
			
			if not hasPressAdvantage then
				-- Dump Blackout Combo based on enemy count
				if EnemiesCount5 < 8 and S.TigerPalm:IsReady() then
					if Cast(S.TigerPalm) then
						return "Tiger Palm - Consume Blackout Combo"
					end
				elseif EnemiesCount5 >= 8 and S.BreathofFire:IsReady() then
					if Cast(S.BreathofFire) then
						return "Breath of Fire - Consume Blackout Combo"
					end
				end
			else
				-- Press the Advantage logic
				if S.RisingSunKick:IsReady() and pressStacks == 10 and EnemiesCount5 <= 3 then
					if Cast(S.RisingSunKick) then
						return "Rising Sun Kick - Press the Advantage 10 Stacks"
					end
				elseif S.BreathofFire:IsReady() then
					if Cast(S.BreathofFire) then
						return "Breath of Fire - Consume Blackout Combo"
					end
				end
			end
		end

		-- Keg Smash with Press the Advantage consideration
		if S.KegSmash:IsReady() and S.KegSmash:ChargesFractional() >= 1.8 then
			local hasPressAdvantage = S.PresstheAdvantage:IsAvailable()
			local pressStacks = Player:BuffStack(S.PresstheAdvantageBuff)
			local canCastAtMaxStacks = pressStacks == 10 and EnemiesCount5 >= 4
			
			if not hasPressAdvantage or pressStacks < 10 or canCastAtMaxStacks then
				if Cast(S.KegSmash) then 
					return "Keg Smash"
				end
			end
		end

		-- Chi Burst for Shado-Pan
		if S.ChiBurst:IsReady() and S.FlurryStrikes:IsAvailable() then
			if Cast(S.ChiBurst) then 
				return "Chi Burst - Shado-Pan"
			end
		end

		-- Breath of Fire
		local breathSettings = GetSetting("breathoffire_usage", {})
		local breathOffensive = breathSettings["offensive"]
		local breathDefensive = breathSettings["defensive"]
		local needsCharredPassions = Player:BuffRemains(S.CharredPassionsBuff) <= 2.65
		local breathDebuffExpiring = Target:DebuffRemains(S.BreathofFireDotDebuff) <= 2.65
		local canUseWithBlackoutCombo = Player:BuffDown(S.BlackoutComboBuff) or EnemiesCount5 > 8
		
		if S.BreathofFire:IsReady() then
			-- Offensive Breath of Fire
			if breathOffensive and needsCharredPassions and canUseWithBlackoutCombo then
				if Cast(S.BreathofFire) then
					return "Breath of Fire - Offensive Setting"
				end
			end
			-- Defensive Breath of Fire
			if breathDefensive and breathDebuffExpiring and canUseWithBlackoutCombo and Target:DebuffUp(S.KegSmashDebuff) then
				if Cast(S.BreathofFire) then
					return "Breath of Fire - Defensive Setting"
				end
			end
		end

		-- Exploding Keg
		if S.ExplodingKeg:IsReady() then
			if Cast(S.ExplodingKeg, Player) then 
				return "Exploding Keg"
			end
		end

		-- Celestial Brew for resource management (Master of Harmony)
		if S.CelestialBrew:IsReady() and S.AspectOfHarmony:IsAvailable() and not Player:BuffUp(S.AspectOfHarmonyBuffSpending) and Player:HealthPercentage() <= 90 then
			local nearTwoCharges = S.CelestialBrew:ChargesFractional() >= 1.9
			local nearMaxVitality = StoredVitality >= (Player:MaxHealth() * 0.95)
			
			if nearTwoCharges or nearMaxVitality then
				if Cast(S.CelestialBrew) then 
					return "Celestial Brew - Resource Management"
				end
			end
		end

		-- Weapons of Order
		if S.WeaponsofOrder:IsReady(Player) and CombatTime > 4 and Player:IsStandingStillFor() > 1 then
			if Cast(S.WeaponsofOrder) then 
				return "Weapons of Order"
			end
		end

		-- Rising Sun Kick without Press the Advantage
		if S.RisingSunKick:IsReady() and not S.PresstheAdvantage:IsAvailable() then
			if Cast(S.RisingSunKick) then 
				return "Rising Sun Kick"
			end
		end

		-- Refresh Rushing Jade Wind
		if S.RushingJadeWind:IsReady() and Player:BuffRemains(S.RushingJadeWindBuff) < 2 then
			if Cast(S.RushingJadeWind) then 
				return "Rushing Jade Wind - Refresh"
			end
		end

		-- Use last charge of Keg Smash
		if S.KegSmash:IsReady() then
			if Cast(S.KegSmash) then 
				return "Keg Smash"
			end
		end

		-- Purifying Brew for Special Delivery
		if S.PurifyingBrew:IsReady() and S.PurifyingBrew:Charges() == 2 and Player:StaggerPercentage() >= 2 and Player:HealthPercentage() <= 90 then
			if Cast(S.PurifyingBrew) then 
				return "Purifying Brew - Special Delivery"
			end
		end

		-- Spinning Crane Kick with Charred Passions
		if S.SpinningCraneKick:IsReady() and Player:BuffUp(S.CharredPassionsBuff) and Player:Energy() >= 40 then
			if Cast(S.SpinningCraneKick) then 
				return "Spinning Crane Kick - Charred Passions"
			end
		end

		-- Spinning Crane Kick with energy management
		local sckConditions = S.SpinningCraneKick:IsReady() and Player:Energy() >= 40 and 
			S.KegSmash:CooldownRemains() > 2 and not Player:PrevGCD(1, S.SpinningCraneKick)
		
		if sckConditions then
			if Cast(S.SpinningCraneKick) then 
				return "Spinning Crane Kick - Energy Management"
			end
		end

		-- Tiger Palm filler
		if S.TigerPalm:IsReady() and Player:Energy() > 35 and S.KegSmash:CooldownRemains() > 1.25 then
			if Cast(S.TigerPalm) then 
				return "Tiger Palm - Filler"
			end
		end
	end

	--- Automatically uses Diffuse Magic when player has dangerous magic debuffs
	local function AutoDiffuseMagic()
		if not GetSetting('dungeonlogics', true) or not S.DiffuseMagic:IsReady(Player) then
			return
		end
		
		-- Dangerous magic debuffs that should trigger Diffuse Magic
		local dangerousDebuffs = {
			[441397] = true, -- Bee-Venom
			[439325] = true, -- Burning Fermentation
			[294961] = true, -- Blazing Chomp
			[280604] = true, -- Iced Spritzer
			[469799] = true, -- Overcharge
			[1214323] = true, -- Lightning Torrent
			[330810] = true, -- Bind Soul
		}
		
		for debuffID in pairs(dangerousDebuffs) do
			if Player:DebuffUp(Spell(debuffID)) then
				if Cast(S.DiffuseMagic) then
					return "Diffuse Magic - " .. MainAddon.GetSpellInfo(debuffID)
				end
			end
		end
	end

	--- ===== APL Main =====
	local function APL()
		TargetIsValid = MainAddon.TargetIsValid()
		
		-- Unit Update
		Enemies5y = Player:GetEnemiesInMeleeRange(5)
		EnemiesCount5 = M.AoEON() and math.max(#Enemies5y, 1) or 1

		-- Maintain Purified Chi buff even out of combat
		if not Player:AffectingCombat() and Player:BuffUp(S.PurifiedChiBuff) and 
		   GetSetting("purybrew_cap", true) and Player:StaggerPercentage() >= 1 and 
		   Player:BuffRemains(S.PurifiedChiBuff) <= 2 then
			if Cast(S.PurifyingBrew) then
				return "Purifying Brew - Maintain Purified Chi"
			end
		end

		-- Combat state updates
		if TargetIsValid or Player:AffectingCombat() then
			BossFightRemains = HL.BossFightRemains()
			FightRemains = BossFightRemains == 11111 and HL.FightRemains(Enemies5y, false) or BossFightRemains
			IsTanking = Player:IsTankingAoE(8) or Player:IsTanking(Target)
			CombatTime = HL.CombatTime()
		end

		-- Trinkets
		ShouldReturn = MainAddon.TrinketTanking()
		if ShouldReturn then
			return "Trinket: " .. ShouldReturn
		end

		-- Handle combat vs non-combat scenarios
		if CombatTime > 0 then
			ShouldReturn = Defensives()
			if ShouldReturn then 
				return "Defensive: " .. ShouldReturn
			end
		else
			ShouldReturn = Utilities()
			if ShouldReturn then 
				return "Utility: " .. ShouldReturn
			end
		end

		-- Auto Diffuse Magic for debuffs
		ShouldReturn = AutoDiffuseMagic()
		if ShouldReturn then
			return "Auto Diffuse: " .. ShouldReturn
		end

		if not TargetIsValid then return end

		-- Precombat actions
		if not Player:AffectingCombat() then
			ShouldReturn = Precombat()
			if ShouldReturn then 
				return "Precombat: " .. ShouldReturn 
			end
		end

		-- Burst Potion
		if Target:IsInRange(8) and MainAddon.UsePotion() then
			MainAddon.SetTopColor(1, "Combat Potion")
		end

		-- Racial abilities
		ShouldReturn = RaceActions()
		if ShouldReturn then return "Racial: " .. ShouldReturn end

		-- Items/Trinkets
		ShouldReturn = ItemActions()
		if ShouldReturn then return "Item: " .. ShouldReturn end
		
		-- Emergency Expel Harm with many healing spheres
		if S.ExpelHarm:IsReady() and S.ExpelHarm:Count() > 4 and Player:HealthPercentage() <= 70 then
			if Cast(S.ExpelHarm) then return "Expel Harm - With Many Healing Spheres" end
		end

		-- Touch of Death execute
		if S.TouchofDeath:IsReady() and EvaluateToD(Target) then
			if M.ForceCastDisplay(S.TouchofDeath) then 
				return "Touch of Death - Execute" 
			end
		end

		-- Target must be in range for rotations
		if not Target:IsInRange(8) then return end

		-- AoE rotation for 3+ targets
		if EnemiesCount5 >= 3 then
			ShouldReturn = AoE()
			if ShouldReturn then return "AoE: " .. ShouldReturn end
		end

		-- Single Target rotation for 1-2 targets
		if EnemiesCount5 <= 2 then
			ShouldReturn = SingleTarget()
			if ShouldReturn then return "ST: " .. ShouldReturn end
		end
	end

	local function Init()
		-- Initialize Brewmaster rotation
	end
	M.SetAPL(268, APL, Init)

	local OldIsReady
	OldIsReady = HL.AddCoreOverride("Spell.IsReady", function(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
		local BaseCheck, Reason = OldIsReady(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
		if MainAddon.PlayerSpecID() == 268 then

			-- Master of Harmony specific logic
			if S.AspectOfHarmony:IsAvailable() then
				-- Hold Breath of Fire when Exploding Keg is ready with Balanced Stratagem stacks
				if self == S.BreathofFire and S.ExplodingKeg:IsReady() and Player:BuffStack(S.BalancedStratagemBuff) > 1 then
					return false, "Hold for Exploding Keg with Balanced Stratagem"
				end
				
				-- Don't interrupt Vitality/Harmony spending with these abilities
				if (self == S.ExplodingKeg or self == S.ChiBurst or self == S.CelestialBrew) and Player:BuffUp(S.AspectOfHarmonyBuffSpending) then
					return false, "Don't use while spending Vitality"
				end
			end

			-- Purifying Brew restrictions
			if self == S.PurifyingBrew then
				if Player:Stagger() == 0 then 
					return false, "No Stagger" 
				end
				
				-- Don't waste Blackout Combo on low stagger unless emergency
				if Player:BuffUp(S.BlackoutComboBuff) and Player:StaggerPercentage() < 80 and Player:HealthPercentage() > 40 then
					return false, "Blackout Combo is active - low stagger"
				end
			end

			-- Celestial Brew - prevent wasting
			if self == S.CelestialBrew then
				-- Check Purified Chi stacks based on defensive mode setting
				local defMode = GetSetting("howtodef", "auto")
				local requiredStacks = defMode == "sliders" and GetSetting("purifiedchistack", 4) or 3
				
				if Player:BuffStack(S.PurifiedChiBuff) < requiredStacks then
					return false, "Not enough Purified Chi stacks (" .. Player:BuffStack(S.PurifiedChiBuff) .. "/" .. requiredStacks .. ")"
				end
			end

			-- Prevent double casting of brew abilities
			if (self == S.PurifyingBrew or self == S.CelestialBrew) and Player:PrevGCD(1, self) then
				return false, "Already cast " .. self:Name()
			end

			-- Prevent Spinning Crane Kick while already channeling
			if self == S.SpinningCraneKick and Player:IsChanneling(self) then
				return false, "Already channeling Spinning Crane Kick"
			end

		end
		return BaseCheck, Reason
	end, 268)

	local OldIsCastable
	OldIsCastable = HL.AddCoreOverride("Spell.IsCastable", function(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
		local BaseCheck, Reason = OldIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
		
		if MainAddon.PlayerSpecID() == 268 then
			-- Niuzao defensive mode stagger check
			if self == S.InvokeNiuzao then
				local niuzaoMode = GetSetting("niuzao_usage", "offensive")
				if niuzaoMode == "defensive" and GetSetting("niuzao_stagger_check", false) then
					if Player:StaggerPercentage() <= GetSetting("niuzao_stagger_spin", 40) then
						return false, "Stagger condition not met for defensive Niuzao"
					end
				end
			end

			-- Ring of Peace encounter restriction
			if self == S.RingofPeace and M.CurrentEncounter == 2847 then
				return false, "Blocked during Captain Dailcry encounter"
			end

			-- Chi Burst casting check
			if self == S.ChiBurst and Player:IsCasting(self) then
				return false, "Already casting Chi Burst"
			end
		end

		return BaseCheck, Reason
	end, 268)
end