---@class MainAddon
local MainAddon = MainAddon
local HL = HeroLibEx
---@class Spell
local Spell = HL.Spell
local CreateSpell = MainAddon.CreateSpell
local CreateMultiSpell = MainAddon.CreateMultiSpell
---@class Item
local Item = HL.Item
local C_Timer = _G['C_Timer']
local MergeTableByKey = HL.Utils.MergeTableByKey

-- Spells
if not Spell.Warlock then Spell.Warlock = {} end

---@class WLCustomTable
Spell.Warlock.Custom = {
    WingBuffet                            = CreateSpell(357214),
    Fear                                  = CreateSpell(5782),
    BurningRush                           = CreateSpell(111400),
    DarkPact                              = CreateSpell(108416),
    MortalCoil                            = CreateSpell(6789),
    HowlofTerror                          = CreateSpell(5484),
    Whiplash                              = CreateSpell(6360),
    FelDomination                         = CreateSpell(333889),
    PetSuccubus                           = CreateSpell(366222),
    PetImp                                = CreateSpell(688),
    PetVoidwalker                         = CreateSpell(697),
    PetFelhunter                          = CreateSpell(691),
    DevourMagic                           = CreateSpell(19505),
    Soulstone                             = CreateSpell(20707),
    Corruption                            = CreateSpell(172),
    DrainLife                             = CreateSpell(234153),
    DemonicGateway                        = CreateSpell(111771),
    DemonicCircle                         = CreateSpell(48018),
    DemonicCircleTeleport                 = CreateSpell(48020),
    
    -- HR Forgot
    ShadowEmbraceDebuff                   = CreateSpell(32390),

    -- Racials
    BloodFury = CreateMultiSpell(20572, 33702, 33697),

    -- PvP
    BaneofFragility = CreateSpell(199954),
    CastingCircle = CreateSpell(221703),
    CallObserver = CreateSpell(201996),
    BondsofFel = CreateSpell(353753),
    ShadowRift = CreateSpell(353294),
    -- PvP Affliction
    BaneofShadows = CreateSpell(234877),
    RapidContagion = CreateSpell(344566),
    Deathbolt = CreateSpell(264106),
    -- PvP Demonology
    CallFelLord = CreateSpell(212459),
    FelObelisk = CreateSpell(353601),
    -- PvP Destruction
    BaneofHavoc = CreateSpell(200546),
}

---@class WLCommonsTable
Spell.Warlock.Commons = {
    -- Racials
    AncestralCall                         = CreateSpell(274738),
    Berserking                            = CreateSpell(26297),
    BloodFury                             = CreateSpell(33702),
    Fireblood                             = CreateSpell(265221),
    -- Abilities
    ShadowBolt                            = CreateSpell(686),
    SummonDarkglare                       = CreateSpell(205180),
    UnendingResolve                       = CreateSpell(104773),
    -- Talents
    GrimoireofSacrifice                   = CreateSpell(108503),
    GrimoireofSacrificeBuff               = CreateSpell(196099),
    SoulConduit                           = CreateSpell(215941),
    SummonSoulkeeper                      = CreateSpell(386256),
    InquisitorsGaze                       = CreateSpell(386344),
    InquisitorsGazeBuff                   = CreateSpell(388068),
    Shadowfury                            = CreateSpell(30283),
    Soulburn                              = CreateSpell(385899),
    -- Buffs
    SpymastersReportBuff                  = CreateSpell(451199),
    -- Debuffs
    -- Command Demon Abilities
    AxeToss                               = CreateSpell(119914),
    Seduction                             = CreateSpell(119909),
    ShadowBulwark                         = CreateSpell(119907),
    SingeMagic                            = CreateSpell(119905),
    SpellLock                             = CreateSpell(119910),
}

---@class WLDiabolistTable
Spell.Warlock.Diabolist = {
    -- Abilities
    InfernalBolt                          = CreateSpell(434506),
    RuinationAbility                      = CreateSpell(434635),
    -- Talents
    DiabolicRitual                        = CreateSpell(428514),
    Ruination                             = CreateSpell(428522),
    SecretsoftheCoven                     = CreateSpell(428518),
    -- Buffs
    DemonicArtMotherBuff                  = CreateSpell(432794),
    DemonicArtOverlordBuff                = CreateSpell(428524),
    DemonicArtPitLordBuff                 = CreateSpell(432795),
    DiabolicRitualMotherBuff              = CreateSpell(432815),
    DiabolicRitualOverlordBuff            = CreateSpell(431944),
    DiabolicRitualPitLordBuff             = CreateSpell(432816),
    InfernalBoltBuff                      = CreateSpell(433891),
}

---@class WLHellcallerTable
Spell.Warlock.Hellcaller = {
    -- Talents
    Malevolence                           = CreateSpell(442726),
    Wither                                = CreateSpell(445468),
    -- Debuffs
    MalevolenceBuff                       = CreateSpell(442726),
    WitherDebuff                          = CreateSpell(445474),
}

---@class WLSoulHarvesterTable
Spell.Warlock.SoulHarvester = {
    -- Talents
    DemonicSoul                           = CreateSpell(449614),
    Quietus                               = CreateSpell(449634),
}

---@class DemonologyTable
Spell.Warlock.Demonology = {
    -- Racials
    AncestralCall                         = CreateSpell(274738),
    Berserking                            = CreateSpell(26297),
    BloodFury                             = CreateSpell(33702),
    Fireblood                             = CreateSpell(265221),
    -- Abilities
    ShadowBolt                            = CreateSpell(686),
    SummonDarkglare                       = CreateSpell(205180),
    UnendingResolve                       = CreateSpell(104773),
    -- Talents
    GrimoireofSacrifice                   = CreateSpell(108503),
    GrimoireofSacrificeBuff               = CreateSpell(196099),
    SoulConduit                           = CreateSpell(215941),
    SummonSoulkeeper                      = CreateSpell(386256),
    Soulburn                              = CreateSpell(385899),
    -- Buffs
    PowerInfusionBuff                     = CreateSpell(10060),
    -- Debuffs
    -- Command Demon Abilities
    AxeToss                               = CreateSpell(119914),
    Seduction                             = CreateSpell(119909),
    ShadowBulwark                         = CreateSpell(119907),
    SingeMagic                            = CreateSpell(119905),
    SpellLock                             = CreateSpell(119910),

    -- Base Abilities
    Felstorm                              = CreateSpell(89751, "Pet"),
    HandofGuldan                          = CreateSpell(105174), -- Splash, 8
    SummonPet                             = CreateSpell(30146),
    -- Talents
    BilescourgeBombers                    = CreateSpell(267211), -- Splash, 8
    CallDreadstalkers                     = CreateSpell(104316),
    Demonbolt                             = CreateSpell(264178),
    DemonicCalling                        = CreateSpell(205145),
    DemonicStrength                       = CreateSpell(267171),
    Doom                                  = CreateSpell(460551),
    FelInvocation                         = CreateSpell(428351),
    FelCovenant                           = CreateSpell(387432),
    FromtheShadows                        = CreateSpell(267170),
    GrimoireFelguard                      = CreateSpell(111898),
    Guillotine                            = CreateSpell(386833),
    ImpGangBoss                           = CreateSpell(387445),
    Implosion                             = CreateSpell(196277), -- Splash, 8
    InnerDemons                           = CreateSpell(267216),
    MarkofFharg                           = CreateSpell(455450),
    MarkofShatug                          = CreateSpell(455449),
    NetherPortal                          = CreateSpell(267217),
    PowerSiphon                           = CreateSpell(264130),
    SacrificedSouls                       = CreateSpell(267214),
    SoulboundTyrant                       = CreateSpell(334585),
    SoulStrike                            = CreateSpell(428344),
    SoulStrikePetAbility                  = CreateSpell(264057, "Pet"),
    SummonCharhound                       = CreateSpell(455476),
    SummonDemonicTyrant                   = CreateSpell(265187),
    SummonGloomhound                      = CreateSpell(455465),
    SummonVilefiend                       = CreateSpell(264119),
    TheExpendables                        = CreateSpell(387600),
    GrandWarlocksDesign                   = CreateSpell(387084),
    ReignofTyranny                        = CreateSpell(427684),
    -- Buffs
    DemonicCallingBuff                    = CreateSpell(205146),
    DemonicCoreBuff                       = CreateSpell(264173),
    DemonicPowerBuff                      = CreateSpell(265273),
    FelCovenantBuff                       = CreateSpell(387437),
    NetherPortalBuff                      = CreateSpell(267218),
    RiteofRuvaraadBuff                    = CreateSpell(409725), -- T30 4pc
    -- Debuffs
    DoomDebuff                            = CreateSpell(603),
    DoomBrandDebuff                       = CreateSpell(423583), -- T31 2pc
    FromtheShadowsDebuff                  = CreateSpell(270569),
}
---@class WLCustomTable
Spell.Warlock.Demonology = MergeTableByKey(Spell.Warlock.Demonology, Spell.Warlock.Custom)
---@class WLCommonsTable
Spell.Warlock.Demonology = MergeTableByKey(Spell.Warlock.Demonology, Spell.Warlock.Commons, true)
---@class WLDiabolistTable
Spell.Warlock.Demonology = MergeTableByKey(Spell.Warlock.Demonology, Spell.Warlock.Diabolist)
---@class WLHellcallerTable
Spell.Warlock.Demonology = MergeTableByKey(Spell.Warlock.Demonology, Spell.Warlock.Hellcaller)
---@class WLSoulHarvesterTable
Spell.Warlock.Demonology = MergeTableByKey(Spell.Warlock.Demonology, Spell.Warlock.SoulHarvester)

---@class AfflictionTable
Spell.Warlock.Affliction = {
  -- Base Abilities
  Agony                                 = CreateSpell(980),
  Corruption                            = CreateSpell(172),
  DrainLife                             = CreateSpell(234153),
  SummonPet                             = CreateSpell(688),
  -- Talents
  AbsoluteCorruption                    = CreateSpell(196103),
  CreepingDeath                         = CreateSpell(264000),
  DrainSoul                             = CreateSpell(198590),
  DrainSoulTalent                       = CreateSpell(388667),
  DreadTouch                            = CreateSpell(389775),
  Haunt                                 = CreateSpell(48181),
  ImprovedShadowBolt                    = CreateSpell(453080),
  InevitableDemise                      = CreateSpell(334319),
  MaleficAffliction                     = CreateSpell(389761),
  MaleficRapture                        = CreateSpell(324536),
  Nightfall                             = CreateSpell(108558),
  Oblivion                              = CreateSpell(417537),
  PhantomSingularity                    = CreateSpell(205179),
  SeedofCorruption                      = CreateSpell(27243),
  ShadowEmbrace                         = CreateSpell(32388),
  SiphonLife                            = CreateSpell(63106),
  SoulRot                               = CreateSpell(386997),
  SoulSwap                              = CreateSpell(386951),
  SoulTap                               = CreateSpell(387073),
  SouleatersGluttony                    = CreateSpell(389630),
  SowtheSeeds                           = CreateSpell(196226),
  TormentedCrescendo                    = CreateSpell(387075),
  UnstableAffliction                    = CreateSpell(316099),
  VileTaint                             = CreateSpell(278350),
  -- Buffs
  InevitableDemiseBuff                  = CreateSpell(334320),
  NightfallBuff                         = CreateSpell(264571),
  MaleficAfflictionBuff                 = CreateSpell(389845),
  TormentedCrescendoBuff                = CreateSpell(387079),
  UmbrafireKindlingBuff                 = CreateSpell(423765), -- T31 4pc
  -- Debuffs
  AgonyDebuff                           = CreateSpell(980),
  CorruptionDebuff                      = CreateSpell(146739),
  HauntDebuff                           = CreateSpell(48181),
  PhantomSingularityDebuff              = CreateSpell(205179),
  SeedofCorruptionDebuff                = CreateSpell(27243),
  SiphonLifeDebuff                      = CreateSpell(63106),
  UnstableAfflictionDebuff              = CreateSpell(316099),
  VileTaintDebuff                       = CreateSpell(386931),
  SoulRotDebuff                         = CreateSpell(386997),
  DreadTouchDebuff                      = CreateSpell(389868),
  ShadowEmbraceDSDebuff                 = CreateSpell(32390),
  ShadowEmbraceSBDebuff                 = CreateSpell(453206),
}
---@class WLCustomTable
Spell.Warlock.Affliction = MergeTableByKey(Spell.Warlock.Affliction, Spell.Warlock.Custom)
---@class WLCommonsTable
Spell.Warlock.Affliction = MergeTableByKey(Spell.Warlock.Affliction, Spell.Warlock.Commons, true)
---@class WLDiabolistTable
Spell.Warlock.Affliction = MergeTableByKey(Spell.Warlock.Affliction, Spell.Warlock.Diabolist)
---@class WLHellcallerTable
Spell.Warlock.Affliction = MergeTableByKey(Spell.Warlock.Affliction, Spell.Warlock.Hellcaller)
---@class WLSoulHarvesterTable
Spell.Warlock.Affliction = MergeTableByKey(Spell.Warlock.Affliction, Spell.Warlock.SoulHarvester)

---@class DestructionTable
Spell.Warlock.Destruction = {
  -- Base Abilities
  Immolate                              = CreateSpell(348),
  Incinerate                            = CreateSpell(29722),
  SummonPet                             = CreateSpell(688),
  -- Talents
  AshenRemains                          = CreateSpell(387252),
  AvatarofDestruction                   = CreateSpell(387159),
  Backdraft                             = CreateSpell(196406),
  BlisteringAtrophy                     = CreateSpell(456939),
  BurntoAshes                           = CreateSpell(387153),
  Cataclysm                             = CreateSpell(152108),
  ChannelDemonfire                      = CreateSpell(196447),
  ChaosBolt                             = CreateSpell(116858),
  ChaosIncarnate                        = CreateSpell(387275),
  Chaosbringer                          = CreateSpell(422057),
  Conflagrate                           = CreateSpell(17962),
  ConflagrationofChaos                  = CreateSpell(387108),
  CrashingChaos                         = CreateSpell(417234),
  CryHavoc                              = CreateSpell(387522),
  Decimation                            = CreateSpell(456985),
  DemonfireMastery                      = CreateSpell(456946),
  DiabolicEmbers                        = CreateSpell(387173),
  DimensionalRift                       = CreateSpell(387976),
  Eradication                           = CreateSpell(196412),
  FireandBrimstone                      = CreateSpell(196408),
  Havoc                                 = CreateSpell(80240),
  ImprovedChaosBolt                     = CreateSpell(456951),
  Inferno                               = CreateSpell(270545),
  InternalCombustion                    = CreateSpell(266134),
  MadnessoftheAzjAqir                   = CreateSpell(387400),
  Mayhem                                = CreateSpell(387506),
  Pyrogenics                            = CreateSpell(387095),
  RagingDemonfire                       = CreateSpell(387166),
  RainofChaos                           = CreateSpell(266086),
  RainofFire                            = CreateMultiSpell(5740,1214467),
  RoaringBlaze                          = CreateSpell(205184),
  Ruin                                  = CreateSpell(387103),
  Shadowburn                            = CreateSpell(17877),
  SoulFire                              = CreateSpell(6353),
  SummonInfernal                        = CreateSpell(1122),
  -- Buffs
  BackdraftBuff                         = CreateSpell(117828),
  DecimationBuff                        = CreateSpell(457555),
  MadnessCBBuff                         = CreateSpell(387409),
  MadnessRoFBuff                        = CreateSpell(387413),
  MadnessSBBuff                         = CreateSpell(387414),
  RainofChaosBuff                       = CreateSpell(266087),
  RitualofRuinBuff                      = CreateSpell(387157),
  BurntoAshesBuff                       = CreateSpell(387154),
  -- Debuffs
  ConflagrateDebuff                     = CreateSpell(265931),
  EradicationDebuff                     = CreateSpell(196414),
  HavocDebuff                           = CreateSpell(80240),
  ImmolateDebuff                        = CreateSpell(157736),
  PyrogenicsDebuff                      = CreateSpell(387096),
  RoaringBlazeDebuff                    = CreateSpell(265931),
}
---@class WLCustomTable
Spell.Warlock.Destruction = MergeTableByKey(Spell.Warlock.Destruction, Spell.Warlock.Custom)
---@class WLCommonsTable
Spell.Warlock.Destruction = MergeTableByKey(Spell.Warlock.Destruction, Spell.Warlock.Commons, true)
---@class WLDiabolistTable
Spell.Warlock.Destruction = MergeTableByKey(Spell.Warlock.Destruction, Spell.Warlock.Diabolist)
---@class WLHellcallerTable
Spell.Warlock.Destruction = MergeTableByKey(Spell.Warlock.Destruction, Spell.Warlock.Hellcaller)
---@class WLSoulHarvesterTable
Spell.Warlock.Destruction = MergeTableByKey(Spell.Warlock.Destruction, Spell.Warlock.SoulHarvester)

-- Items
if not Item.Warlock then Item.Warlock = {} end

---@class WLCustomItemTable
Item.Warlock.Custom = {

}

---@class WLCommonsItemTable
Item.Warlock.Commons = {
  -- TWW Trinkets
  FunhouseLens                          = Item(234217, {13, 14}),
  SignetofthePriory                     = Item(219308, {13, 14}),
  SpymastersWeb                         = Item(220202, {13, 14}),
  -- Older Items
  NeuralSynapseEnhancer                 = Item(168973, {13, 14}),
}

---@class WLAffliItemTable
Item.Warlock.Affliction = {
    -- DF Trinkets
    TimeThiefsGambit                      = Item(207579, {13, 14}),
    -- TWW Trinkets
    AberrantSpellforge                    = Item(212451, {13, 14}),
}
---@class WLCustomItemTable
Item.Warlock.Affliction = MergeTableByKey(Item.Warlock.Affliction, Item.Warlock.Custom)
---@class WLCommonsItemTable
Item.Warlock.Affliction = MergeTableByKey(Item.Warlock.Affliction, Item.Warlock.Commons)

---@class WLDemoItemTable
Item.Warlock.Demonology = {
    -- DF Trinkets
    MirrorofFracturedTomorrows            = Item(207581, {13, 14}),
    -- TWW Trinkets
    ImperfectAscendancySerum              = Item(225654, {13, 14}),
}
---@class WLCustomItemTable
Item.Warlock.Demonology = MergeTableByKey(Item.Warlock.Demonology, Item.Warlock.Custom)
---@class WLCommonsItemTable
Item.Warlock.Demonology = MergeTableByKey(Item.Warlock.Demonology, Item.Warlock.Commons)

---@class WLDestroItemTable
Item.Warlock.Destruction = {
}
---@class WLCustomItemTable
Item.Warlock.Destruction = MergeTableByKey(Item.Warlock.Destruction, Item.Warlock.Custom)
---@class WLCommonsItemTable
Item.Warlock.Destruction = MergeTableByKey(Item.Warlock.Destruction, Item.Warlock.Commons)

Spell.Warlock.Affliction.Agony:SetGeneric(WARLOCK_AFFLICTION_SPECID, "Generic1")
Spell.Warlock.Affliction.Corruption:SetGeneric(WARLOCK_AFFLICTION_SPECID, "Generic2")
Spell.Warlock.Affliction.SiphonLife:SetGeneric(WARLOCK_AFFLICTION_SPECID, "Generic3")
Spell.Warlock.Affliction.MortalCoil:SetGeneric(WARLOCK_AFFLICTION_SPECID, "Generic4")
Spell.Warlock.Affliction.Fear:SetGeneric(WARLOCK_AFFLICTION_SPECID, "Generic5")
Spell.Warlock.Affliction.DrainSoul:SetGeneric(WARLOCK_AFFLICTION_SPECID, "Generic6")

Spell.Warlock.Affliction.Seduction.MaximumRange = 30
Spell.Warlock.Affliction.ShadowBulwark.MaximumRange = 40
Spell.Warlock.Affliction.SingeMagic.MaximumRange = 30
Spell.Warlock.Affliction.SpellLock.MaximumRange = 40

Spell.Warlock.Demonology.Doom:SetGeneric(WARLOCK_DEMONOLOGY_SPECID, "Generic1")
Spell.Warlock.Demonology.Demonbolt:SetGeneric(WARLOCK_DEMONOLOGY_SPECID, "Generic2")
Spell.Warlock.Demonology.MortalCoil:SetGeneric(WARLOCK_DEMONOLOGY_SPECID, "Generic3")
Spell.Warlock.Demonology.Fear:SetGeneric(WARLOCK_DEMONOLOGY_SPECID, "Generic4")

Spell.Warlock.Demonology.AxeToss.MaximumRange = 30
Spell.Warlock.Demonology.Seduction.MaximumRange = 30
Spell.Warlock.Demonology.ShadowBulwark.MaximumRange = 40
Spell.Warlock.Demonology.SingeMagic.MaximumRange = 30
Spell.Warlock.Demonology.SpellLock.MaximumRange = 40

Spell.Warlock.Destruction.Immolate:SetGeneric(WARLOCK_DESTRUCTION_SPECID, "Generic1")
Spell.Warlock.Destruction.Havoc:SetGeneric(WARLOCK_DESTRUCTION_SPECID, "Generic2")
Spell.Warlock.Destruction.MortalCoil:SetGeneric(WARLOCK_DESTRUCTION_SPECID, "Generic3")
Spell.Warlock.Destruction.Fear:SetGeneric(WARLOCK_DESTRUCTION_SPECID, "Generic4")

Spell.Warlock.Destruction.SpellLock.MaximumRange = 40
Spell.Warlock.Destruction.Seduction.MaximumRange = 30
Spell.Warlock.Destruction.ShadowBulwark.MaximumRange = 40
Spell.Warlock.Destruction.SingeMagic.MaximumRange = 30
Spell.Warlock.Destruction.SpellLock.MaximumRange = 40

local Player = HL.Unit.Player
if Player:Class() == "WARLOCK" then
    local maxExecute = 0
    HL:RegisterForEvent(function()
        if MainAddon.Config.GetClassSetting('auto_cancel_br') then
            local function CancelingBR()
                if maxExecute > 5 then
                    maxExecute = 0
                    return
                end
                if Player:BuffUp(111400) then
                    MainAddon.SetTopTexture(6, 'Cancel Buff: Burning Rush')
                    C_Timer.After(0.5, function()
                        maxExecute = maxExecute + 1
                        CancelingBR()
                    end)
                end
            end
            CancelingBR()
        end
    end,"PLAYER_STOPPED_MOVING")
end