function A_270(...)
    --===========================================--
    -- Module Imports and Initialization
    --===========================================--
    ---@class MainAddon
    local MainAddon = MainAddon
    ---@class MainAddon
    local M = MainAddon
    ---@class HealingEngine
    local HealingEngine = MainAddon.HealingEngine
    -- HeroLib
    local HL = HeroLibEx
    ---@class Unit
    local Unit = HL.Unit
    ---@class Unit
    local Player = Unit.Player
    ---@class Unit
    local Target = Unit.Target
    ---@class Unit
    local MouseOver = Unit.MouseOver
    ---@class Unit
    local Focus = Unit.Focus
    ---@class Spell
    local Spell = HL.Spell
    ---@class Item
    local Item = HL.Item
    -- HeroRotation
    local Cast = M.Cast
    local CastCycleAlly = M.CastCycleAlly
    local CastTargetIfAlly = M.CastTargetIfAlly
    local CastAlly = M.CastAlly
    local incdmgmagic = M.incdmgmagic
    -- Lua
    local C_Timer = _G['C_Timer']
    local wipe = _G['wipe']
    local GetTime = _G['GetTime']
    local GetNumGroupMembers = _G['GetNumGroupMembers']
    local tonumber = tonumber
    local num = M.num
    -- Define S/I for spell and item arrays
    local S = Spell.Monk.Mistweaver
    local I = Item.Monk.Mistweaver

    ---@class Monk
	local Monk = M.Monk

    --===========================================--
    -- Toggle and Configuration Setup
    --===========================================--
    -- Create table to exclude above trinkets from On Use function
    local OnUseExcludes = {
        I.IridaltheEarthsMaster:ID()
    }

    -- Define toggle buttons for the rotation
    MainAddon.Toggle.Special["ForceDPS"] = {
        Icon = MainAddon.GetTexture(S.RisingSunKick),
        Name = "Force DPS",
        Description = "This toggle will force DPS.",
		Spec = 270
    }

    MainAddon.Toggle.Special["YulonRamp"] = {
        Icon = MainAddon.GetTexture(S.InvokeYulonTheJadeSerpent),
        Name = "Yu'lon Ramp",
        Description = "Invoke Yu'lon, the Jade Serpent Ramp up toggle.",
		Spec = 270
    }

    -- Setup configuration panel
    local GetSetting = MainAddon.Config.GetClassSetting
    local Config_Key = MainAddon.GetClassVariableName()
    local Config_Color = '00FF98'
    local Config_Table = {
        key = Config_Key,
        title = 'Monk - Mistweaver',
        subtitle = '?? ' .. MainAddon.Version,
        width = 600,
        height = 700,
        profiles = true,
        config = {
            { type = 'header', text = '', size = 24, align = 'Center', color = Config_Color },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
            { type = "header", text = "\"Experience Tranquility.\"", size = 16, align = "center", color = Config_Color },
            { type = "header", text = "?? x yuno", size = 12, align = "center", color = '4C4C4C' },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
            { type = 'header', text = 'Single Target Healing', color = Config_Color },
            { type = 'spacer' },
            
            -- Enveloping Mist
            { type = 'header', text = 'Enveloping Mist', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'EMHP', icon = S.EnvelopingMist:ID(), min = 1, max = 100, default = 60},
            { type = 'spacer' },
            
            -- Emperor's Favor
            { type = 'header', text = "Emperor's Favor", size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'EFAVOR', icon = S.EmperorsFavor:ID(), min = 1, max = 100, default = 65 },
            { type = 'spacer' },
            
            -- Soothing Mist
            { type = 'header', text = 'Soothing Mist', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Start at Health (%)', key = 'SooMStart', icon = S.SoothingMist:ID(), min = 1, max = 100, default = 23 },
            { type = 'spinner', text = 'Cancel at Health (%)', key = 'SooMCancel', icon = S.SoothingMist:ID(), min = 1, max = 115, default = 60},
            { type = 'spacer' },
            
            -- Vivify
            { type = 'header', text = 'Vivify', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Health Threshold (%)', key = 'VHP', icon = S.Vivify:ID(), min = 1, max = 100, default = 40 },
            { type = 'spinner', text = 'Vivacious Viv Threshold (%)', key = 'FreeVHP', icon = S.Vivify:ID(), min = 1, max = 100, default = 85 },
            { type = 'spacer' },
            
            -- Group Healing
            { type = 'header', text = 'Group Healing', color = Config_Color },
            { type = 'spacer' },
            
            -- Celestial Conduit
            { type = 'header', text = 'Celestial Conduit', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'CCHP_underX', icon = S.CelestialConduit:ID(), min = 1, max = 100, default = 60 },
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'CCHP_underX_val', icon = S.CelestialConduit:ID(), min = 1, max = 100, default = 75 },
            { type = 'spinner', text = 'Avg Group Health (%)', key = 'CCHP', icon = S.CelestialConduit:ID(), min = 1, max = 100, default = 75},
            { type = 'spacer' },
            
            -- Chi-Ji
            { type = 'header', text = 'Chi-Ji', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'CHIJINHP_underX', icon = S.InvokeChiJiTheRedCrane:ID(), min = 1, max = 100, default = 30},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'CHIJINHP_underX_val', icon = S.InvokeChiJiTheRedCrane:ID(), min = 1, max = 100, default = 80},
            { type = 'spinner', text = 'Avg Group Health (%)', key = 'CHIJINHP', icon = S.InvokeChiJiTheRedCrane:ID(), min = 1, max = 100, default = 86},
            { type = 'spacer' },
            
            -- Jade Empowerment
            { type = 'header', text = 'Jade Empowerment', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'JEP_underX', icon = S.CracklingJadeLightning:ID(), min = 1, max = 100, default = 60 },
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'JEP_underX_val', icon = S.CracklingJadeLightning:ID(), min = 1, max = 100, default = 80 },
            { type = 'spinner', text = 'Avg Group Health (%)', key = 'JEP', icon = S.CracklingJadeLightning:ID(), min = 1, max = 100, default = 80},
            { type = 'spacer' },
            
            -- Revival / Restoral
            { type = 'header', text = 'Revival / Restoral', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'RVHP_underX', icon = S.Revival:ID(), min = 1, max = 100, default = 45},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'RVHP_underX_val', icon = S.Revival:ID(), min = 1, max = 100, default = 60},
            { type = 'spinner', text = 'Avg Group Health (%)', key = 'RVHP', icon = S.Revival:ID(), min = 1, max = 100, default = 60},
            { type = 'spacer' },
            
            -- Sheilun's Gift
            { type = 'header', text = "Sheilun's Gift", size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'SGHP_underX', icon = S.SheilunsGift:ID(), min = 1, max = 100, default = 45 },
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'SGHP_underX_val', icon = S.SheilunsGift:ID(), min = 1, max = 100, default = 75 },
            { type = 'spinner', text = 'Avg Group Health (%)', key = 'SGHP', icon = S.SheilunsGift:ID(), min = 1, max = 100, default = 75},
            { type = 'spacer' },
            
            -- Yu'lon
            { type = 'header', text = "Yu'lon", size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Targets Required (%)', key = 'YULONHP_underX', icon = S.InvokeYulonTheJadeSerpent:ID(), min = 1, max = 100, default = 30},
            { type = 'spinner', text = 'Target HP Threshold (%)', key = 'YULONHP_underX_val', icon = S.InvokeYulonTheJadeSerpent:ID(), min = 1, max = 100, default = 80},
            { type = 'spinner', text = 'Avg Group Health (%)', key = 'YULONHP', icon = S.InvokeYulonTheJadeSerpent:ID(), min = 1, max = 100, default = 86},
            { type = 'spacer' },

            -- External Cooldowns
            { type = 'header', text = 'External Cooldowns', color = Config_Color },
            { type = 'spacer' },
            
            -- Life Cocoon
            { type = 'header', text = 'Life Cocoon', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Tank Health Threshold (%)', key = 'LCTanksHP', icon = S.LifeCocoon:ID(), min = 1, max = 100, default = 35},
            { type = 'checkspin', text = 'Party Member Threshold (%)', key = 'LCMembersHP', icon = S.LifeCocoon:ID(), min = 1, max = 100, default_spin = 25, default_check = true },
            { type = 'spacer' },
            
            { type = 'header', text = 'Personal Defensives', color = Config_Color },
            { type = 'spacer' },
            
            -- Fortifying Brew
            { type = 'header', text = 'Fortifying Brew', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkspin', text = 'Health Threshold (%)', key = 'FBHP', icon = S.FortifyingBrew:ID(), min = 1, max = 100, default_spin = 30, default_check = true },
            { type = 'spacer' },
            
            -- Diffuse Magic
            { type = 'header', text = 'Diffuse Magic', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkspin', text = 'Health Threshold (%)', key = 'DMHP', icon = S.DiffuseMagic:ID(), min = 1, max = 100, default_spin = 55, default_check = false },
            { type = 'spacer' },
            
            -- Options and Extras
            { type = 'header', text = 'Options and Extras', color = Config_Color },
            { type = 'spacer' },
            
            -- Jade Empowerment Settings
            { type = 'header', text = 'Jade Empowerment Settings', size = 14, align = 'Left', color = Config_Color },
            { type = "dropdown", text = "Usage Mode", key = "jep_setting", multiselect = false, icon = S.CracklingJadeLightning:ID(), list = { { text = "Auto", key = 'auto' }, { text = "Offensive only", key = 'offensive' }, { text = "Defensive only", key = 'defensive' } }, default = 'auto' },
            { type = 'checkbox', text = 'Toast Message before use', icon = S.CracklingJadeLightning:ID(), key = 'toast_CJL', default = true },
            { type = 'spacer' },
            
            -- Mana Tea Settings
            { type = 'header', text = 'Mana Tea Settings', size = 14, align = 'Left', color = Config_Color },
            { type = 'dropdown', text = 'Usage Mode', key = 'mtea_auto', icon = S.ManaTea:ID(), list = { { text = 'With settings below', key = 'settings' }, { text = 'Auto', key = 'auto' }, }, default = 'auto' },
            { type = 'spinner', text = 'Use Below Mana (%)', key = 'MTea', icon = S.ManaTea:ID(), min = 1, max = 100, default = 65},
            { type = 'spinner', text = 'Min Required Stacks', key = 'MTeaStack', icon = S.ManaTea:ID(), min = 1, max = 20, default = 10},
            { type = 'spinner', text = 'Min Group Health (%)', key = 'MTeaHPfriendly', icon = S.ManaTea:ID(), min = 1, max = 100, default = 70},
            { type = 'spacer' },
            
            -- Thunder Focus Tea Settings
            { type = 'header', text = 'Thunder Focus Tea Settings', size = 14, align = 'Left', color = Config_Color },
            { type = 'dropdown', text = 'Usage Mode', key = 'tft', icon = S.ThunderFocusTea:ID(), list = { { text = 'Auto', key = 'auto' }, { text = 'force Enveloping Mist', key = 'em' }, { text = 'force Vivify', key = 'vivify' }, { text = 'force Rising Sun Kick', key = 'rsk' }, }, default = 'auto' },
            { type = 'spacer' },
            
            -- Ability-Specific Settings
            { type = 'header', text = 'Ability Settings', size = 14, align = 'Left', color = Config_Color },
            { type = 'checkbox', text = "M+ Logics", icon = S.DiffuseMagic:ID(), default = true, key = 'diffuse_auto' },
            { type = 'spinner', text = "EM Max Casts During Yu'lon", key = 'EMCastAmountValue', icon = S.EnvelopingMist:ID(), min = 1, max = 20, default = 5 },
            { type = 'checkbox', text = "EM Keep Up On Tank", icon = S.EnvelopingMist:ID(), default = false, key = 'emtankuptime' },
            { type = 'checkbox', text = "Yu'lon Melee Range Only", icon = S.InvokeYulonTheJadeSerpent:ID(), default = false, key = 'YULONmelee' },
            { type = 'spinner', text = "Sheilun's Minimum Charges", key = 'SGCharges', icon = S.SheilunsGift:ID(), min = 1, max = 10, default = 7 },
            { type = 'spacer' },
            
            -- Targeting Settings
            { type = 'header', text = 'Targeting Settings', size = 14, align = 'Left', color = Config_Color },
            { type = 'dropdown', text = 'Soothing Mist Targets', key = 'sm_targets', icon = S.SoothingMist:ID(), list = { { text = 'Tanks', key = 'tanks' }, { text = 'Everyone', key = 'everyone' }, }, default = 'tanks' },
            { type = 'dropdown', text = 'Touch of Death Targets', key = 'tod', multiselect = true, icon = S.TouchOfDeath:ID(), list = { { text = 'Boss', key = 1 }, { text = 'Elites', key = 2 }, { text = 'Normal mobs', key = 3 }, }, default = { 1, 2, 3 }, },
            { type = 'spacer' },
            
            -- Movement Settings
            { type = 'header', text = 'Movement Settings', size = 14, align = 'Left', color = Config_Color },
            { type = 'spinner', text = 'Faeline Stomp Stand Time', key = 'fstompMovingValue', icon = S.FaelineStomp:ID(), min = 0, max = 10, default = 1.25 },
            { type = 'spinner', text = 'Jade Statue Stand Time', key = 'JadeStatueMovingValue', icon = S.SummonJadeSerpentStatue:ID(), min = 0, max = 10, default = 1.25 },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
        }
    }
    Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
    Config_Table.config = MainAddon.BuildHealingTrinketUI(Config_Table.config, Config_Color)
    Config_Table.config = MainAddon.BuildCombatManaPotionUI(Config_Table.config, "Mistweaver", Config_Color)
    MainAddon.SetConfig(270, Config_Table)

    --===========================================--
    -- Variables and State
    --===========================================--
    local ShouldReturn
    ---@type Unit|nil
    local Tanks, Healers, Members, Damagers, Melees, TargetIfAlly
    ---@type Unit|nil
    local SoothingT = nil
    local Enemies8y = {}
    local EnemiesCount8y = 0
    local Enemies10ySplashCount = 0
    local fstompcheckRange = 15
    local TempBlackListEM = {}
    local Settings = {}
    local Var = {}
    Var['SoothingMistUnit'] = nil
    Var['IsStandingStillFor'] = 999
    Var['ExpelHarmHealing'] = 10
    Settings['JadeStatueMovingValue'] = GetSetting('JadeStatueMovingValue')
    Settings['fstompMovingValue'] = GetSetting('fstompMovingValue')
    Var['SoothingMembers'] = {}
    Var['EMCastAmount'] = 0
    Var['EMSucceeded'] = 1
    Var['CombatMonitor_TimeStamp'] = GetTime()
    Var['CombatMonitor_State'] = false

    --===========================================--
    -- Utility Functions
    --===========================================--
    
    --- Monitors if any tanks are in combat (used for out-of-combat healing decisions)
    -- @return boolean True if any tank is in combat
    local function combatMonitor()
        -- Return cached result if within 1 second
        if GetTime() - Var['CombatMonitor_TimeStamp'] < 1 then
            return Var['CombatMonitor_State']
        end
        
        -- Check if any tank is in combat
        if Tanks then
            ---@param TankUnit Unit
            for _, TankUnit in pairs(Tanks) do
                if TankUnit:AffectingCombat() then
                    Var['CombatMonitor_TimeStamp'] = GetTime()
                    Var['CombatMonitor_State'] = true
                    return true
                end
            end
        end

        -- No tanks in combat
        Var['CombatMonitor_TimeStamp'] = GetTime()
        Var['CombatMonitor_State'] = false
        return false
    end

    --- Updates all variables and settings used in the rotation
    -- Fetches unit groups, player state, buffs, and converts UI settings to usable values
    local function UpdateVars()
        -- Unit Groups
        Tanks, Healers, Members, Damagers, Melees, _, _, _, TargetIfAlly = HealingEngine:Fetch()
        Var['SoothingMembers'] = GetSetting('sm_targets', 'tanks') == 'tanks' and Tanks or Members
        
        -- Player State
        Var['AoEON'] = M.AoEON()
        Var['TargetIsValid'] = M.TargetIsValid()
        Var['IsInCombat'] = Player:AffectingCombat()
        Var['ManaPct'] = Player:ManaPercentage()
        Var['ManaAbsolute'] = Player:Mana()
        Var['LowestHP'] = HealingEngine:LowestHP(true, 40)
        Var['AverageHPInRange'] = HealingEngine:MedianHP()
        Var['IsStandingStillFor'] = Player:IsStandingStillFor()
        Var['PlayerHPFiltered'] = Player:RealHealthPercentage()
        
        -- Buffs and Totems
        Var['AncientTeachingsBuff'] = Player:BuffUp(S.AncientTeachingsBuff)
        Var['AwakenedFaelineBuff'] = Player:BuffUp(S.AwakenedFaelineBuff)
        Var['YulonActive'] = Player:TotemIsActive(574571)
        Var['ChiJiActive'] = Player:TotemIsActive(877514)
        Var['JadeStatueActive'] = Player:TotemIsActive(620831)
        Var['TheRedCraneBuff'] = Player:BuffStack(S.TheRedCraneBuff)
        
        -- Count party members with Renewing Mist
        Var['RenewingMistCount'] = 0
        if Members then
            for _, Unit in ipairs(Members) do
                if Unit:BuffUp(S.RenewingMistBuff) then
                    Var['RenewingMistCount'] = Var['RenewingMistCount'] + 1
                end
            end
        end
        
        -- Talents and Group Size
        Var['UpwellingAvailable'] = S.Upwelling:IsAvailable()
        Var['PartySize'] = GetNumGroupMembers()
        Var['MembersAmount'] = Var['PartySize'] < 5 and 5 or Var['PartySize']
        Var['RapidDiffusion'] = S.RapidDiffusion:IsAvailable()
        Var['PeerIntoPeace'] = S.PeerIntoPeace:IsAvailable()

        -- Healing Settings - Simple Slider
        Settings['SooMStart'] = GetSetting('SooMStart')
        Settings['EMHP'] = GetSetting('EMHP')
        Settings['LCTanksHP'] = GetSetting('LCTanksHP')
        Settings['LCMembersHP'] = GetSetting('LCMembersHP_spin')
        Settings['LCMembersHPCheck'] = GetSetting('LCMembersHP_check')
        Settings['RVHP'] = GetSetting('RVHP')
        Settings['YULONHP'] = GetSetting('YULONHP')
        Settings['CHIJINHP'] = GetSetting('CHIJINHP')
        Settings['SGHP'] = GetSetting('SGHP')
        Settings['EFAVOR'] = GetSetting('EFAVOR')
        Settings['VHP'] = GetSetting('VHP')
        Settings['FreeVHP'] = GetSetting('FreeVHP')
        Settings['CCHP'] = GetSetting('CCHP')
        Settings['SooMCancel'] = GetSetting('SooMCancel')

        -- Healing Settings - Under X%
        if Var['MembersAmount'] then
            Settings['RVHP_underX'] = (GetSetting('RVHP_underX') * Var['MembersAmount']) / 100
            Settings['RVHP_underX_val'] = GetSetting('RVHP_underX_val')
            Settings['DCHP_underX'] = 10 * Var['MembersAmount'] / 100
            Settings['DCHP_underX_val'] = 85
            Settings['YULONHP_underX'] = (GetSetting('YULONHP_underX') * Var['MembersAmount']) / 100
            Settings['YULONHP_underX_val'] = GetSetting('YULONHP_underX_val')
            Settings['CHIJINHP_underX'] = (GetSetting('CHIJINHP_underX') * Var['MembersAmount']) / 100
            Settings['CHIJINHP_underX_val'] = GetSetting('CHIJINHP_underX_val')
            Settings['SGHP_underX'] = (GetSetting('SGHP_underX') * Var['MembersAmount']) / 100
            Settings['SGHP_underX_val'] = GetSetting('SGHP_underX_val')
            Settings['CCHP_underX'] = (GetSetting('CCHP_underX') * Var['MembersAmount']) / 100
            Settings['CCHP_underX_val'] = GetSetting('CCHP_underX_val')
            Settings['JEP_underX'] = (GetSetting('JEP_underX') * Var['MembersAmount']) / 100
            Settings['JEP_underX_val'] = GetSetting('JEP_underX_val')            
        end

        -- Defensive Settings
        Settings['DMHP'] = GetSetting('DMHP_spin')
        Settings['DMHPCheck'] = GetSetting('DMHP_check')
        Settings['FBHP'] = GetSetting('FBHP_spin')
        Settings['FBHPCheck'] = GetSetting('FBHP_check')

        -- Misc Settings
        Settings['SGCharges'] = GetSetting('SGCharges')
        Settings['MTea'] = GetSetting('MTea')
        Settings['MTeaStack'] = GetSetting('MTeaStack')
        Settings['MTeaHPfriendly'] = GetSetting('MTeaHPfriendly')
        Settings['fstompMovingValue'] = GetSetting('fstompMovingValue')
        Settings['JadeStatueMovingValue'] = GetSetting('JadeStatueMovingValue')
        Settings['TFT'] = GetSetting('tft')
        Settings['ManaTeaAuto'] = GetSetting('mtea_auto')
        Settings['EMCastAmountValue'] = GetSetting('EMCastAmountValue')
        Settings['JEPSetting'] = GetSetting('jep_setting', 'auto')

        -- Damage Settings
        Settings['TOD'] = GetSetting('tod')

        -- Dynamic Variables
        Var['ExpelHarmHealing'] = (tonumber(MainAddon.GetSpellHealing(S.ExpelHarm:ID())) / Player:MaxHealth()) * 100
        if not Var['IsInCombat'] then
            Var['IsInCombat'] = combatMonitor()
        end
    end

    --- Checks if target is within Faeline Stomp range
    -- @param TargetUnit Unit The unit to check
    -- @return boolean True if the target is in range
    ---@param TargetUnit Unit
    local function FaelineStompInRange(TargetUnit)
        return TargetUnit:IsInRange(fstompcheckRange)
    end

    --- Evaluates if a unit needs Mystic Touch debuff
    -- @param TargetUnit Unit The unit to check
    -- @return boolean True if the unit needs Mystic Touch
    ---@param TargetUnit Unit
    local function EvaluateCycleMysticTouch(TargetUnit)
        return TargetUnit:DebuffDown(S.MysticTouchDebuff, true) and not TargetUnit:IsDummy()
    end

    --- Evaluates if Touch of Death should be used on a target
    -- @param TargetedUnit Unit The target to evaluate
    -- @return boolean, string True if ToD should be used and a reason message
    ---@param TargetedUnit Unit
    local function EvaluateToD(TargetedUnit)
        local TargetHealth = TargetedUnit:Health()
        local PlayerHealth = Player:Health()
        local TargetHealthPercentage = TargetedUnit:HealthPercentage()

        -- Boss units
        if TargetedUnit:IsInBossList() or TargetedUnit:Classification() == "worldboss" then
            if Settings['TOD'][1] then
                if TargetHealth <= PlayerHealth or S.ImprovedTouchOfDeath:IsAvailable() and TargetHealthPercentage <= 15 then
                    return true, "Toggle for boss unit is activated."
                else
                    return false, "Target is above 15% HP."
                end
            end
            return false, "Toggle for boss unit isn't activated."
        end
        
        -- Elite units
        if TargetedUnit:Classification() == "rareelite" or TargetedUnit:Classification() == "elite" then
            if Settings['TOD'][2] then
                if TargetHealth <= PlayerHealth or S.ImprovedTouchOfDeath:IsAvailable() and TargetHealthPercentage <= 15 then
                    return true, "Toggle for elite unit is activated."
                else
                    return false, "Target is above 15% HP."
                end
            end
            return false, "Toggle for elite unit isn't activated."
        end
        
        -- Normal units
        if TargetedUnit:Classification() == "normal" or TargetedUnit:Classification() == "rare" then
            if Settings['TOD'][3] then
                if TargetHealth <= PlayerHealth or S.ImprovedTouchOfDeath:IsAvailable() and TargetHealthPercentage <= 15 then
                    return true, "Toggle for normal unit is activated."
                else
                    return false, "Target is above 15% HP."
                end
            end
            return false, "Toggle for normal unit isn't activated."
        end
        
        return false, "Invalid Target."
    end

    --===========================================--
    -- Target Evaluation Functions
    --===========================================--
    
    -- TARGETIF functions
    local function EvaluateTrue()
        return true
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateTargetIfHealth(TargetedUnit)
        return TargetedUnit:HealthPercentage()
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateTargetIfHPEMYULON(TargetedUnit)
        return TargetedUnit:HealthPercentage() + (num(TargetedUnit:BuffUp(S.EnvelopingMist) or TempBlackListEM[TargetedUnit:UnfilterName()]) * 5)
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateTargetIfHarmony(TargetedUnit)
        return TargetedUnit:BuffRemains(S.ChiHarmony)
    end
    
    -- CASTCYCLE functions
    ---@param TargetedUnit Unit
    local function EvaluateViviYulon(TargetedUnit)
        return TargetedUnit:BuffUp(S.ChiHarmony)
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateViviYulon2(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= 80
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateEMEmergency(TargetedUnit)
        return TargetedUnit:BuffDown(S.EnvelopingMist)
        and not TempBlackListEM[TargetedUnit:UnfilterName()]
        and TargetedUnit:HealthPercentage() <= 30
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateEMHarmony(TargetedUnit)
        return TargetedUnit:BuffDown(S.EnvelopingMist)
        and not TempBlackListEM[TargetedUnit:UnfilterName()]
        and TargetedUnit:BuffUp(S.ChiHarmony)
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateRenewingMist(TargetedUnit)
        return TargetedUnit:BuffDown(S.RenewingMistBuff)
    end
    
    local function EvaluateSheilunsGift()
        return true
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateEmperorsFavor(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= Settings['EFAVOR']
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateVivify(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= Settings['VHP']
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateFreeVivify(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= Settings['FreeVHP']
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateVivifyHarmony(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= Settings['VHP'] and TargetedUnit:BuffUp(S.ChiHarmony)
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateFreeVivifyHarmony(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= Settings['FreeVHP'] and TargetedUnit:BuffUp(S.ChiHarmony)
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateSoothingVivify(TargetedUnit)
        return TargetedUnit:HealthPercentage() <= Settings['SooMStart']
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateTargetIfEMCondition(TargetedUnit)
        return TargetedUnit:BuffDown(S.EnvelopingMist)
        and not TempBlackListEM[TargetedUnit:UnfilterName()]
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateEnvelopingMist(TargetedUnit)
        return TargetedUnit:BuffDown(S.EnvelopingMist)
        and not TempBlackListEM[TargetedUnit:UnfilterName()]
        and TargetedUnit:HealthPercentage() <= Settings['EMHP']
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateEnvelopingMistHarmony(TargetedUnit)
        return TargetedUnit:BuffDown(S.EnvelopingMist)
        and not TempBlackListEM[TargetedUnit:UnfilterName()]
        and TargetedUnit:BuffUp(S.ChiHarmony)
        and TargetedUnit:HealthPercentage() <= Settings['EMHP']
    end

    ---@param TargetedUnit Unit
    local function EvaluateEnvelopingMistTankUptime(TargetedUnit)
        return TargetedUnit:BuffDown(S.EnvelopingMist)
        and not TempBlackListEM[TargetedUnit:UnfilterName()]
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateEnvelopingMistSM(TargetedUnit)
        return TargetedUnit:BuffDown(S.EnvelopingMist)
        and not TempBlackListEM[TargetedUnit:UnfilterName()]
        and TargetedUnit:HealthPercentage() <= Settings['SooMStart']
        and TargetedUnit:HealthPercentage() > (Settings['SooMStart'] * 0.65)
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateEnvelopingMistTfTorBlackOx(TargetedUnit)
        return TargetedUnit:BuffDown(S.EnvelopingMist)
        and not TempBlackListEM[TargetedUnit:UnfilterName()]
        and TargetedUnit:HealthPercentage() <= 89
    end

    ---@param TargetedUnit Unit
    local function EvaluateEnvelopingMistTfTAuto(TargetedUnit)
        return TargetedUnit:BuffDown(S.EnvelopingMist)
        and not TempBlackListEM[TargetedUnit:UnfilterName()]
        and TargetedUnit:HealthPercentage() <= 50
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateLifeCocoonTanks(TargetedUnit)
        return TargetedUnit:RealHealthPercentage() <= Settings['LCTanksHP']
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateLifeCocoonMembers(TargetedUnit)
        return TargetedUnit:RealHealthPercentage() <= Settings['LCMembersHP']
    end
    
    ---@param TargetedUnit Unit
    local function EvaluateVivifyIncDMG(TargetedUnit) 
        return TargetedUnit:HealthPercentage() <= 50 
    end

    --===========================================--
    -- Core Rotation Functions
    --===========================================--
    
    --- Handles resurrection of dead party members
    -- @return string|nil Return message if action was taken
    local function Utilities()
        if Target:IsDeadOrGhost() and Target:IsInParty() and Target:IsAPlayer() and not Target:IsEnemy() then
            if S.Resuscitate:IsReady(Player) then
                if Cast(S.Resuscitate) then
                    return "Resuscitating " .. Target:Name()
                end
            end
        end
        return nil
    end

    --- Handles usage of Jade Empowerment buff with Crackling Jade Lightning
    -- Based on talent and buff stack conditions
    -- @return string|nil Return message if CJL was cast
    local function JadeEmpowerment()
        -- Exit if we don't have the talent or no stacks of the buff
        if not S.JadeEmpowerment:IsAvailable() or Player:BuffStack(S.JadeEmpowermentBuff) == 0 then
            return nil
        end

        -- Local variables for cleaner logic
        local hasAoH = S.AspectOfHarmony:IsAvailable()
        local healingNeeded = HealingEngine:MembersUnderPercentage(Settings['JEP_underX_val']) >= Settings['JEP_underX']
        local canCastCJL = S.CracklingJadeLightning:IsReady() and Var['TargetIsValid'] 
                        and Var['IsStandingStillFor'] >= 1 
                        and not Player:BuffUp(S.AspectOfHarmonyBuffSpending) and not Var['ChiJiActive']
        local JEStacks = Player:BuffStack(S.JadeEmpowermentBuff)
        local mode = Settings['JEPSetting']
        
        -- Track last toast time to prevent spam
        if not Var['LastCJLToastTime'] then
            Var['LastCJLToastTime'] = 0
        end
        
        -- Show toast message for CJL if enabled and not on cooldown
        local function showToastIfEnabled(message)
            if GetSetting('toast_CJL', true) and (GetTime() - Var['LastCJLToastTime'] > 10) then
                MainAddon.UI:ShowToast("Emperor's Capacitor", "CJL coming up!", MainAddon.GetTexture(S.CracklingJadeLightning), 5)
                Var['LastCJLToastTime'] = GetTime()
            end
        end

        -- Auto mode: intelligent decision making
        if mode == 'auto' then
            if hasAoH then
                -- With Master of Harmony: need at least 1 stack
                if JEStacks >= 1 then
                    if healingNeeded then
                        return nil -- Save for healing
                    elseif canCastCJL then
                        showToastIfEnabled("Using CJL with Jade Empowerment!")
                        if Cast(S.CracklingJadeLightning) then
                            return "Using Crackling Jade Lightning with Jade Empowerment (Auto, MoH)"
                        end
                    end
                end
            else
                -- Without Master of Harmony
                if healingNeeded then
                    return nil -- Save for healing
                elseif canCastCJL then
                    showToastIfEnabled("Using CJL with Jade Empowerment!")
                    if Cast(S.CracklingJadeLightning) then
                        return "Using Crackling Jade Lightning with Jade Empowerment (Auto, Conduit)"
                    end
                end
            end
        -- Offensive only: cast CJL regardless of healing needs
        elseif mode == 'offensive' then
            if hasAoH then
                if JEStacks >= 1 and canCastCJL then
                    showToastIfEnabled("Using CJL with Jade Empowerment! (Offensive)")
                    if Cast(S.CracklingJadeLightning) then
                        return "Using Crackling Jade Lightning with Jade Empowerment (Offensive Only)"
                    end
                end
            elseif canCastCJL then
                showToastIfEnabled("Using CJL with Jade Empowerment! (Offensive)")
                if Cast(S.CracklingJadeLightning) then
                    return "Using Crackling Jade Lightning with Jade Empowerment (Offensive Only)"
                end
            end
        -- Defensive only: only cast if healing is needed
        elseif mode == 'defensive' then
            if healingNeeded and canCastCJL then
                showToastIfEnabled("Using CJL with Jade Empowerment! (Defensive)")
                if Cast(S.CracklingJadeLightning) then
                    return "Using Crackling Jade Lightning with Jade Empowerment (Defensive Only)"
                end
            end
        end
        
        return nil
    end

    --- Automatically uses Diffuse Magic when player has dangerous magic debuffs
    -- @return string|nil Return message if Diffuse Magic was cast
    local function AutoDiffuseMagic()
        -- Exit if setting is disabled or spell not ready
        if not GetSetting('diffuse_auto', true) or not S.DiffuseMagic:IsReady(Player) then
            return nil
        end
        
        -- Table of dangerous magic debuff IDs that should trigger Diffuse Magic
        local dangerousDebuffs = {
            -- Raid
            -- [240559] = true, -- None so far
            
            -- Dungeon
            [441397] = true, -- Bee-Venom
            [439325] = true, -- Burning Fermentation
            [294961] = true, -- Blazing Chomp
            [280604] = true, -- Iced Spritzer
            [469799] = true, -- Overcharge
            [1214323] = true, -- Lightning Torrent
            [330810] = true, -- Bind Soul
        }
        
        -- Check if player has any of the dangerous debuffs
        for debuffID in pairs(dangerousDebuffs) do
            if Player:DebuffUp(Spell(debuffID)) then
                if Cast(S.DiffuseMagic) then
                    return "Auto Diffuse Magic for " .. MainAddon.GetSpellInfo(debuffID) .. " debuff"
                end
            end
        end
        
        return nil
    end

    --- Manages defensive cooldown usage
    -- Uses Fortifying Brew and Diffuse Magic based on player health and settings
    -- @return string|nil Return message if a defensive was used
    local function Defensives()
        -- Exit early if any defensive is already active
        local defensivesDown = Player:BuffDown(S.FortifyingBrew) and Player:BuffDown(S.DiffuseMagic)
        if not defensivesDown then
            return nil
        end
    
        -- Fortifying Brew based on health percentage
        if Settings['FBHPCheck'] then
            if S.FortifyingBrew:IsReady(Player) and Var['PlayerHPFiltered'] <= Settings['FBHP'] then
                if Cast(S.FortifyingBrew) then
                    return "Using Fortifying Brew at " .. Var['PlayerHPFiltered'] .. "% health"
                end
            end
        end

        -- Auto Diffuse Magic for debuffs
        ShouldReturn = AutoDiffuseMagic()
        if ShouldReturn then
            return ShouldReturn
        end
    
        -- Diffuse Magic based on health and incoming magic damage
        if Settings['DMHPCheck'] then
            if S.DiffuseMagic:IsReady(Player) 
               and Var['PlayerHPFiltered'] <= Settings['DMHP'] 
               and incdmgmagic(Player) >= (Player:MaxHealth() * 0.10) then
                if Cast(S.DiffuseMagic) then
                    return "Using Diffuse Magic at " .. Var['PlayerHPFiltered'] .. "% health"
                end
            end
        end
        
        return nil
    end    

    --- Handles usage of on-use items and trinkets
    -- @return string|nil Return message if an item was used
    local function Items()
        -- Example for casting an item:
        -- if I.SomeItem:IsEquippedAndReady() and Target:HealthPercentage() < 35 then
        --     if Cast(I.SomeItem) then
        --         return "Using SomeItem at " .. Target:HealthPercentage() .. "% health"
        --     end
        -- end
    
        -- Example for setting the top texture:
        -- if I.AnotherItem:IsEquippedAndReady() then
        --     if MainAddon.SetTopTexture(1, "Example Weapon On-Use") then
        --         return "AnotherItem ready"
        --     end
        -- end
        
        return nil
    end        

    --- Handles special healing (focus/mouseover functionality)
    -- @return string|nil Return message if special healing was performed
    local function HealingSpecial()
        local shouldHeal, isReadyToBeHealed, type = HealingEngine.ShouldHealTargetOrMouseover()
        
        if shouldHeal and isReadyToBeHealed then
            if Focus:IsInRange(40) then
                -- 1. Emperor's Favor with Sheilun's Gift
                if S.SheilunsGift:IsReady(Focus) and S.EmperorsFavor:IsAvailable() and S.SheilunsGift:Count() >= Settings['SGCharges'] then
                    if CastAlly(S.RenewingMist, Focus) then
                        return "Special Focus Healing: Emperor's Favor with Renewing Mist"
                    end
                end
    
                -- 2. Renewing Mist if buff is missing
                if S.RenewingMist:IsReady(Focus) and Focus:BuffDown(S.RenewingMistBuff) then
                    if CastAlly(S.RenewingMist, Focus) then
                        return "Special Focus Healing: Renewing Mist"
                    end
                end
    
                -- 3. Spells during Soothing Mist channel
                if Player:IsChanneling(S.SoothingMist) then
                    -- Enveloping Mist during Soothing Mist
                    if S.EnvelopingMist:IsReady(Focus) and Focus:BuffDown(S.EnvelopingMist) and not TempBlackListEM[Focus:UnfilterName()] then
                        if CastAlly(S.EnvelopingMist, Focus, nil, nil, nil, true) then
                            return "Special Focus Healing: Enveloping Mist during SoothingMist"
                        end
                    end
                    -- Vivify during Soothing Mist
                    if S.Vivify:IsReady(Focus) then
                        if CastAlly(S.Vivify, Focus, nil, nil, nil, true) then
                            return "Special Focus Healing: Vivify during SoothingMist"
                        end
                    end
                end
    
                -- 4. Start Soothing Mist if not moving
                if S.SoothingMist:IsReady(Focus) and not Player:IsMoving() then
                    if CastAlly(S.SoothingMist, Focus) then
                        return "Special Focus Healing: Starting Soothing Mist"
                    end
                end
    
                -- 5. Hard cast Vivify as fallback
                if S.Vivify:IsReady(Focus) then
                    if CastAlly(S.Vivify, Focus) then
                        return "Special Focus Healing: Hard cast Vivify"
                    end
                end
            end
        elseif not isReadyToBeHealed then
            -- Update UI if target/mouseover needs to be set
            if type == "MouseOver" then
                MainAddon.SetTopColor(1, "Focus Mouseover")
            elseif type == "Target" then
                MainAddon.SetTopColor(1, "Focus Target")
            end
        end
        
        return nil
    end    

    --- Handles healing during predicted damage incoming events
    -- @return string|nil Return message if action was taken for incoming damage
    local function DamageIncoming()
        -- 1. Faeline Stomp if buff not active
        if S.FaelineStomp:IsReady() and not Var['AwakenedFaelineBuff'] then
            if Cast(S.FaelineStomp) then
                return "[DmgInc] Using Faeline Stomp to prepare for incoming damage"
            end
        end
    
        -- 2. Spend excess Renewing Mist charges
        if S.RenewingMist:IsReady(Player) and S.RenewingMist:ChargesFractional() >= 1.5 then
            if CastCycleAlly(S.RenewingMist, Members, EvaluateRenewingMist) then
                return "[DmgInc] Spreading Renewing Mist before damage"
            end
        end
    
        -- Different strategies based on available legendaries
        if not S.InvokeChiJiTheRedCrane:IsAvailable() then
            -- Path without Chi-Ji available
            
            -- 3A. Use Celestial Conduit for party-wide healing
            if S.CelestialConduit:IsReady(Player) then
                -- Check for severe party damage situations
                local membersUnder60 = HealingEngine:MembersUnderPercentage(60)
                local membersUnder38 = HealingEngine:MembersUnderPercentage(38)
                if (membersUnder60 / Var['MembersAmount'] > 0.7) or (membersUnder38 / Var['MembersAmount'] > 0.4) then
                    if Cast(S.CelestialConduit) then
                        return "[DmgInc] Using Celestial Conduit for party healing"
                    end
                end
            end
    
            -- 4A. Vivify on critical units
            if S.Vivify:IsReady(Player) then
                if CastCycleAlly(S.Vivify, Members, EvaluateVivifyIncDMG) then
                    return "[DmgInc] Casting Vivify on critical health target"
                end
            end
        else
            -- Path with Chi-Ji available
            
            -- 3B. Build Teachings of the Monastery stacks
            if S.TigerPalm:IsReady() and Player:BuffStack(S.TeachingsOfTheMonasteryBuff) <= 3 then
                if Cast(S.TigerPalm) then
                    return "[DmgInc] Building TeachingsOfTheMonastery stacks"
                end
            end
    
            -- 4B. Invoke Chi-Ji with full stacks
            if S.InvokeChiJiTheRedCrane:IsReady(Player) and Player:BuffStack(S.TeachingsOfTheMonasteryBuff) >= 4 then 
                if Cast(S.InvokeChiJiTheRedCrane) then
                    return "[DmgInc] Invoking Chi-Ji for incoming damage"
                end
            end
    
            -- 5B. ChiJi's DPS rotation for healing
            if Var['ChiJiActive'] then
                if S.RisingSunKick:IsReady() then
                    if Cast(S.RisingSunKick) then
                        return "[DmgInc] Rising Sun Kick during Chi-Ji"
                    end
                elseif S.RushingWindKick:IsReady() then
                    if Cast(S.RushingWindKick) then
                        return "[DmgInc] Rushing Wind Kick during Chi-Ji"
                    end
                end
            end
    
            -- 6B. Blackout Kick to spend TotM
            if S.BlackoutKick:IsReady() then
                if Cast(S.BlackoutKick) then
                    return "[DmgInc] Blackout Kick to spend TeachingsOfTheMonastery"
                end
            end
    
            -- 7B. Enveloping Mist based on Red Crane buff stacks
            if S.EnvelopingMist:IsReady(Player) then
                if Var['TheRedCraneBuff'] >= 2 then
                    if CastCycleAlly(S.EnvelopingMist, Members, EvaluateTargetIfEMCondition) then
                        return "[DmgInc] Enveloping Mist with Red Crane buff"
                    end
                else
                    if CastCycleAlly(S.EnvelopingMist, Members, EvaluateTargetIfEMCondition) then
                        return "[DmgInc] Enveloping Mist on priority target"
                    end
                end
            end
        end
        
        return nil
    end    

    --- Manages major healing cooldowns
    -- @return string|nil Return message if a major healing cooldown was used
    local function HealingCDs()
        -- 1. Life Cocoon priority usage
        if S.LifeCocoon:IsReady(Player) then
            -- Priority on tanks
            if CastCycleAlly(S.LifeCocoon, Tanks, EvaluateLifeCocoonTanks, nil, true, nil, 0.5) then
                return "Using Life Cocoon on low health tank"
            end
            
            -- Check members if setting enabled
            if Settings['LCMembersHPCheck'] then
                if CastCycleAlly(S.LifeCocoon, Members, EvaluateLifeCocoonMembers, nil, true, nil, 0.5) then
                    return "Using Life Cocoon on low health party member"
                end
            end
        end

        -- 2. Invoke Yu'lon when not in ramp phase
        if S.InvokeYulonTheJadeSerpent:IsReady(Player) and not MainAddon.Toggle:GetToggle("YulonRamp") then
            -- Check for multiple members under threshold
            if HealingEngine:MembersUnderPercentage(Settings['YULONHP_underX_val']) >= Settings['YULONHP_underX'] then
                if Cast(S.InvokeYulonTheJadeSerpent) then
                    return "Using Yu'lon - Multiple members under health threshold"
                end
            end
    
            -- Check average party health
            if Var['AverageHPInRange'] <= Settings['YULONHP'] then
                if Cast(S.InvokeYulonTheJadeSerpent) then
                    return "Using Yu'lon - Low average health"
                end
            end
        end
    
        -- 3. Invoke Chi-Ji
        if S.InvokeChiJiTheRedCrane:IsReady(Player) then 
            -- Check for multiple members under threshold
            if HealingEngine:MembersUnderPercentage(Settings['CHIJINHP_underX_val']) >= Settings['CHIJINHP_underX'] then
                if Cast(S.InvokeChiJiTheRedCrane) then
                    return "Using Chi-Ji - Multiple members under health threshold"
                end
            end
    
            -- Check average party health
            if Var['AverageHPInRange'] <= Settings['CHIJINHP'] then
                if Cast(S.InvokeChiJiTheRedCrane) then
                    return "Using Chi-Ji - Low average health"
                end
            end
        end
    
        -- 4. Celestial Conduit
        if S.CelestialConduit:IsReady(Player) then
            -- Check for multiple members under threshold
            if HealingEngine:MembersUnderPercentage(Settings['CCHP_underX_val']) >= Settings['CCHP_underX'] then
                if Cast(S.CelestialConduit) then
                    return "Using Celestial Conduit - Multiple members under health threshold"
                end
            end
    
            -- Check average party health
            if Var['AverageHPInRange'] <= Settings['CCHP'] then
                if Cast(S.CelestialConduit) then
                    return "Using Celestial Conduit - Low average health"
                end
            end
        end
    
        -- 5. Revival usage
        if S.Revival:IsReady(Player) then
            -- Check for multiple members under threshold
            if HealingEngine:MembersUnderPercentage(Settings['RVHP_underX_val']) >= Settings['RVHP_underX'] then
                if Cast(S.Revival) then
                    return "Using Revival - Multiple members under health threshold"
                end
            end
    
            -- Check average party health
            if Var['AverageHPInRange'] <= Settings['RVHP'] then
                if Cast(S.Revival) then
                    return "Using Revival - Low average health"
                end
            end
        end
    
        -- 6. Restoral usage (mirrors Revival logic)
        if S.Restoral:IsReady(Player) then
            -- Check for multiple members under threshold
            if HealingEngine:MembersUnderPercentage(Settings['RVHP_underX_val']) >= Settings['RVHP_underX'] then
                if Cast(S.Restoral) then
                    return "Using Restoral - Multiple members under health threshold"
                end
            end
    
            -- Check average party health
            if Var['AverageHPInRange'] <= Settings['RVHP'] then
                if Cast(S.Restoral) then
                    return "Using Restoral - Low average health"
                end
            end
        end
        
        return nil
    end       

    --- Determines when to use Thunder Focus Tea
    -- @return string|nil Return message if Thunder Focus Tea was used
    local function UseThunderFocusTea()
        if not S.ThunderFocusTea:IsReady(Player) then
            return nil
        end
        
        -- Check for Aspect of Harmony special case
        if (not S.AspectOfHarmony:IsAvailable()) or (Player:BuffUp(S.AspectOfHarmonyBuffStage3)) then
            -- Handle based on Thunder Focus Tea setting
            if Settings['TFT'] == 'auto' then
                -- Auto logic: prioritize based on situation
                
                -- 1. Emergency self-healing with Expel Harm
                if Var['PlayerHPFiltered'] < 40 and S.ExpelHarm:IsReady(Player) then
                    if Cast(S.ThunderFocusTea, true) then
                        return "Using Thunder Focus Tea for Expel Harm (emergency healing)"
                    end
                -- 2. Rising Sun Kick for DPS/healing
                elseif S.RisingSunKick:IsReady() and Var['TargetIsValid'] then
                    if Cast(S.ThunderFocusTea, true) then
                        return "Using Thunder Focus Tea for Rising Sun Kick"
                    end
                elseif S.RushingWindKick:IsReady() and Var['TargetIsValid'] then
                    if Cast(S.ThunderFocusTea, true) then
                        return "Using Thunder Focus Tea for Rushing Wind Kick"
                    end
                end
            elseif Settings['TFT'] == 'em' then
                -- Force Enveloping Mist if any target needs healing
                if Var['LowestHP'] <= 90 then
                    if Cast(S.ThunderFocusTea, true) then
                        return "Using Thunder Focus Tea for Enveloping Mist (forced)"
                    end
                end
            elseif Settings['TFT'] == 'vivify' then
                -- Force Vivify if any target needs healing
                if Var['LowestHP'] <= Settings['VHP'] then
                    if Cast(S.ThunderFocusTea, true) then
                        return "Using Thunder Focus Tea for Vivify (forced)"
                    end
                end
            elseif Settings['TFT'] == 'rsk' then
                -- Force Rising Sun Kick for damage/healing
                if S.RisingSunKick:IsReady() and Var['TargetIsValid'] then
                    if Cast(S.ThunderFocusTea, true) then
                        return "Using Thunder Focus Tea for Rising Sun Kick (forced)"
                    end
                elseif S.RushingWindKick:IsReady() and Var['TargetIsValid'] then
                    if Cast(S.ThunderFocusTea, true) then
                        return "Using Thunder Focus Tea for Rushing Wind Kick (forced)"
                    end
                end
            end
        end
        
        return nil
    end   

    --- Spends the Thunder Focus Tea buff on appropriate spells
    -- @return string|nil Return message if an action was taken
    local function SpendThunderFocusTea()
        -- Auto mode
        if Settings['TFT'] == 'auto' then
            -- Expel Harm priority when low health
            if Var['PlayerHPFiltered'] < 30 and S.ExpelHarm:IsReady(Player) then
                if Cast(S.ExpelHarm) then
                    return "Expel Harm (TFT) - Emergency self-healing"
                end
            -- Enveloping Mist priority for low health targets
            elseif S.EnvelopingMist:IsReady(Player) then
                if CastCycleAlly(S.EnvelopingMist, Members, EvaluateEnvelopingMistTfTAuto) then
                    return "Enveloping Mist (TFT) - Auto priority"
                end
            -- Rising Sun Kick for damage/healing
            elseif S.RisingSunKick:IsReady() and Var['TargetIsValid'] then
                if Cast(S.RisingSunKick) then
                    return "Rising Sun Kick (TFT) - Auto priority"
                end
            elseif S.RushingWindKick:IsReady() and Var['TargetIsValid'] then
                if Cast(S.RushingWindKick) then
                    return "Rushing Wind Kick (TFT) - Auto priority"
                end
            end
        end
       
        -- Forced Enveloping Mist mode
        if Settings['TFT'] == 'em' then
            if S.EnvelopingMist:IsReady(Player) then
                -- Spend immediately
                if CastCycleAlly(S.EnvelopingMist, Members, EvaluateEnvelopingMistTfTorBlackOx) then
                    return "Enveloping Mist (TFT) - upkeep"
                end
                
                -- Regular EM by health threshold
                if CastCycleAlly(S.EnvelopingMist, Members, EvaluateEnvelopingMist) then
                    return "Enveloping Mist (TFT) - By health threshold"
                end
            end
        end
       
        -- Forced Vivify mode
        if Settings['TFT'] == 'vivify' then
            if S.Vivify:IsReady(Player) then
                -- Prioritize targets with Harmony
                if CastCycleAlly(S.Vivify, Members, EvaluateVivifyHarmony) then
                    return "Vivify (TFT) - With Harmony buff"
                end
                
                -- Regular Vivify by health threshold
                if CastCycleAlly(S.Vivify, Members, EvaluateVivify) then
                    return "Vivify (TFT) - By health threshold"
                end
            end
        end
       
        -- Forced Rising Sun Kick mode
        if Settings['TFT'] == 'rsk' then
            if S.RisingSunKick:IsReady() and Var['TargetIsValid'] then
                if Cast(S.RisingSunKick) then
                    return "Rising Sun Kick (TFT) - Forced mode"
                end
            elseif S.RushingWindKick:IsReady() then
                if Cast(S.RushingWindKick) then
                    return "Rushing Wind Kick (TFT) - Forced mode"
                end
            end
        end
        
        return nil
    end 

    ---@type Spell|nil
    local SoothingSpell = nil
    
    --- Determines the best target for Soothing Mist
    -- @return Unit|nil The unit to cast Soothing Mist on, or nil if none found
    local function SoothingTarget()
        if not Player:IsChanneling(S.SoothingMist) then
            return nil
        end
        
        -- With Peer Into Peace talent
        if Var['PeerIntoPeace'] then
            -- First find the current target we're channeling on
            local currentTarget = nil
            for i = 1, #Members do
                local ThisUnit = Members[i]
                if ThisUnit:BuffRemains(S.SoothingMist, false, true) >= 0.3 then
                    currentTarget = ThisUnit
                    break
                end
            end
            
            -- If current target exists but health is above cancel threshold, check for other targets
            if currentTarget and currentTarget:HealthPercentage() > Settings['SooMCancel'] then
                -- 1. Prioritize targets needing Enveloping Mist
                for _, ThisUnit in ipairs(Var['SoothingMembers']) do
                    if EvaluateEnvelopingMistSM(ThisUnit) then
                        return ThisUnit
                    end
                end
                
                -- 2. Look for targets needing emergency Vivify
                for _, ThisUnit in ipairs(Var['SoothingMembers']) do
                    if EvaluateSoothingVivify(ThisUnit) and 
                    ThisUnit:HealthPercentage() <= (Settings['SooMStart'] * 0.65) then
                        return ThisUnit
                    end
                end
                
                -- 3. Look for targets needing regular Vivify
                for _, ThisUnit in ipairs(Var['SoothingMembers']) do
                    if EvaluateSoothingVivify(ThisUnit) then
                        return ThisUnit
                    end
                end
            end
            
            -- Return current target if still valid for healing
            if currentTarget and currentTarget:HealthPercentage() <= Settings['SooMCancel'] then
                return currentTarget
            end
        else
            -- Without Peer Into Peace, find the unit we're already channeling on
            for i = 1, #Members do
                local ThisUnit = Members[i]
                if ThisUnit:BuffRemains(S.SoothingMist, false, true) >= 0.3 then
                    return ThisUnit
                end
            end
        end
        
        return nil
    end

    --- Determines which spell to cast on a Soothing Mist target
    -- @return Spell|nil, Unit|nil The spell to cast and target, or nil if none
    local function SoothingMistSpell()
        -- Without a valid target, can't cast anything
        if not SoothingT then 
            return nil 
        end
        
        -- 1. Emergency Vivify
        if S.Vivify:IsReady(Player) and 
            EvaluateSoothingVivify(SoothingT) and 
            SoothingT:HealthPercentage() <= (Settings['SooMStart'] * 0.65) then
            return S.Vivify, SoothingT
        end
        
        -- 2. Enveloping Mist
        if S.EnvelopingMist:IsReady(Player) and 
            EvaluateEnvelopingMistSM(SoothingT) then
            return S.EnvelopingMist, SoothingT
        end
        
        -- 3. Regular Vivify or targets with Enveloping Mist buff
        if S.Vivify:IsReady(Player) and 
        (EvaluateSoothingVivify(SoothingT) or 
            (SoothingT:BuffUp(S.EnvelopingMist) and 
            SoothingT:HealthPercentage() <= Settings['SooMCancel'])) then
            return S.Vivify, SoothingT
        end
        
        -- No healing needed on current target
        return nil
    end

    --- Handles Soothing Mist targeting and casting
    -- @return string|nil Return message if Soothing Mist was cast
    local function SoothingMist()
        SoothingSpell = nil
        
        -- Don't start a new channel if already channeling
        if Player:IsChanneling(S.SoothingMist) then
            return nil
        end
        
        if not Var['SoothingMembers'] or Player:IsMoving() or not S.SoothingMist:IsReady(Player) then
            return nil
        end

        -- Apply dungeon-specific checks only when in a dungeon and not using Peer Into Peace
        if Player:IsInDungeonArea() and not Var['PeerIntoPeace'] then
            -- Don't start Soothing Mist if instant Vivify is available
            if S.Vivify:IsReady(Player) and Player:BuffUp(S.VivaciousVivificationBuff) then
                return nil
            end

            -- Don't start Soothing Mist if more than 2 units are below 45% health
            local unitsBelow45 = 0
            for _, Unit in ipairs(Members) do
                if Unit:HealthPercentage() < 45 then
                    unitsBelow45 = unitsBelow45 + 1
                end
            end
            
            if unitsBelow45 > 2 then
                return nil
            end
        end

        -- 1. Highest priority: Emergency healing (65% of SooMStart)
        for _, TargetedUnit in ipairs(Var['SoothingMembers']) do
            if EvaluateSoothingVivify(TargetedUnit) and 
            TargetedUnit:HealthPercentage() <= (Settings['SooMStart'] * 0.65) then
                if CastAlly(S.SoothingMist, TargetedUnit) then
                    SoothingSpell = S.Vivify
                    return "Started Soothing Mist for emergency Vivify"
                end
            end
        end

        -- 2. Medium priority: Enveloping Mist
        for _, TargetedUnit in ipairs(Var['SoothingMembers']) do
            if EvaluateEnvelopingMistSM(TargetedUnit) then
                if CastAlly(S.SoothingMist, TargetedUnit) then
                    SoothingSpell = S.EnvelopingMist
                    return "Started Soothing Mist for Enveloping Mist"
                end
            end
        end
        
        -- 3. Lower priority: Regular Vivify
        for _, TargetedUnit in ipairs(Var['SoothingMembers']) do
            if EvaluateSoothingVivify(TargetedUnit) then
                if CastAlly(S.SoothingMist, TargetedUnit) then
                    SoothingSpell = S.Vivify
                    return "Started Soothing Mist for Vivify"
                end
            end
        end
        
        SoothingSpell = nil
        return nil
    end

    --- Focused DPS rotation for Force DPS mode
    -- @return string|nil Return message if a damage ability was used
    local function DamageRotation()
        -- 1. Faeline Stomp if conditions are met
        if Var['TargetIsValid'] then
            if not Var['AncientTeachingsBuff'] or (S.AwakenedFaeline:IsAvailable() and not Var['AwakenedFaelineBuff']) then
                if S.FaelineStomp:IsReady() then
                    if Cast(S.FaelineStomp) then    
                        return "Using Faeline Stomp for DPS"
                    end
                end
            end
        end
    
        -- 2. Spread Mystic Touch with Spinning Crane Kick
        if S.SpinningCraneKick:IsReady(Player) then
            for _, CycleUnit in pairs(Enemies8y) do
                if CycleUnit:GUID() ~= Target:GUID() and EvaluateCycleMysticTouch(CycleUnit) then
                    if Cast(S.SpinningCraneKick) then
                        return "Using Spinning Crane Kick to spread Mystic Touch"
                    end
                end
            end
        end
    
        -- 3. Priority single-target abilities
        if Var['TargetIsValid'] then
            -- Touch of Death if conditions are met
            if S.TouchOfDeath:IsReady() and EvaluateToD(Target) then
                if Cast(S.TouchOfDeath) then
                    return "Using Touch of Death"
                end
            end
    
            -- Rising Sun Kick or Rushing Wind Kick
            if S.RisingSunKick:IsReady() then
                if Cast(S.RisingSunKick) then
                    return "Using Rising Sun Kick"
                end
            elseif S.RushingWindKick:IsReady() then
                if Cast(S.RushingWindKick) then
                    return "Using Rushing Wind Kick"
                end
            end
        end
    
        -- 4. Chi-Burst for AoE damage
        if Var['TargetIsValid'] and S.ChiBurst:IsReady() and not Player:IsMoving() and not Var['ChiJiActive'] then
            if Cast(S.ChiBurst) then
                return "Using Chi-Burst for AoE damage"
            end
        end
    
        -- 5. Spinning Crane Kick as AoE filler
        if Var['ManaPct'] > 30 then
            if S.SpinningCraneKick:IsReady(Player) and EnemiesCount8y >= 8 
               and S.SpinningCraneKick:TimeSinceLastCast() >= 4 then
                if Cast(S.SpinningCraneKick) then
                    return "Using Spinning Crane Kick as AoE filler"
                end
            end
        end
    
        -- 6. Teachings of the Monastery management
        if Var['TargetIsValid'] then
           if Player:BuffStack(S.TeachingsOfTheMonasteryBuff) <= 3 then
                 if S.TigerPalm:IsReady() then
                    if Cast(S.TigerPalm) then
                    return "Using Tiger Palm to build Teachings stacks (mana conservation)"
                    end
                end
            end
    
            -- Blackout Kick to spend stacks
            if S.BlackoutKick:IsReady() then
                if Cast(S.BlackoutKick) then
                    return "Using Blackout Kick to spend Teachings stacks"
                end
            end
    
            -- Tiger Palm as filler
            if S.TigerPalm:IsReady() then
                if Cast(S.TigerPalm) then
                    return "Using Tiger Palm as filler"
                end
            end
        end
        
        return nil
    end

    --- Chi-Ji active rotation for healing through damage
    -- @return string|nil Return message if an action was taken
    local function ChiJiRotation()
        -- 1. Use Faeline Stomp if needed
        if not Var['AncientTeachingsBuff'] or (S.AwakenedFaeline:IsAvailable() and not Var['AwakenedFaelineBuff']) then
            if S.FaelineStomp:IsReady() and Var['TargetIsValid'] then
                if Cast(S.FaelineStomp) then
                    return "Faeline Stomp during Chi-Ji"
                end
            end
        end
    
        if Var['TargetIsValid'] then
            -- 2. Prioritize Blackout Kick for healing
            if S.BlackoutKick:IsReady() then
                if Cast(S.BlackoutKick) then
                    return "Blackout Kick during Chi-Ji"
                end
            end
    
            -- 3. Rising Sun Kick or Rushing Wind Kick
            if S.RisingSunKick:IsReady() then
                if Cast(S.RisingSunKick) then
                    return "Rising Sun Kick during Chi-Ji"
                end
            elseif S.RushingWindKick:IsReady() then
                if Cast(S.RushingWindKick) then
                    return "Rushing Wind Kick during Chi-Ji"
                end
            end
        end
    
        -- 4. Enveloping Mist with high Red Crane buff stacks
        if S.EnvelopingMist:IsReady(Player) and Player:BuffStack(S.InvokeChiJiTheRedCraneBuff) >= 3 then
            if CastCycleAlly(S.EnvelopingMist, Members, EvaluateTargetIfEMCondition) then
                return "Enveloping Mist with Red Crane buff"
            end
        end
    
        if Var['TargetIsValid'] then
            -- 5. Build Teachings of the Monastery stacks
            if Player:BuffStack(S.TeachingsOfTheMonasteryBuff) <= 3 then
                if S.TigerPalm:IsReady() then
                    if Cast(S.TigerPalm) then
                        return "Tiger Palm to build Teachings stacks"
                    end
                end
            end
        end
    
        -- 6. Spinning Crane Kick as filler with many enemies
        if S.SpinningCraneKick:IsReady(Player) and EnemiesCount8y >= 8 and S.SpinningCraneKick:TimeSinceLastCast() >= 2 then
            if Cast(S.SpinningCraneKick) then
                return "Spinning Crane Kick AoE filler"
            end
        end
        
        return nil
    end    

    --- Yu'lon active rotation for maximum healing output
    -- @return string|nil Return message if an action was taken during Yu'lon
    local function YulonRotation()
        -- 1. Enveloping Mist priority when under cast limit
        if S.EnvelopingMist:IsReady(Player) and Var['EMCastAmount'] < Settings['EMCastAmountValue'] then
            -- Emergency healing
            if CastTargetIfAlly(S.EnvelopingMist, TargetIfAlly, "min", EvaluateTargetIfHealth, EvaluateEMEmergency) then
                return "Enveloping Mist on critically low health target during Yu'lon"
            end
            
            -- Targets with Chi Harmony
            if CastCycleAlly(S.EnvelopingMist, Members, EvaluateEMHarmony) then
                return "Enveloping Mist on Chi Harmony target during Yu'lon"
            end
            
            -- Regular healing by threshold
            if CastCycleAlly(S.EnvelopingMist, Members, EvaluateEnvelopingMist) then
                return "Enveloping Mist by health threshold during Yu'lon"
            end
            
            -- Any target missing the buff
            if CastCycleAlly(S.EnvelopingMist, Members, EvaluateTargetIfEMCondition) then
                return "Enveloping Mist during Yu'lon"
            end
            
            -- Spam if nothing else to do
            if CastTargetIfAlly(S.EnvelopingMist, TargetIfAlly, "min", EvaluateTargetIfHPEMYULON, EvaluateTrue) then
                return "Enveloping Mist spam during Yu'lon"
            end
        end
    
        -- 2. Vivify when party health is low
        if HealingEngine:MedianHP(true, 40) <= 90 then
            if S.Vivify:IsReady(Player) then
                -- Emergency cases
                if CastTargetIfAlly(S.Vivify, TargetIfAlly, "min", EvaluateTargetIfHealth, EvaluateViviYulon) then
                    return "Vivify on critically low health target during Yu'lon"
                end
                
                -- Harmony targets
                if CastTargetIfAlly(S.Vivify, TargetIfAlly, "max", EvaluateTargetIfHarmony, EvaluateViviYulon2) then
                    return "Vivify on Chi Harmony target during Yu'lon"
                end
                
                -- Regular healing by threshold
                if CastCycleAlly(S.Vivify, Members, EvaluateVivify) then
                    return "Vivify by health threshold during Yu'lon"
                end
                
                -- Fallback
                if CastCycleAlly(S.Vivify, Members, EvaluateTrue) then
                    return "Vivify general healing during Yu'lon"
                end
            end
        end
        
        return nil
    end    

    --- Caster style healing rotation (with Peer Into Peace)
    -- @return string|nil Return message if an action was taken
    local function CasterHealingRotation()
        -- 1. Jade Serpent Statue if missing
        if S.SummonJadeSerpentStatue:IsReady(Player) and not Var['JadeStatueActive'] 
           and (Var['IsInCombat'] or Player:IsInPvEActivity()) then
            if Cast(S.SummonJadeSerpentStatue) then
                return "Summoning Jade Serpent Statue"
            end
        end

        -- 1a. Keep Enveloping Mist up on tank if enabled
        if Settings['emtankuptime'] and S.EnvelopingMist:IsReady(Player) and #Tanks > 0 then
            if CastCycleAlly(S.EnvelopingMist, Tanks, EvaluateEnvelopingMistTankUptime) then
                return "Enveloping Mist uptime on tank"
            end
        end
    
        -- 2. Rushing Wind Kick for passive healing
        if S.RushingWindKick:IsReady() and Var['AverageHPInRange'] <= 90 then
            if Cast(S.RushingWindKick) then
                return "Rushing Wind Kick for passive healing"
            end
        end
    
        -- 3. High priority Emperor's Favor with full charges
        if S.SheilunsGift:IsReady(Player) and S.EmperorsFavor:IsAvailable() and S.SheilunsGift:Count() == 10 then
            if CastCycleAlly(S.SheilunsGift, Members, EvaluateEmperorsFavor) then
                return "Using Emperor's Favor with Sheilun's Gift (full charges)"
            end
        end
    
        -- 4. Renewing Mist maintenance - tanks first
        if S.RenewingMist:IsReady(Player) then
            -- Prioritize tanks with Harmony
            if CastTargetIfAlly(S.RenewingMist, Tanks, "min", EvaluateTargetIfHarmony, EvaluateRenewingMist) then
                return "Renewing Mist on tank with Chi Harmony"
            end
            
            -- Any tank missing Renewing Mist
            if CastCycleAlly(S.RenewingMist, Tanks, EvaluateRenewingMist) then
                return "Renewing Mist on tank"
            end
        end
    
        -- 5. Renewing Mist on party members
        if Player:IsInPvEActivityExtended() and S.RenewingMist:IsReady(Player) then
            -- Prioritize members with Harmony
            if CastTargetIfAlly(S.RenewingMist, TargetIfAlly, "min", EvaluateTargetIfHarmony, EvaluateRenewingMist) then
                return "Renewing Mist on party member with Chi Harmony"
            end
            
            -- Any member missing Renewing Mist
            if CastCycleAlly(S.RenewingMist, Members, EvaluateRenewingMist) then
                return "Renewing Mist on party member"
            end
        end
    
        -- 6. High priority Sheilun's Gift at max charges
        if S.SheilunsGift:IsReady(Player)
        and S.SheilunsGift:Count() == 10 and not Var['ChiJiActive'] and not S.EmperorsFavor:IsAvailable() then
            if HealingEngine:MembersUnderPercentage(60) >= (60 * Var['MembersAmount']) / 100 then
                if CastTargetIfAlly(S.SheilunsGift, TargetIfAlly, "min", EvaluateTargetIfHealth, EvaluateSheilunsGift) then
                    return "Sheilun's Gift at full charges on priority target"
                end
            end
        end
    
        -- 7. Instant Vivify with ZenPulse
        if S.Vivify:IsReady(Player) and Player:BuffUp(S.ZenPulse) and Player:BuffUp(S.VivaciousVivificationBuff)
           and not MainAddon.Toggle:GetToggle("YulonRamp") then
            
            -- Check if either 40% of party has RM or player has no RM charges
            if (Var['RenewingMistCount'] >= 0.4 * Var['MembersAmount'] or S.RenewingMist:ChargesFractional() < 1) then
                if HealingEngine:MembersUnderPercentage(Settings['DCHP_underX_val']) >= Settings['DCHP_underX'] then
                    if CastCycleAlly(S.Vivify, Members, EvaluateFreeVivify) then
                        return "Instant Vivify with ZenPulse"
                    end
                end
            end
        end
    
        -- 8. Instant Vivify with Vivacious Vivification
        if S.Vivify:IsReady(Player) and Player:BuffUp(S.VivaciousVivificationBuff) and not Var['ChiJiActive'] then
            if CastCycleAlly(S.Vivify, Members, EvaluateFreeVivify) then
                return "Instant Vivify with Vivacious Vivification"
            end
        end
    
        -- 9. Regular Sheilun's Gift usage
        if S.SheilunsGift:IsReady(Player) and S.SheilunsGift:Count() >= Settings['SGCharges'] then
            if HealingEngine:MembersUnderPercentage(Settings['SGHP_underX_val']) >= Settings['SGHP_underX'] 
               and not Var['ChiJiActive'] and not S.EmperorsFavor:IsAvailable() then
                if CastTargetIfAlly(S.SheilunsGift, TargetIfAlly, "min", EvaluateTargetIfHealth, EvaluateSheilunsGift) then
                    return "Sheilun's Gift on priority target"
                end
            end
        end
    
        -- 10. Emperor's Favor via Sheilun's Gift
        if S.SheilunsGift:IsReady(Player) and S.EmperorsFavor:IsAvailable() and S.SheilunsGift:Count() >= Settings['SGCharges'] then
            if CastCycleAlly(S.SheilunsGift, Members, EvaluateEmperorsFavor) then
                return "Using Emperor's Favor with Sheilun's Gift"
            end
        end
    
        -- 11. Chi-Burst to reset Faeline
        if not Var['AwakenedFaelineBuff'] then
            if S.ChiBurst:IsReady() and S.FaelineStomp:CooldownRemains(nil, true) > 2 and Var['TargetIsValid'] then
                if Cast(S.ChiBurst) then
                    return "Chi-Burst to reset Faeline Stomp"
                end
            end
        end
    
        -- 12. Faeline Stomp upkeep
        if not Var['AncientTeachingsBuff'] or (S.AwakenedFaeline:IsAvailable() and not Var['AwakenedFaelineBuff']) then
            if S.FaelineStomp:IsReady() and Var['TargetIsValid'] then
                if Cast(S.FaelineStomp) then
                    return "Faeline Stomp to activate/refresh buffs"
                end
            end
        end
    
        -- 13. Strength of the Black Ox procs
        if Player:BuffUp(S.StrengthoftheBlackOx) and S.CelestialConduit:IsAvailable() then
            -- only if more than 40% of the party are below 75% HP
            local partyMembersUnderThreshold = HealingEngine:MembersUnderPercentage(75)
            local partyPercentageUnderThreshold = partyMembersUnderThreshold / #Members
            
            if partyPercentageUnderThreshold >= 0.4 then
                if S.EnvelopingMist:IsReady(Player) then
                    -- Try party members first
                    if CastCycleAlly(S.EnvelopingMist, Members, EvaluateEnvelopingMistTfTorBlackOx) then
                        return "Enveloping Mist with Black Ox proc on party member"
                    end
                    
                    -- Then tanks if no party members need it
                    if CastCycleAlly(S.EnvelopingMist, Tanks, EvaluateEnvelopingMistTfTorBlackOx) then
                        return "Enveloping Mist with Black Ox proc on tank"
                    end
                end
            end
        end
    
        -- 14. Rapid Diffusion: spread Enveloping Mist with damage
        if Var['RapidDiffusion'] and Var['TargetIsValid'] then
            if S.RisingSunKick:IsReady() and HealingEngine:BuffTotal(S.EnvelopingMist) < #Members then
                if Cast(S.RisingSunKick) then
                    return "Rising Sun Kick with Rapid Diffusion"
                end
            elseif S.RushingWindKick:IsReady() and HealingEngine:BuffTotal(S.EnvelopingMist) < #Members then
                if Cast(S.RushingWindKick) then
                    return "Rushing Wind Kick with Rapid Diffusion"
                end
            end
        end
    
        -- Prevent Red Crane Buff wastage by slowing down
        if Var['ChiJiActive'] and Var['TheRedCraneBuff'] >= 2 and Player:GCDRemains() ~= 0 then
            return nil
        end
    
        -- 15. Run Chi-Ji rotation if active
        if Var['ChiJiActive'] then
            ShouldReturn = ChiJiRotation()
            if ShouldReturn then
                return "Chi-Ji: " .. ShouldReturn
            end
        end
    
        -- 16. Additional Vivify with Vivacious Vivification
        if S.Vivify:IsReady(Player) and Player:BuffUp(S.VivaciousVivificationBuff) then
            if CastCycleAlly(S.Vivify, Members, EvaluateFreeVivifyHarmony) then
                return "Additional Vivify with Harmony and Vivacious Vivification"
            end
            if CastCycleAlly(S.Vivify, Members, EvaluateFreeVivify) then
                return "Additional Vivify with Vivacious Vivification"
            end
        end
    
        -- 17. Enveloping Mist healing
        if S.EnvelopingMist:IsReady(Player) then
            if CastCycleAlly(S.EnvelopingMist, Members, EvaluateEnvelopingMist) then
                return "Enveloping Mist by health threshold"
            end
        end
    
        -- 18. Hard-cast Vivify healing
        if S.Vivify:IsReady(Player) then
            if CastCycleAlly(S.Vivify, Members, EvaluateVivifyHarmony) then
                return "Hard cast Vivify on target with Harmony"
            end
            if CastCycleAlly(S.Vivify, Members, EvaluateVivify) then
                return "Hard cast Vivify by health threshold"
            end
        end
    
        -- 19. Fall back to maintenance healing through DPS if everyone is mostly healthy
        if Var['LowestHP'] <= 95 then
            ShouldReturn = DamageRotation()
            if ShouldReturn then
                return "[Maintenance]: " .. ShouldReturn
            end
        end
        
        return nil
    end   

    --- Standard healing rotation (without Peer Into Peace)
    -- @return string|nil Return message if an action was taken
    local function HealingRotation()
        -- 1. Jade Serpent Statue if missing
        if S.SummonJadeSerpentStatue:IsReady(Player) and not Var['JadeStatueActive'] 
           and (Var['IsInCombat'] or Player:IsInPvEActivity()) then
            if Cast(S.SummonJadeSerpentStatue) then
                return "Summoning Jade Serpent Statue"
            end
        end
        
        -- 1a. Keep Enveloping Mist up on tank if enabled
        if Settings['emtankuptime'] and S.EnvelopingMist:IsReady(Player) and #Tanks > 0 then
            if CastCycleAlly(S.EnvelopingMist, Tanks, EvaluateEnvelopingMistTankUptime) then
                return "Enveloping Mist uptime on tank"
            end
        end
    
        -- 2. High priority Sheilun's Gift at max charges
        if S.SheilunsGift:IsReady(Player)
        and S.SheilunsGift:Count() == 10 and not S.EmperorsFavor:IsAvailable() then
            if HealingEngine:MembersUnderPercentage(60) >= (60 * Var['MembersAmount']) / 100 then
                if CastTargetIfAlly(S.SheilunsGift, TargetIfAlly, "min", EvaluateTargetIfHealth, EvaluateSheilunsGift) then
                    return "Using Sheilun's Gift at full charges on priority target"
                end
            end
        end
    
        -- 3. Instant Vivify with ZenPulse
        if S.Vivify:IsReady(Player) and Player:BuffUp(S.ZenPulse) and Player:BuffUp(S.VivaciousVivificationBuff)
           and not MainAddon.Toggle:GetToggle("YulonRamp") and not Var['ChiJiActive'] then
            
            -- Check if either 40% of party has RM or player has no RM charges
            if (Var['RenewingMistCount'] >= 0.4 * Var['MembersAmount'] or S.RenewingMist:ChargesFractional() < 1) then
                if HealingEngine:MembersUnderPercentage(Settings['DCHP_underX_val']) >= Settings['DCHP_underX'] then
                    if CastCycleAlly(S.Vivify, Members, EvaluateFreeVivify) then
                        return "Instant Vivify with ZenPulse"
                    end
                end
            end
        end
    
        -- 4. Instant Vivify with Vivacious Vivification
        if S.Vivify:IsReady(Player) and Player:BuffUp(S.VivaciousVivificationBuff) 
           and not Var['ChiJiActive'] then
            if CastCycleAlly(S.Vivify, Members, EvaluateFreeVivify) then
                return "Using instant Vivify with Vivacious Vivification"
            end
        end
    
        -- 5. Regular Sheilun's Gift usage
        if S.SheilunsGift:IsReady(Player) and S.SheilunsGift:Count() >= Settings['SGCharges'] then
            if HealingEngine:MembersUnderPercentage(Settings['SGHP_underX_val']) >= Settings['SGHP_underX'] 
               and not Var['ChiJiActive'] and not S.EmperorsFavor:IsAvailable() then
                if CastTargetIfAlly(S.SheilunsGift, TargetIfAlly, "min", EvaluateTargetIfHealth, EvaluateSheilunsGift) then
                    return "Using Sheilun's Gift on priority healing target"
                end
            end
        end
    
        -- 6. Emperor's Favor via Sheilun's Gift
        if S.SheilunsGift:IsReady(Player) and S.EmperorsFavor:IsAvailable() and S.SheilunsGift:Count() >= Settings['SGCharges'] then
            if CastCycleAlly(S.SheilunsGift, Members, EvaluateEmperorsFavor) then
                return "Using Emperor's Favor with Sheilun's Gift"
            end
        end
    
        -- 7. Renewing Mist on tanks
        if S.RenewingMist:IsReady(Player) then
            if CastTargetIfAlly(S.RenewingMist, Tanks, "min", EvaluateTargetIfHarmony, EvaluateRenewingMist) then
                return "Renewing Mist on tank with Chi Harmony"
            end
            if CastCycleAlly(S.RenewingMist, Tanks, EvaluateRenewingMist) then
                return "Renewing Mist on tank"
            end
        end
    
        -- 8. Renewing Mist on party members (extended PvE activity)
        if Player:IsInPvEActivityExtended() and S.RenewingMist:IsReady(Player) then
            if CastTargetIfAlly(S.RenewingMist, TargetIfAlly, "min", EvaluateTargetIfHarmony, EvaluateRenewingMist) then
                return "Renewing Mist on party member with Chi Harmony"
            end
            if CastCycleAlly(S.RenewingMist, Members, EvaluateRenewingMist) then
                return "Renewing Mist on party member"
            end
        end
    
        -- 9. Chi-Burst to try and reset Faeline
        if not Var['AwakenedFaelineBuff'] then
            if S.ChiBurst:IsReady() and S.FaelineStomp:CooldownRemains(nil, true) > 2 and Var['TargetIsValid'] then
                if Cast(S.ChiBurst) then
                    return "Chi-Burst to reset Faeline Stomp"
                end
            end
        end
    
        -- 10. Faeline Stomp if Ancient Teachings is missing or Awakened Faeline is needed
        if not Var['AncientTeachingsBuff'] or (S.AwakenedFaeline:IsAvailable() and not Var['AwakenedFaelineBuff']) then
            if S.FaelineStomp:IsReady() and Var['TargetIsValid'] then
                if Cast(S.FaelineStomp) then
                    return "Faeline Stomp to activate buffs"
                end
            end
        end
    
        -- 11. Strength of the Black Ox procs
        if Player:BuffUp(S.StrengthoftheBlackOx) and S.CelestialConduit:IsAvailable() then
            -- only if more than 40% of the party are below 75% HP
            local partyMembersUnderThreshold = HealingEngine:MembersUnderPercentage(75)
            local partyPercentageUnderThreshold = partyMembersUnderThreshold / #Members
            
            if partyPercentageUnderThreshold >= 0.4 then
                if S.EnvelopingMist:IsReady(Player) then
                    -- Try party members first
                    if CastCycleAlly(S.EnvelopingMist, Members, EvaluateEnvelopingMistTfTorBlackOx) then
                        return "Enveloping Mist with Black Ox proc on party member"
                    end
                    
                    -- Then tanks if no party members need it
                    if CastCycleAlly(S.EnvelopingMist, Tanks, EvaluateEnvelopingMistTfTorBlackOx) then
                        return "Enveloping Mist with Black Ox proc on tank"
                    end
                end
            end
        end
    
        -- 12. Enveloping Mist for Chi-Ji when Red Crane Buff is sufficient
        if Var['TheRedCraneBuff'] >= 3 or (not Var['ChiJiActive'] and Var['TheRedCraneBuff'] > 0) then
            if S.EnvelopingMist:IsReady(Player) then
                -- Tanks with Harmony
                if CastCycleAlly(S.EnvelopingMist, Tanks, EvaluateEMHarmony) then
                    return "Enveloping Mist on tank with Harmony (Chi-Ji)"
                end
                -- Members with Harmony
                if CastCycleAlly(S.EnvelopingMist, Members, EvaluateEMHarmony) then
                    return "Enveloping Mist on party member with Harmony (Chi-Ji)"
                end
                -- Any tank missing EM
                if CastCycleAlly(S.EnvelopingMist, Tanks, EvaluateTargetIfEMCondition) then
                    return "Enveloping Mist on tank (Chi-Ji)"
                end
                -- Any member missing EM
                if CastCycleAlly(S.EnvelopingMist, Members, EvaluateTargetIfEMCondition) then
                    return "Enveloping Mist on party member (Chi-Ji)"
                end
            end
        end
    
        -- 13. Rapid Diffusion: Use RSK or Rushing Wind Kick if not overbuffing EM
        if Var['RapidDiffusion'] and Var['TargetIsValid'] then
            if S.RisingSunKick:IsReady() and HealingEngine:BuffTotal(S.EnvelopingMist) < #Members then
                if Cast(S.RisingSunKick) then
                    return "Rising Sun Kick with Rapid Diffusion"
                end
            elseif S.RushingWindKick:IsReady() and HealingEngine:BuffTotal(S.EnvelopingMist) < #Members then
                if Cast(S.RushingWindKick) then
                    return "Rushing Wind Kick with Rapid Diffusion"
                end
            end
        end
    
        -- Prevent Red Crane Buff wastage by slowing down
        if Var['ChiJiActive'] and Var['TheRedCraneBuff'] >= 2 and Player:GCDRemains() ~= 0 then
            return nil
        end
    
        -- 14. Run Chi-Ji rotation if active
        if Var['ChiJiActive'] then
            ShouldReturn = ChiJiRotation()
            if ShouldReturn then
                return "Chi-Ji: " .. ShouldReturn
            end
        end
    
        -- 15. Additional Vivify with Vivacious Vivification
        if S.Vivify:IsReady(Player) and Player:BuffUp(S.VivaciousVivificationBuff) then
            if CastCycleAlly(S.Vivify, Members, EvaluateFreeVivifyHarmony) then
                return "Using additional Vivify with Harmony and Vivacious Vivification"
            end
            if CastCycleAlly(S.Vivify, Members, EvaluateFreeVivify) then
                return "Using additional Vivify with Vivacious Vivification"
            end
        end
    
        -- 16. Cast Enveloping Mist on members
        if S.EnvelopingMist:IsReady(Player) then
            if CastCycleAlly(S.EnvelopingMist, Members, EvaluateEnvelopingMist) then
                return "Enveloping Mist by health threshold"
            end
        end
    
        -- 17. Hard cast Vivify as a fallback
        if S.Vivify:IsReady(Player) then
            if CastCycleAlly(S.Vivify, Members, EvaluateVivifyHarmony) then
                return "Hard cast Vivify on target with Harmony"
            end
            if CastCycleAlly(S.Vivify, Members, EvaluateVivify) then
                return "Hard cast Vivify by health threshold"
            end
        end
    
        -- 18. Transition to 'DPS to Heal' rotation if everyone is mostly healthy
        if Var['LowestHP'] <= 95 then
            ShouldReturn = DamageRotation()
            if ShouldReturn then
                return "[Maintenance]: " .. ShouldReturn
            end
        end
        
        return nil
    end

    --- Provides filler healing with Expel Harm
    -- @return string|nil Return message if Expel Harm was used
    local function Fillers()
        if Var['IsInCombat'] and Var['TargetIsValid'] then
            -- Use Expel Harm when HP deficit >= potential healing
            if S.ExpelHarm:IsReady(Player) and (100 - Var['PlayerHPFiltered']) >= Var['ExpelHarmHealing'] then
                if Cast(S.ExpelHarm) then
                    return "Using Expel Harm for self-healing"
                end
            end
        end
        return nil
    end
    
    --- Special Yu'lon ramping rotation before pull
    -- @return string|nil Return message if an action was taken for Yu'lon ramping
    local function YulonRamp()
        -- 1. Ensure all Renewing Mist charges are spent
        if S.RenewingMist:IsReady(Player) then
            if CastTargetIfAlly(S.RenewingMist, TargetIfAlly, "min", EvaluateTargetIfHarmony, EvaluateRenewingMist) then
                return "Spreading Renewing Mist for Yu'lon ramp"
            end
        end
    
        -- 2. Cast RSK or RWK to get ready for the pull
        if S.RisingSunKick:IsReady(Player) and Var['TargetIsValid'] then
            if Cast(S.RisingSunKick) then
                return "Using Rising Sun Kick for Yu'lon ramp"
            end
        elseif S.RushingWindKick:IsReady() then
            if Cast(S.RushingWindKick) then
                return "Using Rushing Wind Kick for Yu'lon ramp"
            end
        end
    
        -- 3. Use Mana Tea for the efficiency bonus
        if S.ManaTea:IsReady(Player) and Player:BuffStack(S.ManaTeaStack) >= 3 then
            if Cast(S.ManaTea) then
                return "Using Mana Tea for Yu'lon ramp"
            end
        end
    
        -- 4. Cancel Mana Tea after getting enough benefit
        if Player:IsChanneling(S.ManaTea) and (GetTime() - Player:ChannelStart()) >= 2 then
            MainAddon.SetTopColor(1, "Stop Casting")
        end
    
        -- 5. Cast Sheilun's Gift with Shaohao's Lesson
        if S.SheilunsGift:IsReady(Player) and S.SheilunsGift:Count() >= Settings['SGCharges'] and S.ShaoShaosLesson:IsAvailable() and not S.EmperorsFavor:IsAvailable() then
            if CastTargetIfAlly(S.SheilunsGift, TargetIfAlly, "min", EvaluateTargetIfHealth, EvaluateSheilunsGift) then
                return "Using Sheilun's Gift for Yu'lon ramp"
            end
        end
    
        -- 6. Invoke Yu'lon if Sheilun's Gift was just used
        if not S.SheilunsGift:IsReady(Player) or S.SheilunsGift:TimeSinceLastCast() < 5 then
            if S.InvokeYulonTheJadeSerpent:IsReady(Player) then
                if Cast(S.InvokeYulonTheJadeSerpent) then
                    return "Invoking Yu'lon at optimal timing"
                end
            end
        end
        
        return nil
    end

    --===========================================--
    -- Main Action Priority List
    --===========================================--
    
    --- Main action priority list for the spec
    -- @return string Action message describing what was done
    local function APL()
        -- Update all variables
        UpdateVars()
        
        -- Update enemy tracking
        if Var['AoEON'] then
            Enemies8y = Player:GetEnemiesInRange(8)
        else
			Enemies8y = {}
        end
        EnemiesCount8y = #Enemies8y

        -- Items and trinkets
        ShouldReturn = Items()
        if ShouldReturn then
             return "[Items] " .. ShouldReturn
        end
        
        ShouldReturn = MainAddon.TrinketHealing(Members, OnUseExcludes)
        if ShouldReturn then
            return "[Trinkets] " .. ShouldReturn
        end

        -- Mana potions
        if MainAddon.UseManaPotion() then
            MainAddon.SetTopColor(1, "Combat Potion")
        end

        -- Defensive or utility actions based on combat state
        if Var['IsInCombat'] then
            ShouldReturn = Defensives()
            if ShouldReturn then
                return "[Defensives] " .. ShouldReturn
            end
        else
            ShouldReturn = Utilities()
            if ShouldReturn then 
                return "[Utilities] " .. ShouldReturn 
            end
        end

        -- Use Jade Empowerment with Crackling Jade Lightning
        if Var['TargetIsValid'] then
            ShouldReturn = JadeEmpowerment()
            if ShouldReturn then
                return "[JadeEmpowerment] " .. ShouldReturn
            end
        end

        -- Yu'lon active rotation in Raid areas
        if Var['YulonActive'] and Player:IsInRaidArea() then
            -- Turn off YulonRamp toggle if already active
            if MainAddon.Toggle:GetToggle("YulonRamp") then
                MainAddon.Toggle:SetToggle("YulonRamp", false)
            end
            
            ShouldReturn = YulonRotation() 
            if ShouldReturn then
                return "[Yu'lon] " .. ShouldReturn
            end
        end

        -- Yu'lon ramping mode
        if MainAddon.Toggle:GetToggle("YulonRamp") then
            if S.InvokeYulonTheJadeSerpent:CooldownRemains(nil, true) < 5 then
                if Player:IsChanneling(S.SoothingMist) then
                    MainAddon.SetTopColor(1, "Stop Casting")
                end
                
                ShouldReturn = YulonRamp()
                if ShouldReturn then 
                    return "[Yu'lon Ramp] " .. ShouldReturn
                end
                return 
            end
        end

        -- Force DPS mode
        if MainAddon.Toggle:GetToggle("ForceDPS") then
            if Player:IsChanneling(S.SoothingMist) then
                MainAddon.SetTopColor(1, "Stop Casting")
            end
            
            ShouldReturn = DamageRotation()
            if ShouldReturn then 
                return "[Force DPS] " .. ShouldReturn
            end
            return
        end

        -- Special healing priority
        ShouldReturn = HealingSpecial()
        if ShouldReturn then
            return "[Special] " .. ShouldReturn
        end

        -- Handle incoming damage events
        local Reason, SpellID = MainAddon:DamageIncoming()
        if Reason == "SOON" then
            MainAddon.UI:ShowToast(MainAddon.GetSpellInfo(SpellID), "Predicting major incoming damage.", MainAddon.GetTexture(Spell(SpellID)), 10)

            ShouldReturn = DamageIncoming()
            if ShouldReturn then
                return "[Incoming] " .. ShouldReturn
            end
        end

        -- Celestial Conduit with Unity Within management
        if Player:IsChanneling(S.CelestialConduit) and S.UnityWithin:IsAvailable() then
            if Var['LowestHP'] <= 68 then
                if Player:ChannelRemains() <= 1.5 then
                    if M.ForceCastDisplay(S.CelestialConduit, 6) then
                        return "Optimizing Unity Within timing for low health party"
                    end
                end
            elseif Var['LowestHP'] > 68 then
                if Player:ChannelRemains() <= 0.7 then
                    if M.ForceCastDisplay(S.CelestialConduit, 6) then
                        return "Optimizing Unity Within timing for high health party"
                    end
                end
            end
        end        

        -- Spinning Crane Kick ending management
        if Player:IsChanneling(S.SpinningCraneKick) and Player:ChannelRemains() <= 0.4 then
            MainAddon.IgnoreChannel = true
        end

        -- Mana Tea auto-management
        -- Define mana thresholds and associated conditions
        local ManaThresholds = {
            { mana = 8, timeSinceLastCast = 14, message = "Emergency Mana Tea at critically low mana", priority = true },
            { mana = 40, timeSinceLastCast = 14, message = "Using Mana Tea at 40% mana" },
            { mana = 65, timeSinceLastCast = 14, message = "Using Mana Tea at 65% mana" },
            { mana = 80, timeSinceLastCast = 10, notInCombat = true, message = "Using Mana Tea at 80% mana (out of combat)" },
        }

        -- Auto Mana Tea logic
        if Settings['ManaTeaAuto'] == 'auto' and S.ManaTea:IsReady(Player) then
            for _, threshold in ipairs(ManaThresholds) do
                -- Emergency usage with higher stack requirement
                if threshold.priority and Var['ManaPct'] <= threshold.mana
                and S.ManaTea:TimeSinceLastCast() > threshold.timeSinceLastCast then
                    if Player:BuffStack(S.ManaTeaStack) >= 15 then
                        if Cast(S.ManaTea) then
                            return threshold.message
                        end
                    end
                -- Regular usage based on thresholds
                elseif Var['ManaPct'] <= threshold.mana
                and S.ManaTea:TimeSinceLastCast() > threshold.timeSinceLastCast
                and (not threshold.notInCombat or not Var['IsInCombat'])
                and (HealingEngine:LowestHP() >= 80 or Var['ManaPct'] <= 10)
                then
                    if Cast(S.ManaTea) then
                        return threshold.message
                    end
                end
            end
        end

        -- Settings-based Mana Tea usage
        if Settings['ManaTeaAuto'] == 'settings' and S.ManaTea:IsReady(Player)
        and S.ManaTea:TimeSinceLastCast() > 15
        and (Var['ManaPct'] <= Settings['MTea'] or Var['ManaPct'] <= 10)
        and Player:BuffStack(S.ManaTeaStack) >= Settings['MTeaStack']
        and (HealingEngine:LowestHP() >= Settings['MTeaHPfriendly'] or (Player:BuffStack(S.ManaTeaStack) >= 12 and Var['ManaPct'] <= 8))
        and not Var['YulonActive'] then
            if Cast(S.ManaTea) then
                return Var['ManaPct'] <= 10 and "Emergency Mana Tea at critically low mana" or "Using Mana Tea based on settings"
            end
        end

        -- Mana Tea channel management
        if Player:IsChanneling(S.ManaTea) then
            local criticallyLowMana = Var['ManaPct'] < 8

            -- Cancel conditions based on mode
            local shouldCancel = false
            
            if Settings['ManaTeaAuto'] == 'auto' then
                shouldCancel = Var['ManaPct'] >= 100
                            or ((GetTime() - Player:ChannelStart()) >= 2 and Var['ManaPct'] >= 90)
                            or (Var['IsInCombat'] and HealingEngine:LowestHP(true) <= 70 and not criticallyLowMana)
            elseif Settings['ManaTeaAuto'] == 'settings' then
                shouldCancel = Var['ManaPct'] >= 100
                            or ((GetTime() - Player:ChannelStart()) >= 2 and Var['ManaPct'] >= 90)
                            or (Var['IsInCombat'] and HealingEngine:LowestHP(true) <= (Settings['MTeaHPfriendly'] - 8) and not criticallyLowMana)
            end
            
            if shouldCancel then
                MainAddon.SetTopColor(1, "Stop Casting")
            end

            -- Force minimum channel during critical mana
            if criticallyLowMana and (GetTime() - Player:ChannelStart()) < 3 then
                return "Continuing Mana Tea channel during critical mana"
            end
        end

        -- Combat healing cooldowns
        if Var['IsInCombat'] then
            ShouldReturn = HealingCDs()
            if ShouldReturn then
                return "[CDs] " .. ShouldReturn
            end

            -- Use Thunder Focus Tea if not already active
            if not Player:BuffUp(S.ThunderFocusTea) then
                ShouldReturn = UseThunderFocusTea()
                if ShouldReturn then
                    return "[TFT] " .. ShouldReturn
                end
            end
        end

        -- Spend Thunder Focus Tea buff
        if Player:BuffUp(S.ThunderFocusTea) then
            ShouldReturn = SpendThunderFocusTea()
            if ShouldReturn then
                return "[TFT] " .. ShouldReturn
            end
        end

        -- Handle Soothing Mist target and spell decisions
        if SoothingT then
            SoothingSpell, SoothingSpellTarget = SoothingMistSpell()
        else
            SoothingSpell = nil
        end
        MainAddon.IgnoreChannel = false

        -- Handle ongoing Soothing Mist channel management
        if Player:IsChanneling(S.SoothingMist) then
            -- Cancel channel if more than 2 units drop below 45% health (dungeon only, without Peer Into Peace)
            if Player:IsInDungeonArea() and not Var['PeerIntoPeace'] then
                local unitsBelow45 = 0
                for _, Unit in ipairs(Members) do
                    if Unit:HealthPercentage() < 45 then
                        unitsBelow45 = unitsBelow45 + 1
                    end
                end
                
                if unitsBelow45 > 2 then
                    MainAddon.SetTopColor(1, "Stop Casting")
                    return "Canceling Soothing Mist - multiple units need AoE healing"
                end
            end

            -- Cancel channel if no follow-up spells were cast
            if not SoothingSpell and S.SoothingMist:TimeSinceLastCast() > 1.5 then
                MainAddon.SetTopColor(1, "Stop Casting")
                return
            end
            -- Cancel channel if target is above threshold
            if SoothingT and SoothingT:HealthPercentage() > Settings['SooMCancel'] then
                local foundNewTarget = false
                -- Check if there's another target that needs healing
                for _, TargetedUnit in ipairs(Var['SoothingMembers']) do
                    if EvaluateSoothingVivify(TargetedUnit) and 
                    TargetedUnit:HealthPercentage() <= Settings['SooMStart'] then
                        foundNewTarget = true
                        break
                    end
                end
                
                if foundNewTarget then
                    -- Stop current channel to start a new one on better target
                    MainAddon.SetTopColor(1, "Stop Casting")
                    return
                end
            end

            -- Allow other spells during SoothingMist
            MainAddon.IgnoreChannel = true

            -- Try special healing during channel
            ShouldReturn = HealingSpecial()
            if ShouldReturn then
                return "[Special] " .. ShouldReturn
            end

            -- Cancel prolonged channel with no follow-up spells
            local channelCheck = Var['PeerIntoPeace'] and 3 or 2
            if (not SoothingSpell and S.SoothingMist:TimeSinceLastCast() > channelCheck) then
                -- Before canceling, double-check if current target still needs healing
                if SoothingT and SoothingT:HealthPercentage() <= Settings['SooMCancel'] then
                    -- Continue channeling if target needs healing
                    return "Continuing Soothing Mist on " .. SoothingT:Name()
                else
                    MainAddon.SetTopColor(1, "Stop Casting")
                end
            end
        end

        -- Update current Soothing Mist target
        if Members and #Members > 0 then
            SoothingT = SoothingTarget()
        end
        
        -- Cast spell on Soothing Mist target if needed
        if SoothingSpell then
            local targetToUse = SoothingSpellTarget or SoothingT
            if targetToUse then
                CastAlly(SoothingSpell, targetToUse, nil, nil, nil, true)
                return string.format("Using %s on %s during Soothing Mist", SoothingSpell:Name(), targetToUse:Name())
            end
        end

        -- Soothing Mist management based on style
        if not Player:IsMoving() and not MainAddon.Toggle:GetToggle("ForceDPS") then
            -- Special handling for dungeon content without Peer Into Peace
            if Player:IsInDungeonArea() and not Var['PeerIntoPeace'] then
                -- Don't start Soothing Mist if instant Vivify is available
                if S.Vivify:IsReady(Player) and Player:BuffUp(S.VivaciousVivificationBuff) then
                    -- Skip Soothing Mist when we can cast instant Vivify
                else
                    -- Check for multiple low health units
                    local unitsBelow45 = 0
                    for _, Unit in ipairs(Members) do
                        if Unit:HealthPercentage() < 45 then
                            unitsBelow45 = unitsBelow45 + 1
                        end
                    end
                    
                    -- Only consider Soothing Mist if we don't have multiple low health targets
                    if unitsBelow45 <= 2 then
                        if (not Var['ChiJiActive'] or not Target:IsInRange(5)) and not Var['YulonActive'] and not Player:BuffUp(S.ThunderFocusTea) then
                            if S.SoothingMist:IsReady(Player) then
                                ShouldReturn = SoothingMist()
                                if ShouldReturn then
                                    return "[Melee-SooM] " .. ShouldReturn
                                end
                            end
                        end
                    end
                end
            else
                -- Standard handling for all other content
                if Var['PeerIntoPeace'] then  
                    -- Caster style with Peer Into Peace
                    if S.SoothingMist:IsReady(Player) then
                        ShouldReturn = SoothingMist()
                        if ShouldReturn then
                            return "[Caster-SooM] " .. ShouldReturn
                        end
                    end
                else  
                    -- Fistweaving style without Peer Into Peace
                    if (not Var['ChiJiActive'] or not Target:IsInRange(5)) and not Var['YulonActive'] and not Player:BuffUp(S.ThunderFocusTea) then
                        if S.SoothingMist:IsReady(Player) then
                            ShouldReturn = SoothingMist()
                            if ShouldReturn then
                                return "[Melee-SooM] " .. ShouldReturn
                            end
                        end
                    end
                end
            end
        end

        -- Exit if still channeling Soothing Mist
        if Player:IsChanneling(S.SoothingMist) then
            return
        end

        -- Main healing rotation based on style
        if Var['PeerIntoPeace'] then
            ShouldReturn = CasterHealingRotation()
            if ShouldReturn then
                return "[Caster] " .. ShouldReturn
            end
        else
            ShouldReturn = HealingRotation()
            if ShouldReturn then
                return "[Melee] " .. ShouldReturn
            end
        end      

        -- Slow down chi-ji rotation to avoid buff waste
        if Var['ChiJiActive'] and Var['TheRedCraneBuff'] >= 2 and Player:GCDRemains() ~= 0 then
            return
        end

        -- Out of combat resurrection
        if not Var['IsInCombat'] then
            if Target:IsAPlayer() and Target:IsDeadOrGhost() and Player:IsFriend(Target) and (Target:IsInParty() or Target:IsInRaid()) and Target:EvaluateRebirth() then
				if S.Reawaken:IsReady(Player) then
					if Cast(S.Reawaken) then
						return "Resurrecting " .. Target:Name() .. " with Reawaken"
					end
				end
			end
        end

        -- Fallback to damage rotation
        ShouldReturn = DamageRotation()
        if ShouldReturn then
            return "[Maintenance] " .. ShouldReturn
        end

        -- Final fallback: filler healing
        ShouldReturn = Fillers()
        if ShouldReturn then
            return "[Filler] " .. ShouldReturn
        end
    end

    --===========================================--
    -- Registration and Overrides
    --===========================================--
    
    local function Init()
        -- Any initialization code here
    end
    
    M.SetAPL(270, APL, Init)

    -- Event registrations
    HL:RegisterForEvent(function()
        Tanks, Healers, Members, Damagers, Melees, _, _, _, TargetIfAlly = HealingEngine:Fetch()
        SoothingT = nil
   end, "PLAYER_ENTERING_WORLD", "GROUP_ROSTER_UPDATE")

    HL:RegisterForEvent(function(_, source, destName, _, spellID)
        if source == "player" then
            -- Track Enveloping Mist casts to prevent overlapping
            if spellID == 124682 then
                TempBlackListEM[destName] = true
                C_Timer.After(S.EnvelopingMist:CastTime() + (0.8), function()
                    TempBlackListEM[destName] = nil
                end)
            end
            
            -- Track Soothing Mist target
            if spellID == 115175 then
                Var['SoothingMistUnit'] = destName
            end

            -- Track Enveloping Mist casts during Yu'lon
            if spellID == 124682 then
                if Player:BuffUp(S.YulonBuff) and Var['EMSucceeded'] == 1 then
                    Var['EMCastAmount'] = Var['EMCastAmount'] + 1
                    Var['EMSucceeded'] = 0
                end
            end
        end
    end, "UNIT_SPELLCAST_SENT")

    HL:RegisterForEvent(function(_, unitID, _, spellID)
        if unitID == "player" then
            -- Track interrupted Enveloping Mist casts during Yu'lon
            if spellID == 124682 and Var['EMSucceeded'] == 0 then
                if Player:BuffUp(S.YulonBuff) then
                    if Var['EMCastAmount'] > 0 then
                        Var['EMCastAmount'] = Var['EMCastAmount'] - 1
                        Var['EMSucceeded'] = 1
                    end
                end
            end
        end
    end, "UNIT_SPELLCAST_INTERRUPTED")

    HL:RegisterForEvent(function(_, unitID, arg3, spellID)
        if unitID == "player" then
            -- Track successful Enveloping Mist casts during Yu'lon
            if spellID == 124682 then
                if Player:BuffUp(S.YulonBuff) then
                    Var['EMSucceeded'] = 1
                end
            end
        end
    end, "UNIT_SPELLCAST_SUCCEEDED")

    HL:RegisterForEvent(function()
        -- Clear blacklist when leaving combat or loading UI
        wipe(TempBlackListEM)
    end, "PLAYER_REGEN_ENABLED", "PLAYER_ENTERING_WORLD", "UPDATE_CHAT_WINDOWS", "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")

    HL:RegisterForSelfCombatEvent(
        function(_, _, _, _, _, _, _, _, _, _, _, SpellID)
            -- Reset cast counter when Yu'lon buff drops
            if SpellID == 389422 then
                C_Timer.After(0.5, function()
                    Var['EMCastAmount'] = 0
                end)
            end
    end, "SPELL_AURA_REMOVED")

    -- Spell overrides
    
    -- Override IsUsable to allow targeting with Touch of Death
    local OldIsUsable
    OldIsUsable = HL.AddCoreOverride("Spell.IsUsable",
            function(self)
                if MainAddon.PlayerSpecID() == 270 then
                    if self == S.TouchOfDeath then
                        return true
                    end
                end
                local BaseCheck, Reason = OldIsUsable(self)
                return BaseCheck, Reason
            end
    , 270);

    -- Override IsCastable with additional restrictions
    local OldIsCastable
    OldIsCastable = HL.AddCoreOverride("Spell.IsCastable",
        function(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
            if MainAddon.PlayerSpecID() == 270 then
                -- Sheilun's Gift min charges check
                if self == S.SheilunsGift then
                    if S.SheilunsGift:Count() < Settings['SGCharges'] then
                          return false, 'Not enough charges'
                    end
                end

                -- Mana Tea in raid areas
                if self == S.ManaTea then
                    if Player:IsInRaidArea() then
                        if not Player:AffectingCombat() then
                            return false, 'Not in combat'
                        end
                    end
                end

                -- Chi-Ji range and special encounter checks
                if self == S.InvokeChiJiTheRedCrane then
                    if not Player:CanAttack(Target) or not Target:IsSpellInRange(S.TigerPalm) then
                        return false, 'Not in range'
                    end

                    if M.CurrentEncounter == 2901 then
                        return false, "Special logic - Ki'katal the Harvester"
                    end
                end

                -- Revival/Restoral during Sheilun's Gift cast
                if self == S.Revival or self == S.Restoral then
                    if Player:IsCasting(S.SheilunsGift) then
                        return false, 'Casting Sheiluns Gift'
                    end
                end

                -- Soothing Mist prevention while channeling anything
                if self == S.SoothingMist then
                    if Player:IsChanneling() then
                        return false, 'Already channeling'
                    end
                end

                -- Faeline Stomp restrictions
                if self == S.FaelineStomp then
                    if Var['IsStandingStillFor'] < Settings['fstompMovingValue'] then
                        return false, 'Moving'
                    end
                    if not FaelineStompInRange(Target) then
                        return false, 'Not in range'
                    end
                end

                -- Jade Serpent Statue movement restrictions
                if self == S.SummonJadeSerpentStatue then
                    if Var['IsStandingStillFor'] < Settings['JadeStatueMovingValue'] then
                        return false, 'Moving'
                    end
                end

                -- Allow casting through Soothing Mist
                if self ~= S.SoothingMist then
                    if Player:IsChanneling(S.SoothingMist) then
                        ignoreChannel = true
                    end
                end

                -- Spinning Crane Kick restrictions
                if self == S.SpinningCraneKick then
                    if not Var['IsInCombat'] then
                        return false, 'Not In Combat'
                    end
                    if MainAddon.IgnoreChannel then
                        if Player:IsChanneling(S.SpinningCraneKick) then
                            return false, 'Already channeling'
                        end
                    end
                end

                -- Yu'lon melee range check if setting enabled
                if self == S.InvokeYulonTheJadeSerpent then
                    if GetSetting('YULONmelee') and not Target:IsSpellInRange(S.TigerPalm) then
                        return false, 'Not in range'
                    end
                end

                -- Prevent double-casting Vivify
                if self == S.Vivify then
                    if Player:IsCasting(S.Vivify) then
                        return false, 'Already casting'
                    end
                end
            end

            local BaseCheck, Reason = OldIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
            return BaseCheck, Reason
        end, 
    270);

    -- Override IsReady to allow casting through Soothing Mist
    local OldIsReady
    OldIsReady = HL.AddCoreOverride("Spell.IsReady",
            function(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
                if MainAddon.PlayerSpecID() == 270 then
                    if Player:IsChanneling(S.SoothingMist) then
                        ignoreChannel = true
                    end
                end

                local BaseCheck, Reason = OldIsReady(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
                return BaseCheck, Reason
            end
    , 577);
end