function A_267(...)
  -- HR UPDATE: feat(Destruction): [NFC] Update for 11.1.5 09.06.2025
  -- REMEMBER: replace (SoulShards < 4.5) with (SoulShards < VarCustomSoulShardThreshold) to prevent capping
  -- REMEMBER: S.SummonInfernal:BlockedByUserSettings()
  -- REMEMBER: CastMagic(S.Cataclysm, nil, "152108-Magic", magicgroundspell_cataclysm)
  -- REMEMBER: CastMagic(S.RainofFire, nil, "5740-Magic", magicgroundspell_rof)
  -- REMEMBER: CastMagic(S.<PERSON>n<PERSON>, nil, "1122-Magic", magicgroundspell_inf)
  -- Addon
  ---@class MainAddon
  local MainAddon = MainAddon
  local M = MainAddon
  local Warlock = M.Warlock
  -- HeroLib
  local HL = HeroLibEx
  local Utils = HL.Utils
  ---@class Unit
  local Unit = HL.Unit
  ---@class Unit
  local Player = Unit.Player
  ---@class Unit
  local Target = Unit.Target
  ---@class Unit
  local MouseOver = Unit.MouseOver
  ---@class Unit
  local Pet = Unit.Pet
  ---@class Spell
  local Spell = HL.Spell
  ---@class Item
  local Item = HL.Item
  local Action = HL.Action
  -- HeroRotation
  local Cast = M.Cast
  local CastMagic = M.CastMagic
  local CastTargetIf = M.CastTargetIf
  local CastLeftNameplate = M.CastLeftNameplate
  local AoEON = M.AoEON
  -- LUA
  local UnitPower = _G['UnitPower']
  local IsCurrentSpell = _G['C_Spell']['IsCurrentSpell']
  local min = _G['math'].min
  local max = _G['math'].max
  local floor = _G['math'].floor
  local IsFalling = _G['IsFalling']
  local GetTime = _G['GetTime']
  local GetMouseFoci = _G['GetMouseFoci']
  local C_Timer = _G['C_Timer']
  local num = M.num
  local bool = M.bool
  local Delay         = C_Timer.After
  -- Define S/I for spell and item arrays
  local S = Spell.Warlock.Destruction
  local I = Item.Warlock.Destruction

  local OnUseExcludes = {
    I.SpymastersWeb:ID(),
  }

  ---GUI SETTINGS
  local GetSetting = MainAddon.Config.GetClassSetting
  local Config_Key = MainAddon.GetClassVariableName()
  local Config_Color = '8788EE'
  local Config_Table = {
      key = Config_Key,
      title = 'Warlock - Destruction',
      subtitle = '?? ' .. MainAddon.Version,
      width = 600,
      height = 700,
      profiles = true,
      config = {
            { type = 'header', text = '', size = 24, align = 'Center', color = Config_Color },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
            { type = 'header', text = 'DPS', color = Config_Color },
            { type = 'checkbox', text = ' Auto-switch if target has Havoc', icon = S.Havoc:ID(), key = 'auto_havoc', default = true },
            { type = 'checkbox', text = ' Cataclysm when target is moving', icon = S.Cataclysm:ID(), default = false, key = 'CataclysmMoving' },
            { type = 'checkbox', text = ' Rain of Fire when target is moving', icon = S.RainofFire:ID(), default = false, key = 'RainofFireMoving' },
            { type = 'checkbox', text = ' Summon Infernal when target is moving', icon = S.SummonInfernal:ID(), default = true, key = 'SummonInfernalMoving' },
            { type = 'spacer' },
            { type = 'header', text = 'Defensives', color = Config_Color },
            { type = 'checkspin', text = 'Unending Resolve', key = 'UnendingResolve', min = 1, max = 99, default_spin = 30, default_check = true },
            { type = 'checkspin', text = 'Dark Pact', key = 'dpact', min = 1, max = 99, default_spin = 50, default_check = true },
            { type = 'checkspin', text = 'Mortal Coil (Healing)', key = 'MortalCoil', min = 1, max = 99, default_spin = 40, default_check = false },
            { type = 'checkspin', text = 'Drain Life - Group', key = 'DrainLifeGroup', min = 1, max = 99, default_spin = 20, default_check = false },
            { type = 'checkspin', text = 'Drain Life - Solo', key = 'DrainLifeSolo', min = 1, max = 99, default_spin = 55, default_check = false },
            { type = 'spacer' },
            { type = 'header', text = ' Utilities', color = Config_Color },
            { type = 'checkbox', text = ' Auto Cancel Burning Rush when no movement', icon = S.BurningRush:ID(), key = 'auto_cancel_br', default = true },
            { type = 'dropdown',
                text = 'Summon Pet (Raid / Dungeon)', key = 'PetSummon',
                multiselect = false,
                list = {
                    { text = 'Felhunter', key = 'PetF' },
                    { text = 'Imp', key = 'PetI' },
                    { text = 'Voidwalker', key = 'PetV' },
                    { text = 'Sayaad / Succubus', key = 'PetS' },
                    { text = 'None', key = 'PetNone' },
                },
                default = 'PetF',
            },
            { type = 'dropdown',
            text = 'Summon Pet (Outdoors)', key = 'PetSummonOutdoor',
            multiselect = false,
            list = {
                { text = 'Felhunter', key = 'PetF' },
                { text = 'Imp', key = 'PetI' },
                { text = 'Voidwalker', key = 'PetV' },
                { text = 'Sayaad / Succubus', key = 'PetS' },
                { text = 'None', key = 'PetNone' },
            },
            default = 'PetF',
            },
            { type = 'dropdown',
                text = 'Fel Domination', key = 'fel_domination',
                multiselect = true,
                list = {
                    { text = 'In Combat', key = 1 },
                    { text = 'Out of Combat', key = 2 },
                },
                default = {
                    1, 2
                },
            },
            { type = 'spacer' },
            { type = 'dropdown',
                text = ' Soulstone', key = 'autorebirth',
                multiselect = true,
                icon = S.Soulstone:ID(),
                list = {
                    { text = 'Target', key = 'autorebirth_target' },
                    { text = 'MouseOver', key = 'autorebirth_mouseover' },
                },
                default = {
                },
            },
            { type = 'spacer' },
            {
                type = "checkbox",
                text = "Cast Groundspells only when MouseOver enemies or tank.",
                key = "MOOption",
                default = false
            },
            { type = 'checkbox', text = ' Magic Groundspell - Cataclysm', icon = S.Cataclysm:ID(), key = 'magicgroundspell_cataclysm', default = false },
            { type = 'checkbox', text = ' Magic Groundspell - Summon Infernal', icon = S.SummonInfernal:ID(), key = 'magicgroundspell_inf', default = false },
            { type = 'checkbox', text = ' Magic Groundspell - Rain of Fire', icon = S.RainofFire:ID(), key = 'magicgroundspell_rof', default = false },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
      }
  }
  Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
  Config_Table.config = MainAddon.BuildDPSTrinketUI(Config_Table.config, Config_Color)
  Config_Table.config = MainAddon.BuildCombatPotionUI(Config_Table.config, "Destruction", Config_Color)
  MainAddon.SetConfig(267, Config_Table)

    --- ===== Start Custom =====
    local InCombat
    local TargetIsValid
    local IsStandingStill = false
    local IsFallingvar = false
    local PetInMeleeRangeOfTarget = false
    local TimeStampFalling = GetTime()
    local MOCheck = false
    local ImmoWither = (S.Wither:IsAvailable()) and S.Wither or S.Immolate
    local VarDRSum = 0
    local magicgroundspell_cataclysm = GetSetting('magicgroundspell_cataclysm', false)
    local magicgroundspell_inf = GetSetting('magicgroundspell_inf', false)
    local magicgroundspell_rof = GetSetting('magicgroundspell_rof', false)

    local function UpdateGeneric()
        if ImmoWither then
            ImmoWither:SetGeneric(WARLOCK_DESTRUCTION_SPECID, 'Generic1')
        end
    end

    HL:RegisterForEvent(function()
        ImmoWither = (S.Wither:IsAvailable()) and S.Wither or S.Immolate
        UpdateGeneric()
    end, "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")

    ---@param TargetUnit Unit
    local function EvaluateTargetIfHavoc2(TargetUnit)
        return (TargetUnit:TimeToDie() > 8 and not Target:IsUnit(TargetUnit))
    end

    local function Defensives()
        if S.UnendingResolve:IsReady(Player) and GetSetting('UnendingResolve_check', true) and (Player:HealthPercentage() < GetSetting('UnendingResolve_spin', 30)) then
            if Cast(S.UnendingResolve, true) then
                return "Unending Resolve defensive"
            end
        end

        if S.DarkPact:IsReady(Player) and GetSetting('dpact_check', true) and (Player:HealthPercentage() < GetSetting('dpact_spin', 50)) then
            if Cast(S.DarkPact, true) then
                return "Dark Pact defensive"
            end
        end

        -- Mortal Coil
        if S.MortalCoil:IsReady() and GetSetting('MortalCoil_check', true) and (Player:HealthPercentage() < GetSetting('MortalCoil_spin', 30)) then
            if Cast(S.MortalCoil) then
                return "Mortal Coil defensive"
            end
        end

        -- Drain Life
        if Player:IsInSoloMode() then
            if GetSetting('DrainLifeSolo_check', true) and Player:HealthPercentage() < GetSetting('DrainLifeSolo_spin', 30) then
                if S.DrainLife:IsReady() then
                    if Cast(S.DrainLife) then
                        return "Drain Life solo";
                    end
                end
            end
        else
            if GetSetting('DrainLifeGroup_check', true) and Player:HealthPercentage() < GetSetting('DrainLifeGroup_spin', 30) then
                if S.DrainLife:IsReady() then
                    if Cast(S.DrainLife) then
                        return "Drain Life group";
                    end
                end
            end
        end
    end

    local function Utilities()
        -- Summoning Pet
        if IsStandingStill and GetTime() > TimeStampFalling + 1 then
            if (not Pet:IsActive() or Pet:IsDeadOrGhost()) then
                if S.FelDomination:IsReady(Player) and Player:DebuffDown(S.FelDomination) then
                    if Player:AffectingCombat() and GetSetting('fel_domination', {})[1] or not Player:AffectingCombat() and GetSetting('fel_domination', {})[2] then
                        if Cast(S.FelDomination, true) then
                            return "FelDomination in combat ";
                        end
                    end
                end
                local SummonPet
                if Player:IsInDungeonArea() or Player:IsInRaidArea() then
                    SummonPet = GetSetting('PetSummon', "PetF")
                else
                    SummonPet = GetSetting('PetSummonOutdoor', "PetF")
                end
                if SummonPet == "PetF" and S.PetFelhunter:IsReady(Player) and not IsCurrentSpell(691) then
                    if Cast(S.PetFelhunter) then
                        return "summon_pet ooc";
                    end
                end
                if SummonPet == "PetS" and S.PetSuccubus:IsReady(Player) and not IsCurrentSpell(366222) then
                    if Cast(S.PetSuccubus) then
                        return "summon_pet ooc";
                    end
                end
                if SummonPet == "PetV" and S.PetVoidwalker:IsReady(Player) and not IsCurrentSpell(697) then
                    if Cast(S.PetVoidwalker) then
                        return "summon_pet ooc";
                    end
                end
                if SummonPet == "PetI" and S.PetImp:IsReady(Player) and not IsCurrentSpell(688) then
                    if Cast(S.PetImp) then
                        return "summon_pet ooc";
                    end
                end
            end
        end

        if Player:AffectingCombat() then
            local autorebith = GetSetting('autorebirth', {})
            if S.Soulstone:IsReady(MouseOver) and autorebith['autorebirth_mouseover'] then
                local GetMouseFociCache = GetMouseFoci()
                ---@class Frame
                local MouseFocus = GetMouseFociCache[1]

                local FrameName = MouseFocus and not MouseFocus:IsForbidden() and MouseFocus:GetName() or "None"
                if FrameName ~= "WorldFrame" and FrameName ~= "None" then
                    if MouseOver:EvaluateRebirth() then
                        if Cast(S.Soulstone) then
                            MainAddon.UI:ShowToast("Soulstone", MouseOver:Name(), MainAddon.GetTexture(S.Soulstone))
                            return "Soulstone MouseOver"
                        end
                    end
                end
            end
            if S.Soulstone:IsReady() and autorebith['autorebirth_target'] then
                if Target:EvaluateRebirth() then
                    if Cast(S.Soulstone) then
                        MainAddon.UI:ShowToast("Soulstone", Target:Name(), MainAddon.GetTexture(S.Soulstone))
                        return "Soulstone Target"
                    end
                end
            end
        end
    end
    --- ===== Stop Custom =====

    --- ===== Rotation Variables =====
    local VarCustomSoulShardThreshold = 4.1  -- Global threshold for soul shard comparisons (was 4.5, reduced to 4 to prevent capping)
    local VarAllowRoF2TSpender = 2
    local VarDoRoF2T = VarAllowRoF2TSpender > 1.99 and not (S.Cataclysm:IsAvailable() and S.ImprovedChaosBolt:IsAvailable())
    local VarDisableCB2T = VarDoRoF2T or VarAllowRoF2TSpender > 0.01 and VarAllowRoF2TSpender < 0.99
    local VarPoolSoulShards = false
    local VarHavocActive, VarHavocRemains = false, 0
    local VarHavocImmoTime = 0
    local VarPoolingCondition = false
    local VarPoolingConditionCB = false
    local VarInfernalActive = false
    local VarT1WillLoseCast, VarT2WillLoseCast = false, false
    local SoulShards = 0
    local Enemies40y, EnemiesCount8ySplash
    local BossFightRemains = 11111
    local FightRemains = 11111

    --- ===== Trinket Variables =====
    local Trinket1, Trinket2
    local VarTrinket1ID, VarTrinket2ID
    local VarTrinket1Spell, VarTrinket2Spell
    local VarTrinket1Range, VarTrinket2Range
    local VarTrinket1CastTime, VarTrinket2CastTime
    local VarTrinket1CD, VarTrinket2CD
    local VarTrinket1Ex, VarTrinket2Ex
    local VarTrinket1Buffs, VarTrinket2Buffs
    local VarTrinket1Sync, VarTrinket2Sync
    local VarTrinket1Manual, VarTrinket2Manual
    local VarTrinket1Exclude, VarTrinket2Exclude
    local VarTrinket1BuffDuration, VarTrinket2BuffDuration
    local VarTrinketPriority
    local VarTrinketFailures = 0
    local function SetTrinketVariables()
    local T1, T2 = Player:GetTrinketData(OnUseExcludes)

    -- If we don't have trinket items, try again in 5 seconds.
    if VarTrinketFailures < 5 and ((T1.ID == 0 or T2.ID == 0) or (T1.SpellID > 0 and not T1.Usable or T2.SpellID > 0 and not T2.Usable)) then
        VarTrinketFailures = VarTrinketFailures + 1
        Delay(5, function()
            SetTrinketVariables()
        end
        )
        return
    end

    Trinket1 = T1.Object
    Trinket2 = T2.Object

    VarTrinket1ID = T1.ID
    VarTrinket2ID = T2.ID

    VarTrinket1Spell = T1.Spell
    VarTrinket1Range = T1.Range
    VarTrinket1CastTime = T1.CastTime
    VarTrinket2Spell = T2.Spell
    VarTrinket2Range = T2.Range
    VarTrinket2CastTime = T2.CastTime

    VarTrinket1CD = T1.Cooldown
    VarTrinket2CD = T2.Cooldown

    VarTrinket1Ex = T1.Excluded
    VarTrinket2Ex = T2.Excluded

    VarTrinket1Buffs = Trinket1:HasUseBuff() or VarTrinket1ID == I.FunhouseLens:ID()
    VarTrinket2Buffs = Trinket2:HasUseBuff() or VarTrinket2ID == I.FunhouseLens:ID()
 
    VarTrinket1Sync = 0.5
    if VarTrinket1Buffs and (VarTrinket1CD % 120 == 0 or 120 % VarTrinket1CD == 0) then
        VarTrinket1Sync = 1
    end
    VarTrinket2Sync = 0.5
    if VarTrinket2Buffs and (VarTrinket2CD % 120 == 0 or 120 % VarTrinket2CD == 0) then
        VarTrinket2Sync = 1
    end

    VarTrinket1Manual = VarTrinket1ID == I.SpymastersWeb:ID()
    VarTrinket2Manual = VarTrinket2ID == I.SpymastersWeb:ID()

    VarTrinket1Exclude = VarTrinket1ID == 194301
    VarTrinket2Exclude = VarTrinket2ID == 194301

    if VarTrinket1ID == I.FunhouseLens:ID() then
        VarTrinket1BuffDuration = 15
    elseif VarTrinket1ID == I.SignetofthePriory:ID() then
        VarTrinket1BuffDuration = 20
    else
        VarTrinket1BuffDuration = Trinket1:BuffDuration()
    end
    if VarTrinket2ID == I.FunhouseLens:ID() then
        VarTrinket2BuffDuration = 15
    elseif VarTrinket2ID == I.SignetofthePriory:ID() then
        VarTrinket2BuffDuration = 20
    else
        VarTrinket2BuffDuration = Trinket2:BuffDuration()
    end

    -- Note: If buff duration is 0, set to 1 to avoid divide by zero errors below.
    local T1BuffDur = (VarTrinket1BuffDuration > 0) and VarTrinket1BuffDuration or 1
    local T2BuffDur = (VarTrinket2BuffDuration > 0) and VarTrinket2BuffDuration or 1
    VarTrinketPriority = 1
    if not VarTrinket1Buffs and VarTrinket2Buffs or VarTrinket2Buffs and ((VarTrinket2CD / T2BuffDur) * (VarTrinket2Sync)) > ((VarTrinket1CD / T1BuffDur) * (VarTrinket1Sync)) then
        VarTrinketPriority = 2
    end
    end
    SetTrinketVariables()

    --- ===== Event Registrations =====
    HL:RegisterForEvent(function()
        SetTrinketVariables()
    end, "PLAYER_EQUIPMENT_CHANGED")
    
    HL:RegisterForEvent(function()
        VarAllowRoF2TSpender = 2
        VarDoRoF2T = VarAllowRoF2TSpender > 1.99 and not (S.Cataclysm:IsAvailable() and S.ImprovedChaosBolt:IsAvailable())
        VarDisableCB2T = VarDoRoF2T or VarAllowRoF2TSpender > 0.01 and VarAllowRoF2TSpender < 0.99
        VarPoolSoulShards = false
        VarHavocActive, VarHavocRemains = false, 0
        VarHavocImmoTime = 0
        VarPoolingCondition = false
        VarPoolingConditionCB = false
        VarInfernalActive = false
        VarT1WillLoseCast, VarT2WillLoseCast = false, false
        BossFightRemains = 11111
        FightRemains = 11111
    end, "PLAYER_REGEN_ENABLED")
    
    HL:RegisterForEvent(function()
        S.ChaosBolt:RegisterInFlight()
        S.Incinerate:RegisterInFlight()
        S.SoulFire:RegisterInFlight()
        S.SummonInfernal:RegisterInFlight()
    end, "SPELLS_CHANGED", "LEARNED_SPELL_IN_TAB")
    S.ChaosBolt:RegisterInFlight()
    S.Incinerate:RegisterInFlight()
    S.SoulFire:RegisterInFlight()
    S.SummonInfernal:RegisterInFlight()
    
    --- ===== Helper Functions =====
    local function UnitWithHavoc(enemies)
        for k in pairs(enemies) do
          local CycleUnit = enemies[k]
          if CycleUnit:DebuffUp(S.Havoc) then
            return true, CycleUnit:DebuffRemains(S.HavocDebuff)
          end
        end
        return false, 0, 0
      end
    
    local function ChannelDemonfireCastTime()
        return 3 * Player:SpellHaste() * (S.DemonfireMastery:IsAvailable() and 0.65 or 1)
    end
  
    local function DemonicArt()
        return Player:BuffUp(S.DemonicArtOverlordBuff) or Player:BuffUp(S.DemonicArtMotherBuff) or Player:BuffUp(S.DemonicArtPitLordBuff)
    end

    local function DiabolicRitual()
        return Player:BuffUp(S.DiabolicRitualOverlordBuff) or Player:BuffUp(S.DiabolicRitualMotherBuff) or Player:BuffUp(S.DiabolicRitualPitLordBuff)
    end
    
    local function InfernalActive()
        return Warlock.GuardiansTable.InfernalDuration > 0
    end

    local function InfernalTime()
        return Warlock.GuardiansTable.InfernalDuration or (S.SummonInfernal:InFlight() and 30) or 0
    end

    local function OverfiendActive()
        return Warlock.GuardiansTable.OverfiendDuration > 0
    end

    local function OverfiendTime()
        return Warlock.GuardiansTable.OverfiendDuration or 0
    end

    --- ===== CastTargetIf Filter Functions =====
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterHavoc(TargetUnit)
        -- target_if=min:((-target.time_to_die)<?-15)+dot.immolate.remains+99*(self.target=target)
        return max(TargetUnit:TimeToDie() * -1, -15) + TargetUnit:DebuffRemains(S.ImmolateDebuff) + 99 * num(TargetUnit:GUID() == Target:GUID())
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterImmolate(TargetUnit)
        -- target_if=min:dot.immolate.remains+99*debuff.havoc.remains
        return TargetUnit:DebuffRemains(S.ImmolateDebuff) + 99 * TargetUnit:DebuffRemains(S.HavocDebuff)
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterTTD(TargetUnit)
        -- target_if=min:time_to_die
        return TargetUnit:TimeToDie()
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterWitherRemains(TargetUnit)
        -- target_if=min:dot.wither.remains+99*debuff.havoc.remains+99*!dot.wither.ticking
        return TargetUnit:DebuffRemains(S.WitherDebuff) + 99 * num(TargetUnit:DebuffUp(S.HavocDebuff)) + 99 * num(TargetUnit:DebuffDown(S.WitherDebuff))
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterWitherRemains2(TargetUnit)
        -- target_if=min:dot.wither.remains+99*debuff.havoc.remains
        return TargetUnit:DebuffRemains(S.WitherDebuff) + 99 * num(TargetUnit:DebuffUp(S.HavocDebuff))
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterWitherRemains3(TargetUnit)
        -- target_if=min:dot.wither.remains+dot.immolate.remains-5*debuff.conflagrate.up+100*debuff.havoc.remains
        return TargetUnit:DebuffRemains(S.WitherDebuff) + TargetUnit:DebuffRemains(S.ImmolateDebuff) - 5 * num(TargetUnit:DebuffUp(S.ConflagrateDebuff)) + 100 * num(TargetUnit:DebuffUp(S.HavocDebuff))
    end
    
    --- ===== CastTargetIf Condition Functions =====
    ---@param TargetUnit Unit
    local function EvaluateTargetIfHavoc(TargetUnit)
        -- if=(!cooldown.summon_infernal.up|!talent.summon_infernal)&target.time_to_die>8
        -- if=(!cooldown.summon_infernal.up|!talent.summon_infernal|(talent.inferno&active_enemies>4))&target.time_to_die>8
        -- Note: For both lines, all but time_to_die is handled before CastTargetIf
        return TargetUnit:TimeToDie() > 8
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfImmolateAoE(TargetUnit)
        -- if=dot.immolate.refreshable&(!talent.cataclysm.enabled|cooldown.cataclysm.remains>dot.immolate.remains)&(!(talent.raging_demonfire&talent.channel_demonfire)|cooldown.channel_demonfire.remains>remains|time<5)&(active_dot.immolate<=6&!(talent.diabolic_ritual&talent.inferno)|active_dot.immolate<=4)&target.time_to_die>18
        -- Note: active_dot.immolate handled before CastCycle
        return TargetUnit:DebuffRefreshable(S.ImmolateDebuff) and (not S.Cataclysm:IsAvailable() or S.Cataclysm:CooldownRemains() > TargetUnit:DebuffRemains(S.ImmolateDebuff)) and (not (S.RagingDemonfire:IsAvailable() and S.ChannelDemonfire:IsAvailable()) or S.ChannelDemonfire:CooldownRemains() > TargetUnit:DebuffRemains(S.ImmolateDebuff) or HL.CombatTime() < 5) and TargetUnit:TimeToDie() > 18
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfImmolateAoE2(TargetUnit)
        -- if=((dot.immolate.refreshable&(!talent.cataclysm.enabled|cooldown.cataclysm.remains>dot.immolate.remains))|active_enemies>active_dot.immolate)&target.time_to_die>10&!havoc_active&!(talent.diabolic_ritual&talent.inferno)
        return ((TargetUnit:DebuffRefreshable(S.ImmolateDebuff) and (not S.Cataclysm:IsAvailable() or S.Cataclysm:CooldownRemains() > TargetUnit:DebuffRemains(S.ImmolateDebuff))) or EnemiesCount8ySplash > S.ImmolateDebuff:AuraActiveCount()) and TargetUnit:TimeToDie() > 10 and not VarHavocActive and not (S.DiabolicRitual:IsAvailable() and S.Inferno:IsAvailable())
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfImmolateAoE3(TargetUnit)
        -- if=((dot.immolate.refreshable&variable.havoc_immo_time<5.4)|(dot.immolate.remains<2&dot.immolate.remains<havoc_remains)|!dot.immolate.ticking|(variable.havoc_immo_time<2)*havoc_active)&(!talent.cataclysm.enabled|cooldown.cataclysm.remains>dot.immolate.remains)&target.time_to_die>11&!(talent.diabolic_ritual&talent.inferno)
        return ((TargetUnit:DebuffRefreshable(S.ImmolateDebuff) and VarHavocImmoTime < 5.4) or (TargetUnit:DebuffRemains(S.ImmolateDebuff) < 2 and TargetUnit:DebuffRemains(S.ImmolateDebuff) < VarHavocRemains) or TargetUnit:DebuffDown(S.ImmolateDebuff) or bool(num(VarHavocImmoTime < 2) * num(VarHavocActive))) and (not S.Cataclysm:IsAvailable() or S.Cataclysm:CooldownRemains() > TargetUnit:DebuffRemains(S.ImmolateDebuff)) and TargetUnit:TimeToDie() > 11 and not (S.DiabolicRitual:IsAvailable() and S.Inferno:IsAvailable())
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfImmolateCleave(TargetUnit)
        -- if=(dot.immolate.refreshable&(dot.immolate.remains<cooldown.havoc.remains|!dot.immolate.ticking))&(!talent.cataclysm|cooldown.cataclysm.remains>remains)&(!talent.soul_fire|cooldown.soul_fire.remains+(!talent.mayhem*action.soul_fire.cast_time)>dot.immolate.remains)&target.time_to_die>15
        return (TargetUnit:DebuffRefreshable(S.ImmolateDebuff) and (TargetUnit:DebuffRemains(S.ImmolateDebuff) < S.Havoc:CooldownRemains() or TargetUnit:DebuffDown(S.ImmolateDebuff))) and (not S.Cataclysm:IsAvailable() or S.Cataclysm:CooldownRemains() > TargetUnit:DebuffRemains(S.ImmolateDebuff)) and (not S.SoulFire:IsAvailable() or S.SoulFire:CooldownRemains() + (num(not S.Mayhem:IsAvailable()) * S.SoulFire:CastTime()) > TargetUnit:DebuffRemains(S.ImmolateDebuff)) and TargetUnit:TimeToDie() > 15
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfImmolateHavoc(TargetUnit)
        -- if=(((dot.immolate.refreshable&variable.havoc_immo_time<5.4)&target.time_to_die>5)|((dot.immolate.remains<2&dot.immolate.remains<havoc_remains)|!dot.immolate.ticking|variable.havoc_immo_time<2)&target.time_to_die>11)&soul_shard<4.5
        -- Note: Soul Shard check handled before CastTargetIf call.
        return ((TargetUnit:DebuffRefreshable(S.ImmolateDebuff) and VarHavocImmoTime < 5.4) and TargetUnit:TimeToDie() > 5) or ((TargetUnit:DebuffRemains(S.ImmolateDebuff) < 2 and TargetUnit:DebuffRemains(S.ImmolateDebuff) < VarHavocRemains) or TargetUnit:DebuffDown(S.ImmolateDebuff) or VarHavocImmoTime < 2) and TargetUnit:TimeToDie() > 11
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfShadowburn(TargetUnit)
        -- if=((buff.malevolence.up&((talent.cataclysm&talent.raging_demonfire&active_enemies<=10&fight_remains>=60)|(talent.cataclysm&!talent.raging_demonfire&active_enemies<=8&fight_remains>=60)|active_enemies<=5))|(!talent.wither&talent.cataclysm&active_enemies<=5)|active_enemies<=3)&((cooldown.shadowburn.full_recharge_time<=gcd.max*3|debuff.eradication.remains<=gcd.max&talent.eradication&!action.chaos_bolt.in_flight&!talent.diabolic_ritual)&(talent.conflagration_of_chaos|talent.blistering_atrophy)&time_to_die<5|fight_remains<=8)
        -- Note: First half handled before CastTargetIf.
        return (S.Shadowburn:FullRechargeTime() <= Player:GCD() * 3 or TargetUnit:DebuffRemains(S.EradicationDebuff) <= Player:GCD() and S.Eradication:IsAvailable() and not S.ChaosBolt:InFlight() and not S.DiabolicRitual:IsAvailable()) and (S.ConflagrationofChaos:IsAvailable() or S.BlisteringAtrophy:IsAvailable()) and TargetUnit:TimeToDie() < 5 or BossFightRemains <= 8
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfWitherAoE(TargetUnit)
        -- if=dot.wither.refreshable&(!talent.cataclysm.enabled|cooldown.cataclysm.remains>dot.wither.remains)&(!(talent.raging_demonfire&talent.channel_demonfire)|cooldown.channel_demonfire.remains>remains|time<5)&(active_dot.wither<=4|time>15)&target.time_to_die>18
        -- Note: Wither count performed before CastTargetIf.
        return TargetUnit:DebuffRefreshable(S.WitherDebuff) and (not S.Cataclysm:IsAvailable() or S.Cataclysm:CooldownRemains() > TargetUnit:DebuffRemains(S.WitherDebuff)) and (not (S.RagingDemonfire:IsAvailable() and S.ChannelDemonfire:IsAvailable()) or S.ChannelDemonfire:CooldownRemains() > TargetUnit:DebuffRemains(S.WitherDebuff) or HL.CombatTime() < 5) and TargetUnit:TimeToDie() > 18
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfWitherAoE2(TargetUnit)
        -- if=dot.wither.refreshable&(!talent.cataclysm.enabled|cooldown.cataclysm.remains>dot.wither.remains)&(!(talent.raging_demonfire&talent.channel_demonfire)|cooldown.channel_demonfire.remains>remains|time<5)&active_dot.wither<=active_enemies&target.time_to_die>18
        -- Note: Checked active_dot.wither<=active_enemies prior to CastTargetIf.
        return TargetUnit:DebuffRefreshable(S.WitherDebuff) and (not S.Cataclysm:IsAvailable() or S.Cataclysm:CooldownRemains() > TargetUnit:DebuffRemains(S.WitherDebuff)) and (not (S.RagingDemonfire:IsAvailable() and S.ChannelDemonfire:IsAvailable()) or S.ChannelDemonfire:CooldownRemains() > TargetUnit:DebuffRemains(S.WitherDebuff) or HL.CombatTime() < 5) and TargetUnit:TimeToDie() > 18
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfWitherCleave(TargetUnit)
        -- if=talent.internal_combustion&(((dot.wither.remains-5*action.chaos_bolt.in_flight)<dot.wither.duration*0.4)|dot.wither.remains<3|(dot.wither.remains-action.chaos_bolt.execute_time)<5&action.chaos_bolt.usable)&(!talent.soul_fire|cooldown.soul_fire.remains+action.soul_fire.cast_time>(dot.wither.remains-5))&target.time_to_die>8&!action.soul_fire.in_flight_to_target
        -- Note: Checked internal_combustion and soul_fire.in_flight_to_target before CastTargetIf.
        return (((TargetUnit:DebuffRemains(S.WitherDebuff) - 5 * num(S.ChaosBolt:InFlight())) < S.WitherDebuff:MaxDuration() * 0.4) or TargetUnit:DebuffRemains(S.WitherDebuff) < 3 or (TargetUnit:DebuffRemains(S.WitherDebuff) - S.ChaosBolt:ExecuteTime()) < 5 and S.ChaosBolt:IsReady()) and (not S.SoulFire:IsAvailable() or S.SoulFire:CooldownRemains() + S.SoulFire:CastTime() > (TargetUnit:DebuffRemains(S.WitherDebuff) - 5)) and TargetUnit:TimeToDie() > 8
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfWitherCleave2(TargetUnit)
        -- if=!talent.internal_combustion&(((dot.wither.remains-5*(action.chaos_bolt.in_flight))<dot.wither.duration*0.3)|dot.wither.remains<3)&(!talent.soul_fire|cooldown.soul_fire.remains+action.soul_fire.cast_time>(dot.wither.remains))&target.time_to_die>8&!action.soul_fire.in_flight_to_target
        -- Note: Checked internal_combustion and soul_fire.in_flight_to_target before CastTargetIf.
        return (((TargetUnit:DebuffRemains(S.WitherDebuff) - 5 * num(S.ChaosBolt:InFlight())) < S.WitherDebuff:PandemicThreshold()) or TargetUnit:DebuffRemains(S.WitherDebuff) < 3) and (not S.SoulFire:IsAvailable() or S.SoulFire:CooldownRemains() + S.SoulFire:CastTime() > (TargetUnit:DebuffRemains(S.WitherDebuff))) and TargetUnit:TimeToDie() > 8
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfWitherHavoc(TargetUnit)
        -- if=(((dot.wither.refreshable&variable.havoc_immo_time<5.4)&target.time_to_die>5)|((dot.wither.remains<2&dot.wither.remains<havoc_remains)|!dot.wither.ticking|variable.havoc_immo_time<2)&target.time_to_die>11)&soul_shard<4.5
        return ((TargetUnit:DebuffRefreshable(S.WitherDebuff) and VarHavocImmoTime < 5.4) and TargetUnit:TimeToDie() > 5) or ((TargetUnit:DebuffRemains(S.WitherDebuff) < 2 and TargetUnit:DebuffRemains(S.WitherDebuff) < VarHavocRemains) or TargetUnit:DebuffDown(S.WitherDebuff) or VarHavocImmoTime < 2) and TargetUnit:TimeToDie() > 11
    end

    local function Precombat()
        -- summon_pet
        -- Moved to APL()
        -- variable,name=cleave_apl,default=0,op=reset
        VarCleaveAPL = false
        -- variable,name=trinket_1_buffs,value=trinket.1.has_use_buff|trinket.1.is.funhouse_lens
        -- variable,name=trinket_2_buffs,value=trinket.2.has_use_buff|trinket.2.is.funhouse_lens
        -- variable,name=trinket_1_sync,op=setif,value=1,value_else=0.5,condition=variable.trinket_1_buffs&(trinket.1.cooldown.duration%%cooldown.summon_infernal.duration=0|cooldown.summon_infernal.duration%%trinket.1.cooldown.duration=0)
        -- variable,name=trinket_2_sync,op=setif,value=1,value_else=0.5,condition=variable.trinket_2_buffs&(trinket.2.cooldown.duration%%cooldown.summon_infernal.duration=0|cooldown.summon_infernal.duration%%trinket.2.cooldown.duration=0)
        -- variable,name=trinket_1_manual,value=trinket.1.is.spymasters_web
        -- variable,name=trinket_2_manual,value=trinket.2.is.spymasters_web
        -- variable,name=trinket_1_exclude,value=trinket.1.is.whispering_incarnate_icon
        -- variable,name=trinket_2_exclude,value=trinket.2.is.whispering_incarnate_icon
        -- variable,name=trinket_1_buff_duration,value=trinket.1.proc.any_dps.duration+(trinket.1.is.funhouse_lens*15)+(trinket.1.is.signet_of_the_priory*20)
        -- variable,name=trinket_2_buff_duration,value=trinket.2.proc.any_dps.duration+(trinket.2.is.funhouse_lens*15)+(trinket.2.is.signet_of_the_priory*20)
        -- variable,name=trinket_priority,op=setif,value=2,value_else=1,condition=!variable.trinket_1_buffs&variable.trinket_2_buffs|variable.trinket_2_buffs&((trinket.2.cooldown.duration%variable.trinket_2_buff_duration)*(1+0.5*trinket.2.has_buff.intellect)*(variable.trinket_2_sync))>((trinket.1.cooldown.duration%variable.trinket_1_buff_duration)*(1+0.5*trinket.1.has_buff.intellect)*(variable.trinket_1_sync))
        -- Note: Trinket variables moved to variable declarations and PLAYER_EQUIPMENT_CHANGED registration.
        -- variable,name=allow_rof_2t_spender,default=2,op=reset
        -- variable,name=do_rof_2t,value=variable.allow_rof_2t_spender>1.99&!(talent.cataclysm&talent.improved_chaos_bolt),op=set
        -- variable,name=disable_cb_2t,value=variable.do_rof_2t|variable.allow_rof_2t_spender>0.01&variable.allow_rof_2t_spender<0.99
        -- grimoire_of_sacrifice,if=talent.grimoire_of_sacrifice.enabled
        if S.GrimoireofSacrifice:IsReady() then
            if Cast(S.GrimoireofSacrifice) then
                return "grimoire_of_sacrifice precombat 2";
            end
        end
        -- snapshot_stats
        -- soul_fire
        if S.SoulFire:IsReady() and (not Player:IsCasting(S.SoulFire)) then
            if Cast(S.SoulFire) then
                return "soul_fire precombat 4";
            end
        end
        -- incinerate
        if S.Incinerate:IsReady() and (not Player:IsCasting(S.Incinerate)) then
            if Cast(S.Incinerate) then
                return "incinerate precombat 8";
            end
        end
    end

    local function Items()
        -- use_item,name=spymasters_web,if=pet.infernal.remains>=10&pet.infernal.remains<=20&buff.spymasters_report.stack>=38&(fight_remains>240|fight_remains<=140)|fight_remains<=30
        if I.SpymastersWeb:IsEquippedAndReady() and (InfernalTime() >= 10 and InfernalTime() <= 20 and Player:BuffStack(S.SpymastersReportBuff) >= 38 and (FightRemains > 240 or FightRemains <= 140) or FightRemains <= 30) then
        if Cast(I.SpymastersWeb) then return "spymasters_web items 2"; end
        end
        -- use_item,slot=trinket1,if=(variable.infernal_active|!talent.summon_infernal|variable.trinket_1_will_lose_cast)&(variable.trinket_priority=1|variable.trinket_2_exclude|!trinket.2.has_cooldown|(trinket.2.cooldown.remains|variable.trinket_priority=2&cooldown.summon_infernal.remains>20&!variable.infernal_active&trinket.2.cooldown.remains<cooldown.summon_infernal.remains))&variable.trinket_1_buffs&!variable.trinket_1_manual|(variable.trinket_1_buff_duration+1>=fight_remains)
        if Trinket1 and Trinket1:IsReady() and not VarTrinket1Ex and not Player:IsItemBlacklisted(Trinket1) and ((VarInfernalActive or not S.SummonInfernal:IsAvailable() or VarT1WillLoseCast) and (VarTrinketPriority == 1 or VarTrinket2Exclude or not Trinket2:HasCooldown() or (Trinket2:CooldownDown() or VarTrinketPriority == 2 and S.SummonInfernal:CooldownRemains() > 20 and not VarInfernalActive and Trinket2:CooldownRemains() < S.SummonInfernal:CooldownRemains())) and VarTrinket1Buffs and not VarTrinket1Manual or (VarTrinket1BuffDuration + 1 >= BossFightRemains)) then
        if Cast(Trinket1) then return "trinket1 (" .. Trinket1:Name() .. ") items 4"; end
        end
        -- use_item,slot=trinket2,if=(variable.infernal_active|!talent.summon_infernal|variable.trinket_2_will_lose_cast)&(variable.trinket_priority=2|variable.trinket_1_exclude|!trinket.1.has_cooldown|(trinket.1.cooldown.remains|variable.trinket_priority=1&cooldown.summon_infernal.remains>20&!variable.infernal_active&trinket.1.cooldown.remains<cooldown.summon_infernal.remains))&variable.trinket_2_buffs&!variable.trinket_2_manual|(variable.trinket_2_buff_duration+1>=fight_remains)
        if Trinket2 and Trinket2:IsReady() and not VarTrinket2Ex and not Player:IsItemBlacklisted(Trinket2) and ((VarInfernalActive or not S.SummonInfernal:IsAvailable() or VarT2WillLoseCast) and (VarTrinketPriority == 2 or VarTrinket1Exclude or not Trinket1:HasCooldown() or (Trinket1:CooldownDown() or VarTrinketPriority == 1 and S.SummonInfernal:CooldownRemains() > 20 and not VarInfernalActive and Trinket1:CooldownRemains() < S.SummonInfernal:CooldownRemains())) and VarTrinket2Buffs and not VarTrinket2Manual or (VarTrinket2BuffDuration + 1 >= BossFightRemains)) then
        if Cast(Trinket2) then return "trinket2 (" .. Trinket2:Name() .. ") items 6"; end
        end
        -- use_item,use_off_gcd=1,slot=trinket1,if=!variable.trinket_1_buffs&!variable.trinket_1_manual&(!variable.trinket_1_buffs&(trinket.2.cooldown.remains|!variable.trinket_2_buffs)|talent.summon_infernal&cooldown.summon_infernal.remains_expected>20&!prev_gcd.1.summon_infernal|!talent.summon_infernal)
        if Trinket1 and Trinket1:IsReady() and not VarTrinket1Ex and not Player:IsItemBlacklisted(Trinket1) and (not VarTrinket1Buffs and not VarTrinket1Manual and (not VarTrinket1Buffs and (Trinket2:CooldownDown() or not VarTrinket2Buffs) or S.SummonInfernal:IsAvailable() and S.SummonInfernal:CooldownRemains() > 20 and not Player:PrevGCDP(1, S.SummonInfernal) or not S.SummonInfernal:IsAvailable())) then
        if Cast(Trinket1) then return "trinket1 (" .. Trinket1:Name() .. ") items 8"; end
        end
        -- use_item,use_off_gcd=1,slot=trinket2,if=!variable.trinket_2_buffs&!variable.trinket_2_manual&(!variable.trinket_2_buffs&(trinket.1.cooldown.remains|!variable.trinket_1_buffs)|talent.summon_infernal&cooldown.summon_infernal.remains_expected>20&!prev_gcd.1.summon_infernal|!talent.summon_infernal)
        if Trinket2 and Trinket2:IsReady() and not VarTrinket2Ex and not Player:IsItemBlacklisted(Trinket2) and (not VarTrinket2Buffs and not VarTrinket2Manual and (not VarTrinket2Buffs and (Trinket1:CooldownDown() or not VarTrinket1Buffs) or S.SummonInfernal:IsAvailable() and S.SummonInfernal:CooldownRemains() > 20 and not Player:PrevGCDP(1, S.SummonInfernal) or not S.SummonInfernal:IsAvailable())) then
        if Cast(Trinket2) then return "trinket2 (" .. Trinket2:Name() .. ") items 10"; end
        end
        -- use_item,use_off_gcd=1,slot=main_hand
        -- Note: Including all non-trinket items
        local ItemToUse, _, ItemRange = Player:GetUseableItems(OnUseExcludes, nil, true)
        if ItemToUse and ItemToUse:IsReady() then
        if Cast(ItemToUse) then return "non-trinket item (" .. ItemToUse:Name() .. ") items 12"; end
        end
    end

    local function oGCD()
        -- invoke_external_buff,name=power_infusion,if=variable.infernal_active|!talent.summon_infernal|(fight_remains<cooldown.summon_infernal.remains_expected+10+cooldown.invoke_power_infusion_0.duration&fight_remains>cooldown.invoke_power_infusion_0.duration)|fight_remains<cooldown.summon_infernal.remains_expected+15
        -- Note: Not handling external PI.
        -- berserking,if=variable.infernal_active|!talent.summon_infernal|(fight_remains<(cooldown.summon_infernal.remains_expected+cooldown.berserking.duration)&(fight_remains>cooldown.berserking.duration))|fight_remains<cooldown.summon_infernal.remains_expected
        if S.Berserking:IsReady() and (VarInfernalActive or not S.SummonInfernal:IsAvailable() or (FightRemains < (S.SummonInfernal:CooldownRemains() + 12) and (FightRemains > 12)) or FightRemains < S.SummonInfernal:CooldownRemains()) then
            if Cast(S.Berserking) then return "berserking ogcd 4"; end
        end
        -- blood_fury,if=variable.infernal_active|!talent.summon_infernal|(fight_remains<cooldown.summon_infernal.remains_expected+10+cooldown.blood_fury.duration&fight_remains>cooldown.blood_fury.duration)|fight_remains<cooldown.summon_infernal.remains
        if S.BloodFury:IsReady() and (VarInfernalActive or not S.SummonInfernal:IsAvailable() or (FightRemains < (S.SummonInfernal:CooldownRemains() + 10 + 15) and (FightRemains > 15)) or FightRemains < S.SummonInfernal:CooldownRemains()) then
            if Cast(S.BloodFury) then return "blood_fury ogcd 6"; end
        end
        -- fireblood,if=variable.infernal_active|!talent.summon_infernal|(fight_remains<cooldown.summon_infernal.remains_expected+10+cooldown.fireblood.duration&fight_remains>cooldown.fireblood.duration)|fight_remains<cooldown.summon_infernal.remains_expected
        if S.Fireblood:IsReady() and (VarInfernalActive or not S.SummonInfernal:IsAvailable() or (FightRemains < (S.SummonInfernal:CooldownRemains() + 10 + 8) and (FightRemains > 8)) or FightRemains < S.SummonInfernal:CooldownRemains()) then
            if Cast(S.Fireblood) then return "fireblood ogcd 8"; end
        end
        -- ancestral_call,if=variable.infernal_active|!talent.summon_infernal|(fight_remains<(cooldown.summon_infernal.remains_expected+cooldown.berserking.duration)&(fight_remains>cooldown.berserking.duration))|fight_remains<cooldown.summon_infernal.remains_expected
        -- Note: Assume they copied from berserking and actually meant to use ancestral_call durations
        if S.AncestralCall:IsReady() and (VarInfernalActive or not S.SummonInfernal:IsAvailable() or (FightRemains < (S.SummonInfernal:CooldownRemains() + 15) and (FightRemains > 15)) or FightRemains < S.SummonInfernal:CooldownRemains()) then
            if Cast(S.AncestralCall) then return "ancestral_call ogcd 10"; end
        end
    end

    local function Havoc()
        -- conflagrate,if=talent.backdraft&buff.backdraft.down&soul_shard>=1&soul_shard<=4
        if S.Conflagrate:IsReady() and (S.Backdraft:IsAvailable() and Player:BuffDown(S.BackdraftBuff) and SoulShards >= 1 and SoulShards <= 4) then
            if Cast(S.Conflagrate) then return "conflagrate havoc 2"; end
        end
        -- soul_fire,if=cast_time<havoc_remains&soul_shard<2.5
        if S.SoulFire:IsReady() and (S.SoulFire:CastTime() < VarHavocRemains and SoulShards < 2.5) then
            if Cast(S.SoulFire) then return "soul_fire havoc 4"; end
        end
        -- cataclysm,if=raid_event.adds.in>15|(talent.wither&dot.wither.remains<action.wither.duration*0.3)
        if S.Cataclysm:IsReady() then
            if CastMagic(S.Cataclysm, nil, "152108-Magic", magicgroundspell_cataclysm) then return "cataclysm havoc 8"; end
        end
        -- immolate,target_if=min:dot.immolate.remains+100*debuff.havoc.remains,if=(((dot.immolate.refreshable&variable.havoc_immo_time<5.4)&target.time_to_die>5)|((dot.immolate.remains<2&dot.immolate.remains<havoc_remains)|!dot.immolate.ticking|variable.havoc_immo_time<2)&target.time_to_die>11)&soul_shard<4.5
        if S.Immolate:IsReady() and (SoulShards < VarCustomSoulShardThreshold) then
            if CastTargetIf(S.Immolate, Enemies8ySplash, "min", EvaluateTargetIfFilterImmolate, EvaluateTargetIfImmolateHavoc) then return "immolate havoc 10"; end
        end
        -- wither,target_if=min:dot.wither.remains+100*debuff.havoc.remains,if=(((dot.wither.refreshable&variable.havoc_immo_time<5.4)&target.time_to_die>5)|((dot.wither.remains<2&dot.wither.remains<havoc_remains)|!dot.wither.ticking|variable.havoc_immo_time<2)&target.time_to_die>11)&soul_shard<4.5
        -- Note: ETIFWitherRemains2 is 99*debuff.havoc.remains. Just using that.
        if S.Wither:IsReady() and (SoulShards < VarCustomSoulShardThreshold) then
            if CastTargetIf(S.Wither, Enemies8ySplash, "min", EvaluateTargetIfFilterWitherRemains2, EvaluateTargetIfWitherHavoc) then return "wither havoc 12"; end
        end
        -- shadowburn,if=active_enemies<=4&(cooldown.shadowburn.full_recharge_time<=gcd.max*3|debuff.eradication.remains<=gcd.max&talent.eradication&!action.chaos_bolt.in_flight&!talent.diabolic_ritual)&(talent.conflagration_of_chaos|talent.blistering_atrophy)
        if S.Shadowburn:IsReady() and (EnemiesCount8ySplash <= 4 and (S.Shadowburn:FullRechargeTime() <= Player:GCD() * 3 or Target:DebuffRemains(S.EradicationDebuff) <= Player:GCD() and S.Eradication:IsAvailable() and not S.ChaosBolt:InFlight() and not S.DiabolicRitual:IsAvailable()) and (S.ConflagrationofChaos:IsAvailable() or S.BlisteringAtrophy:IsAvailable())) then
            if Cast(S.Shadowburn) then return "shadowburn havoc 14"; end
        end
        -- shadowburn,if=active_enemies<=4&havoc_remains<=gcd.max*3
        if S.Shadowburn:IsReady() and (EnemiesCount8ySplash <= 4 and VarHavocRemains <= Player:GCD() * 3) then
            if Cast(S.Shadowburn) then return "shadowburn havoc 16"; end
        end
        -- chaos_bolt,if=cast_time<havoc_remains&((!talent.improved_chaos_bolt&active_enemies<=2)|(talent.improved_chaos_bolt&((talent.wither&talent.inferno&active_enemies<=2)|(((talent.wither&talent.cataclysm)|(!talent.wither&talent.inferno))&active_enemies<=3)|(!talent.wither&talent.cataclysm&active_enemies<=5))))
        if S.ChaosBolt:IsReady() and (S.ChaosBolt:CastTime() < VarHavocRemains and ((not S.ImprovedChaosBolt:IsAvailable() and EnemiesCount8ySplash <= 2) or (S.ImprovedChaosBolt:IsAvailable() and ((S.Wither:IsAvailable() and S.Inferno:IsAvailable() and EnemiesCount8ySplash <= 2) or (((S.Wither:IsAvailable() and S.Cataclysm:IsAvailable()) or (not S.Wither:IsAvailable() and S.Inferno:IsAvailable())) and EnemiesCount8ySplash <= 3) or (not S.Wither:IsAvailable() and S.Cataclysm:IsAvailable() and EnemiesCount8ySplash <= 5))))) then
            if Cast(S.ChaosBolt) then return "chaos_bolt havoc 18"; end
        end
        -- rain_of_fire,if=active_enemies>=3
        if S.RainofFire:IsReady() and (EnemiesCount8ySplash >= 3) then
            if CastMagic(S.RainofFire, nil, "5740-Magic", magicgroundspell_rof) then return "rain_of_fire havoc 20"; end
        end
        -- channel_demonfire,if=soul_shard<4.5
        if S.ChannelDemonfire:IsReady() and (SoulShards < VarCustomSoulShardThreshold) then
            if Cast(S.ChannelDemonfire) then return "channel_demonfire havoc 22"; end
        end
        -- conflagrate,if=!talent.backdraft
        if S.Conflagrate:IsReady() and not S.Backdraft:IsAvailable() then
            if Cast(S.Conflagrate) then return "conflagrate havoc 24"; end
        end
        -- dimensional_rift,if=soul_shard<4.7&(charges>2|fight_remains<cooldown.dimensional_rift.duration)
        if S.DimensionalRift:IsReady() and (SoulShards < 4.7 and (S.DimensionalRift:Charges() > 2 or FightRemains < S.DimensionalRift:Cooldown())) then
            if Cast(S.DimensionalRift) then return "dimensional_rift havoc 26"; end
        end
        -- incinerate,if=cast_time<havoc_remains
        if S.Incinerate:IsReady() and (S.Incinerate:CastTime() < VarHavocRemains) then
            if Cast(S.Incinerate) then return "incinerate havoc 28"; end
        end
    end
      
    local function Aoe()
        -- call_action_list,name=ogcd
        local ShouldReturn = oGCD(); if ShouldReturn then return ShouldReturn; end
        -- call_action_list,name=items
        local ShouldReturn = Items(); if ShouldReturn then return ShouldReturn; end
        -- malevolence,if=cooldown.summon_infernal.remains>=55&soul_shard<4.7&(active_enemies<=3+active_dot.wither|time>30)
        if S.Malevolence:IsReady() and ((S.SummonInfernal:CooldownRemains() >= 55 or S.SummonInfernal:BlockedByUserSettings()) and SoulShards < 4.7 and (EnemiesCount8ySplash <= 3 + S.WitherDebuff:AuraActiveCount() or HL.CombatTime() > 30)) then
          if Cast(S.Malevolence) then return "malevolence aoe 2"; end
        end
        -- rain_of_fire,if=demonic_art
        if S.RainofFire:IsReady() and (DemonicArt()) then
          if CastMagic(S.RainofFire, nil, "5740-Magic", magicgroundspell_rof) then return "rain_of_fire aoe 4"; end
        end
        -- wait,sec=((buff.diabolic_ritual_mother_of_chaos.remains+buff.diabolic_ritual_overlord.remains+buff.diabolic_ritual_pit_lord.remains)),if=(diabolic_ritual&(buff.diabolic_ritual_mother_of_chaos.remains+buff.diabolic_ritual_overlord.remains+buff.diabolic_ritual_pit_lord.remains)<gcd.max*0.25)&soul_shard>2
        -- TODO: Add wait?
        -- incinerate,if=(diabolic_ritual&(buff.diabolic_ritual_mother_of_chaos.remains+buff.diabolic_ritual_overlord.remains+buff.diabolic_ritual_pit_lord.remains)<=action.incinerate.cast_time&(buff.diabolic_ritual_mother_of_chaos.remains+buff.diabolic_ritual_overlord.remains+buff.diabolic_ritual_pit_lord.remains)>gcd.max*0.25)
        if S.Incinerate:IsReady() and (DiabolicRitual() and VarDRSum <= S.Incinerate:CastTime() and VarDRSum > Player:GCD() * 0.25) then
          if Cast(S.Incinerate) then return "incinerate aoe 6"; end
        end
        -- call_action_list,name=havoc,if=havoc_active&havoc_remains>gcd.max&active_enemies<(5+!talent.wither)&(!cooldown.summon_infernal.up|!talent.summon_infernal)
        if VarHavocActive and VarHavocRemains > Player:GCD() and EnemiesCount8ySplash < (5 + num(not S.Wither:IsAvailable())) and (S.SummonInfernal:CooldownDown() or not S.SummonInfernal:IsAvailable()) then
          local ShouldReturn = Havoc(); if ShouldReturn then return ShouldReturn; end
        end
        -- dimensional_rift,if=soul_shard<4.7&(charges>2|fight_remains<cooldown.dimensional_rift.duration)
        if S.DimensionalRift:IsReady() and (SoulShards < 4.7 and (S.DimensionalRift:Charges() > 2 or FightRemains < S.DimensionalRift:Cooldown())) then
          if Cast(S.DimensionalRift) then return "dimensional_rift aoe 8"; end
        end
        -- rain_of_fire,if=!talent.inferno&soul_shard>=(4.5-0.1*(active_dot.immolate+active_dot.wither))|soul_shard>=(3.5-0.1*(active_dot.immolate+active_dot.wither))|buff.ritual_of_ruin.up
        if S.RainofFire:IsReady() and (not S.Inferno:IsAvailable() and SoulShards >= (4.5 - 0.1 * (S.ImmolateDebuff:AuraActiveCount() + S.WitherDebuff:AuraActiveCount())) or SoulShards >= (3.5 - 0.1 * (S.ImmolateDebuff:AuraActiveCount() + S.WitherDebuff:AuraActiveCount())) or Player:BuffUp(S.RitualofRuinBuff)) then
          if CastMagic(S.RainofFire, nil, "5740-Magic", magicgroundspell_rof) then return "rain_of_fire aoe 10"; end
        end
        -- wither,target_if=min:dot.wither.remains+99*debuff.havoc.remains+99*!dot.wither.ticking,if=dot.wither.refreshable&(!talent.cataclysm.enabled|cooldown.cataclysm.remains>dot.wither.remains)&(!(talent.raging_demonfire&talent.channel_demonfire)|cooldown.channel_demonfire.remains>remains|time<5)&(active_dot.wither<=4|time>15)&target.time_to_die>18
        if S.Wither:IsReady() and ((S.WitherDebuff:AuraActiveCount() <= 4 or HL.CombatTime() > 15)) then
          if CastTargetIf(S.Wither, Enemies8ySplash, "min", EvaluateTargetIfFilterWitherRemains, EvaluateTargetIfWitherAoE) then return "wither aoe 12"; end
        end
        -- channel_demonfire,if=dot.immolate.remains+dot.wither.remains>cast_time&talent.raging_demonfire
        if S.ChannelDemonfire:IsReady() and (Target:DebuffRemains(S.ImmolateDebuff) + Target:DebuffRemains(S.WitherDebuff) > ChannelDemonfireCastTime() and S.RagingDemonfire:IsAvailable()) then
          if Cast(S.ChannelDemonfire) then return "channel_demonfire aoe 14"; end
        end
        -- shadowburn,if=((buff.malevolence.up&((talent.cataclysm&active_enemies<=10)|(talent.inferno&active_enemies<=6)))|(talent.wither&talent.cataclysm&active_enemies<=6)|(!talent.wither&talent.cataclysm&active_enemies<=4)|active_enemies<=3)&((cooldown.shadowburn.full_recharge_time<=gcd.max*3|debuff.eradication.remains<=gcd.max&talent.eradication&!action.chaos_bolt.in_flight&!talent.diabolic_ritual)&(talent.conflagration_of_chaos|talent.blistering_atrophy)|fight_remains<=8)
        if S.Shadowburn:IsReady() and (((Player:BuffUp(S.MalevolenceBuff) and ((S.Cataclysm:IsAvailable() and EnemiesCount8ySplash <= 10) or (S.Inferno:IsAvailable() and EnemiesCount8ySplash <= 6))) or (S.Wither:IsAvailable() and S.Cataclysm:IsAvailable() and EnemiesCount8ySplash <= 6) or (not S.Wither:IsAvailable() and S.Cataclysm:IsAvailable() and EnemiesCount8ySplash <= 4) or EnemiesCount8ySplash <= 3) and ((S.Shadowburn:FullRechargeTime() <= Player:GCD() * 3 or Target:DebuffRemains(S.EradicationDebuff) <= Player:GCD() and S.Eradication:IsAvailable() and not S.ChaosBolt:InFlight() and not S.DiabolicRitual:IsAvailable()) and (S.ConflagrationofChaos:IsAvailable() or S.BlisteringAtrophy:IsAvailable()) or BossFightRemains <= 8)) then
          if Cast(S.Shadowburn) then return "shadowburn aoe 16"; end
        end
        -- shadowburn,target_if=min:time_to_die,if=((buff.malevolence.up&((talent.cataclysm&active_enemies<=10)|(talent.inferno&active_enemies<=6)))|(talent.wither&talent.cataclysm&active_enemies<=6)|(!talent.wither&talent.cataclysm&active_enemies<=4)|active_enemies<=3)&((cooldown.shadowburn.full_recharge_time<=gcd.max*3|debuff.eradication.remains<=gcd.max&talent.eradication&!action.chaos_bolt.in_flight&!talent.diabolic_ritual)&(talent.conflagration_of_chaos|talent.blistering_atrophy)&time_to_die<5|fight_remains<=8)
        if S.Shadowburn:IsReady() and (((Player:BuffUp(S.MalevolenceBuff) and ((S.Cataclysm:IsAvailable() and EnemiesCount8ySplash <= 10) or (S.Inferno:IsAvailable() and EnemiesCount8ySplash <= 6))) or (S.Wither:IsAvailable() and S.Cataclysm:IsAvailable() and EnemiesCount8ySplash <= 6) or (not S.Wither:IsAvailable() and S.Cataclysm:IsAvailable() and EnemiesCount8ySplash <= 4) or EnemiesCount8ySplash <= 3) and ((S.Shadowburn:FullRechargeTime() <= Player:GCD() * 3 or Target:DebuffRemains(S.EradicationDebuff) <= Player:GCD() and S.Eradication:IsAvailable() and not S.ChaosBolt:InFlight() and not S.DiabolicRitual:IsAvailable()) and (S.ConflagrationofChaos:IsAvailable() or S.BlisteringAtrophy:IsAvailable()) and Target:TimeToDie() < 5 or BossFightRemains <= 8)) then
          if CastTargetIf(S.Shadowburn, Enemies8ySplash, "min", EvaluateTargetIfFilterTTD, EvaluateTargetIfShadowburn) then return "shadowburn aoe 18"; end
        end
        -- ruination
        if S.RuinationAbility:IsReady() then
          if Cast(S.RuinationAbility) then return "ruination aoe 20"; end
        end
        -- rain_of_fire,if=pet.infernal.active&talent.rain_of_chaos
        if S.RainofFire:IsReady() and (InfernalActive() and S.RainofChaos:IsAvailable()) then
          if CastMagic(S.RainofFire, nil, "5740-Magic", magicgroundspell_rof) then return "rain_of_fire aoe 22"; end
        end
        -- soul_fire,target_if=min:dot.wither.remains+dot.immolate.remains-5*debuff.conflagrate.up+100*debuff.havoc.remains,if=(buff.decimation.up)&!talent.raging_demonfire&havoc_active
        if S.SoulFire:IsReady() and (Player:BuffUp(S.DecimationBuff) and not S.RagingDemonfire:IsAvailable() and VarHavocActive) then
          if CastTargetIf(S.SoulFire, Enemies8ySplash, "min", EvaluateTargetIfFilterWitherRemains3, nil) then return "soul_fire aoe 24"; end
        end
        -- soul_fire,target_if=min:(dot.wither.remains+dot.immolate.remains-5*debuff.conflagrate.up+100*debuff.havoc.remains),if=buff.decimation.up&active_dot.immolate<=4
        if S.SoulFire:IsReady() and (Player:BuffUp(S.DecimationBuff) and S.ImmolateDebuff:AuraActiveCount() <= 4) then
          if CastTargetIf(S.SoulFire, Enemies8ySplash, "min", EvaluateTargetIfFilterWitherRemains3, nil) then return "soul_fire aoe 26"; end
        end
        -- infernal_bolt,if=soul_shard<2.5
        if S.InfernalBolt:IsReady() and (SoulShards < 2.5) then
          if Cast(S.InfernalBolt) then return "infernal_bolt aoe 28"; end
        end
        -- chaos_bolt,if=((soul_shard>3.5-(0.1*active_enemies))&!action.rain_of_fire.enabled)|(!talent.wither&talent.cataclysm&active_enemies<=3)
        if S.ChaosBolt:IsReady() and (((SoulShards > 3.5 - (0.1 * EnemiesCount8ySplash)) and not S.RainofFire:IsAvailable()) or (not S.Wither:IsAvailable() and S.Cataclysm:IsAvailable() and EnemiesCount8ySplash <= 3)) then
          if Cast(S.ChaosBolt) then return "chaos_bolt aoe 30"; end
        end
        -- cataclysm,if=raid_event.adds.in>15|talent.wither
        if S.Cataclysm:IsReady() then
          if CastMagic(S.Cataclysm, nil, "152108-Magic", magicgroundspell_cataclysm) then return "cataclysm aoe 32"; end
        end
        -- havoc,target_if=min:((-target.time_to_die)<?-15)+dot.immolate.remains+99*(self.target=target),if=(!cooldown.summon_infernal.up|!talent.summon_infernal|(talent.inferno&active_enemies>4))&target.time_to_die>8&(cooldown.malevolence.remains>15|!talent.malevolence)|time<5
        if S.Havoc:IsReady() and ((S.SummonInfernal:CooldownDown() or not S.SummonInfernal:IsAvailable() or (S.Inferno:IsAvailable() and EnemiesCount8ySplash > 4)) and (S.Malevolence:CooldownRemains() > 15 or not S.Malevolence:IsAvailable()) or HL.CombatTime() < 5) then
          local BestUnit, BestConditionValue, CUCV = nil, nil, nil
          for _, CycleUnit in pairs(Enemies8ySplash) do
            if CycleUnit:GUID() ~= Target:GUID() then
              if BestConditionValue then
                CUCV = EvaluateTargetIfFilterHavoc(CycleUnit)
              end
              if not CycleUnit:IsFacingBlacklisted() and not CycleUnit:IsUserCycleBlacklisted() and (CycleUnit:AffectingCombat() or CycleUnit:IsDummy())
                and (not BestConditionValue or Utils.CompareThis("min", CUCV, BestConditionValue)) then
                BestUnit, BestConditionValue = CycleUnit, CUCV
              end
            end
          end
          if BestUnit and EvaluateTargetIfHavoc(BestUnit) then
            CastLeftNameplate(BestUnit, S.Havoc)
          end
        end
        -- wither,target_if=min:dot.wither.remains+99*debuff.havoc.remains,if=dot.wither.refreshable&(!talent.cataclysm.enabled|cooldown.cataclysm.remains>dot.wither.remains)&(!(talent.raging_demonfire&talent.channel_demonfire)|cooldown.channel_demonfire.remains>remains|time<5)&active_dot.wither<=active_enemies&target.time_to_die>18
        if S.Wither:IsReady() and (S.WitherDebuff:AuraActiveCount() <= EnemiesCount8ySplash) then
          if CastTargetIf(S.Wither, Enemies8ySplash, "min", EvaluateTargetIfFilterWitherRemains2, EvaluateTargetIfWitherAoE2) then return "wither aoe 34"; end
        end
        -- immolate,target_if=min:dot.immolate.remains+99*debuff.havoc.remains,if=dot.immolate.refreshable&(!talent.cataclysm.enabled|cooldown.cataclysm.remains>dot.immolate.remains)&(!(talent.raging_demonfire&talent.channel_demonfire)|cooldown.channel_demonfire.remains>remains|time<5)&(active_dot.immolate<=6&!(talent.diabolic_ritual&talent.inferno)|active_dot.immolate<=4)&target.time_to_die>18
        if S.Immolate:IsReady() and (S.ImmolateDebuff:AuraActiveCount() <= 6 and not (S.DiabolicRitual:IsAvailable() and S.Inferno:IsAvailable()) or S.ImmolateDebuff:AuraActiveCount() <= 4) then
          if CastTargetIf(S.Immolate, Enemies8ySplash, "min", EvaluateTargetIfFilterImmolate, EvaluateTargetIfImmolateAoE) then return "immolate aoe 36"; end
        end
        -- call_action_list,name=ogcd
        -- Note: Skipping this line, as the ogcd call at the start of the function covers it.
        -- summon_infernal,if=cooldown.invoke_power_infusion_0.up|cooldown.invoke_power_infusion_0.duration=0|fight_remains>=120
        -- Note: Not handling power_infusion conditions.
        if S.SummonInfernal:IsReady() then
          if CastMagic(S.SummonInfernal, nil, "1122-Magic", magicgroundspell_inf) then return "summon_infernal aoe 38"; end
        end
        -- rain_of_fire,if=debuff.pyrogenics.down&active_enemies<=4&!talent.diabolic_ritual
        if S.RainofFire:IsReady() and (Target:DebuffDown(S.PyrogenicsDebuff) and EnemiesCount8ySplash <= 4 and not S.DiabolicRitual:IsAvailable()) then
          if CastMagic(S.RainofFire, nil, "5740-Magic", magicgroundspell_rof) then return "rain_of_fire aoe 40"; end
        end
        -- channel_demonfire,if=dot.immolate.remains+dot.wither.remains>cast_time
        if S.ChannelDemonfire:IsReady() and (Target:DebuffRemains(S.ImmolateDebuff) + Target:DebuffRemains(S.WitherDebuff) > ChannelDemonfireCastTime()) then
          if Cast(S.ChannelDemonfire) then return "channel_demonfire aoe 42"; end
        end
        -- immolate,target_if=min:dot.immolate.remains+99*debuff.havoc.remains,if=((dot.immolate.refreshable&(!talent.cataclysm.enabled|cooldown.cataclysm.remains>dot.immolate.remains))|active_enemies>active_dot.immolate)&target.time_to_die>10&!havoc_active&!(talent.diabolic_ritual&talent.inferno)
        if S.Immolate:IsReady() then
          if CastTargetIf(S.Immolate, Enemies8ySplash, "min", EvaluateTargetIfFilterImmolate, EvaluateTargetIfImmolateAoE2) then return "immolate aoe 44"; end
        end
        -- immolate,target_if=min:dot.immolate.remains+99*debuff.havoc.remains,if=((dot.immolate.refreshable&variable.havoc_immo_time<5.4)|(dot.immolate.remains<2&dot.immolate.remains<havoc_remains)|!dot.immolate.ticking|(variable.havoc_immo_time<2)*havoc_active)&(!talent.cataclysm.enabled|cooldown.cataclysm.remains>dot.immolate.remains)&target.time_to_die>11&!(talent.diabolic_ritual&talent.inferno)
        if S.Immolate:IsReady() then
          if CastTargetIf(S.Immolate, Enemies8ySplash, "min", EvaluateTargetIfFilterImmolate, EvaluateTargetIfImmolateAoE3) then return "immolate aoe 46"; end
        end
        -- dimensional_rift
        if S.DimensionalRift:IsReady() then
          if Cast(S.DimensionalRift) then return "dimensional_rift aoe 48"; end
        end
        -- soul_fire,target_if=min:(dot.wither.remains+dot.immolate.remains-5*debuff.conflagrate.up+100*debuff.havoc.remains),if=buff.decimation.up
        if S.SoulFire:IsReady() and (Player:BuffUp(S.DecimationBuff)) then
          if CastTargetIf(S.SoulFire, Enemies8ySplash, "min", EvaluateTargetIfFilterWitherRemains3, nil) then return "soul_fire aoe 50"; end
        end
        -- incinerate,if=talent.fire_and_brimstone.enabled&buff.backdraft.up
        if S.Incinerate:IsReady() and (S.FireandBrimstone:IsAvailable() and Player:BuffUp(S.BackdraftBuff)) then
          if Cast(S.Incinerate) then return "incinerate aoe 52"; end
        end
        -- conflagrate,if=buff.backdraft.stack<2|!talent.backdraft
        if S.Conflagrate:IsReady() and (Player:BuffStack(S.BackdraftBuff) < 2 or not S.Backdraft:IsAvailable()) then
          if Cast(S.Conflagrate) then return "conflagrate aoe 54"; end
        end
        -- incinerate
        if S.Incinerate:IsReady() then
          if Cast(S.Incinerate) then return "incinerate aoe 56"; end
        end
    end
      
    local function Cleave()
        -- call_action_list,name=items
        -- call_action_list,name=ogcd
        local ShouldReturn = oGCD(); if ShouldReturn then return ShouldReturn; end
        -- call_action_list,name=havoc,if=havoc_active&havoc_remains>gcd.max
        if VarHavocActive and VarHavocRemains > Player:GCD() then
          local ShouldReturn = Havoc(); if ShouldReturn then return ShouldReturn; end
        end
        -- variable,name=pool_soul_shards,value=cooldown.havoc.remains<=5|talent.mayhem
        VarPoolSoulShards = (S.Havoc:CooldownRemains() <= 5) or S.Mayhem:IsAvailable()
        -- malevolence,if=(!cooldown.summon_infernal.up|!talent.summon_infernal)
        if S.Malevolence:IsReady() and (S.SummonInfernal:CooldownDown() or not S.SummonInfernal:IsAvailable() or S.SummonInfernal:BlockedByUserSettings()) then
          if Cast(S.Malevolence) then return "malevolence cleave 2"; end
        end
        -- havoc,target_if=min:((-target.time_to_die)<?-15)+dot.immolate.remains+99*(self.target=target),if=(!cooldown.summon_infernal.up|!talent.summon_infernal)&target.time_to_die>8
        if S.Havoc:IsReady() and (S.SummonInfernal:CooldownDown() or not S.SummonInfernal:IsAvailable()) then
          local BestUnit, BestConditionValue, CUCV = nil, nil, nil
          for _, CycleUnit in pairs(Enemies40y) do
            if CycleUnit:GUID() ~= Target:GUID() then
              if BestConditionValue then
                CUCV = EvaluateTargetIfFilterHavoc(CycleUnit)
              end
              if not CycleUnit:IsFacingBlacklisted() and not CycleUnit:IsUserCycleBlacklisted() and (CycleUnit:AffectingCombat() or CycleUnit:IsDummy())
                and (not BestConditionValue or Utils.CompareThis("min", CUCV, BestConditionValue)) then
                BestUnit, BestConditionValue = CycleUnit, CUCV
              end
            end
          end
          if BestUnit and EvaluateTargetIfHavoc(BestUnit) then
            CastLeftNameplate(BestUnit, S.Havoc)
          end
        end
        -- chaos_bolt,if=demonic_art
        if S.ChaosBolt:IsReady() and (DemonicArt()) then
          if Cast(S.ChaosBolt) then return "chaos_bolt cleave 4"; end
        end
        -- soul_fire,if=buff.decimation.react&(soul_shard<=4|buff.decimation.remains<=gcd.max*2)&debuff.conflagrate.remains>=execute_time&cooldown.havoc.remains
        if S.SoulFire:IsReady() and (Player:BuffUp(S.DecimationBuff) and (SoulShards <= 4 or Player:BuffRemains(S.DecimationBuff) <= Player:GCD() * 2) and Target:DebuffRemains(S.ConflagrateDebuff) >= S.SoulFire:ExecuteTime() and S.Havoc:CooldownDown()) then
          if Cast(S.SoulFire) then return "soul_fire cleave 6"; end
        end
        -- wither,target_if=min:dot.wither.remains+99*debuff.havoc.remains,if=talent.internal_combustion&(((dot.wither.remains-5*action.chaos_bolt.in_flight)<dot.wither.duration*0.4)|dot.wither.remains<3|(dot.wither.remains-action.chaos_bolt.execute_time)<5&action.chaos_bolt.usable)&(!talent.soul_fire|cooldown.soul_fire.remains+action.soul_fire.cast_time>(dot.wither.remains-5))&target.time_to_die>8&!action.soul_fire.in_flight_to_target
        if S.Wither:IsReady() and (S.InternalCombustion:IsAvailable() and not S.SoulFire:InFlight()) then
          if CastTargetIf(S.Wither, Enemies8ySplash, "min", EvaluateTargetIfFilterWitherRemains2, EvaluateTargetIfWitherCleave) then return "wither cleave 8"; end
        end
        -- wither,target_if=min:dot.wither.remains+99*debuff.havoc.remains,if=!talent.internal_combustion&(((dot.wither.remains-5*(action.chaos_bolt.in_flight))<dot.wither.duration*0.3)|dot.wither.remains<3)&(!talent.soul_fire|cooldown.soul_fire.remains+action.soul_fire.cast_time>(dot.wither.remains))&target.time_to_die>8&!action.soul_fire.in_flight_to_target
        if S.Wither:IsReady() and (not S.InternalCombustion:IsAvailable() and not S.SoulFire:InFlight()) then
          if CastTargetIf(S.Wither, Enemies8ySplash, "min", EvaluateTargetIfFilterWitherRemains2, EvaluateTargetIfWitherCleave2) then return "wither cleave 8"; end
        end
        -- conflagrate,if=(talent.roaring_blaze.enabled&full_recharge_time<=gcd.max*2)|recharge_time<=8&(diabolic_ritual&(buff.diabolic_ritual_mother_of_chaos.remains+buff.diabolic_ritual_overlord.remains+buff.diabolic_ritual_pit_lord.remains)<gcd.max)&!variable.pool_soul_shards
        if S.Conflagrate:IsReady() and ((S.RoaringBlaze:IsAvailable() and S.Conflagrate:FullRechargeTime() <= Player:GCD() * 2) or S.Conflagrate:Recharge() <= 8 and (DiabolicRitual() and (VarDRSum) < Player:GCD()) and not VarPoolSoulShards) then
          if Cast(S.Conflagrate) then return "conflagrate cleave 10"; end
        end
        -- shadowburn,if=(cooldown.shadowburn.full_recharge_time<=gcd.max*3|debuff.eradication.remains<=gcd.max&talent.eradication&!action.chaos_bolt.in_flight&!talent.diabolic_ritual)&(talent.conflagration_of_chaos|talent.blistering_atrophy)|fight_remains<=8
        if S.Shadowburn:IsReady() and ((S.Shadowburn:FullRechargeTime() <= Player:GCD() * 3 or Target:DebuffRemains(S.EradicationDebuff) <= Player:GCD() and S.Eradication:IsAvailable() and not S.ChaosBolt:InFlight() and not S.DiabolicRitual:IsAvailable()) and (S.ConflagrationofChaos:IsAvailable() or S.BlisteringAtrophy:IsAvailable()) or BossFightRemains <= 8) then
          if Cast(S.Shadowburn) then return "shadowburn cleave 12"; end
        end
        -- chaos_bolt,if=buff.ritual_of_ruin.up
        if S.ChaosBolt:IsReady() and (Player:BuffUp(S.RitualofRuinBuff)) then
            if Cast(S.ChaosBolt) then return "chaos_bolt cleave 14"; end
          end
        if S.SummonInfernal:CooldownRemains() >= 90 and S.RainofChaos:IsAvailable() then
          -- rain_of_fire,if=cooldown.summon_infernal.remains>=90&talent.rain_of_chaos
          if S.RainofFire:IsReady() then
            if CastMagic(S.RainofFire, nil, "5740-Magic", magicgroundspell_rof) then return "rain_of_fire cleave 16"; end
          end
          -- shadowburn,if=cooldown.summon_infernal.remains>=90&talent.rain_of_chaos
          if S.Shadowburn:IsReady() then
            if Cast(S.Shadowburn) then return "shadowburn cleave 18"; end
          end
          -- chaos_bolt,if=cooldown.summon_infernal.remains>=90&talent.rain_of_chaos
          if S.ChaosBolt:IsReady() then
            if Cast(S.ChaosBolt) then return "chaos_bolt cleave 20"; end
          end
        end
        -- ruination,if=(debuff.eradication.remains>=execute_time|!talent.eradication|!talent.shadowburn)
        if S.RuinationAbility:IsReady() and (Target:DebuffRemains(S.EradicationDebuff) >= S.RuinationAbility:ExecuteTime() or not S.Eradication:IsAvailable() or not S.Shadowburn:IsAvailable()) then
          if Cast(S.RuinationAbility) then return "ruination cleave 22"; end
        end
        -- cataclysm,if=raid_event.adds.in>15
        if S.Cataclysm:IsReady() then
          if CastMagic(S.Cataclysm, nil, "152108-Magic", magicgroundspell_cataclysm) then return "cataclysm cleave 24"; end
        end
        -- channel_demonfire,if=talent.raging_demonfire&(dot.immolate.remains+dot.wither.remains-5*(action.chaos_bolt.in_flight&talent.internal_combustion))>cast_time
        if S.ChannelDemonfire:IsReady() and (S.RagingDemonfire:IsAvailable() and (Target:DebuffRemains(S.ImmolateDebuff) + Target:DebuffRemains(S.WitherDebuff) - 5 * num(S.ChaosBolt:InFlight() and S.InternalCombustion:IsAvailable())) > S.ChannelDemonfire:CastTime()) then
          if Cast(S.ChannelDemonfire) then return "channel_demonfire cleave 26"; end
        end
        -- soul_fire,if=soul_shard<=3.5&(debuff.conflagrate.remains>cast_time+travel_time|!talent.roaring_blaze&buff.backdraft.up)&!variable.pool_soul_shards
        if S.SoulFire:IsReady() and (SoulShards <= 3.5 and (Target:DebuffRemains(S.RoaringBlazeDebuff) > S.SoulFire:CastTime() + S.SoulFire:TravelTime() or not S.RoaringBlaze:IsAvailable() and Player:BuffUp(S.BackdraftBuff)) and not VarPoolSoulShards) then
          if Cast(S.SoulFire) then return "soul_fire cleave 28"; end
        end
        -- immolate,target_if=min:dot.immolate.remains+99*debuff.havoc.remains,if=(dot.immolate.refreshable&(dot.immolate.remains<cooldown.havoc.remains|!dot.immolate.ticking))&(!talent.cataclysm|cooldown.cataclysm.remains>remains)&(!talent.soul_fire|cooldown.soul_fire.remains+(!talent.mayhem*action.soul_fire.cast_time)>dot.immolate.remains)&target.time_to_die>15
        if S.Immolate:IsReady() then
          if CastTargetIf(S.Immolate, Enemies8ySplash, "min", EvaluateTargetIfFilterImmolate, EvaluateTargetIfImmolateCleave) then return "immolate cleave 30"; end
        end
        -- summon_infernal
        if S.SummonInfernal:IsReady() then
          if CastMagic(S.SummonInfernal, nil, "1122-Magic", magicgroundspell_inf) then return "summon_infernal cleave 32"; end
        end
        -- incinerate,if=talent.diabolic_ritual&(diabolic_ritual&(buff.diabolic_ritual_mother_of_chaos.remains+buff.diabolic_ritual_overlord.remains+buff.diabolic_ritual_pit_lord.remains-2-!variable.disable_cb_2t*action.chaos_bolt.cast_time-variable.disable_cb_2t*gcd.max)<=0)
        if S.Incinerate:IsReady() and (S.DiabolicRitual:IsAvailable() and (DiabolicRitual() and (VarDRSum - 2 - num(not VarDisableCB2T) * S.ChaosBolt:CastTime() - num(VarDisableCB2T) * Player:GCD()) <= 0)) then
          if Cast(S.Incinerate) then return "incinerate cleave 34"; end
        end
        -- rain_of_fire,if=variable.pooling_condition&!talent.wither&buff.rain_of_chaos.up
        if S.RainofFire:IsReady() and (VarPoolingCondition and not S.Wither:IsAvailable() and Player:BuffUp(S.RainofChaosBuff)) then
          if CastMagic(S.RainofFire, nil, "5740-Magic", magicgroundspell_rof) then return "rain_of_fire cleave 36"; end
        end
        -- rain_of_fire,if=variable.allow_rof_2t_spender>=1&!talent.wither&talent.pyrogenics&debuff.pyrogenics.remains<=gcd.max&(!talent.rain_of_chaos|cooldown.summon_infernal.remains>=gcd.max*3)&variable.pooling_condition
        if S.RainofFire:IsReady() and (VarAllowRoF2TSpender >= 1 and not S.Wither:IsAvailable() and S.Pyrogenics:IsAvailable() and Target:DebuffRemains(S.PyrogenicsDebuff) <= Player:GCD() and (not S.RainofChaos:IsAvailable() or S.SummonInfernal:CooldownRemains() >= Player:GCD() * 3) and VarPoolingCondition) then
          if CastMagic(S.RainofFire, nil, "5740-Magic", magicgroundspell_rof) then return "rain_of_fire cleave 38"; end
        end
        -- rain_of_fire,if=variable.do_rof_2t&variable.pooling_condition&(cooldown.summon_infernal.remains>=gcd.max*3|!talent.rain_of_chaos)
        if S.RainofFire:IsReady() and (VarDoRoF2T and VarPoolingCondition and (S.SummonInfernal:CooldownRemains() >= Player:GCD() * 3 or not S.RainofChaos:IsAvailable())) then
          if CastMagic(S.RainofFire, nil, "5740-Magic", magicgroundspell_rof) then return "rain_of_fire cleave 40"; end
        end
        -- soul_fire,if=soul_shard<=4&talent.mayhem
        if S.SoulFire:IsReady() and (SoulShards <= 4 and S.Mayhem:IsAvailable()) then
          if Cast(S.SoulFire) then return "soul_fire cleave 42"; end
        end
        -- chaos_bolt,if=!variable.disable_cb_2t&variable.pooling_condition_cb&(cooldown.summon_infernal.remains>=gcd.max*3|soul_shard>4|!talent.rain_of_chaos)
        if S.ChaosBolt:IsReady() and (not VarDisableCB2T and VarPoolingConditionCB and (S.SummonInfernal:CooldownRemains() >= Player:GCD() * 3 or SoulShards > 4 or not S.RainofChaos:IsAvailable())) then
          if Cast(S.ChaosBolt) then return "chaos_bolt cleave 44"; end
        end
        -- channel_demonfire
        if S.ChannelDemonfire:IsReady() then
          if Cast(S.ChannelDemonfire) then return "channel_demonfire cleave 46"; end
        end
        -- dimensional_rift
        if S.DimensionalRift:IsReady() then
          if Cast(S.DimensionalRift) then return "dimensional_rift cleave 48"; end
        end
        -- infernal_bolt
        if S.InfernalBolt:IsReady() then
          if Cast(S.InfernalBolt) then return "infernal_bolt cleave 50"; end
        end
        -- conflagrate,if=charges>(max_charges-1)|fight_remains<gcd.max*charges
        if S.Conflagrate:IsReady() and (S.Conflagrate:Charges() > (S.Conflagrate:MaxCharges() - 1) or FightRemains < Player:GCD() * S.Conflagrate:Charges()) then
          if Cast(S.Conflagrate) then return "conflagrate cleave 52"; end
        end
        -- incinerate
        if S.Incinerate:IsReady() then
          if Cast(S.Incinerate) then return "incinerate cleave 54"; end
        end
      end
      
      local function Variables()
        -- variable,name=havoc_immo_time,op=reset
        VarHavocImmoTime = 0
        -- variable,name=pooling_condition,value=(soul_shard>=3|(talent.secrets_of_the_coven&buff.infernal_bolt.up|buff.decimation.up)&soul_shard>=3),default=1,op=set
        VarPoolingCondition = SoulShards >= 3 or (S.SecretsoftheCoven:IsAvailable() and Player:BuffUp(S.InfernalBoltBuff) or Player:BuffUp(S.DecimationBuff)) and SoulShards >= 3
        -- variable,name=pooling_condition_cb,value=variable.pooling_condition|pet.infernal.active&soul_shard>=3,default=1,op=set
        VarPoolingConditionCB = VarPoolingCondition or InfernalActive() and SoulShards >= 3
        -- cycling_variable,name=havoc_immo_time,op=add,value=dot.immolate.remains*debuff.havoc.up<?dot.wither.remains*debuff.havoc.up
        for _, CycleUnit in pairs(Enemies8ySplash) do
          local HavocUp = num(CycleUnit:DebuffUp(S.HavocDebuff))
          VarHavocImmoTime = VarHavocImmoTime + max(CycleUnit:DebuffRemains(S.ImmolateDebuff) * HavocUp, CycleUnit:DebuffRemains(S.WitherDebuff) * HavocUp)
        end
        -- variable,name=infernal_active,op=set,value=pet.infernal.active|(cooldown.summon_infernal.duration-cooldown.summon_infernal.remains)<20
        VarInfernalActive = InfernalActive() or S.SummonInfernal:TimeSinceLastCast() < 20
        -- variable,name=trinket_1_will_lose_cast,value=((floor((fight_remains%trinket.1.cooldown.duration)+1)!=floor((fight_remains+(cooldown.summon_infernal.duration-cooldown.summon_infernal.remains))%cooldown.summon_infernal.duration))&(floor((fight_remains%trinket.1.cooldown.duration)+1))!=(floor(((fight_remains-cooldown.summon_infernal.remains)%trinket.1.cooldown.duration)+1))|((floor((fight_remains%trinket.1.cooldown.duration)+1)=floor((fight_remains+(cooldown.summon_infernal.duration-cooldown.summon_infernal.remains))%cooldown.summon_infernal.duration))&(((fight_remains-cooldown.summon_infernal.remains%%trinket.1.cooldown.duration)-cooldown.summon_infernal.remains-variable.trinket_1_buff_duration)>0)))&cooldown.summon_infernal.remains>20
        -- Note: Let's avoid divide by zero...
        local T1CD = (VarTrinket1CD and VarTrinket1CD > 0) and VarTrinket1CD or 1
        VarT1WillLoseCast = ((floor((FightRemains / T1CD) + 1) ~= floor((FightRemains + (120 - S.SummonInfernal:CooldownRemains())) / 120)) and (floor((FightRemains / T1CD) + 1)) ~= (floor(((FightRemains - S.SummonInfernal:CooldownRemains()) / T1CD) + 1)) or ((floor((FightRemains / T1CD) + 1) == floor((FightRemains + (120 - S.SummonInfernal:CooldownRemains())) / 120)) and (((FightRemains - S.SummonInfernal:CooldownRemains() % T1CD) - S.SummonInfernal:CooldownRemains() - VarTrinket1BuffDuration) > 0))) and S.SummonInfernal:CooldownRemains() > 20
        -- variable,name=trinket_2_will_lose_cast,value=((floor((fight_remains%trinket.2.cooldown.duration)+1)!=floor((fight_remains+(cooldown.summon_infernal.duration-cooldown.summon_infernal.remains))%cooldown.summon_infernal.duration))&(floor((fight_remains%trinket.2.cooldown.duration)+1))!=(floor(((fight_remains-cooldown.summon_infernal.remains)%trinket.2.cooldown.duration)+1))|((floor((fight_remains%trinket.2.cooldown.duration)+1)=floor((fight_remains+(cooldown.summon_infernal.duration-cooldown.summon_infernal.remains))%cooldown.summon_infernal.duration))&(((fight_remains-cooldown.summon_infernal.remains%%trinket.2.cooldown.duration)-cooldown.summon_infernal.remains-variable.trinket_2_buff_duration)>0)))&cooldown.summon_infernal.remains>20
        -- Note: Let's avoid divide by zero...
        local T2CD = (VarTrinket2CD and VarTrinket2CD > 0) and VarTrinket2CD or 1
        VarT2WillLoseCast = ((floor((FightRemains / T2CD) + 1) ~= floor((FightRemains + (120 - S.SummonInfernal:CooldownRemains())) / 120)) and (floor((FightRemains / T2CD) + 1)) ~= (floor(((FightRemains - S.SummonInfernal:CooldownRemains()) / T2CD) + 1)) or ((floor((FightRemains / T2CD) + 1) == floor((FightRemains + (120 - S.SummonInfernal:CooldownRemains())) / 120)) and (((FightRemains - S.SummonInfernal:CooldownRemains() % T2CD) - S.SummonInfernal:CooldownRemains() - VarTrinket2BuffDuration) > 0))) and S.SummonInfernal:CooldownRemains() > 20
    end

    --- ======= MAIN =======
    local function APL()
        -- Unit Update
        magicgroundspell_cataclysm = GetSetting('magicgroundspell_cataclysm', false)
        magicgroundspell_inf = GetSetting('magicgroundspell_inf', false)
        magicgroundspell_rof = GetSetting('magicgroundspell_rof', false)
        TargetIsValid = M.TargetIsValid()
        InCombat = Player:AffectingCombat()
        IsStandingStill = not Player:IsMoving()
        IsFallingvar = IsFalling() and true or false
        MOCheck = (MouseOver:IsEnemy() or MouseOver:IsATank() or MouseOver:IsAMelee())
        if IsFallingvar then
            TimeStampFalling = GetTime()
        end
        local PetCleaveAbility = (Action.FindBySpellID(S.Whiplash:ID()) and S.Whiplash) or nil

        if PetCleaveAbility then
            PetInMeleeRangeOfTarget = Target:IsSpellInActionRange(PetCleaveAbility)
        end

        if AoEON() then
            Enemies40y = Player:GetEnemiesInRange(40)
            local PetNPCID = Pet:NPCID()
            if (PetNPCID == 1860 or PetNPCID == 1863 or PetNPCID == 417 or PetNPCID == 58964) and PetCleaveAbility and PetInMeleeRangeOfTarget then
                Enemies8ySplash = Player:GetEnemiesInSpellActionRange(PetCleaveAbility)
            else
                Enemies8ySplash = Target:GetEnemiesInSplashRange(12)
                if #Enemies8ySplash < 2 then
                    Enemies8ySplash = Enemies40y
                end
            end
        else
            Enemies40y = {}
            Enemies8ySplash = { Target }
        end
        EnemiesCount8ySplash = #Enemies8ySplash
        MainAddon.InfoText = EnemiesCount8ySplash
        
        if TargetIsValid or InCombat then
            -- Define gcd.max (0.25 seconds to allow for latency and player reaction time)
            GCDMax = Player:GCD() + 0.25

            -- Check Havoc Status
            VarHavocActive, VarHavocRemains = UnitWithHavoc(Enemies40y)

            -- Calculate fight_remains
            BossFightRemains = HL.BossFightRemains()
            FightRemains = BossFightRemains
            if FightRemains == 11111 then
            FightRemains = HL.FightRemains(Enemies8ySplash, false)
            end

            -- Soul Shards
            SoulShards = Player:SoulShardsP()

            -- Variables for buffs/debuffs that we check often.
            VarDRMotherBuffRemains = Player:BuffRemains(S.DiabolicRitualMotherBuff)
            VarDROverlordBuffRemains = Player:BuffRemains(S.DiabolicRitualOverlordBuff)
            VarDRPitLordBuffRemains = Player:BuffRemains(S.DiabolicRitualPitLordBuff)
            VarDRSum = VarDRMotherBuffRemains + VarDROverlordBuffRemains + VarDRPitLordBuffRemains
            VarSFCDRPlusCT = S.SoulFire:CooldownRemains() + S.SoulFire:CastTime()
        end

        --MainAddon.UpdateVariable("SoulShards", SoulShards)
        --MainAddon.UpdateVariable("condition", 4 - 0.1 * S.ImmolateDebuff:AuraActiveCount())

        if GetSetting('auto_havoc', true) and Target:DebuffUp(S.HavocDebuff) then
            if #Enemies40y > 1 then
                if MainAddon.SetTopColor(1, "Target Enemy") then
                    return "Changing Target because of Havoc"
                end
            end
        end

        -- Burst Potion
        if Target:IsSpellInRange(S.Corruption) then
            if MainAddon.UsePotion() then
                MainAddon.SetTopColor(1, "Combat Potion")
            end
        end

        local ShouldReturn = Defensives();
        if ShouldReturn then
            return ShouldReturn;
        end

        local ShouldReturn = Utilities();
        if ShouldReturn then
            return ShouldReturn;
        end

        if M.TargetIsValid() then
            -- Trinkets
            local shouldReturn = MainAddon.TrinketDPS()
            if shouldReturn then
                return shouldReturn
            end  

            -- Precombat
            if (not Player:AffectingCombat()) then
                local ShouldReturn = Precombat();
                if ShouldReturn then
                    return ShouldReturn;
                end
            end

            -- YUNO: easy rain of fire
            if S.RainofFire:IsReady() and SoulShards > 3.5 and ((VarHavocActive and EnemiesCount8ySplash > 5) or (not VarHavocActive and EnemiesCount8ySplash > 3)) then
                if CastMagic(S.RainofFire, nil, "5740-Magic", magicgroundspell_rof) then return "Rain of Fire AoE spender"; end
            end

            Variables()
            -- call_action_list,name=aoe,if=(active_enemies>=3)&!variable.cleave_apl
            if AoEON() and EnemiesCount8ySplash >= 3 and not VarCleaveAPL then
                local ShouldReturn = Aoe(); if ShouldReturn then return ShouldReturn; end
            end
            -- call_action_list,name=cleave,if=active_enemies!=1|variable.cleave_apl
            if AoEON() and (EnemiesCount8ySplash > 1 or VarCleaveAPL) then
                local ShouldReturn = Cleave(); if ShouldReturn then return ShouldReturn; end
            end
            -- call_action_list,name=ogcd
            local ShouldReturn = oGCD(); if ShouldReturn then return ShouldReturn; end
            -- call_action_list,name=items
            local ShouldReturn = Items(); if ShouldReturn then return ShouldReturn; end
            -- malevolence,if=cooldown.summon_infernal.remains>=55
            if S.Malevolence:IsReady() and (S.SummonInfernal:CooldownRemains() >= 55 or S.SummonInfernal:BlockedByUserSettings()) then
                if Cast(S.Malevolence) then return "malevolence main 2"; end
            end
            -- wait,sec=((buff.diabolic_ritual_mother_of_chaos.remains+buff.diabolic_ritual_overlord.remains+buff.diabolic_ritual_pit_lord.remains)),if=(diabolic_ritual&(buff.diabolic_ritual_mother_of_chaos.remains+buff.diabolic_ritual_overlord.remains+buff.diabolic_ritual_pit_lord.remains)<gcd.max*0.25)&soul_shard>2
            -- TODO: Add wait?
            -- chaos_bolt,if=demonic_art
            if S.ChaosBolt:IsReady() and (DemonicArt()) then
                if Cast(S.ChaosBolt) then return "chaos_bolt main 4"; end
            end
            -- soul_fire,if=buff.decimation.react&(soul_shard<=4|buff.decimation.remains<=gcd.max*2)&debuff.conflagrate.remains>=execute_time
            if S.SoulFire:IsReady() and (Player:BuffUp(S.DecimationBuff) and (SoulShards <= 4 or Player:BuffRemains(S.DecimationBuff) <= Player:GCD() * 2) and Target:DebuffRemains(S.ConflagrateDebuff) >= S.SoulFire:ExecuteTime()) then
                if Cast(S.SoulFire) then return "soul_fire main 6"; end
            end
            -- wither,if=talent.internal_combustion&(((dot.wither.remains-5*action.chaos_bolt.in_flight)<dot.wither.duration*0.4)|dot.wither.remains<3|(dot.wither.remains-action.chaos_bolt.execute_time)<5&action.chaos_bolt.usable)&(!talent.soul_fire|cooldown.soul_fire.remains+action.soul_fire.cast_time>(dot.wither.remains-5))&target.time_to_die>8&!action.soul_fire.in_flight_to_target
            if S.Wither:IsReady() and (S.InternalCombustion:IsAvailable() and (((Target:DebuffRemains(S.WitherDebuff) - 5 * num(S.ChaosBolt:InFlight())) < S.WitherDebuff:MaxDuration() * 4) or Target:DebuffRemains(S.WitherDebuff) < 3 or (Target:DebuffRemains(S.WitherDebuff) - S.ChaosBolt:ExecuteTime()) < 5 and S.ChaosBolt:IsReady()) and (not S.SoulFire:IsAvailable() or VarSFCDRPlusCT > (Target:DebuffRemains(S.WitherDebuff) - 5)) and Target:TimeToDie() > 8 and not S.SoulFire:InFlight()) then
                if Cast(S.Wither) then return "wither main 8"; end
            end
            -- conflagrate,if=talent.roaring_blaze&debuff.conflagrate.remains<1.5|full_recharge_time<=gcd.max*2|recharge_time<=8&(diabolic_ritual&(buff.diabolic_ritual_mother_of_chaos.remains+buff.diabolic_ritual_overlord.remains+buff.diabolic_ritual_pit_lord.remains)<gcd.max)&soul_shard>=1.5
            if S.Conflagrate:IsReady() and (S.RoaringBlaze:IsAvailable() and Target:DebuffRemains(S.RoaringBlazeDebuff) < 1.5 or S.Conflagrate:FullRechargeTime() <= Player:GCD() * 2 or S.Conflagrate:Recharge() <= 8 and (DiabolicRitual() and VarDRSum < Player:GCD()) and SoulShards >= 1.5) then
                if Cast(S.Conflagrate) then return "conflagrate main 10"; end
            end
            -- shadowburn,if=(cooldown.shadowburn.full_recharge_time<=gcd.max*3|debuff.eradication.remains<=gcd.max&talent.eradication&!action.chaos_bolt.in_flight&!talent.diabolic_ritual)&(talent.conflagration_of_chaos|talent.blistering_atrophy)&!demonic_art|fight_remains<=8
            if S.Shadowburn:IsReady() and ((S.Shadowburn:FullRechargeTime() <= Player:GCD() * 3 or Target:DebuffRemains(S.EradicationDebuff) <= Player:GCD() and S.Eradication:IsAvailable() and not S.ChaosBolt:InFlight() and not S.DiabolicRitual:IsAvailable()) and (S.ConflagrationofChaos:IsAvailable() or S.BlisteringAtrophy:IsAvailable()) and not DemonicArt() or BossFightRemains <= 8) then
                if Cast(S.Shadowburn) then return "shadowburn main 12"; end
            end
            -- chaos_bolt,if=buff.ritual_of_ruin.up
            if S.ChaosBolt:IsReady() and (Player:BuffUp(S.RitualofRuinBuff)) then
                if Cast(S.ChaosBolt) then return "chaos_bolt main 14"; end
            end
            -- shadowburn,if=(cooldown.summon_infernal.remains>=90&talent.rain_of_chaos)|buff.malevolence.up
            if S.Shadowburn:IsReady() and ((S.SummonInfernal:CooldownRemains() >= 90 and S.RainofChaos:IsAvailable()) or Player:BuffUp(S.MalevolenceBuff)) then
                if Cast(S.Shadowburn) then return "shadowburn main 16"; end
            end
            -- chaos_bolt,if=(cooldown.summon_infernal.remains>=90&talent.rain_of_chaos)|buff.malevolence.up
            if S.ChaosBolt:IsReady() and ((S.SummonInfernal:CooldownRemains() >= 90 and S.RainofChaos:IsAvailable()) or Player:BuffUp(S.MalevolenceBuff)) then
                if Cast(S.ChaosBolt) then return "chaos_bolt main 16"; end
            end
            -- ruination,if=(debuff.eradication.remains>=execute_time|!talent.eradication|!talent.shadowburn)
            if S.RuinationAbility:IsReady() and (Target:DebuffRemains(S.EradicationDebuff) >= S.RuinationAbility:ExecuteTime() or not S.Eradication:IsAvailable() or not S.Shadowburn:IsAvailable()) then
                if Cast(S.RuinationAbility) then return "ruination main 18"; end
            end
            -- cataclysm,if=raid_event.adds.in>15&(talent.wither&dot.wither.refreshable)
            if S.Cataclysm:IsReady() and (S.Wither:IsAvailable() and Target:DebuffRefreshable(S.WitherDebuff)) then
                if CastMagic(S.Cataclysm, nil, "152108-Magic", magicgroundspell_cataclysm) then return "cataclysm main 20"; end
            end
            -- channel_demonfire,if=talent.raging_demonfire&(dot.immolate.remains+dot.wither.remains-5*(action.chaos_bolt.in_flight&talent.internal_combustion))>cast_time
            if S.ChannelDemonfire:IsReady() and (S.RagingDemonfire:IsAvailable() and (Target:DebuffRemains(S.ImmolateDebuff) + Target:DebuffRemains(S.WitherDebuff) - 5 * num(S.ChaosBolt:InFlight() and S.InternalCombustion:IsAvailable())) > S.ChannelDemonfire:CastTime()) then
                if Cast(S.ChannelDemonfire) then return "channel_demonfire main 22"; end
            end
            -- wither,if=!talent.internal_combustion&(((dot.wither.remains-5*(action.chaos_bolt.in_flight))<dot.wither.duration*0.3)|dot.wither.remains<3)&(!talent.cataclysm|cooldown.cataclysm.remains>dot.wither.remains)&(!talent.soul_fire|cooldown.soul_fire.remains+action.soul_fire.cast_time>(dot.wither.remains))&target.time_to_die>8&!action.soul_fire.in_flight_to_target
            if S.Wither:IsReady() and (not S.InternalCombustion:IsAvailable() and (((Target:DebuffRemains(S.WitherDebuff) - 5 * num(S.ChaosBolt:InFlight())) < S.WitherDebuff:PandemicThreshold()) or Target:DebuffRemains(S.WitherDebuff) < 3) and (not S.Cataclysm:IsAvailable() or S.Cataclysm:CooldownRemains() > Target:DebuffRemains(S.WitherDebuff)) and (not S.SoulFire:IsAvailable() or VarSFCDRPlusCT > Target:DebuffRemains(S.WitherDebuff)) and Target:TimeToDie() > 8 and not S.SoulFire:InFlight()) then
                if Cast(S.Wither) then return "wither main 24"; end
            end
            -- immolate,if=(((dot.immolate.remains-5*(action.chaos_bolt.in_flight&talent.internal_combustion))<dot.immolate.duration*0.3)|dot.immolate.remains<3|(dot.immolate.remains-action.chaos_bolt.execute_time)<5&talent.internal_combustion&action.chaos_bolt.usable)&(!talent.soul_fire|cooldown.soul_fire.remains+action.soul_fire.cast_time>(dot.immolate.remains-5*talent.internal_combustion))&target.time_to_die>8&!action.soul_fire.in_flight_to_target
            if S.Immolate:IsReady() and ((((Target:DebuffRemains(S.ImmolateDebuff) - 5 * num(S.ChaosBolt:InFlight() and S.InternalCombustion:IsAvailable())) < S.ImmolateDebuff:PandemicThreshold()) or Target:DebuffRemains(S.ImmolateDebuff) < 3 or (Target:DebuffRemains(S.ImmolateDebuff) - S.ChaosBolt:ExecuteTime()) < 5 and S.InternalCombustion:IsAvailable() and S.ChaosBolt:IsReady()) and (not S.Cataclysm:IsAvailable() or S.Cataclysm:CooldownRemains() > Target:DebuffRemains(S.ImmolateDebuff)) and (not S.SoulFire:IsAvailable() or VarSFCDRPlusCT > (Target:DebuffRemains(S.ImmolateDebuff) - 5 * num(S.InternalCombustion:IsAvailable()))) and Target:TimeToDie() > 8 and not S.SoulFire:InFlight()) then
                if Cast(S.Immolate) then return "immolate main 26"; end
            end
            -- summon_infernal
            if S.SummonInfernal:IsReady() then
                if CastMagic(S.SummonInfernal, nil, "1122-Magic", magicgroundspell_inf) then return "summon_infernal main 28"; end
            end
            -- incinerate,if=talent.diabolic_ritual&(diabolic_ritual&(buff.diabolic_ritual_mother_of_chaos.remains+buff.diabolic_ritual_overlord.remains+buff.diabolic_ritual_pit_lord.remains-2-!variable.disable_cb_2t*action.chaos_bolt.cast_time-variable.disable_cb_2t*gcd.max)<=0)
            if S.Incinerate:IsReady() and (S.DiabolicRitual:IsAvailable() and (DiabolicRitual() and (VarDRSum - 2 - num(not VarDisableCB2T) * S.ChaosBolt:CastTime() - num(VarDisableCB2T) * Player:GCD()) <= 0)) then
                if Cast(S.Incinerate) then return "incinerate main 30"; end
            end
            -- chaos_bolt,if=variable.pooling_condition_cb&(cooldown.summon_infernal.remains>=gcd.max*3|soul_shard>4|!talent.rain_of_chaos)
            if S.ChaosBolt:IsReady() and (VarPoolingConditionCB and (S.SummonInfernal:CooldownRemains() >= Player:GCD() * 3 or SoulShards > 4 or not S.RainofChaos:IsAvailable())) then
                if Cast(S.ChaosBolt) then return "chaos_bolt main 32"; end
            end
            -- channel_demonfire
            if S.ChannelDemonfire:IsReady() then
                if Cast(S.ChannelDemonfire) then return "channel_demonfire main 34"; end
            end
            -- dimensional_rift
            if S.DimensionalRift:IsReady() then
                if Cast(S.DimensionalRift) then return "dimensional_rift main 36"; end
            end
            -- infernal_bolt
            if S.InfernalBolt:IsReady() then
                if Cast(S.InfernalBolt) then return "infernal_bolt main 38"; end
            end
            -- conflagrate,if=charges>(max_charges-1)|fight_remains<gcd.max*charges
            if S.Conflagrate:IsReady() and (S.Conflagrate:Charges() > (S.Conflagrate:MaxCharges() - 1) or FightRemains < Player:GCD() * S.Conflagrate:Charges()) then
                if Cast(S.Conflagrate) then return "conflagrate main 40"; end
            end
            -- soul_fire,if=buff.backdraft.up
            if S.SoulFire:IsReady() and (Player:BuffUp(S.BackdraftBuff)) then
                if Cast(S.SoulFire) then return "soul_fire main 42"; end
            end
            -- incinerate
            if S.Incinerate:IsReady() then
                if Cast(S.Incinerate) then return "incinerate main 36"; end
            end
        end
    end

    local function OnInit()
        Warlock.LoadEvent()

        S.ImmolateDebuff:RegisterAuraTracking()
        S.WitherDebuff:RegisterAuraTracking()

        C_Timer.After(5, function()
            if Player:Level() >= 19 and S.PetSuccubus:IsAvailable() and not Action.FindBySpellID(S.Whiplash:ID()) then
                StaticPopupDialogs["LOCKPOPUP"] = {
                    text = "??: This rotations needs the Whiplash (Succubus/Sayaad Spell) on your action bar to function properly. Put on your action bar, regardless if you're going to use the succubus or not.",
                    button1 = "OK",
                    OnShow = function(self)
                        self.button1:Disable()
                    end,
                    OnUpdate = function(self)
                        if Action.FindBySpellID(S.Whiplash:ID()) then
                            self.button1:Enable()
                        end
                    end,
                }
                StaticPopup_Show("LOCKPOPUP")
            end
        end)
    end
    M.SetAPL(267, APL, OnInit)

  -- soul_shard
  local function WarlockSoulShards()
    return UnitPower(Player.UnitID, 7, true) * 0.1
  end

  HL.AddCoreOverride("Player.SoulShardsP",
          function()
            local Shard = WarlockSoulShards()
            if MainAddon.PlayerSpecID() == 267 then
                if not Player:IsCasting() then
                    return Shard
                else
                    if Player:IsCasting(S.ChaosBolt) or Player:IsCasting(S.RainofFire) and S.Inferno:IsAvailable() then
                        return Shard - 2
                    elseif Player:IsCasting(S.RainofFire) and not S.Inferno:IsAvailable() then
                        return Shard - 3
                    elseif Player:IsCasting(S.SummonPet) then
                        return Shard - 1
                    elseif Player:IsCasting(S.Incinerate) then
                        return min(Shard + 0.2, 5)
                    elseif Player:IsCasting(S.Conflagrate) then
                        return min(Shard + 0.5, 5)
                    elseif Player:IsCasting(S.SoulFire) then
                        return min(Shard + 1, 5)
                    else
                        return Shard
                    end
                end
            end
            return Shard
          end
  , 267)

  local DestroOldSpellIsCastable
  DestroOldSpellIsCastable = HL.AddCoreOverride("Spell.IsCastable",
          function(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
              local BaseCheck, Reason = DestroOldSpellIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
              if MainAddon.PlayerSpecID() == 267 then
                if self == S.Cataclysm then
                    if not GetSetting('CataclysmMoving', false) then
                        if Target:IsMoving() then
                            return false, "Target is moving"
                        end
                    end
                end

                if self == S.RainofFire then
                    if not GetSetting('RainofFireMoving', false) then
                        if Target:IsMoving() then
                            return false, "Target is moving"
                        end
                    end
                end

                if self == S.SummonInfernal then
                    if not GetSetting('SummonInfernalMoving', false) then
                        if Target:IsMoving() then
                            return false, "Target is moving"
                        end
                    end
                end

                if self == S.SummonPet then
                    return BaseCheck and Player:SoulShardsP() > 0 and (not Player:IsCasting(self)) and not (Pet:IsActive() or Player:BuffUp(S.GrimoireofSacrificeBuff))
                elseif self == S.Immolate or self == S.Cataclysm or self == S.SummonSoulkeeper then
                    return BaseCheck and not Player:IsCasting(self)
                -- Custom addition
                elseif self == S.InfernalBolt or self == S.Ruination then
                    return BaseCheck and not Player:IsCasting(self)
                else
                    return BaseCheck
                end
            end
            return BaseCheck, Reason
          end
  , 267)

  local DestroOldSpellIsReady
  DestroOldSpellIsReady = HL.AddCoreOverride("Spell.IsReady",
          function(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
            local BaseCheck = DestroOldSpellIsReady(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
            if MainAddon.PlayerSpecID() == 267 then
                if GetSetting('MOOption', false) then
                    if self == S.RainofFire or self == S.Cataclysm or self == S.SummonInfernal then
                        if not MOCheck or HL.CombatTime() < 1 then
                            return false, "MOCheck is false"
                        end
                    end
                end

                if self == S.GrimoireofSacrifice then
                    return BaseCheck and Player:BuffDown(S.GrimoireofSacrificeBuff)
                elseif self == S.ChannelDemonfire then
                    return BaseCheck and not Player:IsCasting(self)
                else
                    return BaseCheck
                end
            end
            return BaseCheck
        end
  , 267)

  local DestroOldPlayerAffectingCombat
  DestroOldPlayerAffectingCombat = HL.AddCoreOverride("Player.AffectingCombat",
          function(self)
                if MainAddon.PlayerSpecID() == 267 then
                    return S.Incinerate:InFlight() or Player:IsCasting(S.SoulFire) or Player:IsCasting(S.ChaosBolt) or S.ChaosBolt:InFlight() or DestroOldPlayerAffectingCombat(self)
                end
                return DestroOldPlayerAffectingCombat(self)
          end
  , 267)
end