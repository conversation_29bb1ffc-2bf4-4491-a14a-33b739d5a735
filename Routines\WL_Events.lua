    ---@class MainAddon
    local MainAddon = MainAddon
    ---@class MainAddon
    local M = MainAddon
    -- HeroLib
    local HL = HeroLibEx
    ---@class HeroCache
    local Cache = HeroCache;
    ---@class Unit
    local Unit = HL.Unit
    ---@class Unit
    local Player = Unit.Player
    ---@class Spell
    local Spell = HL.Spell
    ---@class Item
    local Item = HL.Item
    -- HeroRotation
    local GetTime = _G['GetTime']
    local UnitGUID = _G['UnitGUID']
    local find = string.find


    local S = Spell.Warlock.Demonology
    local I = Item.Warlock.Demonology

    -- File Locals
    M.Warlock = {}
    local Warlock = M.Warlock

    function Warlock.LoadEvent()
      --[[
      Warlock.ImmolationTable = {
        Destruction = {
          ImmolationDebuff = {},
        }
      }]]
      
      Warlock.GuardiansTable = {
        --{ID, name, spawnTime, ImpCasts, Duration, despawnTime}
        Pets = {
        },
        ImpCount = 0,
        FelguardDuration = 0,
        DreadstalkerDuration = 0,
        DemonicTyrantDuration = 0,
        VilefiendDuration = 0,
        PitLordDuration = 0,
        InfernalDuration = 0,
        OverfiendDuration = 0,
        BlasphemyDuration = 0,
        DarkglareDuration = 0,

        -- Used for Wild Imps spawn prediction
        InnerDemonsNextCast = 0,
        ImpsSpawnedFromHoG = 0
      }
      
      local PetsData = {
        [98035] = {
          name = "Dreadstalker",
          duration = 12.25
        },
        [55659] = {
          name = "Wild Imp",
          duration = 20
        },
        [143622] = {
          name = "Wild Imp",
          duration = 20
        },
        [17252] = {
          name = "Felguard",
          duration = 17
        },
        [135002] = {
          name = "Demonic Tyrant",
          duration = 15
        },
        [135816] = {
          name = "Vilefiend",
          duration = 15
        },
        [196111] = {
          name = "Pit Lord",
          duration = 10
        },
        [89] = {
          name = "Infernal",
          duration = 30
        },
        [185584] = {
          name = "Blasphemy",
          duration = 8
        },
        [103673] = {
          name = "Darkglare",
          duration = 25
        },
        -- Vilefiend Variants
        [226268] = { -- Gloomhound
          name = "Vilefiend",
          duration = 15
        },
        [226269] = { -- Charhound
          name = "Vilefiend",
          duration = 15
        },
        -- Destruction Overfiend
        [217429] = {
          name = "Overfiend",
          duration = 8
        },
      }
      
      --------------------------
      ----- Affliction ---------
      --------------------------
      -- Soul Rot buff tracker
      --[[Warlock.SoulRotBuffUp = false
      Warlock.SoulRotAppliedTime = 0
      HL:RegisterForSelfCombatEvent(
        function (_, Event, _, _, _, _, _, DestGUID, _, _, _, SpellID)
          if DestGUID == Player:GUID() and SpellID == 386998 then
            if Event == "SPELL_AURA_APPLIED" then
              Warlock.SoulRotBuffUp = true
              Warlock.SoulRotAppliedTime = GetTime()
            end
            if Event == "SPELL_AURA_REMOVED" then
              Warlock.SoulRotBuffUp = false
            end
          end
        end
        , "SPELL_AURA_APPLIED"
        , "SPELL_AURA_REMOVED"
      )]]
      
      --------------------------
      ----- Destruction --------
      --------------------------
      -- Immolate OnApply/OnRefresh Listener
      --[[HL:RegisterForSelfCombatEvent(
        function (...)
          DestGUID, _, _, _, SpellID = select(8, ...)
      
          --- Record the Immolate
          if SpellID == 157736 then
            Warlock.ImmolationTable.Destruction.ImmolationDebuff[DestGUID] = 0
          end
        end
        , "SPELL_AURA_APPLIED"
        , "SPELL_AURA_REFRESH"
      )
      -- Immolate OnRemove Listener
      HL:RegisterForSelfCombatEvent(
        function (...)
          DestGUID, _, _, _, SpellID = select(8, ...)
      
          -- Removes the Unit from Immolate Table
          if SpellID == 157736 then
            if Warlock.ImmolationTable.Destruction.ImmolationDebuff[DestGUID] then
              Warlock.ImmolationTable.Destruction.ImmolationDebuff[DestGUID] = nil
            end
          end
        end
        , "SPELL_AURA_REMOVED"
      )
      -- Immolate OnUnitDeath Listener
      HL:RegisterForCombatEvent(
        function (...)
          DestGUID = select(8, ...)
          -- Removes the Unit from Immolate Table
          if Warlock.ImmolationTable.Destruction.ImmolationDebuff[DestGUID] then
            Warlock.ImmolationTable.Destruction.ImmolationDebuff[DestGUID] = nil
          end
        end
        , "UNIT_DIED"
        , "UNIT_DESTROYED"
      )
      -- Conflagrate Listener
      HL:RegisterForSelfCombatEvent(
        function (...)
          DestGUID, _, _, _, SpellID = select(8, ...)
      
          -- Add a stack to the table
          if SpellID == 17962 then
            if Warlock.ImmolationTable.Destruction.ImmolationDebuff[DestGUID] then
              Warlock.ImmolationTable.Destruction.ImmolationDebuff[DestGUID] = Warlock.ImmolationTable.Destruction.ImmolationDebuff[DestGUID]+1
            end
          end
        end
        , "SPELL_CAST_SUCCESS"
      )]]
      
      --------------------------
      ----- Demonology ---------
      --------------------------
      -- Update the GuardiansTable
      function Warlock.UpdatePetTable()
        for key, petTable in pairs(Warlock.GuardiansTable.Pets) do
          if petTable then
            -- Remove expired pets
            if GetTime() >= petTable.despawnTime then
              if petTable.name == "Wild Imp" then
                Warlock.GuardiansTable.ImpCount = Warlock.GuardiansTable.ImpCount - 1
              end
              if petTable.name == "Felguard"  then
                Warlock.GuardiansTable.FelguardDuration = 0
              elseif petTable.name == "Dreadstalker" then
                Warlock.GuardiansTable.DreadstalkerDuration = 0
              elseif petTable.name == "Demonic Tyrant" then
                Warlock.GuardiansTable.DemonicTyrantDuration = 0
              elseif petTable.name == "Vilefiend" then
                Warlock.GuardiansTable.VilefiendDuration = 0
              elseif petTable.name == "Pit Lord" then
                Warlock.GuardiansTable.PitLordDuration = 0
              elseif petTable.name == "Infernal" then
                Warlock.GuardiansTable.InfernalDuration = 0
              elseif petTable.name == "Blasphemy" then
                Warlock.GuardiansTable.BlasphemyDuration = 0
              elseif petTable.name == "Overfiend" then
                Warlock.GuardiansTable.OverfiendDuration = 0
              elseif petTable.name == "Darkglare" then
                Warlock.GuardiansTable.DarkglareDuration = 0
              end
              Warlock.GuardiansTable.Pets[key] = nil
            end
          end
          -- Remove any imp that has casted all of its bolts
          if petTable.ImpCasts <= 0 then
            Warlock.GuardiansTable.ImpCount = Warlock.GuardiansTable.ImpCount - 1
            Warlock.GuardiansTable.Pets[key] = nil
          end
          -- Update Durations
          if GetTime() <= petTable.despawnTime then
            petTable.Duration = petTable.despawnTime - GetTime()
            if petTable.name == "Felguard" then
              Warlock.GuardiansTable.FelguardDuration = petTable.Duration
            elseif petTable.name == "Dreadstalker" then
              Warlock.GuardiansTable.DreadstalkerDuration = petTable.Duration
            elseif petTable.name == "Demonic Tyrant" then
              Warlock.GuardiansTable.DemonicTyrantDuration = petTable.Duration
            elseif petTable.name == "Vilefiend" then
              Warlock.GuardiansTable.VilefiendDuration = petTable.Duration
            elseif petTable.name == "Pit Lord" then
              Warlock.GuardiansTable.PitLordDuration = petTable.Duration
            elseif petTable.name == "Infernal" then
              Warlock.GuardiansTable.InfernalDuration = petTable.Duration
            elseif petTable.name == "Blasphy" then
              Warlock.GuardiansTable.BlasphemyDuration = petTable.Duration
            elseif petTable.name == "Overfiend" then
              Warlock.GuardiansTable.OverfiendDuration = petTable.Duration
            elseif petTable.name == "Darkglare" then
              Warlock.GuardiansTable.DarkglareDuration = petTable.Duration
            end
          end
        end
      end
      
      -- Add demon to table
      HL:RegisterForSelfCombatEvent(
        function (...)
          local timestamp,Event,_,SourceGUID,_,_,_,UnitPetGUID,_,_,_,SpellID=select(1,...)
          ---@diagnostic disable-next-line: param-type-mismatch
          local _, _, _, _, _, _, _, UnitPetID = find(UnitPetGUID, "(%S+)-(%d+)-(%d+)-(%d+)-(%d+)-(%d+)-(%S+)")
          UnitPetID = tonumber(UnitPetID)

          -- Add pet
          if (UnitPetGUID ~= UnitGUID("pet") and Event == "SPELL_SUMMON" and PetsData[UnitPetID]) then
            local summonedPet = PetsData[UnitPetID]
            local petDuration
            if summonedPet.name == "Wild Imp" then
              Warlock.GuardiansTable.ImpCount = Warlock.GuardiansTable.ImpCount + 1
              petDuration = summonedPet.duration
            elseif summonedPet.name == "Felguard" then
              Warlock.GuardiansTable.FelguardDuration = summonedPet.duration
              petDuration = summonedPet.duration
            elseif summonedPet.name == "Dreadstalker" then
              Warlock.GuardiansTable.DreadstalkerDuration = summonedPet.duration
              petDuration = summonedPet.duration
            elseif summonedPet.name == "Demonic Tyrant" then
              if (SpellID == 265187) then
                Warlock.GuardiansTable.DemonicTyrantDuration = summonedPet.duration
                petDuration = summonedPet.duration
              end
            elseif summonedPet.name == "Vilefiend" then
              Warlock.GuardiansTable.VilefiendDuration = summonedPet.duration
              petDuration = summonedPet.duration
            elseif summonedPet.name == "Pit Lord" then
              Warlock.GuardiansTable.PitLordDuration = summonedPet.duration
              petDuration = summonedPet.duration
            elseif summonedPet.name == "Infernal" then
              Warlock.GuardiansTable.InfernalDuration = summonedPet.duration
              petDuration = summonedPet.duration
            elseif summonedPet.name == "Blasphemy" then
              Warlock.GuardiansTable.BlasphemyDuration = summonedPet.duration
              petDuration = summonedPet.duration
            elseif summonedPet.name == "Overfiend" then
              Warlock.GuardiansTable.OverfiendDuration = summonedPet.duration
              petDuration = summonedPet.duration
            elseif summonedPet.name == "Darkglare" then
              Warlock.GuardiansTable.DarkglareDuration = summonedPet.duration
              petDuration = summonedPet.duration
            end
            local petTable = {
              ID = UnitPetGUID,
              name = summonedPet.name,
              spawnTime = GetTime(),
              ImpCasts = 5,
              Duration = petDuration,
              despawnTime = GetTime() + tonumber(petDuration)
            }
            table.insert(Warlock.GuardiansTable.Pets,petTable)
          end

          -- Add 15 seconds and 7 casts to all pets when Tyrant is cast
          if PetsData[UnitPetID] and PetsData[UnitPetID].name == "Demonic Tyrant" then
            for key, petTable in pairs(Warlock.GuardiansTable.Pets) do
              if (petTable and petTable.name ~= "Demonic Tyrant" and petTable.name ~= "Pit Lord") then
                petTable.despawnTime = petTable.despawnTime + 15
                petTable.ImpCasts = petTable.ImpCasts + 7
              end
            end
          end

          -- Update when next Wild Imp will spawn from Inner Demons talent
          if UnitPetID == 143622 then
            Warlock.GuardiansTable.InnerDemonsNextCast = GetTime() + 12
          end

          -- Updates how many Wild Imps have yet to spawn from HoG cast
          if UnitPetID == 55659 and Warlock.GuardiansTable.ImpsSpawnedFromHoG > 0 then
            Warlock.GuardiansTable.ImpsSpawnedFromHoG = Warlock.GuardiansTable.ImpsSpawnedFromHoG - 1
          end

          -- Update the pet table
          Warlock.UpdatePetTable()
        end
        , "SPELL_SUMMON"
        , "SPELL_CAST_SUCCESS"
      )

      -- Decrement ImpCasts and Implosion Listener
      HL:RegisterForCombatEvent(
        function (...)
          local SourceGUID,_,_,_,UnitPetGUID,_,_,_,SpellID = select(4, ...)

          -- Check for imp bolt casts
          if SpellID == 104318 then
            for key, petTable in pairs(Warlock.GuardiansTable.Pets) do
              if SourceGUID == petTable.ID then
                petTable.ImpCasts = petTable.ImpCasts - 1
              end
            end
          end

          -- Clear the imp table upon Implosion cast
          if SourceGUID == Player:GUID() and SpellID == 196277 then
            for key, petTable in pairs(Warlock.GuardiansTable.Pets) do
              if petTable.name == "Wild Imp" then
                Warlock.GuardiansTable.Pets[key] = nil
              end
            end
            Warlock.GuardiansTable.ImpCount = 0
          end

          -- Update the imp table
          Warlock.UpdatePetTable()
        end
        , "SPELL_CAST_SUCCESS"
      )
      
      -- Track when we last received PI
      --[[Warlock.LastPI = 0
      HL:RegisterForCombatEvent(
        function (...)
          DestGUID, _, _, _, SpellID = select(8, ...)
      
          --- Record the Immolate
          if SpellID == 10060 and DestGUID == Player:GUID() then
            Warlock.LastPI = GetTime()
          end
        end
        , "SPELL_AURA_APPLIED"
        , "SPELL_AURA_REFRESH"
      )]]
      
      -- Keep track how many Soul Shards we have
      --[[Warlock.SoulShards = 0
      function Warlock.UpdateSoulShards()
        Warlock.SoulShards = Player:SoulShards()
      end]]
      
        -- On Successful HoG cast add how many Imps will spawn
        HL:RegisterForSelfCombatEvent(
          function(_, event, _, _, _, _, _, _, _, _, _, SpellID)
            if SpellID == 105174 then
              Warlock.GuardiansTable.ImpsSpawnedFromHoG = Warlock.GuardiansTable.ImpsSpawnedFromHoG + (Player:SoulShards() >= 3 and 3 or Player:SoulShards())
            end
          end
          , "SPELL_CAST_SUCCESS"
        )    


        -- CUSTOM
        --[[
        HL:RegisterForEvent(
          function(event, unitTarget, castGUID, spellID)
              if unitTarget == 'player' then
                  local SpecID = MainAddon.PlayerSpecID();
                  if SpecID == 266 then
                      local found = false
                      if spellID == 30146 then
                          ---@param InterruptSpell Spell
                          for i, InterruptSpell in pairs(MainAddon.CONST.Interrupts) do
                              if InterruptSpell.SpellID == 119914 or InterruptSpell.SpellID == 89766 then
                                  found = true
                              end
                          end
    
                          if not found then
                              local PlayerClass, PlayerSpec = HL.SpecID_ClassesSpecs[SpecID][1], string.gsub(HL.SpecID_ClassesSpecs[SpecID][2], "%s+", "")
                              _G['C_Timer'].After(3, function()
                                for Spec, Spells in pairs(HL.Spell[PlayerClass]) do
                                    if Spec == PlayerSpec then
                                        ---@param ThisSpell Spell
                                        for SpellKey, ThisSpell in pairs(Spells) do
                                            if ThisSpell:IsAvailable() and ThisSpell:ID() == 119914 then
                                              ThisSpell.offGCD = true
                                                MainAddon.CONST.Interrupts[SpellKey] = HL.Spell[PlayerClass][PlayerSpec][SpellKey]
                                            end
                                        end
                                    end
                                end
                            end)
                          end
                      end
                  end
    
                  if SpecID == 267 or SpecID == 265 then
                    local found = false
                      if spellID == 691 then
                          ---@param InterruptSpell Spell
                          for i, InterruptSpell in pairs(MainAddon.CONST.Interrupts) do
                              if InterruptSpell.SpellID == 119910 then
                                  found = true
                              end
                          end
    
                          if not found then
                              local PlayerClass, PlayerSpec = HL.SpecID_ClassesSpecs[SpecID][1], string.gsub(HL.SpecID_ClassesSpecs[SpecID][2], "%s+", "")
                              _G['C_Timer'].After(3, function()
                                for Spec, Spells in pairs(HL.Spell[PlayerClass]) do
                                    if Spec == PlayerSpec then
                                        ---@param ThisSpell Spell
                                        for SpellKey, ThisSpell in pairs(Spells) do
                                            if ThisSpell:IsAvailable() and ThisSpell:ID() == 119910 then
                                                ThisSpell.offGCD = true
                                                MainAddon.CONST.Interrupts[SpellKey] = HL.Spell[PlayerClass][PlayerSpec][SpellKey]
                                            end
                                        end
                                    end
                                end
                            end)
                          end
                      end
                  end
              end
          end,
        "UNIT_SPELLCAST_SUCCEEDED")
        ]]

        HL:RegisterForEvent(
          function(event, unitTarget)
              if unitTarget == 'player' then
                C_Timer.After(4, function()
                  if Unit.Pet:IsActive() then
                      local SpecID = MainAddon.PlayerSpecID();
                      local PlayerClass, PlayerSpec = HL.SpecID_ClassesSpecs[SpecID][1], string.gsub(HL.SpecID_ClassesSpecs[SpecID][2], "%s+", "")
                      for Spec, Spells in pairs(HL.Spell[PlayerClass]) do
                          if Spec == PlayerSpec then
                              ---@param ThisSpell Spell
                              for SpellKey, ThisSpell in pairs(Spells) do
                                  if ThisSpell:IsAvailable() and (ThisSpell:ID() == 119914 or ThisSpell:ID() == 119910) then
                                      ThisSpell.offGCD = true
                                      MainAddon.CONST.Interrupts[SpellKey] = HL.Spell[PlayerClass][PlayerSpec][SpellKey]
                                  end
                              end
                          end
                      end
                  end
                end)
              end
          end,
        "UNIT_PET")
    end