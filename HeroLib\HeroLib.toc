## Interface: 110105

## Title: Hero|cFFA330C9Lib|r
## Author: HeroTC, Aethys
## Version: 11.1.5.04
## Notes: Core Library used by HeroTC Addons. Can be used by others 3rd-Party Addons.

## DefaultState: Enabled
## LoadOnDemand: 0
## Dependencies: HeroDBC, HeroCache
## SavedVariables: HeroLibDB

# Utils
Utils.lua

# GUI
GUI\Panels.lua

# API
Settings.lua
Core.lua

# Classes
Class\Main.lua
Class\Unit\Main.lua
Class\Unit\Power.lua
Class\Unit\Range.lua
Class\Unit\Cast.lua
Class\Unit\Aura.lua
Class\Unit\Control.lua
Class\Unit\TimeToDie.lua
Class\Unit\List.lua
Class\Unit\Player\Main.lua
Class\Unit\Player\Power.lua
Class\Unit\Player\Aura.lua
Class\Unit\Player\Enemies.lua
Class\Unit\Player\Equipment.lua
Class\Unit\Player\Stat.lua
Class\Unit\Player\Instance.lua
Class\Unit\Player\Tank.lua
Class\Unit\Player\Totem.lua
Class\Unit\Pet\Main.lua
Class\Spell\Main.lua
Class\Spell\Cooldown.lua
Class\Item.lua

# Events
Events\Main.lua
Events\Unit.lua
Events\Player.lua
Events\SplashEnemies.lua
Events\Aura.lua
Events\PMultiplier.lua
Events\Spell.lua
Events\Prev_Spell.lua
Events\InFlight.lua
Events\Action.lua

# Misc
Misc\ToSort.lua
Misc\Overrides.lua

Main.lua
