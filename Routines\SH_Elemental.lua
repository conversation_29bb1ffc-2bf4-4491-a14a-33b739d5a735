function A_262(...)
    -- HR UPDATE: feat(Elemental): Update to latest APL for 11.1.5
    -- REMEMBER: CastMagic(S.LiquidMagmaTotem, nil, "192222-Magic", magicgroundspell_lmt)
    -- REMEMBER: if Player:IsMovingFor() >= GetSetting('movement_threshold', 1) then
    ---@class MainAddon
    local MainAddon = MainAddon
    ---@class MainAddon
    local M = MainAddon
    -- HeroLib
    local HL = HeroLibEx
    ---@class HeroCache
    local Cache = HeroCache
    ---@type Unit
    local Unit = HL.Unit
    ---@type Unit
    local Player = Unit.Player
    ---@type Unit
    local Target = Unit.Target
    ---@type Unit
    local MouseOver = Unit.MouseOver
    ---@class Unit
    local Party = Unit.Party
    ---@type Unit
    local Pet = Unit.Pet
    ---@type Spell
    local Spell = HL.Spell
    ---@type Item
    local Item = HL.Item
    local AoEON = M.AoEON
    local Cast = M.Cast
    local CastMagic = M.CastMagic
    local CastTargetIf = MainAddon.CastTargetIf
    local CastCycle = MainAddon.CastCycle
    local CastLeftNameplate = MainAddon.CastLeftNameplate
    -- LUAs
    local GetTime = _G['GetTime']
    local C_Timer = _G['C_Timer']
    local GetWeaponEnchantInfo = _G['GetWeaponEnchantInfo']
    local GetNumGroupMembers = _G['GetNumGroupMembers']
    local UnitGroupRolesAssigned = _G['UnitGroupRolesAssigned']
    local IsInGroup = _G['IsInGroup']
    local StaticPopup_Show = _G['StaticPopup_Show']
    local num = M.num
    local select = select
    local mathmin = math.min
    local Delay             = C_Timer.After

    ---@class Shaman
    local Shaman = M.Shaman

    -- Define S/I for spell and item arrays
    local S = Spell.Shaman.Elemental
    local I = Item.Shaman.Elemental

    MainAddon.Toggle.Special["HoldMaelstrom"] = {
      Icon = MainAddon.GetTexture(Spell(344179)),
      Name = "Hold Maelstrom",
      Description = "Hold Maelstrom stacks.",
      Spec = 262
    }

    -- Create table to exclude above trinkets from On Use function
    local OnUseExcludes = {
      -- TWW Trinkets
      I.FunhouseLens:ID(),
      I.HouseofCards:ID(),
      I.SpymastersWeb:ID(),
      -- Older Items
      I.NeuralSynapseEnhancer:ID(),
    }

    -- GUI Settings
    local GetSetting = MainAddon.Config.GetClassSetting
    local Config_Key = MainAddon.GetClassVariableName()
    local Config_Color = '0070DD'
    local Config_Table = {
        key = Config_Key,
        title = 'Shaman - Elemental',
        subtitle = '?? ' .. MainAddon.Version,
        width = 600,
        height = 700,
        profiles = true,
        config = {
            { type = 'header', text = '', size = 18, align = 'Center', color = Config_Color },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
            { type = 'header', text = 'DPS', color = Config_Color },
            { type = 'spinner', text = ' Start AoE rotation at x targets', key = 'aoetcount', icon = S.ChainLightning:ID(), min = 1, max = 5, default = 3 },
            { type = 'checkspin', text = ' AoE: Pool Maelstrom before Ascendance (Threshold)', icon = S.MaelstromSurgeBuff:ID(), key = 'poolmaelstromvalue', min = 1, max = 110, default_spin = 110, default_check = true },
            { type = 'checkspin', text = ' Pool Maelstrom - TTD Threshold', icon = S.MaelstromSurgeBuff:ID(), key = 'holdmaelstromvalue', min = 0, max = 120, default_spin = 20, default_check = false },
            { type = 'checkbox', text = 'Enable Raid Prepull Sequence', icon = S.StormElemental:ID(), key = 'prepull_enabled', default = true },
            { type = 'spinner', text = 'Movement threshold for moving filler logic', key = 'movement_threshold', min = 0, max = 15, default = 1 },         
            { type = 'spacer' },
            { type = 'header', text = 'Defensives', color = Config_Color },
            { type = 'checkspin', text = ' Astral Shift', icon = 108271, key = 'AstralShift', min = 1, max = 99, default_spin = 35, default_check = false },
            { type = 'checkspin', text = ' Healing Surge (Raid/Party)', icon = 8004, key = 'HealingSurge', min = 1, max = 99, default_spin = 30, default_check = true },
            { type = 'checkspin', text = ' Healing Surge (Solo)', icon = 8004, key = 'HealingSurgeSolo', min = 1, max = 99, default_spin = 60, default_check = true },
            { type = 'checkspin', text = ' Healing Stream Totem (Raid/Party)', icon = 5394, key = 'HSTGroup', min = 1, max = 99, default_spin = 40, default_check = false },
            { type = 'checkspin', text = ' Healing Stream Totem (Solo)', icon = 5394, key = 'HSTSolo', min = 1, max = 99, default_spin = 70, default_check = false },
            { type = 'checkspin', text = ' Stone Bulwark Totem', icon = S.StoneBulwarkTotem:ID(), key = 'Bulwark', min = 1, max = 99, default_spin = 25, default_check = true },
            { type = 'dropdown', text = 'Earth Elemental usage', key = 'EarthElemental', icon = S.EarthElemental:ID(), multiselect = true, list = { { text = 'On Aggro', key = 'EEDEF_aggro' }, { text = 'Tank is dead', key = 'EEDEF_tank_dead' } }, default = { 'EEDEF_aggro', 'EEDEF_tank_dead' } },
            { type = 'spacer' },
            { type = 'header', text = 'Utilities', color = Config_Color },
            { type = 'dropdown', text = 'Shield', key = 'shield',
              list = {
                  { text = 'Lightning Shield', key = 1 },
                  { text = 'Earth Shield', key = 2 },
              },
              default = 1,
            },
            { type = 'checkbox', text = ' Stop Rotation Ghost Wolf', icon = 2645, key = 'StopWolf', default = true },
            { type = 'dropdown', text = 'Ghost Wolf (Out of Combat)', icon = 2645, key = 'AutoWolfOOC', list = {{ text = 'Everywhere', key = 1 }, { text = 'Dungeon', key = 2 }, { text = 'Raid', key = 3 }, { text = 'Open world only', key = 5 }, { text = 'None', key = 4 }}, default = 5 }, 
            { type = 'dropdown', text = 'Ghost Wolf (In Combat)', icon = 2645, key = 'AutoWolfCombat', list = {{ text = 'Everywhere', key = 1 }, { text = 'Dungeon', key = 2 }, { text = 'Raid', key = 3 }, { text = 'Open world only', key = 5 }, { text = 'None', key = 4 }}, default = 4 },            
            { type = 'dropdown',
              text = ' Skyfury', key = 'sky',
              icon = S.Skyfury:ID(),
              multiselect = true,
              list = {
                  { text = 'Self', key = 'sky_self' },
                  { text = 'Friends', key = 'sky_friends' },
              },
              default = {
                  'sky_self',
                  'sky_friends'
              },
            },
            { type = 'checkspin', text = " Spiritwalker's Grace - Movement threshold", icon = S.SpiritwalkersGrace:ID(), key = 'swg', min = 0, max = 15, default_spin = 2, default_check = true },
            { type = 'checkbox', text = ' Magic Groundspell - Liquid Magma Totem', icon = S.LiquidMagmaTotem:ID(), key = 'magicgroundspell_lmt', default = false },
            { type = 'spacer' },
            { type = 'header', text = 'WeakAuras', color = Config_Color },
            { type = 'button', text = 'Import Enemies Count', width = 150, callback = function()
                if not _G.WeakAuras then
                    return false
                end
                _G.WeakAuras.Import("!WA:2!9zvWUTTrq0OyeeyHEW1PWiinfqXfXigfwWoPnhsrqIOdDCAvSDPKQtBrb1sYrIBm1Ul2DPTvUf1IICQh8NGqb6LEsFc5lGWOFb5q)aYxqNDjTTSTS1bXDND2HZ8M3mdlT2S9MnA2O398H0qoRbpvgc3(kY93SthfOlj7xSyeHfgZLBXPmDWQUB001tujIQejK(nH91(D4YEeTVWxt7bf7k5ymABXxobf1Gs3lsQ7wiWHXzWh3barnLac1EenLhpypUmABjrmy7IfdJafrNkjAi2PdNPh9CAIK3VIdpjYjHtIE3mhMQaFgPhibssVyhf9nWCE9st00Yhypd2fKXbAscW0ZLDJCPKe6UqBVWeIsTqGIY6MaYTR5vFZv)(SBmxW6TmHDBXSgLTA5tyr(g3T9ho9(fYUz0m4D(RzAFWXA32rscbZRBGrM9EYoKqmqzgPof38A46H2BX7fq05wibD5Ky5Bqu6frQREOkMeX37v5PNPgkHUOvA2xaoAeLfZpbiFLQ6tNDEdN3R0qnperdfE9Bw6p(BpsIiMm19tPrJwmMTZ6UR9k5U)WWo09HOTPr64F)teFXeToRyz20sEQoHYa5MTAw)fB4Ebx4OLEgVkYlKNWLF3u4VHHPknVNrBXD6KYSq09wSCf8NeWSpRIj1wJ7wgyrfyXpLJf3(khgYrUaFpwJ9OcOTyMtSwlresC8qSKP)ajfLI0SqBy5ud3oqlPD7IOXFUGSy5)5Oruni3gJYF4BfP0iruDKOyAeK9GpOsdSwFljGqMxJTCRxp7rbRJURo2ZqkvLhGj6KemnwUiq3afVFPvotKs7uHX1vEjHYQffXzv0XaZEYy4W85hzu0q(HO5TAGWI9zcpKKmMjECL60Gg60G7nFTqWkBPhuD55x8rph02T37N9Tpn(0IJzJ1RJxEDqYrd4U)yh0Ir1MJQx1SASdAsKDbZrg5vZ3n2XnIj9igp6yVRAUOYvgprNlR6QjisbYCROmP9YLlNd1kXnSWSFRTEwTMU(TAuZPUB2tfvMm1rltHCd4fgdH7eKAzfhN6AK2btDJ8xTwJM(nAwZRPtkgbbgcmiXgqbOcMsn56U13ATw1piLvqwMl7PxDeY1FT9fVlCFcRVy2c(xUk15DPHILo21kU5z8qRSF5()63wX4PIpLynxHjEjpc(NRi(8j2)TOg8bFe7udsgj5hZlU)TdimQHUZzp0dPUs9AzpjB63def0qlbwxD81pmY0zfvXYWLka7FhPgy0XeWzt70dtw2lM1(6zKSGSWSPd6qzuv8zfl(QlEaruFSuGg6RJLGkglx7lU7e7ruOVqcHutym1f0l5KapigODJ1VvCNjOyrBJJ7unIYYxIMEHr4YqW1YPAlUjTlJlHnfMZuwPUsjxIhnPjFNU36aZOPg4CNhhOSdvdO9eCPEIjTtACkw6Y6BFEqBiYz9ST(v3AUSNCiUnGeUtxSbmYAU7LzSXq0zYhUVMel5T)jU12azNAixq9OLx2VLMMq19RyfCrw9mZZZM2BptB13oXiwFC26qSlm(Ulq5YtKZCHH)OXuE(7YM4hACEc0sxgr78VK3F04e3OUqSmFET6Hz(LDOMgEzqz5RXkCAN(zx7cg8E6plAEA07DR7(s8ZOQv3F5voqbjDSFAv21g1XKaWcsIMm1hhlXyQ)cASQNR7gt(dQoxC(zh54g1EgvrcsGOyjLzg3dXIBpbReUYjLg5Jvx1ovUuPs4uzSzanppn4iBhhGs7q7wEwL33u9RR(1ZU7)(Q))p")
            end },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' },
            { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
        }
    }
    Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
    Config_Table.config = MainAddon.BuildDPSTrinketUI(Config_Table.config, Config_Color)
    Config_Table.config = MainAddon.BuildCombatPotionUI(Config_Table.config, "Elemental", Config_Color)
    MainAddon.SetConfig(262, Config_Table)

    -- ===== Start Custom =====
    local inCombat = false
    local inDungeon = false
    local inRaid = false
    local isMoving
    local HasMainHandEnchant, MHEnchantTimeRemains
    ---@type Unit?
    local TankUnit = nil
    local VarPoolForAscendance = false
    local magicgroundspell_lmt = GetSetting('magicgroundspell_lmt', false)
    local EnemiesMixed = {}

    local function GetTank()
        ---@param v Unit
        for i, v in pairs(Party) do
            if v:Exists() and UnitGroupRolesAssigned(v:ID()) == "TANK" then
                return v
            end
        end
        return nil
    end

    HL:RegisterForEvent(function(arg1, unitID)
      TankUnit = GetTank()
    end, "GROUP_ROSTER_UPDATE", "GROUP_JOINED", "GROUP_LEFT", "PLAYER_REGEN_ENABLED", "PLAYER_REGEN_DISABLED")

    local function EvaluateTrue()
        return true
    end

    local function AutoWolf()
      if inCombat and GetSetting('AutoWolfCombat', 4) ~= 4 and Player:BuffDown(S.GhostWolf) then
          if isMoving and S.GhostWolf:IsReady(Player) and not Target:IsInRange(30) then
              if GetSetting('AutoWolfCombat', 4) == 1 then
                  if Cast(S.GhostWolf) then
                      return "Auto Wolf Combat 1"
                  end
              end
              if GetSetting('AutoWolfCombat', 4) == 2 and inDungeon then
                  if Cast(S.GhostWolf) then
                      return "Auto Wolf Combat 2"
                  end
              end
              if GetSetting('AutoWolfCombat', 4) == 3 and inRaid then
                  if Cast(S.GhostWolf) then
                      return "Auto Wolf Combat 3"
                  end
              end
              if GetSetting('AutoWolfOOC', 4) == 5 and not inDungeon and not inRaid then
                  if Cast(S.GhostWolf) then
                      return "Auto Wolf OOC 5"
                  end
              end
          end
      end

      if not inCombat and GetSetting('AutoWolfOOC', 5) ~= 4 and Player:BuffDown(S.GhostWolf) then
          if Player:IsMovingFor() >= 2 and S.GhostWolf:IsReady(Player) then
              if GetSetting('AutoWolfOOC', 5) == 1 then
                  if Cast(S.GhostWolf) then
                      return "Auto Wolf OOC 1"
                  end
              end
              if GetSetting('AutoWolfOOC', 5) == 2 and inDungeon then
                  if Cast(S.GhostWolf) then
                      return "Auto Wolf OOC 2"
                  end
              end
              if GetSetting('AutoWolfOOC', 5) == 3 and inRaid then
                  if Cast(S.GhostWolf) then
                      return "Auto Wolf OOC 3"
                  end
              end
              if GetSetting('AutoWolfOOC', 5) == 5 and not inDungeon and not inRaid then
                  if Cast(S.GhostWolf) then
                      return "Auto Wolf OOC 5"
                  end
              end
          end
        end        
    end

    local function Defensives()
      local PlayerHealthPercentage = Player:HealthPercentage()

      if GetSetting('AstralShift_check', false) and S.AstralShift:IsReady(Player) and PlayerHealthPercentage <= GetSetting('AstralShift_spin', 30) then
          if Cast(S.AstralShift, true) then
              return "Defensive: AstralShift";
          end
      end

      if GetSetting('Bulwark_check', false) and S.StoneBulwarkTotem:IsReady(Player) and PlayerHealthPercentage <= GetSetting('Bulwark_spin', 30) then
          if Cast(S.StoneBulwarkTotem) then
              return "Defensive: Buwlark";
          end
      end

      if not Player:IsInSoloMode() then
          if GetSetting('HealingSurge_check', false) and PlayerHealthPercentage <= GetSetting('HealingSurge_spin', 30)
          and S.HealingSurge:IsReady(Player) then
              if Cast(S.HealingSurge) then
                  return "Defensive: HealingSurge RAID/PARTY";
              end
          end
          if GetSetting('HSTGroup_check', false) and MainAddon.HealingEngine:MedianHP() <= GetSetting('HSTGroup_spin', 30)
          and S.HealingStreamTotem:IsReady(Player) and not Player:TotemIsActive(S.HealingStreamTotem:Name()) then
              if Cast(S.HealingStreamTotem) then
                  return "Defensive: HST RAID/PARTY";
              end
          end
      else
          if GetSetting('HealingSurgeSolo_check', false) and PlayerHealthPercentage <= GetSetting('HealingSurgeSolo_spin', 30)
          and S.HealingSurge:IsReady(Player) then
              if Cast(S.HealingSurge) then
                  return "Defensive: HealingSurge SOLO";
              end
          end
          if GetSetting('HSTSolo_check', false) and PlayerHealthPercentage <= GetSetting('HSTSolo_spin', 30)
          and S.HealingStreamTotem:IsReady(Player) and not Player:TotemIsActive(S.HealingStreamTotem:Name()) then
              if Cast(S.HealingStreamTotem) then
                  return "Defensive: HST SOLO";
              end
          end
      end

      -- Earth Elemental
      if inCombat and S.EarthElemental:IsReady(Player) then
        local EEUsage = GetSetting('EarthElemental', {})
        if IsInGroup() and inDungeon then
            -- Use Earth Elemental if 'Tank is dead' option is selected
            if EEUsage['EEDEF_tank_dead'] then
                 if TankUnit and TankUnit:IsDeadOrGhost() then
                     if Cast(S.EarthElemental) then
                         return "Defensive: Earth Elemental - Tank Dead";
                     end
                 end
             end
             -- Use Earth Elemental if 'On Aggro' option is selected and player has aggro
             if EEUsage['EEDEF_aggro'] and Player:IsTankingAoE(40) then
                 if Cast(S.EarthElemental) then
                     return "Defensive: Earth Elemental - Aggro";
                 end
             end
          end
        end
    end

    local function Shields()
      if GetSetting('shield', 1) == 1 then
          if Player:BuffDown(S.LightningShield) and S.LightningShield:IsReady(Player) then
              if Cast(S.LightningShield) then
                  return "Lightning Shield"
              end
          end
      else
          if S.ElementalOrbit:IsAvailable() then
              if S.EarthShield:IsReady(Player) and (Player:BuffDown(S.EarthShieldSelfBuff) or (not inCombat and Player:BuffStack(S.EarthShieldSelfBuff) < 5)) then
                  if Cast(S.EarthShield, Player) then
                      return 'Earth Shield'
                  end
              end
          else
              if S.EarthShield:IsReady(Player) and (Player:BuffDown(S.EarthShieldOtherBuff) or (not inCombat and Player:BuffStack(S.EarthShieldOtherBuff) < 5)) then
                  if Cast(S.EarthShield, Player) then
                      return 'Earth Shield'
                  end
              end
          end
      end

      if S.ElementalOrbit:IsAvailable() then
          if Player:BuffDown(S.LightningShield) and S.LightningShield:IsReady(Player) then
              if Cast(S.LightningShield) then
                  return "Lightning Shield - Elemental Orbit"
              end
          end
          if Player:BuffDown(S.EarthShieldSelfBuff) and Player:BuffDown(S.EarthShieldOtherBuff) and S.EarthShield:IsReady(Player) then
              if Cast(S.EarthShield, Player) then
                  return "Earth Shield - Elemental Orbit"
              end
          end

          if (Player:BuffUp(S.EarthShieldSelfBuff) or Player:BuffUp(S.EarthShieldOtherBuff)) then
              if Target:IsATank() and not Target:IsUnit(Player) then
                  if S.EarthShield:IsReady(Target) and Target:BuffDown(S.EarthShieldSelfBuff, true) and Target:BuffDown(S.EarthShieldOtherBuff, true) then
                      if Cast(S.EarthShield) then
                          return "Earth Shield - Elemental Orbit - Tank"
                      end
                  end
              end
          end
      end
    end

    local function Buff()
        -- Check weapon enchants
        HasMainHandEnchant, MHEnchantTimeRemains = GetWeaponEnchantInfo()
        -- flametongue_weapon,if=talent.improved_flametongue_weapon.enabled
        if S.ImprovedFlametongueWeapon:IsAvailable() and (not HasMainHandEnchant or MHEnchantTimeRemains < 600000) and S.FlametongueWeapon:IsReady(Player) then
            if Cast(S.FlametongueWeapon) then return "flametongue_weapon enchant"; end
        end

        local sky = GetSetting('sky', {})
        if S.Skyfury:IsReady(Player) and (sky['sky_self'] and Player:BuffDown(S.Skyfury, true) or sky['sky_friends'] and M.GroupBuffMissing(S.Skyfury)) then
            if Cast(S.Skyfury) then
                return "Skyfury";
            end
        end

        -- thunderstrike_ward
        local ShieldEnchantID = select(8, GetWeaponEnchantInfo())
        if S.ThunderstrikeWard:IsReady(Player) and (not ShieldEnchantID or ShieldEnchantID ~= 7587) then
          if Cast(S.ThunderstrikeWard) then return "Thunderstrike Ward"; end
        end

        if inCombat and M.TargetIsValid() then
            if GetSetting('swg_check', true) and S.SpiritwalkersGrace:IsReady(Player) then
                if Player:IsMovingFor() > GetSetting('swg_spin', 2) then
                    if Cast(S.SpiritwalkersGrace) then
                        return "Spiritwalker's Grace";
                    end
                end
            end
        end
    end    
    
    -- ===== End Custom =====

    -- ===== Rotation Variables =====
    local VarMaelstrom
    local VarMaelCap = 100 + 50 * num(S.SwellingMaelstrom:IsAvailable()) + 25 * num(S.PrimordialCapacity:IsAvailable())
    local BossFightRemains = 11111
    local FightRemains = 11111
    local HasMainHandEnchant, MHEnchantTimeRemains
    local Enemies40y, Enemies10ySplash
    Shaman.ClusterTargets = 0

    --- ===== Trinket Variables =====
    ---@type Item
    local Trinket1, Trinket2
    local VarTrinket1ID, VarTrinket2ID
    ---@type Spell
    local VarTrinket1Spell, VarTrinket2Spell
    local VarTrinket1Range, VarTrinket2Range
    local VarTrinket1CastTime, VarTrinket2CastTime
    local VarTrinket1CD, VarTrinket2CD
    local VarTrinket1Ex, VarTrinket2Ex
    local VarTrinket1Buffs, VarTrinket2Buffs
    local VarSpecialTrinket1, VarSpecialTrinket2
    local VarTrinketFailures = 0

    --- ===== Trinket Variables =====
    local function SetTrinketVariables()
      local T1, T2 = Player:GetTrinketData(OnUseExcludes)
    
      -- If we don't have trinket items, try again in 5 seconds.
      if VarTrinketFailures < 5 and ((T1.ID == 0 or T2.ID == 0) or (T1.SpellID > 0 and not T1.Usable or T2.SpellID > 0 and not T2.Usable)) then
        VarTrinketFailures = VarTrinketFailures + 1
        Delay(5, function()
            SetTrinketVariables()
          end
        )
        return
      end
    
      Trinket1 = T1.Object
      Trinket2 = T2.Object
    
      VarTrinket1ID = T1.ID
      VarTrinket2ID = T2.ID
    
      VarTrinket1Spell = T1.Spell
      VarTrinket1Range = T1.Range
      VarTrinket1CastTime = T1.CastTime
      VarTrinket2Spell = T2.Spell
      VarTrinket2Range = T2.Range
      VarTrinket2CastTime = T2.CastTime
    
      VarTrinket1CD = T1.Cooldown
      VarTrinket2CD = T2.Cooldown
    
      VarTrinket1Ex = T1.Excluded
      VarTrinket2Ex = T2.Excluded
    
      VarTrinket1Buffs = Trinket1:HasUseBuff() or VarTrinket1ID == I.FunhouseLens:ID()
      VarTrinket2Buffs = Trinket2:HasUseBuff() or VarTrinket2ID == I.FunhouseLens:ID()
      VarSpecialTrinket1 = (VarTrinket1ID == I.HouseofCards:ID() or VarTrinket1ID == I.FunhouseLens:ID()) and not VarTrinket2Buffs and S.FirstAscendant:IsAvailable()
      VarSpecialTrinket2 = (VarTrinket2ID == I.HouseofCards:ID() or VarTrinket2ID == I.FunhouseLens:ID()) and not VarTrinket1Buffs and S.FirstAscendant:IsAvailable()
    end
    SetTrinketVariables()

    --- ===== Event Registrations =====
    HL:RegisterForEvent(function()
      VarTrinketFailures = 0
      SetTrinketVariables()
    end, "PLAYER_EQUIPMENT_CHANGED")

    --- ===== Event Registrations =====
    HL:RegisterForEvent(function()
      VarMaelCap = 100 + 50 * num(S.SwellingMaelstrom:IsAvailable()) + 25 * num(S.PrimordialCapacity:IsAvailable())
      S.PrimordialWave:RegisterInFlightEffect(327162)
      S.PrimordialWave:RegisterInFlight()
      S.LavaBurst:RegisterInFlight()

      S.LightningBolt:RegisterInFlight()
      S.ElementalBlast:RegisterInFlight()
    end, "LEARNED_SPELL_IN_TAB")
    S.PrimordialWave:RegisterInFlightEffect(327162)
    S.PrimordialWave:RegisterInFlight()
    S.LavaBurst:RegisterInFlight()

    S.LightningBolt:RegisterInFlight()
    S.ElementalBlast:RegisterInFlight()

    --- ===== Helper Functions =====
    local function RollingThunderNextTick()
      return 50 - (GetTime() - Shaman.LastRollingThunderTick)
    end

    local function LowestFlameShock(Enemies)
      local Lowest, BestTarget
      ---@param Enemy Unit
      for _, Enemy in pairs(Enemies) do
        local FSRemains = Enemy:DebuffRemains(S.FlameShockDebuff)
        if not Lowest or FSRemains < Lowest then
          Lowest = FSRemains
          BestTarget = Enemy
        end
      end
      return Lowest, BestTarget
    end

  
    --- ===== CastTargetIf Filter Functions =====
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterFlameShockRemains(TargetUnit)
      -- target_if=min:dot.flame_shock.remains
      return TargetUnit:DebuffRemains(S.FlameShockDebuff)
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFilterLightningRodRemains(TargetUnit)
      -- target_if=min:debuff.lightning_rod.remains
      return TargetUnit:DebuffRemains(S.LightningRodDebuff)
    end
    
    --- ===== CastTargetIf Condition Functions =====
    ---@param TargetUnit Unit
    local function EvaluateTargetIfEarthquakeAoE(TargetUnit)
      -- if=(debuff.lightning_rod.remains=0&talent.lightning_rod.enabled|maelstrom>variable.mael_cap-30)&(buff.echoes_of_great_sundering_es.up|buff.echoes_of_great_sundering_eb.up|!talent.echoes_of_great_sundering.enabled)
      -- Note: Buff checked before CastTargetIf.
      return TargetUnit:DebuffDown(S.LightningRodDebuff) and S.LightningRod:IsAvailable() or VarMaelstrom > VarMaelCap - 30
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFlameShockAoe(TargetUnit)
      -- if=cooldown.primordial_wave.remains<gcd&!dot.flame_shock.ticking&(talent.primordial_wave|spell_targets.chain_lightning<=3)&cooldown.ascendance.remains>10
      -- Note: All but !dot.flame_shock.ticking checked before CastTargetIf.
      return TargetUnit:DebuffDown(S.FlameShockDebuff)
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFlameShockST(TargetUnit)
      -- if=active_enemies=1&(dot.flame_shock.remains<2|active_dot.flame_shock=0)&(dot.flame_shock.remains<cooldown.primordial_wave.remains|!talent.primordial_wave.enabled)&(dot.flame_shock.remains<cooldown.liquid_magma_totem.remains|!talent.liquid_magma_totem.enabled)&!buff.surge_of_power.up&talent.fire_elemental.enabled
      -- Note: Target count, SoP buff, and FireElemental talent checked before CastTargetIf.
      return (TargetUnit:DebuffRemains(S.FlameShockDebuff) < 2 or S.FlameShockDebuff:AuraActiveCount() == 0) and (TargetUnit:DebuffRemains(S.FlameShockDebuff) < S.PrimordialWave:CooldownRemains() or not S.PrimordialWave:IsAvailable()) and (TargetUnit:DebuffRemains(S.FlameShockDebuff) < S.LiquidMagmaTotem:CooldownRemains() or not S.LiquidMagmaTotem:IsAvailable())
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfFlameShockST2(TargetUnit)
      -- if=spell_targets.chain_lightning>1&(talent.deeply_rooted_elements.enabled|talent.ascendance.enabled|talent.primordial_wave.enabled|talent.searing_flames.enabled|talent.magma_chamber.enabled)&(buff.surge_of_power.up&!buff.stormkeeper.up|!talent.surge_of_power.enabled)&dot.flame_shock.remains<6&talent.fire_elemental.enabled,cycle_targets=1
      -- Note: All but dot.flame_shock.remains<6 checked before CastTargetIf.
      return TargetUnit:DebuffRemains(S.FlameShockDebuff) < 6
    end
    ---@param TargetUnit Unit
    local function EvaluateTargetIfSpenderST(TargetUnit)
      -- if=maelstrom>variable.mael_cap-15|debuff.lightning_rod.remains<gcd|fight_remains<5
      return VarMaelstrom > VarMaelCap - 15 or TargetUnit:DebuffRemains(S.LightningRodDebuff) < Player:GCD() or BossFightRemains < 5
    end

    --- ===== CastCycle Functions =====
    ---@param TargetUnit Unit
    local function EvaluateCycleFlameShockRefreshable(TargetUnit)
      -- target_if=refreshable
      return TargetUnit:DebuffRefreshable(S.FlameShockDebuff)
    end
    ---@param TargetUnit Unit
    local function EvaluateCycleFlameShockRemains(TargetUnit)
      -- target_if=dot.flame_shock.remains
      return TargetUnit:DebuffUp(S.FlameShockDebuff)
    end
    ---@param TargetUnit Unit
    local function EvaluateCycleFlameShockRemains2(TargetUnit)
      -- target_if=dot.flame_shock.remains>2
      return TargetUnit:DebuffRemains(S.FlameShockDebuff) > 2
    end
    ---@param TargetUnit Unit
    local function EvaluateCycleLightningRod(TargetUnit)
      return TargetUnit:DebuffRefreshable(S.LightningRodDebuff)
    end

    --- ===== Rotation Functions =====
    local function Precombat()
      -- snapshot_stats
      -- flametongue_weapon,if=talent.improved_flametongue_weapon
      -- lightning_shield
      -- thunderstrike_ward
      -- Note: Moved above 3 lines to APL()
      -- variable,name=mael_cap,value=100+50*talent.swelling_maelstrom+25*talent.primordial_capacity
      -- variable,name=trinket_1_buffs,value=(trinket.1.has_use_buff|trinket.1.is.funhouse_lens)
      -- variable,name=trinket_2_buffs,value=(trinket.2.has_use_buff|trinket.2.is.funhouse_lens)
      -- variable,name=special_trinket1,value=(trinket.1.is.house_of_cards|trinket.1.is.funhouse_lens)&!(trinket.2.has_use_buff|trinket.2.is.funhouse_lens)&talent.first_ascendant
      -- variable,name=special_trinket2,value=(trinket.2.is.house_of_cards|trinket.2.is.funhouse_lens)&!(trinket.1.has_use_buff|trinket.1.is.funhouse_lens)&talent.first_ascendant
      -- Note: Moved above to variable declarations.
      -- stormkeeper
      if S.Stormkeeper:IsViable() and (not Player:StormkeeperUp()) then
        if Cast(S.Stormkeeper) then return "stormkeeper precombat 2"; end
      end
      -- Manually added: Opener abilities
      if S.StormElemental:IsViable() and (not Shaman.StormElemental.GreaterActive) then
        if Cast(S.StormElemental) then return "storm_elemental precombat 4"; end
      end
      if S.PrimordialWave:IsViable() then
        if Cast(S.PrimordialWave) then return "primordial_wave precombat 6"; end
      end
      if S.AncestralSwiftness:IsViable() then
        if Cast(S.AncestralSwiftness) then return "ancestral_swiftness precombat 8"; end
      end
      if S.LavaBurst:IsViable() then
        if Cast(S.LavaBurst) then return "lavaburst precombat 10"; end
      end
    end

    local function Prepull()
        if not GetSetting('prepull_enabled', true) then return end
        
        local CombinedPullTimer = M.CombinedPullTimer()

        -- Storm Elemental fallback
        if S.StormElemental:IsReady(Player, nil, nil, nil, true) and not Shaman.StormElemental.GreaterActive and (Player:PrevGCDP(1, S.LavaBurst) or Player:IsCasting(S.LavaBurst)) and CombinedPullTimer < 1.5 then
            if Cast(S.StormElemental) then return "storm_elemental prepull" end
        end

        if (CombinedPullTimer > 0 and CombinedPullTimer < 30) then
            -- Storm Elemental at -4.5s
            if S.StormElemental:IsReady(Player, nil, nil, nil, true) and not Shaman.StormElemental.GreaterActive and CombinedPullTimer < 4.5 and not Player:CanAttack(Target) then
                if Cast(S.StormElemental) then return "storm_elemental prepull" end
            end
            
            -- Stormkeeper at -3s
            if S.Stormkeeper:IsReady(Player, nil, nil, nil, true) and not Player:StormkeeperUp() and CombinedPullTimer < 3 then
                if Cast(S.Stormkeeper) then return "stormkeeper prepull" end
            end
            
            if CombinedPullTimer < 1.8 then
                -- Lava Burst at -1.8s
                if S.LavaBurst:IsReady(Target, nil, nil, nil, true) and Player:CanAttack(Target) then
                    if Cast(S.LavaBurst) then return "lava_burst prepull" end
                end
            end
        end
    end
    
    local function Aoe()
      -- fire_elemental
      if S.FireElemental:IsViable() then
        if Cast(S.FireElemental) then return "fire_elemental aoe 2"; end
      end
      -- storm_elemental,if=!buff.storm_elemental.up|!talent.echo_of_the_elementals
      if S.StormElemental:IsViable() and ((not Shaman.StormElemental.GreaterActive and not Shaman.StormElemental.LesserActive) or not S.EchooftheElementals:IsAvailable()) then
        if Cast(S.StormElemental) then return "storm_elemental aoe 4"; end
      end
      -- stormkeeper
      if S.Stormkeeper:IsViable() then
        if Cast(S.Stormkeeper) then return "stormkeeper aoe 6"; end
      end
      -- liquid_magma_totem,if=(cooldown.primordial_wave.remains<5*gcd|!talent.primordial_wave)&(active_dot.flame_shock<=active_enemies-3|active_dot.flame_shock<(active_enemies>?3))
      if S.LiquidMagmaTotem:IsViable() and ((S.PrimordialWave:CooldownRemains() < 5 * Player:GCD() or not S.PrimordialWave:IsAvailable()) and (S.FlameShockDebuff:AuraActiveCount() <= Shaman.ClusterTargets - 3 or S.FlameShockDebuff:AuraActiveCount() < mathmin(Shaman.ClusterTargets, 3))) then
        if CastMagic(S.LiquidMagmaTotem, nil, "192222-Magic", magicgroundspell_lmt) then return "liquid_magma_totem aoe 8"; end
      end
      -- flame_shock,target_if=min:debuff.lightning_rod.remains,if=cooldown.primordial_wave.remains<gcd&active_dot.flame_shock=0&(talent.primordial_wave|spell_targets.chain_lightning<=3)&cooldown.ascendance.remains>10
      if S.FlameShock:IsViable() and (S.PrimordialWave:CooldownRemains() < Player:GCD() and S.FlameShockDebuff:AuraActiveCount() == 0 and (S.PrimordialWave:IsAvailable() or Shaman.ClusterTargets <= 3) and S.Ascendance:CooldownRemains() > 10) then
        if CastTargetIf(S.FlameShock, Enemies10ySplash, "min", EvaluateTargetIfFilterLightningRodRemains, EvaluateTargetIfFlameShockAoe) then return "flame_shock aoe 10"; end
      end
      -- primordial_wave,if=active_dot.flame_shock=active_enemies>?6|(cooldown.liquid_magma_totem.remains>15|!talent.liquid_magma_totem)&cooldown.ascendance.remains>15
      if S.PrimordialWave:IsViable() and (S.FlameShockDebuff:AuraActiveCount() == mathmin(Shaman.ClusterTargets, 6) or (S.LiquidMagmaTotem:CooldownRemains() > 15 or not S.LiquidMagmaTotem:IsAvailable()) and S.Ascendance:CooldownRemains() > 15) then
        if Cast(S.PrimordialWave) then return "primordial_wave aoe 12"; end
      end
      -- ancestral_swiftness
      if S.AncestralSwiftness:IsViable() then
        if Cast(S.AncestralSwiftness) then return "ancestral_swiftness aoe 14"; end
      end
      -- ascendance,if=(talent.first_ascendant|fight_remains>200|fight_remains<80|buff.spymasters_web.up|variable.trinket_1_buffs&!trinket.1.is.spymasters_web&trinket.1.ready_cooldown|variable.trinket_2_buffs&!trinket.2.is.spymasters_web&trinket.2.ready_cooldown|equipped.neural_synapse_enhancer&cooldown.neural_synapse_enhancer.remains=0|equipped.bestinslots&cooldown.bestinslots.remains=0)&(buff.fury_of_storms.up|!talent.fury_of_the_storms)
      if S.Ascendance:IsReady() 
      and (Shaman.LightningElemental.Active or not S.FuryoftheStorms:IsAvailable()) then        
        if Cast(S.Ascendance) then return "ascendance aoe 16"; end
      end
      -- tempest,target_if=min:debuff.lightning_rod.remains,if=buff.arc_discharge.stack<2&(buff.surge_of_power.up|!talent.surge_of_power)
      if S.TempestAbility:IsViable() and (Player:BuffStack(S.ArcDischargeBuff) < 2 and (Player:BuffUp(S.SurgeofPowerBuff) or not S.SurgeofPower:IsAvailable())) then
        if CastTargetIf(S.TempestAbility, Enemies10ySplash, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then return "tempest aoe 18"; end
      end
      -- lightning_bolt,if=buff.stormkeeper.up&buff.surge_of_power.up&spell_targets.chain_lightning=2
      if S.LightningBolt:IsViable() and (Player:StormkeeperUp() and Player:BuffUp(S.SurgeofPowerBuff) and Shaman.ClusterTargets == 2) then
        if Cast(S.LightningBolt) then return "lightning_bolt aoe 20"; end
      end
      -- chain_lightning,if=active_enemies>=6&buff.surge_of_power.up
      if S.ChainLightning:IsViable() and (Shaman.ClusterTargets >= 6 and Player:BuffUp(S.SurgeofPowerBuff)) then
        if Cast(S.ChainLightning) then return "chain_lightning aoe 22"; end
      end
      -- chain_lightning,if=buff.storm_frenzy.stack=2&!talent.surge_of_power&maelstrom<variable.mael_cap-(15+buff.stormkeeper.up*spell_targets.chain_lightning*spell_targets.chain_lightning)
      if S.ChainLightning:IsViable() and (Player:BuffStack(S.StormFrenzyBuff) == 2 and not S.SurgeofPower:IsAvailable() and VarMaelstrom < VarMaelCap - (15 + num(Player:StormkeeperUp()) * Shaman.ClusterTargets * Shaman.ClusterTargets)) then
        if Cast(S.ChainLightning) then return "chain_lightning aoe 24"; end
      end
      -- lava_burst,target_if=dot.flame_shock.remains,if=cooldown_react&buff.lava_surge.up&buff.fusion_of_elements_fire.up&!buff.master_of_the_elements.up&(maelstrom>52-5*talent.eye_of_the_storm&(buff.echoes_of_great_sundering_es.up|!talent.echoes_of_great_sundering))
      if S.LavaBurst:IsViable() and (Player:BuffUp(S.LavaSurgeBuff) and Player:BuffUp(S.FusionofElementsFire) and not Player:MotEUp() and (VarMaelstrom > 52 - 5 * num(S.EyeoftheStorm:IsAvailable() and Player:BuffUp(S.EchoesofGreatSunderingBuff)))) then
        if CastCycle(S.LavaBurst, Enemies10ySplash, EvaluateCycleFlameShockRemains) then return "lava_burst aoe 26"; end
      end
      -- earthquake,if=(maelstrom>variable.mael_cap-10*(spell_targets.chain_lightning+1)|buff.master_of_the_elements.up|buff.ascendance.up&buff.ascendance.remains<3|fight_remains<5)&(buff.echoes_of_great_sundering_es.up|buff.echoes_of_great_sundering_eb.up|!talent.echoes_of_great_sundering&(!talent.elemental_blast|active_enemies>1+talent.tempest))
      if S.Earthquake:IsViable() and ((VarMaelstrom > VarMaelCap - 10 * (Shaman.ClusterTargets + 1) or Player:MotEUp() or Player:BuffUp(S.AscendanceBuff) and Player:BuffRemains(S.AscendanceBuff) < 3 or BossFightRemains < 5) and (Player:BuffUp(S.EchoesofGreatSunderingBuff) or not S.EchoesofGreatSundering:IsAvailable() and (not S.ElementalBlast:IsAvailable() or Shaman.ClusterTargets > 1 + num(S.Tempest:IsAvailable())))) then
        if Cast(S.Earthquake) then return "earthquake aoe 28"; end
      end
      -- elemental_blast,target_if=min:debuff.lightning_rod.remains,if=(maelstrom>variable.mael_cap-10*(spell_targets.chain_lightning+1)|buff.master_of_the_elements.up|buff.ascendance.up&buff.ascendance.remains<3|fight_remains<5)&(active_enemies<=1+talent.tempest|talent.echoes_of_great_sundering&!buff.echoes_of_great_sundering_eb.up)
      if S.ElementalBlast:IsViable() and ((VarMaelstrom > VarMaelCap - 10 * (Shaman.ClusterTargets + 1) or Player:MotEUp() or Player:BuffUp(S.AscendanceBuff) and Player:BuffRemains(S.AscendanceBuff) < 3 or BossFightRemains < 5) and (Shaman.ClusterTargets <= 1 + num(S.Tempest:IsAvailable()) or S.EchoesofGreatSundering:IsAvailable() and Player:BuffDown(S.EchoesofGreatSunderingBuff))) then
        if CastTargetIf(S.ElementalBlast, Enemies10ySplash, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then return "elemental_blast aoe 30"; end
      end
      -- earth_shock,target_if=min:debuff.lightning_rod.remains,if=(maelstrom>variable.mael_cap-10*(spell_targets.chain_lightning+1)|buff.master_of_the_elements.up|buff.ascendance.up&buff.ascendance.remains<3|fight_remains<5)&talent.echoes_of_great_sundering&!buff.echoes_of_great_sundering_es.up
      if S.EarthShock:IsViable() and ((VarMaelstrom > VarMaelCap - 10 * (Shaman.ClusterTargets + 1) or Player:MotEUp() or Player:BuffUp(S.AscendanceBuff) and Player:BuffRemains(S.AscendanceBuff) < 3 or BossFightRemains < 5) and S.EchoesofGreatSundering:IsAvailable() and Player:BuffDown(S.EchoesofGreatSunderingBuff)) then
        if CastTargetIf(S.EarthShock, Enemies10ySplash, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then return "earth_shock aoe 32"; end
      end
      -- earthquake,if=talent.lightning_rod&lightning_rod<active_enemies&(buff.stormkeeper.up|buff.tempest.up|!talent.surge_of_power)&(buff.echoes_of_great_sundering_es.up|buff.echoes_of_great_sundering_eb.up|!talent.echoes_of_great_sundering&(!talent.elemental_blast|active_enemies>1+talent.tempest))
      if S.Earthquake:IsViable() and (S.LightningRod:IsAvailable() and S.LightningRodDebuff:AuraActiveCount() < Shaman.ClusterTargets and (Player:StormkeeperUp() or Player:BuffUp(S.TempestBuff) or not S.SurgeofPower:IsAvailable()) and (Player:BuffUp(S.EchoesofGreatSunderingBuff) or not S.EchoesofGreatSundering:IsAvailable() and (not S.ElementalBlast:IsAvailable() or Shaman.ClusterTargets > 1 + num(S.Tempest:IsAvailable())))) then
        if Cast(S.Earthquake) then return "earthquake aoe 34"; end
      end
      -- elemental_blast,target_if=min:debuff.lightning_rod.remains,if=talent.lightning_rod&lightning_rod<active_enemies&(buff.stormkeeper.up|buff.tempest.up|!talent.surge_of_power)&(active_enemies<=1+talent.tempest|talent.echoes_of_great_sundering&!buff.echoes_of_great_sundering_eb.up)
      if S.ElementalBlast:IsViable() and (S.LightningRod:IsAvailable() and S.LightningRodDebuff:AuraActiveCount() < Shaman.ClusterTargets and (Player:StormkeeperUp() or Player:BuffUp(S.TempestBuff) or not S.SurgeofPower:IsAvailable()) and (Shaman.ClusterTargets <= 1 + num(S.Tempest:IsAvailable()) or S.EchoesofGreatSundering:IsAvailable() and Player:BuffDown(S.EchoesofGreatSunderingBuff))) then
        if CastTargetIf(S.ElementalBlast, Enemies10ySplash, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then return "elemental_blast aoe 36"; end
      end
      -- earth_shock,target_if=min:debuff.lightning_rod.remains,if=talent.lightning_rod&lightning_rod<active_enemies&(buff.stormkeeper.up|buff.tempest.up|!talent.surge_of_power)&talent.echoes_of_great_sundering&!buff.echoes_of_great_sundering_es.up
      if S.EarthShock:IsViable() and (S.LightningRod:IsAvailable() and S.LightningRodDebuff:AuraActiveCount() < Shaman.ClusterTargets and (Player:StormkeeperUp() or Player:BuffUp(S.TempestBuff) or not S.SurgeofPower:IsAvailable()) and S.EchoesofGreatSundering:IsAvailable() and Player:BuffDown(S.EchoesofGreatSunderingBuff)) then
        if CastTargetIf(S.EarthShock, Enemies10ySplash, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then return "earth_shock aoe 38"; end
      end
      -- icefury,if=talent.fusion_of_elements&!(buff.fusion_of_elements_nature.up|buff.fusion_of_elements_fire.up)&(active_enemies<=4|!talent.elemental_blast|!talent.echoes_of_great_sundering)
      if S.Icefury:IsViable() and (S.FusionofElements:IsAvailable() and not (Player:BuffUp(S.FusionofElementsNature) or Player:BuffUp(S.FusionofElementsFire)) and (Shaman.ClusterTargets <= 4 or not S.ElementalBlast:IsAvailable() or not S.EchoesofGreatSundering:IsAvailable())) then
        if Cast(S.Icefury) then return "icefury aoe 40"; end
      end
      -- lava_burst,target_if=dot.flame_shock.remains,if=cooldown_react&buff.lava_surge.up&!buff.master_of_the_elements.up&talent.master_of_the_elements&active_enemies<=3
      if S.LavaBurst:IsViable() and (Player:BuffUp(S.LavaSurgeBuff) and not Player:MotEUp() and S.MasteroftheElements:IsAvailable() and Shaman.ClusterTargets <= 3) then
        if CastCycle(S.LavaBurst, Enemies10ySplash, EvaluateCycleFlameShockRemains) then return "lava_burst aoe 42"; end
      end
      -- lava_burst,target_if=dot.flame_shock.remains>2,if=!buff.master_of_the_elements.up&talent.master_of_the_elements&(buff.stormkeeper.up|buff.tempest.up|maelstrom>82-10*talent.eye_of_the_storm|maelstrom>52-5*talent.eye_of_the_storm&(buff.echoes_of_great_sundering_eb.up|!talent.elemental_blast))&active_enemies<=3&!talent.lightning_rod&talent.call_of_the_ancestors
      if S.LavaBurst:IsViable() and (not Player:MotEUp() and S.MasteroftheElements:IsAvailable() and (Player:StormkeeperUp() or Player:BuffUp(S.TempestBuff) or VarMaelstrom > 82 - 10 * num(S.EyeoftheStorm:IsAvailable()) or VarMaelstrom > 52 - 5 * num(S.EyeoftheStorm:IsAvailable()) and (Player:BuffUp(S.EchoesofGreatSunderingBuff) or not S.ElementalBlast:IsAvailable())) and Shaman.ClusterTargets <= 3 and not S.LightningRod:IsAvailable() and S.CalloftheAncestors:IsAvailable()) then
        if CastCycle(S.LavaBurst, Enemies10ySplash, EvaluateCycleFlameShockRemains2) then return "lava_burst aoe 44"; end
      end
      -- lava_burst,target_if=dot.flame_shock.remains>2,if=!buff.master_of_the_elements.up&active_enemies=2
      if S.LavaBurst:IsViable() and (not Player:MotEUp() and Shaman.ClusterTargets == 2) then
        if CastCycle(S.LavaBurst, Enemies10ySplash, EvaluateCycleFlameShockRemains2) then return "lava_burst aoe 46"; end
      end
      -- flame_shock,target_if=min:debuff.lightning_rod.remains,if=active_dot.flame_shock=0&buff.fusion_of_elements_fire.up&(!talent.elemental_blast|!talent.echoes_of_great_sundering&active_enemies>1+talent.tempest)
      if S.FlameShock:IsViable() and (S.FlameShockDebuff:AuraActiveCount() == 0 and Player:BuffUp(S.FusionofElementsFire) and (not S.ElementalBlast:IsAvailable() or not S.EchoesofGreatSundering:IsAvailable() and Shaman.ClusterTargets > 1 + num(S.Tempest:IsAvailable()))) then
        if CastTargetIf(S.FlameShock, Enemies10ySplash, "min", EvaluateTargetIfFilterLightningRodRemains, EvaluateTargetIfFlameShockAoe) then return "flame_shock aoe 48"; end
      end
      -- earthquake,if=((buff.stormkeeper.up&spell_targets.chain_lightning>=6|buff.tempest.up)&talent.surge_of_power)&(buff.echoes_of_great_sundering_es.up|buff.echoes_of_great_sundering_eb.up|!talent.echoes_of_great_sundering&(!talent.elemental_blast|active_enemies>1+talent.tempest))
      if S.Earthquake:IsViable() and (((Player:StormkeeperUp() and Shaman.ClusterTargets >= 6 or Player:BuffUp(S.TempestBuff)) and S.SurgeofPower:IsAvailable()) and (Player:BuffUp(S.EchoesofGreatSunderingBuff) or not S.EchoesofGreatSundering:IsAvailable() and (not S.ElementalBlast:IsAvailable() or Shaman.ClusterTargets > 1 + num(S.Tempest:IsAvailable())))) then
        if Cast(S.Earthquake) then return "earthquake aoe 50"; end
      end
      -- elemental_blast,target_if=min:debuff.lightning_rod.remains,if=((buff.stormkeeper.up&active_enemies>=6|buff.tempest.up)&talent.surge_of_power)&(active_enemies<=1+talent.tempest|talent.echoes_of_great_sundering&!buff.echoes_of_great_sundering_eb.up)
      if S.ElementalBlast:IsViable() and (((Player:StormkeeperUp() and Shaman.ClusterTargets >= 6 or Player:BuffUp(S.TempestBuff)) and S.SurgeofPower:IsAvailable()) and (Shaman.ClusterTargets <= 1 + num(S.Tempest:IsAvailable()) or S.EchoesofGreatSundering:IsAvailable() and Player:BuffDown(S.EchoesofGreatSunderingBuff))) then
        if CastTargetIf(S.ElementalBlast, Enemies10ySplash, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then return "elemental_blast aoe 52"; end
      end
      -- earth_shock,target_if=min:debuff.lightning_rod.remains,if=((buff.stormkeeper.up&active_enemies>=6|buff.tempest.up)&talent.surge_of_power)&talent.echoes_of_great_sundering&!buff.echoes_of_great_sundering_es.up
      if S.EarthShock:IsViable() and (((Player:StormkeeperUp() and Shaman.ClusterTargets >= 6 or Player:BuffUp(S.TempestBuff)) and S.SurgeofPower:IsAvailable()) and S.EchoesofGreatSundering:IsAvailable() and Player:BuffDown(S.EchoesofGreatSunderingBuff)) then
        if CastTargetIf(S.EarthShock, Enemies10ySplash, "min", EvaluateTargetIfFilterLightningRodRemains, nil) then return "earth_shock aoe 54"; end
      end
      -- frost_shock,if=buff.icefury_dmg.up&!buff.ascendance.up&!buff.stormkeeper.up&talent.call_of_the_ancestors
      if S.FrostShock:IsViable() and (Player:IcefuryUp() and Player:BuffDown(S.AscendanceBuff) and not Player:StormkeeperUp() and S.CalloftheAncestors:IsAvailable()) then
        if Cast(S.FrostShock) then return "frost_shock moving aoe 56"; end
      end
      -- chain_lightning
      if S.ChainLightning:IsViable() then
        if Cast(S.ChainLightning) then return "chain_lightning aoe 58"; end
      end
      if Player:IsMovingFor() >= GetSetting('movement_threshold', 1) then
        -- flame_shock,moving=1,target_if=refreshable
        if S.FlameShock:IsViable() then
          if CastCycle(S.FlameShock, Enemies10ySplash, EvaluateCycleFlameShockRefreshable) then return "flame_shock moving aoe 60"; end
        end
        -- frost_shock,moving=1
        if S.FrostShock:IsViable() then
          if Cast(S.FrostShock) then return "frost_shock moving aoe 62"; end
        end
      end
    end
    
    local function SingleTarget()
      -- fire_elemental
      if S.FireElemental:IsViable() then
        if Cast(S.FireElemental) then return "fire_elemental single_target 2"; end
      end
      -- storm_elemental,if=!buff.storm_elemental.up|!talent.echo_of_the_elementals
      if S.StormElemental:IsViable() and ((not Shaman.StormElemental.GreaterActive and not Shaman.StormElemental.LesserActive) or not S.EchooftheElementals:IsAvailable()) then
        if Cast(S.StormElemental) then return "storm_elemental single_target 4"; end
      end
      -- stormkeeper,if=!talent.fury_of_the_storms|cooldown.primordial_wave.remains<gcd|!talent.primordial_wave
      if S.Stormkeeper:IsViable() and (not S.FuryoftheStorms:IsAvailable() or S.PrimordialWave:CooldownRemains() < Player:GCD() or not S.PrimordialWave:IsAvailable()) then
        if Cast(S.Stormkeeper) then return "stormkeeper single_target 6"; end
      end
      -- liquid_magma_totem,if=!dot.flame_shock.ticking&!buff.surge_of_power.up&!buff.master_of_the_elements.up
      if S.LiquidMagmaTotem:IsViable() and (Target:DebuffDown(S.FlameShockDebuff) and Player:BuffDown(S.SurgeofPowerBuff) and not Player:MotEUp()) then
        if CastMagic(S.LiquidMagmaTotem, nil, "192222-Magic", magicgroundspell_lmt) then return "liquid_magma_totem single_target 8"; end
      end
      -- flame_shock,if=!dot.flame_shock.ticking&!buff.surge_of_power.up&!buff.master_of_the_elements.up
      if S.FlameShock:IsViable() and (Target:DebuffDown(S.FlameShockDebuff) and Player:BuffDown(S.SurgeofPowerBuff) and not Player:MotEUp()) then
        if Cast(S.FlameShock) then return "flame_shock single_target 10"; end
      end
      -- primordial_wave
      if S.PrimordialWave:IsViable() then
        if Cast(S.PrimordialWave) then return "primordial_wave single_target 12"; end
      end
      -- ancestral_swiftness
      if S.AncestralSwiftness:IsViable() then
        if Cast(S.AncestralSwiftness) then return "ancestral_swiftness single_target 14"; end
      end
      -- ascendance,if=(talent.first_ascendant|fight_remains>200|fight_remains<80|buff.spymasters_web.up|variable.trinket_1_buffs&!trinket.1.is.spymasters_web&trinket.1.ready_cooldown|variable.trinket_2_buffs&!trinket.2.is.spymasters_web&trinket.2.ready_cooldown|equipped.neural_synapse_enhancer&cooldown.neural_synapse_enhancer.remains=0|equipped.bestinslots&cooldown.bestinslots.remains=0)&(buff.fury_of_storms.up|!talent.fury_of_the_storms)&(cooldown.primordial_wave.remains>25|!talent.primordial_wave)
      if S.Ascendance:IsReady() 
      and ((Shaman.LightningElemental.Active or not S.FuryoftheStorms:IsAvailable()) 
      and (S.PrimordialWave:CooldownRemains() > 25 or not S.PrimordialWave:IsAvailable())) then
        if Cast(S.Ascendance) then return "ascendance single_target 16"; end
      end
      -- tempest,if=buff.surge_of_power.up
      if S.TempestAbility:IsViable() and (Player:BuffUp(S.SurgeofPowerBuff)) then
        if Cast(S.TempestAbility) then return "tempest single_target 18"; end
      end
      -- lightning_bolt,if=buff.surge_of_power.up
      if S.LightningBolt:IsViable() and (Player:BuffUp(S.SurgeofPowerBuff)) then
        if Cast(S.LightningBolt) then return "lightning_bolt single_target 20"; end
      end
      -- tempest,if=buff.storm_frenzy.stack=2&!talent.surge_of_power.enabled
      if S.TempestAbility:IsViable() and (Player:BuffStack(S.StormFrenzyBuff) == 2 and not S.SurgeofPower:IsAvailable()) then
        if Cast(S.TempestAbility) then return "tempest single_target 22"; end
      end
      -- liquid_magma_totem,if=dot.flame_shock.refreshable&!buff.master_of_the_elements.up
      if S.LiquidMagmaTotem:IsViable() and (Target:DebuffRefreshable(S.FlameShockDebuff) and not Player:MotEUp()) then
        if CastMagic(S.LiquidMagmaTotem, nil, "192222-Magic", magicgroundspell_lmt) then return "liquid_magma_totem single_target 24"; end
      end
      -- flame_shock,if=dot.flame_shock.refreshable&!buff.surge_of_power.up&!buff.master_of_the_elements.up&talent.erupting_lava
      if S.FlameShock:IsViable() and (Target:DebuffRefreshable(S.FlameShockDebuff) and Player:BuffDown(S.SurgeofPowerBuff) and not Player:MotEUp() and S.EruptingLava:IsAvailable()) then
        if Cast(S.FlameShock) then return "flame_shock single_target 26"; end
      end
      -- elemental_blast,if=maelstrom>variable.mael_cap-15|buff.master_of_the_elements.up
      if S.ElementalBlast:IsViable() and (VarMaelstrom > VarMaelCap - 15 or Player:MotEUp()) then
        if Cast(S.ElementalBlast) then return "elemental_blast single_target 28"; end
      end
      -- earth_shock,if=maelstrom>variable.mael_cap-15|buff.master_of_the_elements.up
      if S.EarthShock:IsViable() and (VarMaelstrom > VarMaelCap - 15 or Player:MotEUp()) then
        if Cast(S.EarthShock) then return "earth_shock single_target 30"; end
      end
      -- icefury,if=!(buff.fusion_of_elements_nature.up|buff.fusion_of_elements_fire.up)
      if S.Icefury:IsViable() and (not (Player:BuffUp(S.FusionofElementsNature) or Player:BuffUp(S.FusionofElementsFire))) then
        if Cast(S.Icefury) then return "icefury single_target 32"; end
      end
      -- lava_burst,target_if=dot.flame_shock.remains>=2,if=!buff.master_of_the_elements.up&(!talent.master_of_the_elements|buff.lava_surge.up|buff.tempest.up|buff.stormkeeper.up|cooldown.lava_burst.charges_fractional>1.8|maelstrom>82-10*talent.eye_of_the_storm|maelstrom>52-5*talent.eye_of_the_storm&(buff.echoes_of_great_sundering_eb.up|!talent.elemental_blast))
      if S.LavaBurst:IsViable() and (Target:DebuffRemains(S.FlameShockDebuff) >= 2 and not Player:MotEUp() and (not S.MasteroftheElements:IsAvailable() or Player:BuffUp(S.LavaSurgeBuff) or Player:BuffUp(S.TempestBuff) or Player:StormkeeperUp() or S.LavaBurst:ChargesFractional() > 1.8 or VarMaelstrom > 82 - 10 * num(S.EyeoftheStorm:IsAvailable()) or VarMaelstrom > 52 - 5 * num(S.EyeoftheStorm:IsAvailable()) and (Player:BuffUp(S.EchoesofGreatSunderingBuff) or not S.ElementalBlast:IsAvailable()))) then
        if Cast(S.LavaBurst) then return "lava_burst single_target 34"; end
      end
      -- earthquake,if=buff.echoes_of_great_sundering_eb.up&(buff.tempest.up|buff.stormkeeper.up)&talent.surge_of_power&!talent.master_of_the_elements
      if S.Earthquake:IsViable() and (Player:BuffUp(S.EchoesofGreatSunderingBuff) and (Player:BuffUp(S.TempestBuff) or Player:StormkeeperUp()) and S.SurgeofPower:IsAvailable() and not S.MasteroftheElements:IsAvailable()) then
        if Cast(S.Earthquake) then return "earthquake single_target 36"; end
      end
      -- elemental_blast,if=(buff.tempest.up|buff.stormkeeper.up)&talent.surge_of_power&!talent.master_of_the_elements
      if S.ElementalBlast:IsViable() and ((Player:BuffUp(S.TempestBuff) or Player:StormkeeperUp()) and S.SurgeofPower:IsAvailable() and not S.MasteroftheElements:IsAvailable()) then
        if Cast(S.ElementalBlast) then return "elemental_blast single_target 38"; end
      end
      -- earth_shock,if=(buff.tempest.up|buff.stormkeeper.up)&talent.surge_of_power&!talent.master_of_the_elements
      if S.EarthShock:IsViable() and ((Player:BuffUp(S.TempestBuff) or Player:StormkeeperUp()) and S.SurgeofPower:IsAvailable() and not S.MasteroftheElements:IsAvailable()) then
        if Cast(S.EarthShock) then return "earth_shock single_target 40"; end
      end
      -- tempest
      if S.TempestAbility:IsViable() then
        if Cast(S.TempestAbility) then return "tempest single_target 42"; end
      end
      -- lightning_bolt,if=buff.storm_elemental.up&buff.wind_gust.stack<4
      if S.LightningBolt:IsViable() and ((Shaman.StormElemental.GreaterActive or Shaman.StormElemental.LesserActive) and Player:BuffStack(S.WindGustBuff) < 4) then
        if Cast(S.LightningBolt) then return "lightning_bolt single_target 44"; end
      end
      -- frost_shock,if=buff.icefury_dmg.up&!buff.ascendance.up&!buff.stormkeeper.up&talent.call_of_the_ancestors
      if S.FrostShock:IsViable() and (Player:IcefuryUp() and Player:BuffDown(S.AscendanceBuff) and not Player:StormkeeperUp() and S.CalloftheAncestors:IsAvailable()) then
        if Cast(S.FrostShock) then return "frost_shock moving single_target 46"; end
      end
      -- lightning_bolt
      if S.LightningBolt:IsViable() then
        if Cast(S.LightningBolt) then return "lightning_bolt single_target 48"; end
      end
      if Player:IsMovingFor() >= GetSetting('movement_threshold', 1) then
        -- flame_shock,moving=1,target_if=refreshable
        -- Note: Since SingleTarget() now doesn't cover 2 target cleave, the below line covers this one as well.
        -- flame_shock,moving=1,if=movement.distance>6
        if S.FlameShock:IsViable() then
          if Cast(S.FlameShock) then return "flame_shock single_target 50"; end
        end
        -- frost_shock,moving=1
        if S.FrostShock:IsViable() then
          if Cast(S.FrostShock) then return "frost_shock single_target 52"; end
        end
      end
    end

    --- ======= MAIN =======
    local function APL()
        magicgroundspell_lmt = GetSetting('magicgroundspell_lmt', false)
        inCombat = Player:AffectingCombat()
        inDungeon = Player:IsInDungeonArea()
        inRaid = Player:IsInRaidArea()
        isMoving = Player:IsMoving()

        VarPoolForAscendance = false
        if AoEON() and 
           Shaman.ClusterTargets >= GetSetting('aoetcount', 3) and 
           S.Ascendance:IsAvailable() and S.Ascendance:CooldownRemains() < 5 and 
           GetSetting('poolmaelstromvalue_check', true) and 
           VarMaelstrom < GetSetting('poolmaelstromvalue_spin', 110) then
            VarPoolForAscendance = true
        end

        --Stop WOLF
        if Player:BuffUp(S.GhostWolf) and GetSetting('StopWolf', false) then
            return
        end
        Shaman.Targets = 0
        Enemies40y = Player:GetEnemiesInRange(40)
        Enemies10ySplash = Target:GetEnemiesInSplashRange(10)
        Shaman.Targets = #Enemies40y
        Shaman.ClusterTargets = #Target:GetEnemiesInSplashRange(10)

        if AoEON() then
            if Shaman.ClusterTargets < 2 then
                Shaman.ClusterTargets = Shaman.Targets
                EnemiesMixed = Enemies40y
            else
                Shaman.Targets = Shaman.ClusterTargets
                EnemiesMixed = Enemies10ySplash
            end
        else
            Shaman.Targets = 1
            Shaman.ClusterTargets = 1
            EnemiesMixed = {Target}
        end

        if M.TargetIsValid() or inCombat then
            -- Calculate fight_remains
            BossFightRemains = HL.BossFightRemains()
            FightRemains = BossFightRemains
            if FightRemains == 11111 then
                FightRemains = HL.FightRemains(EnemiesMixed, false)
            end

            -- Store our Maelstrom count into a variable
            VarMaelstrom = Player:MaelstromP()
        else
            VarMaelstrom = 0
        end

        -- Burst Potion
        if Target:IsSpellInRange(S.LightningBolt) then
            if MainAddon.UsePotion() then
                MainAddon.SetTopColor(1, "Combat Potion")
            end
        end

        -- Defensive
        local ShouldReturn = Defensives();
        if ShouldReturn then
            return ShouldReturn;
        end

        local ShouldReturn = Shields()
        if ShouldReturn then
            return ShouldReturn;
        end

        local ShouldReturn = AutoWolf()
        if ShouldReturn then
            return ShouldReturn;
        end

        local ShouldReturn = Buff()
        if ShouldReturn then
            return ShouldReturn;
        end

        -- Auto Tremor Totem
        if S.TremorTotem:IsReady(Player) then
            local ShouldReturn = Shaman.EvaluateTremor(S.TremorTotem)
            if ShouldReturn then
                return ShouldReturn
            end
        end

        -- Prepull sequence
        if inRaid and HL.CombatTime() < 2 then
          local ShouldReturn = Prepull()
          if ShouldReturn then return ShouldReturn end
        end

        if M.TargetIsValid() then
          -- Trinkets
          local shouldReturn = MainAddon.TrinketDPS()
          if shouldReturn then
              return shouldReturn
          end  

          -- call Precombat
          if not Player:AffectingCombat() then
            local ShouldReturn = Precombat(); if ShouldReturn then return ShouldReturn; end
          end
          -- spiritwalkers_grace,moving=1,if=movement.distance>6
          -- Note: Too situational to include
          -- wind_shear
          if true then
            -- blood_fury,if=!talent.ascendance.enabled|buff.ascendance.up|cooldown.ascendance.remains>50
            if S.BloodFury:IsReady() and (not S.Ascendance:IsAvailable() or Player:BuffUp(S.AscendanceBuff) or S.Ascendance:CooldownRemains() > 50) then
              if Cast(S.BloodFury) then return "blood_fury main 2"; end
            end
            -- berserking,if=!talent.ascendance.enabled|buff.ascendance.up
            if S.Berserking:IsReady() and (not S.Ascendance:IsAvailable() or Player:BuffUp(S.AscendanceBuff)) then
              if Cast(S.Berserking) then return "berserking main 4"; end
            end
            -- fireblood,if=!talent.ascendance.enabled|buff.ascendance.up|cooldown.ascendance.remains>50
            if S.Fireblood:IsReady() and (not S.Ascendance:IsAvailable() or Player:BuffUp(S.AscendanceBuff) or S.Ascendance:CooldownRemains() > 50) then
              if Cast(S.Fireblood) then return "fireblood main 6"; end
            end
            -- ancestral_call,if=!talent.ascendance.enabled|buff.ascendance.up|cooldown.ascendance.remains>50
            if S.AncestralCall:IsReady() and (not S.Ascendance:IsAvailable() or Player:BuffUp(S.AscendanceBuff) or S.Ascendance:CooldownRemains() > 50) then
              if Cast(S.AncestralCall) then return "ancestral_call main 8"; end
            end
          end
          -- use_item,name=spymasters_web,if=(fight_remains>180&buff.spymasters_report.stack>25|buff.spymasters_report.stack>35|fight_remains<80)&cooldown.ascendance.ready&(buff.fury_of_storms.up|!talent.fury_of_the_storms)&(cooldown.primordial_wave.remains>25|!talent.primordial_wave|spell_targets.chain_lightning>=2)|buff.ascendance.remains>12&buff.spymasters_report.stack>25|fight_remains<21
          if I.SpymastersWeb:IsEquippedAndReady() and ((FightRemains > 180 and Player:BuffStack(S.SpymastersReportBuff) > 35 or Player:BuffStack(S.SpymastersReportBuff) > 35 or FightRemains < 80) and S.Ascendance:CooldownUp() and (Player:BuffUp(S.FuryofStormsBuff) or not S.FuryoftheStorms:IsAvailable()) and (S.PrimordialWave:CooldownRemains() > 25 or not S.PrimordialWave:IsAvailable() or Shaman.ClusterTargets >= 2) or Player:BuffRemains(S.AscendanceBuff) > 12 and Player:BuffStack(S.SpymastersReportBuff) > 25 or BossFightRemains < 21) then
            if Cast(I.SpymastersWeb) then return "spymasters_web main 10"; end
          end
          -- use_item,name=neural_synapse_enhancer,use_off_gcd=1,if=buff.ascendance.remains>12|cooldown.ascendance.remains>10
          if I.NeuralSynapseEnhancer:IsEquippedAndReady() and (Player:BuffRemains(S.AscendanceBuff) > 12 or S.Ascendance:CooldownRemains() > 10) then
            if Cast(I.NeuralSynapseEnhancer) then return "neural_synapse_enhancer main 12"; end
          end
          if (VarSpecialTrinket1 or VarSpecialTrinket2) and (Player:BuffRemains(S.AscendanceBuff) > 12 or S.Ascendance:CooldownRemains() > 90) or BossFightRemains < 16 then
            -- use_item,name=house_of_cards,use_off_gcd=1,if=(variable.special_trinket1|variable.special_trinket2)&(buff.ascendance.remains>12|cooldown.ascendance.remains>90)|fight_remains<16
            if I.HouseofCards:IsEquippedAndReady() then
              if Cast(I.HouseofCards) then return "house_of_cards main 14"; end
            end
            -- use_item,name=funhouse_lens,use_off_gcd=1,if=(variable.special_trinket1|variable.special_trinket2)&(buff.ascendance.remains>12|cooldown.ascendance.remains>90)|fight_remains<16
            if I.FunhouseLens:IsEquippedAndReady() then
              if Cast(I.FunhouseLens) then return "funhouse_lens main 16"; end
            end
          end
          -- use_item,slot=trinket1,use_off_gcd=1,if=!trinket.1.is.spymasters_web&!variable.special_trinket1&variable.trinket_1_buffs&((cooldown.primordial_wave.remains>25|!talent.primordial_wave|spell_targets.chain_lightning>=2)&(cooldown.ascendance.remains>trinket.1.cooldown.duration-5|buff.spymasters_report.stack>25)|buff.ascendance.remains>12|fight_remains<21)
          -- Note: spymasters_web already excluded via OnUseExcludes/VarTrinket1Ex
          if Trinket1 and Trinket1:IsReady() and not VarTrinket1Ex and not Player:IsItemBlacklisted(Trinket1) and (not VarSpecialTrinket1 and VarTrinket1Buffs and ((S.PrimordialWave:CooldownRemains() > 25 or not S.PrimordialWave:IsAvailable() or Shaman.ClusterTargets >= 2) and (S.Ascendance:CooldownRemains() > VarTrinket1CD - 5 or Player:BuffStack(S.SpymastersReportBuff) > 25) or Player:BuffRemains(S.AscendanceBuff) > 12 or BossFightRemains < 21)) then
            if Cast(Trinket1) then return "use_item trinket1 ("..Trinket1:Name()..") main 18"; end
          end
          -- use_item,slot=trinket2,use_off_gcd=1,if=!trinket.2.is.spymasters_web&!variable.special_trinket2&variable.trinket_2_buffs&((cooldown.primordial_wave.remains>25|!talent.primordial_wave|spell_targets.chain_lightning>=2)&(cooldown.ascendance.remains>trinket.2.cooldown.duration-5|buff.spymasters_report.stack>25)|buff.ascendance.remains>12|fight_remains<21)
          -- Note: spymasters_web already excluded via OnUseExcludes/VarTrinket2Ex
          if Trinket2 and Trinket2:IsReady() and not VarTrinket2Ex and not Player:IsItemBlacklisted(Trinket2) and (not VarSpecialTrinket2 and VarTrinket2Buffs and ((S.PrimordialWave:CooldownRemains() > 25 or not S.PrimordialWave:IsAvailable() or Shaman.ClusterTargets >= 2) and (S.Ascendance:CooldownRemains() > VarTrinket2CD - 5 or Player:BuffStack(S.SpymastersReportBuff) > 25) or Player:BuffRemains(S.AscendanceBuff) > 12 or BossFightRemains < 21)) then
            if Cast(Trinket2) then return "use_item trinket2 ("..Trinket2:Name()..") main 20"; end
          end
          -- use_item,slot=main_hand,use_off_gcd=1,if=(buff.fury_of_storms.up|!talent.fury_of_the_storms|cooldown.stormkeeper.remains>10)&(cooldown.primordial_wave.remains>25|!talent.primordial_wave)&cooldown.ascendance.remains>15|buff.ascendance.remains>12
          -- Note: Expanding to all non-trinket items
          local ItemToUse, _, ItemRange = Player:GetUseableItems(OnUseExcludes, nil, true)
          if ItemToUse and ((Shaman.LightningElemental.Active or not S.FuryoftheStorms:IsAvailable() or S.Stormkeeper:CooldownRemains() > 10) and (S.PrimordialWave:CooldownRemains() > 25 or not S.PrimordialWave:IsAvailable()) and S.Ascendance:CooldownRemains() > 15 or Player:BuffRemains(S.AscendanceBuff) > 12) then
            if Cast(ItemToUse) then return "use_item non-trinket ("..ItemToUse:Name()..") main 22"; end
          end
          -- use_item,slot=trinket1,use_off_gcd=1,if=!variable.trinket_1_buffs&(cooldown.ascendance.remains>20|trinket.2.cooldown.remains>20&cooldown.neural_synapse_enhancer.remains>20&cooldown.bestinslots.remains>20)
          if Trinket1 and Trinket1:IsReady() and not VarTrinket1Ex and not Player:IsItemBlacklisted(Trinket1) and (not VarTrinket1Buffs and (S.Ascendance:CooldownRemains() > 20 or Trinket2:CooldownRemains() > 20 and (I.NeuralSynapseEnhancer:CooldownRemains() > 20 or not I.NeuralSynapseEnhancer:IsEquipped()) and (I.BestinSlotsCaster:CooldownRemains() > 20 or not I.BestinSlotsCaster:IsEquipped()))) then
            if Cast(Trinket1) then return "use_item trinket1 ("..Trinket1:Name()..") main 24"; end
          end
          -- use_item,slot=trinket2,use_off_gcd=1,if=!variable.trinket_2_buffs&(cooldown.ascendance.remains>20|trinket.1.cooldown.remains>20&cooldown.neural_synapse_enhancer.remains>20&cooldown.bestinslots.remains>20)
          if Trinket2 and Trinket2:IsReady() and not VarTrinket2Ex and not Player:IsItemBlacklisted(Trinket2) and (not VarTrinket2Buffs and (S.Ascendance:CooldownRemains() > 20 or Trinket1:CooldownRemains() > 20 and (I.NeuralSynapseEnhancer:CooldownRemains() > 20 or not I.NeuralSynapseEnhancer:IsEquipped()) and (I.BestinSlotsCaster:CooldownRemains() > 20 or not I.BestinSlotsCaster:IsEquipped()))) then
            if Cast(Trinket2) then return "use_item trinket2 ("..Trinket2:Name()..") main 26"; end
          end
          -- lightning_shield,if=buff.lightning_shield.down
          -- Note: Handled above.
          -- natures_swiftness
          if S.NaturesSwiftness:IsReady() and Player:BuffDown(S.NaturesSwiftness) then
            if Cast(S.NaturesSwiftness) then return "natures_swiftness main 24"; end
          end
          -- invoke_external_buff,name=power_infusion
          -- Note: Not handling external buffs.
          -- run_action_list,name=aoe,if=spell_targets.chain_lightning>2
          if AoEON() and (Shaman.ClusterTargets >= GetSetting('aoetcount', 3)) then
            local ShouldReturn = Aoe(); if ShouldReturn then return ShouldReturn; end
            if Cast(S.Pool) then return "Pool for Aoe()"; end
          end
          -- run_action_list,name=single_target
          local ShouldReturn = SingleTarget(); if ShouldReturn then return ShouldReturn; end
          if Cast(S.Pool) then return "Pool for SingleTarget()"; end
        end
    end

    local function Init()
        S.FlameShockDebuff:RegisterAuraTracking()
        S.LightningRodDebuff:RegisterAuraTracking()
    end
    M.SetAPL(262, APL, Init)

    local ElemOldPlayerAffectingCombat
    ElemOldPlayerAffectingCombat = HL.AddCoreOverride("Player.AffectingCombat",
        function(self)
            return Player:IsCasting(S.LavaBurst) or S.LavaBurst:InFlight() 
            or Player:IsCasting(S.LightningBolt) or S.LightningBolt:InFlight() 
            or Player:IsCasting(S.ElementalBlast) or S.ElementalBlast:InFlight()
            or S.PrimordialWave:InFlight()
            or Player:IsCasting(S.Stormkeeper)
            or ElemOldPlayerAffectingCombat(self)
        end
    , 262)

    local OldSpellIsReady
    OldSpellIsReady = HL.AddCoreOverride ("Spell.IsReady",
        function (self, TargetetedUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)

            -- Ascendance pooling
            if VarPoolForAscendance and (self == S.Earthquake or self == S.EarthShock or self == S.ElementalBlast or self == S.Ascendance) then
              return false, "Pooling Maelstrom for Ascendance"
            end

            if self == S.EarthShock or self == S.ElementalBlast or self == S.Earthquake or self == S.NaturesSwiftness then
                if MainAddon.Toggle:GetToggle('HoldMaelstrom') or (GetSetting('holdmaelstromvalue_check', false) and FightRemains < GetSetting('holdmaelstromvalue_spin', 20)) then
                    return false, "Holding Maelstrom"
                end
            end

            local BaseCheck, Reason = OldSpellIsReady(self, TargetetedUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
            return BaseCheck, Reason
        end
    , 262)

    local OldIsCastable
    OldIsCastable = HL.AddCoreOverride("Spell.IsCastable",
            function(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                if Player:BuffUp(S.SpiritwalkersGrace) then
                    ignoreMovement = true
                end

                if self == S.NaturesSwiftness then
                    if Player:BuffUp(S.NaturesSwiftness) then
                        return false, "Natures Swiftness is already active"
                    end
                end

                if self == S.TempestAbility then
                    if Player:IsCasting(S.TempestAbility) then
                        return false, "Tempest is already Casting"
                    end
                end 

                if self == S.LiquidMagmaTotem then
                    if Player:TotemIsActive(971079) then
                      return false, "Liquid Magma Totem is not active"
                    end
                end

                if self == S.AstralShift then
                    if not inCombat then
                        return false, "Out of Combat"
                    end
                end

                local BaseCheck, Reason = OldIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
                return BaseCheck, Reason
            end
    , 262);

    HL.AddCoreOverride ("Player.MaelstromP",
    function()
      local Maelstrom = Player:Maelstrom()
      if not Player:IsCasting() then
        return Maelstrom
      else
        if Player:IsCasting(S.ElementalBlast) then
          return Maelstrom - 90
        elseif Player:IsCasting(S.Icefury) then
          return Maelstrom + 10
        elseif Player:IsCasting(S.LightningBolt) then
          return Maelstrom + 6
        elseif Player:IsCasting(S.LavaBurst) then
          return Maelstrom + 8
        elseif Player:IsCasting(S.ChainLightning) then
          --TODO: figure out the *actual* maelstrom you'll get from hitting your current target...
          --return Maelstrom + (4 * #SplashedEnemiesTable[Target])
          -- If you're hitting the best target with CL , this is 4*Shaman.ClusterTargets
          return Maelstrom + (2 * Shaman.ClusterTargets)
        else
          return Maelstrom
        end
      end
    end
  , 262)
  
  HL.AddCoreOverride ("Spell.IsViable",
    function(self)
      local BaseCheck = self:IsReady()
      if self == S.Stormkeeper or self == S.ElementalBlast or self == S.Icefury then
        local MovementPredicate = Player:BuffUp(S.SpiritwalkersGraceBuff) or not Player:IsMoving()
        return BaseCheck and MovementPredicate and not Player:IsCasting(self)
      elseif self == S.LightningBolt or self == S.ChainLightning then
        local MovementPredicate = Player:BuffUp(S.SpiritwalkersGraceBuff) or Player:BuffUp(S.StormkeeperBuff) or not Player:IsMoving()
        return BaseCheck and MovementPredicate
      elseif self == S.LavaBurst then
        local MovementPredicate = Player:BuffUp(S.SpiritwalkersGraceBuff) or Player:BuffUp(S.LavaSurgeBuff) or not Player:IsMoving()
        local a = Player:BuffUp(S.LavaSurgeBuff)
        local b = S.LavaBurst:Charges() >= 1 and not Player:IsCasting(S.LavaBurst)
        local c = S.LavaBurst:Charges() == 2 and Player:IsCasting(S.LavaBurst)
        return BaseCheck and MovementPredicate and (a or b or c)
      else
        return BaseCheck
      end
    end
  , 262)
  
  HL.AddCoreOverride ("Player.MotEUp",
    function()
      if not S.MasteroftheElements:IsAvailable() then return false end
      local MotEBuffUp = Player:BuffUp(S.MasteroftheElementsBuff)
      if not Player:IsCasting() then
        return MotEBuffUp
      else
        if Player:IsCasting(S.LavaBurst) then
          return true
        elseif Player:IsCasting(S.ElementalBlast) or Player:IsCasting(S.Icefury) or Player:IsCasting(S.LightningBolt) or Player:IsCasting(S.ChainLightning) then 
          return false
        else
          return MotEBuffUp
        end
      end
    end
  , 262)
  
  HL.AddCoreOverride ("Player.PotMUp",
    function()
      if not S.PoweroftheMaelstrom:IsAvailable() then return false end
      local PotMStacks = Player:BuffStack(S.PoweroftheMaelstromBuff)
      if not Player:IsCasting() then
        return PotMStacks > 0
      else
        if PotMStacks == 1 and (Player:IsCasting(S.LightningBolt) or Player:IsCasting(S.ChainLightning)) then
          return false
        else
          return PotMStacks > 0
        end
      end
    end
  , 262)
  
  HL.AddCoreOverride ("Player.StormkeeperUp",
      function()
        if not S.Stormkeeper:IsAvailable() then return false end
        local StormkeeperBuffUp = Player:BuffUp(S.StormkeeperBuff)
        if not Player:IsCasting() then
          return StormkeeperBuffUp
        else
          if Player:IsCasting(S.Stormkeeper) then
            return true
          else
            return StormkeeperBuffUp
          end
        end
      end
  , 262)
  
  HL.AddCoreOverride ("Player.IcefuryUp",
    function()
      if not S.Icefury:IsAvailable() then return false end
      local IcefuryBuffUp = Player:BuffUp(S.IcefuryBuff)
      if not Player:IsCasting() then
        return IcefuryBuffUp
      else
        if Player:IsCasting(S.Icefury) then
          return true
        else
          return IcefuryBuffUp
        end
      end
    end
  , 262)

    --- ===== Fire Elemental Tracker =====
    Shaman.FireElemental = {
      GreaterActive = false,
      LesserActive = false
    }
    Shaman.StormElemental = {
      GreaterActive = false,
      LesserActive = false
    }
    -- CUSTOM
    Shaman.LightningElemental = {
      Active = false
    }

    HL:RegisterForSelfCombatEvent(
      function (...)
        local DestGUID, _, _, _, SpellID = select(8, ...)
        -- CUSTOM
        if SpellID == 191716 then
            Shaman.LightningElemental.Active = true
            C_Timer.After(12, function()
              Shaman.LightningElemental.Active = false
            end)
        end

        -- Fire Elemental. SpellIDs are without and with Primal Elementalist
        if SpellID == 188592 or SpellID == 118291 then
          Shaman.FireElemental.GreaterActive = true
          C_Timer.After(30, function()
            Shaman.FireElemental.GreaterActive = false
          end)
        elseif SpellID == 462992 or SpellID == 462991 then
          Shaman.FireElemental.LesserActive = true
          C_Timer.After(15, function()
            Shaman.FireElemental.LesserActive = false
          end)
        -- Storm Elemental. SpellIDs are without and with Primal Elementalist
        elseif SpellID == 157299 or SpellID == 157319 then
          Shaman.StormElemental.GreaterActive = true
          C_Timer.After(30, function()
            Shaman.StormElemental.GreaterActive = false
          end)
        elseif SpellID == 462993 or SpellID == 462990 then
          Shaman.StormElemental.LesserActive = true
          C_Timer.After(15, function()
            Shaman.StormElemental.LesserActive = false
          end)
        end
      end
      , "SPELL_SUMMON"
    )
end