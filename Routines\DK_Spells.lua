---@class MainAddon
local MainAddon = MainAddon
---@class MainAddon
local M = MainAddon
-- HeroLib
local HL = HeroLibEx
 ---@class Unit
 local Unit = HL.Unit
 ---@class Unit
 local Player = Unit.Player
---@class Spell
local Spell = HL.Spell
local CreateSpell = MainAddon.CreateSpell
local CreateMultiSpell = MainAddon.CreateMultiSpell
---@class Item
local Item = HL.Item
local MergeTableByKey = HL.Utils.MergeTableByKey
local CastCycle = M.CastCycle
-- HeroRotation
local GetTime = _G['GetTime']

local OneRuneSpenders = { 42650, 55090, 207311, 43265, 152280, 77575, 115989, 45524, 3714, 343294, 111673 }

M.DeathKnight = {}
---@class DeathKnight
local DeathKnight = M.DeathKnight

-- GhoulTable
DeathKnight.GhoulTable = {
  AbominationExpiration = 0,
  ApocMagusExpiration = 0,
  ArmyMagusExpiration = 0,
  GargoyleExpiration = 0,
  SummonExpiration = 0,
  SummonedAbomination = nil,
  SummonedGargoyle = nil,
  SummonedGhoul = nil,
}
-- DnDTable
DeathKnight.DnDTable = {}
-- BonestormTable
DeathKnight.BonestormTable = {}

--- ============================ CONTENT ============================
--- ===== Ghoul Tracking =====
HL:RegisterForSelfCombatEvent(function(_, _, _, _, _, _, _, destGUID, _, _, _, spellId)
  if spellId == 46585 then
    DeathKnight.GhoulTable.SummonedGhoul = destGUID
    -- Unsure if there's any items that could extend the ghouls time past 60 seconds
    DeathKnight.GhoulTable.SummonExpiration = GetTime() + 60
  end
  if spellId == 49206 or spellId == 207349 then
    DeathKnight.GhoulTable.SummonedGargoyle = destGUID
    DeathKnight.GhoulTable.GargoyleExpiration = GetTime() + 25
  end
  if spellId == 455395 then
    DeathKnight.GhoulTable.SummonedAbomination = destGUID
    DeathKnight.GhoulTable.AbominationExpiration = GetTime() + 30
  end
end, "SPELL_SUMMON")

HL:RegisterForSelfCombatEvent(function(_, _, _, _, _, _, _, _, _, _, _, spellId)
  if spellId == 327574 then
    DeathKnight.GhoulTable.SummonedGhoul = nil
    DeathKnight.GhoulTable.SummonExpiration = 0
  end
end, "SPELL_CAST_SUCCESS")

HL:RegisterForCombatEvent(function(_, _, _, _, _, _, _, destGUID)
  if destGUID == DeathKnight.GhoulTable.SummonedGhoul then
    DeathKnight.GhoulTable.SummonedGhoul = nil
    DeathKnight.GhoulTable.SummonExpiration = 0
  end
  if destGUID == DeathKnight.GhoulTable.SummonedGargoyle then
    DeathKnight.GhoulTable.SummonedGargoyle = nil
    DeathKnight.GhoulTable.GargoyleExpiration = 0
  end
  if destGUID == DeathKnight.GhoulTable.SummonedAbomination then
    DeathKnight.GhoulTable.SummonedAbomination = nil
    DeathKnight.GhoulTable.AbominationExpiration = 0
  end
end, "UNIT_DESTROYED")

--- ===== Ghoul Tracker Functions =====
function DeathKnight.GhoulTable:AbomRemains()
  if DeathKnight.GhoulTable.AbominationExpiration == 0 then return 0 end
  return DeathKnight.GhoulTable.AbominationExpiration - GetTime()
end

function DeathKnight.GhoulTable:AbomActive()
  return DeathKnight.GhoulTable.SummonedAbomination ~= nil and DeathKnight.GhoulTable:AbomRemains() > 0
end

function DeathKnight.GhoulTable:ApocMagusRemains()
  return DeathKnight.GhoulTable.ApocMagusExpiration - GetTime()
end

function DeathKnight.GhoulTable:ApocMagusActive()
  return DeathKnight.GhoulTable:ApocMagusRemains() > 0
end

function DeathKnight.GhoulTable:ArmyMagusRemains()
  return DeathKnight.GhoulTable.ArmyMagusExpiration - GetTime()
end

function DeathKnight.GhoulTable:ArmyMagusActive()
  return DeathKnight.GhoulTable:ArmyMagusRemains() > 0
end

function DeathKnight.GhoulTable:GargRemains()
  if DeathKnight.GhoulTable.GargoyleExpiration == 0 then return 0 end
  return DeathKnight.GhoulTable.GargoyleExpiration - GetTime()
end

function DeathKnight.GhoulTable:GargActive()
  return DeathKnight.GhoulTable.SummonedGargoyle ~= nil and DeathKnight.GhoulTable:GargRemains() > 0
end

function DeathKnight.GhoulTable:GhoulRemains()
  if DeathKnight.GhoulTable.SummonExpiration == 0 then return 0 end
  return DeathKnight.GhoulTable.SummonExpiration - GetTime()
end

function DeathKnight.GhoulTable:GhoulActive()
  return DeathKnight.GhoulTable.SummonedGhoul ~= nil and DeathKnight.GhoulTable:GhoulRemains() > 0
end

--- ===== Death and Decay/Bonestorm Tracking =====
HL:RegisterForCombatEvent(function(_, _, _, srcGUID, _, _, _, destGUID, _, _, _, spellId)
  if srcGUID == Player:GUID() then
    -- Death and Decay
    if spellId == 52212 then
      DeathKnight.DnDTable[destGUID] = GetTime()
    -- Defile
    elseif spellId == 156000 then
      DeathKnight.DnDTable[destGUID] = GetTime()
    -- Bonestorm
    elseif spellId == 196528 then
      DeathKnight.BonestormTable[destGUID] = GetTime()
    end
  end
end, "SPELL_DAMAGE")

HL:RegisterForCombatEvent(function(_, _, _, _, _, _, _, destGUID)
  if DeathKnight.DnDTable[destGUID] then
    DeathKnight.DnDTable[destGUID] = nil
  end
end, "UNIT_DIED", "UNIT_DESTROYED")  


--- ============================ CONTENT ============================
-- Spells
if not Spell.DeathKnight then 
    Spell.DeathKnight = {} 
end

---@type DKCustomTable
Spell.DeathKnight.Custom = {
    -- Custom
    BloodFury = CreateMultiSpell(20572, 33702, 33697),
    DarkCommand = CreateSpell(56222),
    DeathGrip = CreateSpell(49576),
    GorefiendsGrasp = CreateSpell(108199),
    BlindingSleet = CreateSpell(207167),
    Lichborne = CreateSpell(49039),
    ControlUndead = CreateSpell(111673),
    RaiseAlly = CreateSpell(61999),
    DeathChain = CreateSpell(203173),
    Reanimation = CreateSpell(210128),
    DarkSuccorBuff = CreateSpell(101568),
    DeathPact = CreateSpell(17471),
    Strangulate = CreateSpell(47476),
}

---@type DKCommonsTable
Spell.DeathKnight.Commons = {
    -- Abilities
    DeathAndDecay                         = CreateSpell(43265),
    DeathCoil                             = CreateSpell(47541),
    -- Talents
    AbominationLimb                       = CreateSpell(383269),
    AntiMagicBarrier                      = CreateSpell(205727),
    AntiMagicShell                        = CreateSpell(48707),
    AntiMagicZone                         = CreateSpell(51052),
    Asphyxiate                            = CreateSpell(221562),
    Assimilation                          = CreateSpell(374383),
    ChainsofIce                           = CreateSpell(45524),
    CleavingStrikes                       = CreateSpell(316916),
    DeathStrike                           = CreateSpell(49998),
    EmpowerRuneWeapon                     = CreateSpell(47568),
    IceboundFortitude                     = CreateSpell(48792),
    IcyTalons                             = CreateSpell(194878),
    RaiseDead                             = CreateSpell(46585),
    RunicAttenuation                      = CreateSpell(207104),
    SacrificialPact                       = CreateSpell(327574),
    SoulReaper                            = CreateSpell(343294),
    UnholyGround                          = CreateSpell(374265),
    UnyieldingWill                        = CreateSpell(457574),
    -- Buffs
    AbominationLimbBuff                   = CreateSpell(383269),
    DeathAndDecayBuff                     = CreateSpell(188290),
    DeathStrikeBuff                       = CreateSpell(101568),
    EmpowerRuneWeaponBuff                 = CreateSpell(47568),
    IcyTalonsBuff                         = CreateSpell(194879),
    RuneofHysteriaBuff                    = CreateSpell(326918),
    UnholyStrengthBuff                    = CreateSpell(53365),
    -- Debuffs
    BloodPlagueDebuff                     = CreateSpell(55078),
    FrostFeverDebuff                      = CreateSpell(55095),
    MarkofFyralathDebuff                  = CreateSpell(414532),
    SoulReaperDebuff                      = CreateSpell(343294),
    VirulentPlagueDebuff                  = CreateSpell(191587),
    -- Racials
    AncestralCall                         = CreateSpell(274738),
    ArcanePulse                           = CreateSpell(260364),
    ArcaneTorrent                         = CreateSpell(50613),
    BagofTricks                           = CreateSpell(312411),
    Berserking                            = CreateSpell(26297),
    BloodFury                             = CreateSpell(20572),
    Fireblood                             = CreateSpell(265221),
    LightsJudgment                        = CreateSpell(255647),
    -- Interrupts
    MindFreeze                            = CreateSpell(47528),
    -- Custom
    Pool                                  = CreateSpell(999910)
}

---@type DeathbringerTable
Spell.DeathKnight.Deathbringer = {
  -- Talents
  BindinDarkness                        = CreateSpell(440031),
  DarkTalons                            = CreateSpell(436687),
  Exterminate                           = CreateSpell(441378),
  ReaperofSouls                         = CreateSpell(440002),
  ReapersMark                           = CreateSpell(439843),
  WitherAway                            = CreateSpell(441894),
  -- Buffs
  ExterminateBuff                       = CreateSpell(441416),
  PainfulDeathBuff                      = CreateSpell(447954),
  ReaperofSoulsBuff                     = CreateSpell(469172),
  -- Debuffs
  ReapersMarkDebuff                     = CreateSpell(434765),
}

---@type RideroftheApocalypseTable
Spell.DeathKnight.RideroftheApocalypse = {
  -- Talents
  AFeastofSouls                         = CreateSpell(444072),
  ApocalypseNow                         = CreateSpell(444040),
  -- Buffs
  AFeastofSoulsBuff                     = CreateSpell(440861),
  HungeringThirst                       = CreateSpell(444037),
  MograinesMightBuff                    = CreateSpell(444505),
  -- Debuffs
  TrollbaneSlowDebuff                   = CreateSpell(444834),
}
---@type SanlaynTable
Spell.DeathKnight.Sanlayn = {
    -- Abilities
    VampiricStrikeAction                  = CreateSpell(433895),
    -- Talents
    FrenziedBloodthirst                   = CreateSpell(434075),
    GiftoftheSanlayn                      = CreateSpell(434152),
    VampiricStrike                        = CreateSpell(433901),
    -- Buffs
    EssenceoftheBloodQueenBuff            = CreateSpell(433925),
    GiftoftheSanlaynBuff                  = CreateSpell(434153),
    InflictionofSorrowBuff                = CreateSpell(460049),
    VampiricStrikeBuff                    = CreateSpell(433899),
    -- Debuffs
    InciteTerrorDebuff                    = CreateSpell(458478),
}

---@class BloodTable
Spell.DeathKnight.Blood = {
    -- Abilities
    -- Talents
    BloodBoil                             = CreateSpell(50842),
    BloodTap                              = CreateSpell(221699),
    Blooddrinker                          = CreateSpell(206931),
    Bonestorm                             = CreateSpell(194844),
    Coagulopathy                          = CreateSpell(391477),
    Consumption                           = CreateSpell(274156),
    DancingRuneWeapon                     = CreateSpell(49028),
    DeathsCaress                          = CreateSpell(195292),
    EverlastingBond                       = CreateSpell(377668),
    HeartStrike                           = CreateSpell(206930),
    Heartbreaker                          = CreateSpell(221536),
    Marrowrend                            = CreateSpell(195182),
    RelishinBlood                         = CreateSpell(317610),
    RuneTap                               = CreateSpell(194679),
    ShatteringBone                        = CreateSpell(377640),
    Tombstone                             = CreateSpell(219809),
    VampiricBlood                         = CreateSpell(55233),
    -- Buffs
    BoneShieldBuff                        = CreateSpell(195181),
    CoagulopathyBuff                      = CreateSpell(391481),
    ConsumptionBuff                       = CreateSpell(274156),
    DancingRuneWeaponBuff                 = CreateSpell(81256),
    InnerResilienceBuff                   = CreateSpell(450706), -- Tome of Light's Devotion buff
    HemostasisBuff                        = CreateSpell(273947),
    IceboundFortitudeBuff                 = CreateSpell(48792),
    RuneTapBuff                           = CreateSpell(194679),
    VampiricBloodBuff                     = CreateSpell(55233),
    -- TWW2 Effects
    LuckoftheDrawBuff                     = CreateSpell(1218601), -- TWW S2 2P
    PiledriverBuff                        = CreateSpell(457506), -- TWW S2 4P
    UnbreakableBuff                       = CreateSpell(457468), -- TWW S2 2P
    UnbrokenBuff                          = CreateSpell(457473), -- TWW S2 2P
}
---@class DKCustomTable
Spell.DeathKnight.Blood = MergeTableByKey(Spell.DeathKnight.Blood, Spell.DeathKnight.Custom)
---@class DKCommonsTable
Spell.DeathKnight.Blood = MergeTableByKey(Spell.DeathKnight.Blood, Spell.DeathKnight.Commons, true)
---@class DeathbringerTable
Spell.DeathKnight.Blood = MergeTableByKey(Spell.DeathKnight.Blood, Spell.DeathKnight.Deathbringer)
---@class RideroftheApocalypseTable
Spell.DeathKnight.Blood = MergeTableByKey(Spell.DeathKnight.Blood, Spell.DeathKnight.RideroftheApocalypse)
---@class SanlaynTable
Spell.DeathKnight.Blood = MergeTableByKey(Spell.DeathKnight.Blood, Spell.DeathKnight.Sanlayn)

---@class FrostTable
Spell.DeathKnight.Frost = {
  -- Abilities
  FrostStrike                           = CreateSpell(49143),
  HowlingBlast                          = CreateSpell(49184),
  -- Talents
  ArcticAssault                         = CreateSpell(456230),
  Avalanche                             = CreateSpell(207142),
  BitingCold                            = CreateSpell(377056),
  Bonegrinder                           = CreateSpell(377098),
  BreathofSindragosa                    = CreateSpell(152279),
  ChillStreak                           = CreateSpell(305392),
  ColdHeart                             = CreateSpell(281208),
  EnduringStrength                      = CreateSpell(377190),
  Frostscythe                           = CreateSpell(207230),
  FrostwyrmsFury                        = CreateSpell(279302),
  GatheringStorm                        = CreateSpell(194912),
  GlacialAdvance                        = CreateSpell(194913),
  HornofWinter                          = CreateSpell(57330),
  Icebreaker                            = CreateSpell(392950),
  Icecap                                = CreateSpell(207126),
  Obliterate                            = CreateSpell(49020),
  Obliteration                          = CreateSpell(281238),
  PillarofFrost                         = CreateSpell(51271),
  RageoftheFrozenChampion               = CreateSpell(377076),
  RemorselessWinter                     = CreateSpell(196770),
  ShatteredFrost                        = CreateSpell(455993),
  ShatteringBlade                       = CreateSpell(207057),
  SmotheringOffense                     = CreateSpell(435005),
  TheLongWinter                         = CreateSpell(456240),
  UnleashedFrenzy                       = CreateSpell(376905),
  -- Buffs
  BonegrinderFrostBuff                  = CreateSpell(377103),
  ColdHeartBuff                         = CreateSpell(281209),
  GatheringStormBuff                    = CreateSpell(211805),
  KillingMachineBuff                    = CreateSpell(51124),
  PillarofFrostBuff                     = CreateSpell(51271),
  RimeBuff                              = CreateSpell(59052),
  UnleashedFrenzyBuff                   = CreateSpell(376907),
  -- Debuffs
  RazoriceDebuff                        = CreateSpell(51714),
  -- TWW2 Effects
  MurderousFrenzyBuff                   = CreateSpell(1222698), -- TWW S2 4P
  WinningStreakBuff                     = CreateSpell(1217897), -- TWW S2 2P
}
---@class DKCustomTable
Spell.DeathKnight.Frost = MergeTableByKey(Spell.DeathKnight.Frost, Spell.DeathKnight.Custom)
---@class DKCommonsTable
Spell.DeathKnight.Frost = MergeTableByKey(Spell.DeathKnight.Frost, Spell.DeathKnight.Commons, true)
---@class DeathbringerTable
Spell.DeathKnight.Frost = MergeTableByKey(Spell.DeathKnight.Frost, Spell.DeathKnight.Deathbringer)
---@class RideroftheApocalypseTable
Spell.DeathKnight.Frost = MergeTableByKey(Spell.DeathKnight.Frost, Spell.DeathKnight.RideroftheApocalypse)
---@class SanlaynTable
Spell.DeathKnight.Frost = MergeTableByKey(Spell.DeathKnight.Frost, Spell.DeathKnight.Sanlayn)

---@class UnholyTable
Spell.DeathKnight.Unholy = {
  -- Abilities
  FesteringScytheAction                 = CreateSpell(458128),
  -- Talents
  Apocalypse                            = CreateSpell(275699),
  ArmyoftheDead                         = CreateSpell(42650),
  BurstingSores                         = CreateSpell(207264),
  ClawingShadows                        = CreateSpell(207311),
  CoilofDevastation                     = CreateSpell(390270),
  CommanderoftheDead                    = CreateSpell(390259),
  DarkTransformation                    = CreateSpell(63560),
  Defile                                = CreateSpell(152280),
  DoomedBidding                         = CreateSpell(455386),
  Epidemic                              = CreateSpell(207317),
  FesteringStrike                       = CreateSpell(85948),
  Festermight                           = CreateSpell(377590),
  HarbingerofDoom                       = CreateSpell(276023),
  ImprovedDeathCoil                     = CreateSpell(377580),
  MenacingMagus                         = CreateSpell(455135),
  Morbidity                             = CreateSpell(377592),
  Outbreak                              = CreateSpell(77575),
  Pestilence                            = CreateSpell(277234),
  Plaguebringer                         = CreateSpell(390175),
  RaiseAbomination                      = CreateSpell(455395),
  RaiseDead                             = CreateSpell(46584),
  RottenTouch                           = CreateSpell(390275),
  ScourgeStrike                         = CreateSpell(55090),
  SummonGargoyle                        = CreateMultiSpell(49206, 207349),
  Superstrain                           = CreateSpell(390283),
  UnholyAssault                         = CreateSpell(207289),
  UnholyBlight                          = CreateSpell(115989),
  VileContagion                         = CreateSpell(390279),
  -- Buffs
  CommanderoftheDeadBuff                = CreateSpell(390260),
  FesteringScytheBuff                   = CreateSpell(458123),
  FestermightBuff                       = CreateSpell(377591),
  RunicCorruptionBuff                   = CreateSpell(51460),
  SuddenDoomBuff                        = CreateSpell(81340),
  -- Debuffs
  DeathRotDebuff                        = CreateSpell(377540),
  FesteringWoundDebuff                  = CreateSpell(194310),
  RottenTouchDebuff                     = CreateSpell(390276),
  -- TWW2 Effects
  UnholyCommanderBuff                   = CreateSpell(456698), -- TWW S2 4P
  WinningStreakBuff                     = CreateSpell(1216813), -- TWW S2 2P
}
---@class DKCustomTable
Spell.DeathKnight.Unholy = MergeTableByKey(Spell.DeathKnight.Unholy, Spell.DeathKnight.Custom)
---@class DKCommonsTable
Spell.DeathKnight.Unholy = MergeTableByKey(Spell.DeathKnight.Unholy, Spell.DeathKnight.Commons, true)
---@class DeathbringerTable
Spell.DeathKnight.Unholy = MergeTableByKey(Spell.DeathKnight.Unholy, Spell.DeathKnight.Deathbringer)
---@class RideroftheApocalypseTable
Spell.DeathKnight.Unholy = MergeTableByKey(Spell.DeathKnight.Unholy, Spell.DeathKnight.RideroftheApocalypse)
---@class SanlaynTable
Spell.DeathKnight.Unholy = MergeTableByKey(Spell.DeathKnight.Unholy, Spell.DeathKnight.Sanlayn)

-- Items
if not Item.DeathKnight then Item.DeathKnight = {} end

---@class DKCustomItemTable
Item.DeathKnight.Custom = {
    -- Custom
    TreemouthFesteringSplinter            = Item(193652, {13, 14}),
    DecorationofFlame                     = Item(194299, {13, 14}),
    WardofFacelessIre                     = Item(203714, {13, 14}),
    EnduringDreadplate                    = Item(202616, {13, 14}),
    GranythsEnduringScale                 = Item(212757, {13, 14}),
    FyrakksTaintedRageheart               = Item(207174, {13, 14}),
    ShadowmoonInsignia                    = Item(150526, {13, 14}),
    -- HR Forgot
    SignetofthePriory                     = Item(219308, {13, 14}),
}

---@class DKCommonsItemTable
Item.DeathKnight.Commons = {
  -- TWW Trinkets
  FunhouseLens                          = Item(234217, {13, 14}),
  TreacherousTransmitter                = Item(221023, {13, 14}),
  ImprovisedSeaforiumPacemaker          = Item(232541, {13, 14}),
}

---@class BloodItemTable
Item.DeathKnight.Blood = {
    -- TWW Trinkets
    TomeofLightsDevotion                  = Item(219309, {13, 14}),
    -- TWW Items
    BestinSlots                           = Item(232526, {16}),
}
---@class DKCustomItemTable
Item.DeathKnight.Blood = MergeTableByKey(Item.DeathKnight.Custom, Item.DeathKnight.Blood)
---@class DKCommonsItemTable
Item.DeathKnight.Blood = MergeTableByKey(Item.DeathKnight.Commons, Item.DeathKnight.Blood)

---@class FrostItemTable
Item.DeathKnight.Frost = {}
---@class DKCustomItemTable
Item.DeathKnight.Frost = MergeTableByKey(Item.DeathKnight.Custom, Item.DeathKnight.Frost)
---@class DKCommonsItemTable
Item.DeathKnight.Frost = MergeTableByKey(Item.DeathKnight.Commons, Item.DeathKnight.Frost)

---@class UnholyItemTable
Item.DeathKnight.Unholy = {
    -- TWW Trinkets
    SignetofthePriory                     = Item(219308, {13, 14}),
}
---@class DKCustomItemTable
Item.DeathKnight.Unholy = MergeTableByKey(Item.DeathKnight.Custom, Item.DeathKnight.Unholy)
---@class DKCommonsItemTable
Item.DeathKnight.Unholy = MergeTableByKey(Item.DeathKnight.Commons, Item.DeathKnight.Unholy)


Spell.DeathKnight.Commons.EmpowerRuneWeapon.offGCD = true
Spell.DeathKnight.Commons.RaiseDead.offGCD = true

Spell.DeathKnight.Blood.DarkCommand:SetGeneric(250, "Generic1")
Spell.DeathKnight.Blood.HeartStrike:SetGeneric(250, "Generic2")
Spell.DeathKnight.Blood.SoulReaper:SetGeneric(250, "Generic3")
Spell.DeathKnight.Blood.DeathsCaress:SetGeneric(250, "Generic4")
Spell.DeathKnight.Blood.Asphyxiate:SetGeneric(250, "Generic5")
Spell.DeathKnight.Blood.DeathGrip:SetGeneric(250, "Generic6")
Spell.DeathKnight.Blood.ControlUndead:SetGeneric(250, "Generic7")
Spell.DeathKnight.Blood.ChainsofIce:SetGeneric(250, "Generic8")

Spell.DeathKnight.Blood.EmpowerRuneWeapon.offGCD = true
Spell.DeathKnight.Blood.IceboundFortitude.offGCD = true
Spell.DeathKnight.Blood.AntiMagicShell.offGCD = true
Spell.DeathKnight.Blood.Lichborne.offGCD = true
Spell.DeathKnight.Blood.DarkCommand.offGCD = true
Spell.DeathKnight.Blood.BloodTap.offGCD = true
Spell.DeathKnight.Blood.IceboundFortitude.offGCD = true
Spell.DeathKnight.Blood.RuneTap.offGCD = true
Spell.DeathKnight.Blood.DarkCommand.offGCD = true
Spell.DeathKnight.Blood.VampiricStrikeAction.ForceDisplaySpellList = 433901
Spell.DeathKnight.Blood.BloodBoil.MeleeRange = 8
Spell.DeathKnight.Blood.AbominationLimb.MeleeRange = 6
Spell.DeathKnight.Blood.BlindingSleet.MeleeRange = 8

Spell.DeathKnight.Unholy.FesteringStrike:SetGeneric(252, "Generic1")
Spell.DeathKnight.Unholy.ScourgeStrike:SetGeneric(252, "Generic2")
Spell.DeathKnight.Unholy.VampiricStrikeAction.MorphedSpellGeneric = Spell.DeathKnight.Unholy.ScourgeStrike
Spell.DeathKnight.Unholy.SoulReaper:SetGeneric(252, "Generic3")
Spell.DeathKnight.Unholy.Outbreak:SetGeneric(252, "Generic4")
Spell.DeathKnight.Unholy.VileContagion:SetGeneric(252, "Generic5")
Spell.DeathKnight.Unholy.Asphyxiate:SetGeneric(252, "Generic6")
Spell.DeathKnight.Unholy.ControlUndead:SetGeneric(252, "Generic7")
Spell.DeathKnight.Unholy.DeathGrip:SetGeneric(252, "Generic8")

Spell.DeathKnight.Unholy.SummonGargoyle.offGCD = true
Spell.DeathKnight.Unholy.EmpowerRuneWeapon.offGCD = true
Spell.DeathKnight.Unholy.IceboundFortitude.offGCD = true
Spell.DeathKnight.Unholy.AntiMagicShell.offGCD = true
Spell.DeathKnight.Unholy.Lichborne.offGCD = true
Spell.DeathKnight.Unholy.DarkCommand.offGCD = true
Spell.DeathKnight.Unholy.IceboundFortitude.offGCD = true
Spell.DeathKnight.Unholy.VampiricStrikeAction.ForceDisplaySpellList = 433901

Spell.DeathKnight.Unholy.DeathAndDecay.MeleeRange = 6
Spell.DeathKnight.Unholy.Defile.MeleeRange = 6
Spell.DeathKnight.Unholy.AbominationLimb.MeleeRange = 6
Spell.DeathKnight.Unholy.Epidemic.MeleeRange = 60
Spell.DeathKnight.Unholy.BlindingSleet.MeleeRange = 8

Spell.DeathKnight.Frost.FrostStrike:SetGeneric(251, "Generic1")
Spell.DeathKnight.Frost.Obliterate:SetGeneric(251, "Generic2")
Spell.DeathKnight.Frost.HowlingBlast:SetGeneric(251, "Generic3")
Spell.DeathKnight.Frost.Asphyxiate:SetGeneric(251, "Generic4")
Spell.DeathKnight.Frost.ControlUndead:SetGeneric(251, "Generic5")
Spell.DeathKnight.Frost.ChainsofIce:SetGeneric(251, "Generic6")
Spell.DeathKnight.Frost.GlacialAdvance:SetGeneric(251, "Generic7")
Spell.DeathKnight.Frost.DeathGrip:SetGeneric(251, "Generic8")

Spell.DeathKnight.Frost.PillarofFrost.offGCD = true
Spell.DeathKnight.Frost.RaiseDead.offGCD = true
Spell.DeathKnight.Frost.BreathofSindragosa.offGCD = true
Spell.DeathKnight.Frost.EmpowerRuneWeapon.offGCD = true
Spell.DeathKnight.Frost.IceboundFortitude.offGCD = true
Spell.DeathKnight.Frost.AntiMagicShell.offGCD = true
Spell.DeathKnight.Frost.Lichborne.offGCD = true
Spell.DeathKnight.Frost.DarkCommand.offGCD = true
Spell.DeathKnight.Frost.IceboundFortitude.offGCD = true
Spell.DeathKnight.Frost.DarkCommand.offGCD = true

Spell.DeathKnight.Frost.BlindingSleet.MeleeRange = 8
Spell.DeathKnight.Frost.RemorselessWinter.MeleeRange = 8
Spell.DeathKnight.Frost.GlacialAdvance.MeleeRange = 10
Spell.DeathKnight.Frost.Frostscythe.MeleeRange = 10
Spell.DeathKnight.Frost.AbominationLimb.MeleeRange = 6
Spell.DeathKnight.Frost.BreathofSindragosa.MeleeRange = 8
Spell.DeathKnight.Frost.FrostwyrmsFury.MeleeRange = 10
Spell.DeathKnight.Frost.DeathAndDecay.MeleeRange = 6

do
    ---@param ThisUnit Unit
    local function EvaluateEnemies(ThisUnit)
       return not ThisUnit:IsInRange(8) and Spell.DeathKnight.Custom.DeathGrip:CanCC(ThisUnit)
    end
    
    function DeathKnight:DeathGrip_OOR()
        if Spell.DeathKnight.Custom.DeathGrip:IsReady(Player) then
            local Enemies = Player:GetEnemiesInRange(30)
           if CastCycle(Spell.DeathKnight.Custom.DeathGrip, Enemies, EvaluateEnemies) then
              return "Death Grip OOR"
           end
        end
        return false
    end
end
