function A_269(...)
  -- HR UPDATE: feat(<PERSON>walker): Update to latest APL for 11.1.5 (09.06.2025)
  ---@class MainAddon
  local MainAddon = MainAddon
  local M = MainAddon
  -- HeroLib
  local HL = HeroLibEx
  ---@class HealingEngine
  local HealingEngine = MainAddon.HealingEngine
  --- @class Unit
  local Unit = HL.Unit
  ---@class Unit
  local Player = Unit.Player
  ---@class Unit
  local Target = Unit.Target
  --- @class Spell
  local Spell = HL.Spell
  --- @class Item
  local Item = HL.Item
  local Cast = M.Cast
  local AoEON = MainAddon.AoEON
  --- Lua
  local pairs = _G.pairs
  --- @class Utils
  local Utils = HL.Utils
  local CastTargetIf = MainAddon.CastTargetIf
  local CastCycle = MainAddon.CastCycle
  local CastCycleAlly = MainAddon.CastCycleAlly
  local CastLeftNameplate = MainAddon.CastLeftNameplate
  local mathmin       = math.min
  local num = M.num
  local Delay         = C_Timer.After

  local Monk = M.Monk

  -- Define S/I for spell and item arrays
  local S = Spell.Monk.Windwalker
  local I = Item.Monk.Windwalker

  ---GUI SETTINGS
  local GetSetting = MainAddon.Config.GetClassSetting
  local Config_Key = MainAddon.GetClassVariableName()
  local Config_Color = '00FF98'
  local Config_Table = {
      key = Config_Key,
      title = 'Monk - Windwalker',
      subtitle = '?? ' .. MainAddon.Version,
      width = 600,
      height = 700,
      profiles = true,
      config = {
          { type = 'header', text = '', size = 24, align = 'Center', color = Config_Color },
          { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
          { type = "header", text = "\"Float like a butterfly, sting like a bee.\"", size = 16, align = "center", color = Config_Color },
          { type = "header", text = "?? x yuno", size = 12, align = "center", color = '4C4C4C' },
          { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
          { type = 'header', text = 'APL', color = Config_Color }, 
          {
            type = 'dropdown',
            text = ' Rotation Mode',
            icon = S.TeachingsoftheMonastery:ID(),
            key = 'rotation_mode',
            list = {
                { text = 'SimC APL', key = 'APLsimc' },
                { text = 'Custom Rotation', key = 'APLcustom' }
            },
            default = 'APLcustom'
          },          
          { type = 'spacer' },
          { type = 'header', text = 'DPS', color = Config_Color },
          { type = 'spacer' },
          { type = 'checkspin', text = 'Stand still threshold (in seconds)', key = 'DPSMovingValue', min = 0, max = 10, default_spin = 0.35, default_check = false },
          { type = 'dropdown', text = '    Affected Spells:', 
            key = 'DPSMovingValueSpells', 
            multiselect = true, 
            list = { 
              { text = 'Fists of Fury', key = 'fists' },
              { text = 'Invoke Xuen, the White Tiger', key = 'xuen' }, 
              { text = 'Storm, Earth and Fire', key = 'sef' },
              { text = 'Strike of the Windlord', key = 'winds' }, 
              { text = 'Whirling Dragon Punch', key = 'wdp' }
            }, 
            default = { 'sef', 'xuen', 'winds', 'wdp', 'fists' } 
          },
          { type = 'checkspin', text = ' Check for enemies in melee (in %)', key = 'meleeratio', min = 1, max = 100, default_spin = 20, default_check = false, },
          { type = 'dropdown', text = '    Affected Spells:', 
            key = 'meleeratiospells', 
            multiselect = true, 
            list = { 
              { text = 'Fists of Fury', key = 'ratioFists' }, 
              { text = 'Invoke Xuen, the White Tiger', key = 'ratioXuen' }, 
              { text = 'Storm, Earth and Fire', key = 'ratioStormEarthFire' }, 
              { text = 'Strike of the Windlord', key = 'ratioWindlord' },
              { text = 'Whirling Dragon Punch', key = 'ratioWDP' }
            }, 
            default = { 'ratioSEF', 'ratioXuen', 'ratioFists', 'ratioWindlord', 'ratioWDP' } 
          },          
          { type = 'checkbox', text = ' Use Whirling Dragon Punch while moving', icon = S.WhirlingDragonPunch:ID(), default = false, key = 'WDPMoving' },           
          { type = 'spacer' },
          { type = 'header', text = 'Defensives', color = Config_Color },
          { type = 'checkbox', text = ' Save Touch of Karma for defensives', icon = S.TouchofKarma:ID(), default = false, key = 'ToKdef' },           
          { type = "checkspin", text = " Vivacious Vivification (self)", key = "vivaciousself", icon = S.Vivify:ID(), min = 1, max = 100, default_spin = 35, default_check = true },
          { type = 'checkspin', text = " Vivacious Vivification (party)", key = 'vivify_party', icon = S.Vivify:ID(), min = 1, max = 100, default_spin = 35, default_check = true },
          { type = 'dropdown',
          text = ' Vivacious Vivification - Targets', key = 'vivacious_targets',
          icon = S.VivaciousVivification:ID(),
          multiselect = true,
          list = {
              { text = 'Healer', key = 'healer' },
              { text = 'Tank', key = 'tank' },
              { text = 'DPS', key = 'dps' },
          },
          default = {
              "healer",
              "dps"
          },
          },
          { type = 'checkbox', text = ' Toast message about Crackling Jade Lightning', icon = S.CracklingJadeLightning:ID(), key = 'toast_CJL', default = true },
          { type = 'checkspin', text = 'Fortifying Brew', key = 'fortbrew', icon = S.FortifyingBrew:ID(), min = 1, max = 100, default_spin = 30, default_check = true },
          { type = 'checkspin', text = 'Diffuse Magic', key = 'diffuse', icon = S.DiffuseMagic:ID(), min = 1, max = 100, default_spin = 20, default_check = true },
          { type = 'checkbox', text = '    use it for the affix (Devour)', default = true, key = 'diffuse_affix' },
          { type = 'checkspin', text = 'Touch of Karma', key = 'TouchOfKarma', icon = S.TouchofKarma:ID(), min = 1, max = 100, default_spin = 20, default_check = true },
          { type = 'spacer' },
          { type = 'spacer' },
          { type = 'spacer' },
          { type = 'spacer' },
          { type = 'spacer' }, { type = 'ruler' }, { type = 'spacer' },
      }
  }
  Config_Table.config = MainAddon.BuildCooldownByTimerUI(Config_Table.config, Config_Color, Config_Key)
  Config_Table.config = MainAddon.BuildDPSTrinketUI(Config_Table.config, Config_Color)
  Config_Table.config = MainAddon.BuildCombatPotionUI(Config_Table.config, "Windwalker", Config_Color)
  MainAddon.SetConfig(269, Config_Table)

  -- Track Xuen, the White Tiger
  Monk.Xuen = {}
  Monk.Xuen.Active = false
  Monk.Xuen.GUID = 0
  Monk.Xuen.ExpireTime = 0
  HL:RegisterForSelfCombatEvent(
      function(...)
          local destGUID, _, _, _, spellID = select(8, ...)
          if spellID == 123904 then
          Monk.Xuen.Active = true
          Monk.Xuen.GUID = destGUID
          Monk.Xuen.ExpireTime = GetTime() + 45
          end
      end
      , "SPELL_SUMMON"
  )

  HL:RegisterForCombatEvent(
      function(...)
          local destGUID = select(8, ...)
          if destGUID == Monk.Xuen.GUID then
          Monk.Xuen.Active = false
          Monk.Xuen.GUID = 0
          Monk.Xuen.ExpireTime = 0
          end
      end
      , "UNIT_DIED"
  )

  local OnUseExcludes = {
    -- DF Trinkets
    I.AlgetharPuzzleBox:ID(),
    I.BeacontotheBeyond:ID(),
    I.DragonfireBombDispenser:ID(),
    I.EruptingSpearFragment:ID(),
    I.ManicGrieftorch:ID(),
    -- TWW Trinkets
    I.ImperfectAscendancySerum:ID(),
    I.MadQueensMandate:ID(),
    I.SignetofthePriory:ID(),
    I.TreacherousTransmitter:ID(),
    -- DF Other Items
    I.Djaruun:ID(),
  }

  --- Custom
  local CombatTime = 0

  --- ===== Rotation Variables =====
  local VarTotMMaxStacks = 4
  local DungeonSlice
  local CombatTime
  local Enemies5y = {}
  local Enemies8y = {}
  local IsInMeleeRange
  local EnemiesCount8y = 0
  local BossFightRemains = 11111
  local FightRemains = 11111

  --- ===== Trinket Item Objects =====
  local Trinket1, Trinket2
  local VarTrinket1ID, VarTrinket2ID
  local VarTrinket1Spell, VarTrinket2Spell
  local VarTrinket1Range, VarTrinket2Range
  local VarTrinket1CastTime, VarTrinket2CastTime
  local VarTrinket1CD, VarTrinket2CD
  local VarTrinket1Ex, VarTrinket2Ex
  local VarTrinketFailures = 0
  local function SetTrinketVariables()
    local T1, T2 = Player:GetTrinketData(OnUseExcludes)
    if VarTrinketFailures < 5 and ((T1.ID == 0 or T2.ID == 0) or (T1.SpellID > 0 and not T1.Usable or T2.SpellID > 0 and not T2.Usable)) then
      VarTrinketFailures = VarTrinketFailures + 1
      Delay(5, function()
          SetTrinketVariables()
        end
      )
    end

    Trinket1 = T1.Object
    Trinket2 = T2.Object

    VarTrinket1ID = T1.ID
    VarTrinket2ID = T2.ID

    VarTrinket1Spell = T1.Spell
    VarTrinket1Range = T1.Range
    VarTrinket1CastTime = T1.CastTime
    VarTrinket2Spell = T2.Spell
    VarTrinket2Range = T2.Range
    VarTrinket2CastTime = T2.CastTime

    VarTrinket1CD = T1.Cooldown
    VarTrinket2CD = T2.Cooldown

    VarTrinket1Ex = T1.Excluded
    VarTrinket2Ex = T2.Excluded
  end
  SetTrinketVariables()
  
  --- ===== Event Registrations =====
  HL:RegisterForEvent(function()
      BossFightRemains = 11111
      FightRemains = 11111
  end, "PLAYER_REGEN_ENABLED")
      
  HL:RegisterForEvent(function()
    VarTrinketFailures = 0
    SetTrinketVariables()
  end, "PLAYER_EQUIPMENT_CHANGED")

  ---@param TargetUnit Unit
  local function EvaluateVivify(TargetUnit)
      return TargetUnit:HealthPercentage() <= GetSetting('vivify_party_spin', 35)
  end
 
  --- ===== Helper Functions =====
  local function ComboStrike(SpellObject)
    return (not Player:PrevGCD(1, SpellObject))
  end

  -- Track last toast time to prevent spam for Crackling Jade Lightning
  Monk.LastCJLToastTime = 0

  -- Show toast message for CJL if enabled and not on cooldown
  local function ShowCJLToastIfEnabled(stacks)
    if GetSetting('toast_CJL', true) and (GetTime() - Monk.LastCJLToastTime > 10) then
      MainAddon.UI:ShowToast("Emperor's Capacitor", "CJL coming up!", MainAddon.GetTexture(S.CracklingJadeLightning), 5)
      Monk.LastCJLToastTime = GetTime()
    end
  end

  local function ToDLogic()
      if S.TouchofDeath:IsReady() then
          -- Check if the target is valid for Touch of Death
          if (S.ImpTouchofDeath:IsAvailable() and Target:HealthPercentage() < 15) or 
            (Target:Health() < Player:Health()) then
              if Cast(S.TouchofDeath) then
                  return "Touch of Death"
              end
          end
      end
  end

  --- ===== Start Custom =====
  local function Utilities()
      if Target:IsDeadOrGhost() and Target:IsInParty() and Target:IsAPlayer() and not Target:IsEnemy() then
          if S.Resuscitate:IsReady(Player) then
              if Cast(S.Resuscitate) then
                  return 'Resuscitate';
              end
          end
      end
  end

  local function Defensives()
      local DefensivesDown = Player:BuffDown(S.FortifyingBrew) and Player:BuffDown(S.DiffuseMagic) and Player:BuffDown(S.TouchofKarma)
      if DefensivesDown then
          if GetSetting('TouchOfKarma_check', false) then
              if S.TouchofKarma:IsReady(Target) and Player:HealthPercentage() <= GetSetting('TouchOfKarma_spin', 30) then
                  if Cast(S.TouchofKarma) then
                      return "TouchOfKarma custom";
                  end
              end
          end

          if GetSetting('fortbrew_check', false) then
              if S.FortifyingBrew:IsReady(Player) and Player:HealthPercentage() <= GetSetting('fortbrew_spin', 30) then
                  if Cast(S.FortifyingBrew) then
                      return "FortifyingBrew custom";
                  end
              end
          end

          if GetSetting("diffuse_affix", true) then
              if Monk.ShouldUseDiffuseMagic() then
                  if Cast(S.DiffuseMagic) then return "Diffuse Magic for Devour" end
              end
          end

          if GetSetting('diffuse_check', false) then
              if S.DiffuseMagic:IsReady(Player) and Player:HealthPercentage() <= GetSetting('diffuse_spin', 30) then
                  if Cast(S.DiffuseMagic) then
                      return "Diffuse Magic custom";
                  end
              end
          end

          if Player:BuffUp(S.VivaciousVivification) and GetSetting("vivaciousself_check", true) and Player:HealthPercentage() <= GetSetting("vivaciousself_spin", 35) then
            if Cast(S.Vivify, Player) then
              return "Vivify Player"
            end
          end

          if GetSetting('vivify_party_check', true) and Player:IsInDungeonArea() and not Player:IsInRaidArea() and Player:BuffUp(S.VivaciousVivification) then
            local Tanks, Healers, Members, Damagers, Melees = HealingEngine:Fetch()
            local Reg_Targets = GetSetting('vivacious_targets', {})
            if HL.Utils.tableCount(Reg_Targets) > 0 then
                local Reg_Tanks = Reg_Targets['tank']
                local Reg_Healers = Reg_Targets['healer']
                local Reg_DPS = Reg_Targets['dps']

                if Reg_Tanks and Tanks then
                    if CastCycleAlly(S.Vivify, Tanks, EvaluateVivify) then
                        return "Vivify Tanks"
                    end
                end

                if Reg_Healers and Healers then
                    if CastCycleAlly(S.Vivify, Healers, EvaluateVivify) then
                        return "Vivify Healers"
                    end
                end

                if Reg_DPS and Damagers then
                    if CastCycleAlly(S.Vivify, Damagers, EvaluateVivify) then
                        return "Vivify DPS"
                    end
                end
            end
         end
      end
  end
  --- ===== End Custom =====

  --- ===== CastTargetIf Filter Functions =====
  local function EvaluateTargetIfFilterTargetHP(TargetUnit)
    return TargetUnit:Health()
  end

  local function EvaluateTargetIfFilterTTD(TargetUnit)
    return TargetUnit:TimeToDie()
  end

  --- ===== Rotation Functions =====
  local function Precombat()
      -- flask
      -- food
      -- augmentation
      -- snapshot_stats
      -- Manually added: openers
      -- tiger_palm,if=!prev.tiger_palm
      if S.TigerPalm:IsReady() then
        -- YUNO
        if Player:Chi() < 4 then
          if Cast(S.TigerPalm) then
            return "tiger_palm precombat 2"
          end
        end
      end     
      -- rising_sun_kick
      if S.RisingSunKick:IsReady() then
        -- YUNO
        if Player:Chi() >= 4 then
          if Cast(S.RisingSunKick) then
            return "rising_sun_kick precombat 4"
          end
        end
      end        
  end

    local function Trinkets()
      -- use_item,name=imperfect_ascendancy_serum,use_off_gcd=1,if=pet.xuen_the_white_tiger.active|!talent.invoke_xuen_the_white_tiger&(cooldown.storm_earth_and_fire.ready|!talent.storm_earth_and_fire)&(cooldown.strike_of_the_windlord.ready|!talent.strike_of_the_windlord&cooldown.fists_of_fury.ready)|fight_remains<25
      if I.ImperfectAscendancySerum:IsEquippedAndReady() and (Monk.Xuen.Active or not S.InvokeXuenTheWhiteTiger:IsAvailable() and (S.StormEarthAndFire:CooldownUp() or not S.StormEarthAndFire:IsAvailable()) and (S.StrikeoftheWindlord:CooldownUp() or not S.StrikeoftheWindlord:IsAvailable() and S.FistsofFury:CooldownUp()) or BossFightRemains < 25) then
        if Cast(I.ImperfectAscendancySerum) then return "imperfect_ascendancy_serum trinkets 2"; end
      end
      -- use_item,name=mad_queens_mandate,target_if=min:target.health,if=!trinket.1.has_use_buff&!trinket.2.has_use_buff|(trinket.1.has_use_buff|trinket.2.has_use_buff)&cooldown.invoke_xuen_the_white_tiger.remains>30
      if I.MadQueensMandate:IsEquippedAndReady() and (not Trinket1:HasUseBuff() and not Trinket2:HasUseBuff() or (Trinket1:HasUseBuff() or Trinket2:HasUseBuff()) and S.InvokeXuenTheWhiteTiger:CooldownRemains() > 30) then
        if Cast(I.MadQueensMandate) then return "mad_queens_mandate trinkets 4"; end
      end
      -- use_item,name=treacherous_transmitter,if=!fight_style.dungeonslice&(cooldown.invoke_xuen_the_white_tiger.remains<4|talent.xuens_bond&pet.xuen_the_white_tiger.active)|fight_style.dungeonslice&((fight_style.DungeonSlice&active_enemies=1&(time<10|talent.xuens_bond&talent.celestial_conduit)|!fight_style.dungeonslice|active_enemies>1)&cooldown.storm_earth_and_fire.ready&(target.time_to_die>14&!fight_style.dungeonroute|target.time_to_die>22)&(active_enemies>2|debuff.acclamation.up|!talent.ordered_elements&time<5)&(chi>2&talent.ordered_elements|chi>5|chi>3&energy<50|energy<50&active_enemies=1|prev.tiger_palm&!talent.ordered_elements&time<5)|fight_remains<30)|buff.invokers_delight.up
      if I.TreacherousTransmitter:IsEquippedAndReady() and (not DungeonSlice and (S.InvokeXuenTheWhiteTiger:CooldownRemains() < 4 or S.XuensBond:IsAvailable() and Monk.Xuen.Active) or DungeonSlice and ((DungeonSlice and EnemiesCount8y == 1 and (CombatTime < 10 or S.XuensBond:IsAvailable() and S.CelestialConduit:IsAvailable()) or not DungeonSlice or EnemiesCount8y > 1) and S.StormEarthAndFire:CooldownUp() and (Target:TimeToDie() > 14 and not DungeonSlice or Target:TimeToDie() > 22) and (EnemiesCount8y > 2 or Target:DebuffUp(S.AcclamationDebuff) or not S.OrderedElements:IsAvailable() and CombatTime < 5) and (Player:Chi() > 2 and S.OrderedElements:IsAvailable() or Player:Chi() > 5 or Player:Chi() > 3 and Player:Energy() < 50 or Player:Energy() < 50 and EnemiesCount8y == 1 or Player:PrevGCD(1, S.TigerPalm) and not S.OrderedElements:IsAvailable() and CombatTime < 5) or BossFightRemains < 30) or Player:BuffUp(S.InvokersDelightBuff)) then
        if Cast(I.TreacherousTransmitter) then return "treacherous_transmitter trinkets 6"; end
      end
      -- use_item,name=junkmaestros_mega_magnet,if=!trinket.1.has_use_buff&!trinket.2.has_use_buff|(trinket.1.has_use_buff|trinket.2.has_use_buff)&cooldown.invoke_xuen_the_white_tiger.remains>30|fight_remains<5
      if I.JunkmaestrosMegaMagnet:IsEquippedAndReady() and Player:BuffUp(S.JunkmaestrosBuff) and (not Trinket1:HasUseBuff() and not Trinket2:HasUseBuff() or (Trinket1:HasUseBuff() or Trinket2:HasUseBuff()) and S.InvokeXuenTheWhiteTiger:CooldownRemains() > 30 or BossFightRemains < 5) then
        if Cast(I.JunkmaestrosMegaMagnet) then return "junkmaestros_mega_magnet trinkets 8"; end
      end
      -- signet_of_the_priory,if=pet.xuen_the_white_tiger.active|fight_remains<20
      if I.SignetofthePriory:IsEquippedAndReady() and (Monk.Xuen.Active or BossFightRemains < 20) then
        if Cast(I.SignetofthePriory) then return "signet_of_the_priory trinkets 10"; end
      end
      -- use_item,slot=trinket1,if=pet.xuen_the_white_tiger.active
      if Trinket1 and Trinket1:IsReady() and not VarTrinket1Ex and not Player:IsItemBlacklisted(Trinket1) and Trinket1:HasUseBuff() and Monk.Xuen.Active then
        if Cast(Trinket1) then return "Generic use_items for " .. Trinket1:Name() .. " (trinkets stat_buff trinket1)"; end
      end
      -- use_item,slot=trinket2,if=pet.xuen_the_white_tiger.active
      if Trinket2 and Trinket2:IsReady() and not VarTrinket2Ex and not Player:IsItemBlacklisted(Trinket2) and Trinket2:HasUseBuff() and Monk.Xuen.Active then
        if Cast(Trinket2) then return "Generic use_items for " .. Trinket2:Name() .. " (trinkets stat_buff trinket2)"; end
      end
      -- use_item,slot=trinket1,if=!trinket.1.has_use_buff&!trinket.2.has_use_buff|(trinket.1.has_use_buff|trinket.2.has_use_buff)&cooldown.invoke_xuen_the_white_tiger.remains>30
      if Trinket1 and Trinket1:IsReady() and not VarTrinket1Ex and not Player:IsItemBlacklisted(Trinket1) and (not Trinket1:HasUseBuff() and not Trinket2:HasUseBuff() or (Trinket1:HasUseBuff() or Trinket2:HasUseBuff()) and S.InvokeXuenTheWhiteTiger:CooldownRemains() > 30) then
        if Cast(Trinket1) then return "Generic use_items for " .. Trinket1:Name() .. " (trinkets dmg_buff trinket1)"; end
      end
      -- use_item,slot=trinket2,if=!trinket.1.has_use_buff&!trinket.2.has_use_buff|(trinket.1.has_use_buff|trinket.2.has_use_buff)&cooldown.invoke_xuen_the_white_tiger.remains>30
      if Trinket2 and Trinket2:IsReady() and not VarTrinket2Ex and not Player:IsItemBlacklisted(Trinket2) and (not Trinket1:HasUseBuff() and not Trinket2:HasUseBuff() or (Trinket1:HasUseBuff() or Trinket2:HasUseBuff()) and S.InvokeXuenTheWhiteTiger:CooldownRemains() > 30) then
        if Cast(Trinket2) then return "Generic use_items for " .. Trinket2:Name() .. " (trinkets dmg_buff trinket2)"; end
      end
      -- do_treacherous_transmitter_task,if=pet.xuen_the_white_tiger.active|fight_remains<20
    end  
  
    local function Cooldowns()
      -- Note: Variables from APL(), as they're only used in this function, so we'll keep them local here.
      -- variable,name=sef_condition,value=target.time_to_die>6&(cooldown.rising_sun_kick.remains|active_enemies>2|!talent.ordered_elements)&(prev.invoke_xuen_the_white_tiger|(talent.celestial_conduit|!talent.last_emperors_capacitor)&buff.bloodlust.up&(cooldown.strike_of_the_windlord.remains<5|!talent.strike_of_the_windlord)&talent.sequenced_strikes|buff.invokers_delight.remains>15|(cooldown.strike_of_the_windlord.remains<5|!talent.strike_of_the_windlord)&cooldown.storm_earth_and_fire.full_recharge_time<cooldown.invoke_xuen_the_white_tiger.remains&cooldown.fists_of_fury.remains<5&(!talent.last_emperors_capacitor|talent.celestial_conduit)|talent.last_emperors_capacitor&buff.the_emperors_capacitor.stack>17&cooldown.invoke_xuen_the_white_tiger.remains>cooldown.storm_earth_and_fire.full_recharge_time)|fight_remains<30|buff.invokers_delight.remains>15&(cooldown.rising_sun_kick.remains|active_enemies>2|!talent.ordered_elements)|fight_style.patchwerk&buff.bloodlust.up&(cooldown.rising_sun_kick.remains|active_enemies>2|!talent.ordered_elements)&talent.celestial_conduit&time>10
      local VarSefCondition = Target:TimeToDie() > 6 and (S.RisingSunKick:CooldownDown() or EnemiesCount8y > 2 or not S.OrderedElements:IsAvailable()) and (Player:PrevGCD(1, S.InvokeXuenTheWhiteTiger) or (S.CelestialConduit:IsAvailable() or not S.LastEmperorsCapacitor:IsAvailable()) and Player:BloodlustUp() and (S.StrikeoftheWindlord:CooldownRemains() < 5 or not S.StrikeoftheWindlord:IsAvailable()) and S.SequencedStrikes:IsAvailable() or Player:BuffRemains(S.InvokersDelightBuff) > 15 or (S.StrikeoftheWindlord:CooldownRemains() < 5 or not S.StrikeoftheWindlord:IsAvailable()) and S.StormEarthAndFire:FullRechargeTime() < S.InvokeXuenTheWhiteTiger:CooldownRemains() and S.FistsofFury:CooldownRemains() < 5 and (not S.LastEmperorsCapacitor:IsAvailable() or S.CelestialConduit:IsAvailable()) or S.LastEmperorsCapacitor:IsAvailable() and Player:BuffStack(S.TheEmperorsCapacitorBuff) > 17 and S.InvokeXuenTheWhiteTiger:CooldownRemains() > S.StormEarthAndFire:FullRechargeTime()) or BossFightRemains < 30 or Player:BuffRemains(S.InvokersDelightBuff) > 15 and (S.RisingSunKick:CooldownDown() or EnemiesCount8y > 2 or not S.OrderedElements:IsAvailable()) or (not DungeonSlice and Player:BloodlustUp() and (S.RisingSunKick:CooldownDown() or EnemiesCount8y > 2 or not S.OrderedElements:IsAvailable()) and S.CelestialConduit:IsAvailable() and CombatTime > 10)
      -- variable,name=xuen_dungeonslice_condition,value=active_enemies=1&(time<10|talent.xuens_bond&talent.celestial_conduit&target.time_to_die>14)|active_enemies>1&cooldown.storm_earth_and_fire.ready&target.time_to_die>14&(active_enemies>2|debuff.acclamation.up|!talent.ordered_elements&time<5)&((chi>2&!talent.ordered_elements|talent.ordered_elements|!talent.ordered_elements&energy<50)|talent.sequenced_strikes&talent.energy_burst&talent.revolving_whirl)|fight_remains<30|active_enemies>3&target.time_to_die>5|fight_style.dungeonslice&time>50&target.time_to_die>1&talent.xuens_bond
      local VarXuenDungeonsliceCondition = EnemiesCount8y == 1 and (CombatTime < 10 or S.XuensBond:IsAvailable() and S.CelestialConduit:IsAvailable() and Target:TimeToDie() > 14) or EnemiesCount8y > 1 and S.StormEarthAndFire:CooldownUp() and Target:TimeToDie() > 14 and (EnemiesCount8y > 2 or Target:DebuffUp(S.AcclamationDebuff) or not S.OrderedElements:IsAvailable() and CombatTime < 5) and ((Player:Chi() > 2 and not S.OrderedElements:IsAvailable() or S.OrderedElements:IsAvailable() or not S.OrderedElements:IsAvailable() and Player:Energy() < 50) or S.SequencedStrikes:IsAvailable() and S.EnergyBurst:IsAvailable() and S.RevolvingWhirl:IsAvailable()) or BossFightRemains < 30 or EnemiesCount8y > 3 and Target:TimeToDie() > 5 or DungeonSlice and CombatTime > 50 and Target:TimeToDie() > 1 and S.XuensBond:IsAvailable()
      -- variable,name=xuen_condition,value=(fight_style.DungeonSlice&active_enemies=1&(time<10|talent.xuens_bond&talent.celestial_conduit)|!fight_style.dungeonslice|active_enemies>1)&cooldown.storm_earth_and_fire.ready&(target.time_to_die>14&!fight_style.dungeonroute|target.time_to_die>22)&(active_enemies>2|debuff.acclamation.up|!talent.ordered_elements&time<5)&(chi>2&talent.ordered_elements|chi>5|chi>3&energy<50|energy<50&active_enemies=1|prev.tiger_palm&!talent.ordered_elements&time<5)|fight_remains<30|fight_style.dungeonroute&talent.celestial_conduit&target.time_to_die>14
      local VarXuenCondition = (DungeonSlice and EnemiesCount8y == 1 and (CombatTime < 10 or S.XuensBond:IsAvailable() and S.CelestialConduit:IsAvailable()) or not DungeonSlice or EnemiesCount8y > 1) and S.StormEarthAndFire:CooldownUp() and (Target:TimeToDie() > 14 and not DungeonSlice or Target:TimeToDie() > 22) and (EnemiesCount8y > 2 or Target:DebuffUp(S.AcclamationDebuff) or not S.OrderedElements:IsAvailable() and CombatTime < 5) and (Player:Chi() > 2 and S.OrderedElements:IsAvailable() or Player:Chi() > 5 or Player:Chi() > 3 and Player:Energy() < 50 or Player:Energy() < 50 and EnemiesCount8y == 1 or Player:PrevGCD(1, S.TigerPalm) and not S.OrderedElements:IsAvailable() and CombatTime < 5) or BossFightRemains < 30 or DungeonSlice and S.CelestialConduit:IsAvailable() and Target:TimeToDie() > 14
      -- variable,name=xuen_dungeonroute_condition,value=cooldown.storm_earth_and_fire.ready&(active_enemies>1&cooldown.storm_earth_and_fire.ready&target.time_to_die>22&(active_enemies>2|debuff.acclamation.up|!talent.ordered_elements&time<5)&((chi>2&!talent.ordered_elements|talent.ordered_elements|!talent.ordered_elements&energy<50)|talent.sequenced_strikes&talent.energy_burst&talent.revolving_whirl)|fight_remains<30|active_enemies>3&target.time_to_die>15|time>50&(target.time_to_die>10&talent.xuens_bond|target.time_to_die>20))|buff.storm_earth_and_fire.remains>5
      local VarXuenDungeonrouteCondition = S.StormEarthAndFire:CooldownUp() and (EnemiesCount8y > 1 and S.StormEarthAndFire:CooldownUp() and Target:TimeToDie() > 22 and (EnemiesCount8y > 2 or Target:DebuffUp(S.AcclamationDebuff) or not S.OrderedElements:IsAvailable() and CombatTime < 5) and ((Player:Chi() > 2 and not S.OrderedElements:IsAvailable() or S.OrderedElements:IsAvailable() or not S.OrderedElements:IsAvailable() and Player:Energy() < 50) or S.SequencedStrikes:IsAvailable() and S.EnergyBurst:IsAvailable() and S.RevolvingWhirl:IsAvailable()) or BossFightRemains < 30 or EnemiesCount8y > 3 and Target:TimeToDie() > 15 or CombatTime > 50 and (Target:TimeToDie() > 10 and S.XuensBond:IsAvailable() or Target:TimeToDie() > 20)) or Player:BuffRemains(S.StormEarthAndFireBuff) > 5
      -- variable,name=sef_dungeonroute_condition,value=time<50&target.time_to_die>10&(buff.bloodlust.up|active_enemies>2|cooldown.strike_of_the_windlord.remains<2|talent.last_emperors_capacitor&buff.the_emperors_capacitor.stack>17)|target.time_to_die>10&(cooldown.storm_earth_and_fire.full_recharge_time<cooldown.invoke_xuen_the_white_tiger.remains|cooldown.invoke_xuen_the_white_tiger.remains<30&(cooldown.storm_earth_and_fire.full_recharge_time<30|cooldown.storm_earth_and_fire.full_recharge_time<40&talent.flurry_strikes))&(talent.sequenced_strikes&talent.energy_burst&talent.revolving_whirl|talent.flurry_strikes|chi>3|energy<50)&(active_enemies>2|!talent.ordered_elements|cooldown.rising_sun_kick.remains)&!talent.flurry_strikes|target.time_to_die>10&talent.flurry_strikes&(active_enemies>2|!talent.ordered_elements|cooldown.rising_sun_kick.remains)&(talent.last_emperors_capacitor&buff.the_emperors_capacitor.stack>17&cooldown.storm_earth_and_fire.full_recharge_time<cooldown.invoke_xuen_the_white_tiger.remains&cooldown.invoke_xuen_the_white_tiger.remains>15|!talent.last_emperors_capacitor&cooldown.storm_earth_and_fire.full_recharge_time<cooldown.invoke_xuen_the_white_tiger.remains&cooldown.invoke_xuen_the_white_tiger.remains>15)
      local VarSefDungeonrouteCondition = CombatTime < 50 and Target:TimeToDie() > 10 and (Player:BloodlustUp() or EnemiesCount8y > 2 or S.StrikeoftheWindlord:CooldownRemains() < 2 or S.LastEmperorsCapacitor:IsAvailable() and Player:BuffStack(S.TheEmperorsCapacitorBuff) > 17) or Target:TimeToDie() > 10 and (S.StormEarthAndFire:FullRechargeTime() < S.InvokeXuenTheWhiteTiger:CooldownRemains() or S.InvokeXuenTheWhiteTiger:CooldownRemains() < 30 and (S.StormEarthAndFire:FullRechargeTime() < 30 or S.StormEarthAndFire:FullRechargeTime() < 40 and S.FlurryStrikes:IsAvailable())) and (S.SequencedStrikes:IsAvailable() and S.EnergyBurst:IsAvailable() and S.RevolvingWhirl:IsAvailable() or S.FlurryStrikes:IsAvailable() or Player:Chi() > 3 or Player:Energy() < 50) and (EnemiesCount8y > 2 or not S.OrderedElements:IsAvailable() or S.RisingSunKick:CooldownDown()) and not S.FlurryStrikes:IsAvailable() or Target:TimeToDie() > 10 and S.FlurryStrikes:IsAvailable() and (EnemiesCount8y > 2 or not S.OrderedElements:IsAvailable() or S.RisingSunKick:CooldownDown()) and (S.LastEmperorsCapacitor:IsAvailable() and Player:BuffStack(S.TheEmperorsCapacitorBuff) > 17 and S.StormEarthAndFire:FullRechargeTime() < S.InvokeXuenTheWhiteTiger:CooldownRemains() and S.InvokeXuenTheWhiteTiger:CooldownRemains() > 15 or not S.LastEmperorsCapacitor:IsAvailable() and S.StormEarthAndFire:FullRechargeTime() < S.InvokeXuenTheWhiteTiger:CooldownRemains() and S.InvokeXuenTheWhiteTiger:CooldownRemains() > 15)
      -- Note: The actual cooldowns APL function starts here.
      -- invoke_external_buff,name=power_infusion,if=pet.xuen_the_white_tiger.active&(!buff.bloodlust.up|buff.bloodlust.up&cooldown.strike_of_the_windlord.remains)
      -- Not handling external buffs.
      -- storm_earth_and_fire,target_if=max:target.time_to_die,if=fight_style.dungeonroute&buff.invokers_delight.remains>15&(active_enemies>2|!talent.ordered_elements|cooldown.rising_sun_kick.remains)
      if S.StormEarthAndFire:IsReady() and (DungeonSlice and Player:BuffRemains(S.InvokersDelightBuff) > 15 and (EnemiesCount8y > 2 or not S.OrderedElements:IsAvailable() or S.RisingSunKick:CooldownDown())) then
        if Cast(S.StormEarthAndFire) then return "storm_earth_and_fire cooldowns dungeonroute 2"; end
      end
      -- tiger_palm,if=(target.time_to_die>14&!fight_style.dungeonroute|target.time_to_die>22)&!cooldown.invoke_xuen_the_white_tiger.remains&(chi<5&!talent.ordered_elements|chi<3)&(combo_strike|!talent.hit_combo)
      if S.TigerPalm:IsReady() and ((Target:TimeToDie() > 14 and not DungeonSlice or Target:TimeToDie() > 22) and S.InvokeXuenTheWhiteTiger:CooldownUp() and (Player:Chi() < 5 and not S.OrderedElements:IsAvailable() or Player:Chi() < 3) and (ComboStrike(S.TigerPalm) or not S.HitCombo:IsAvailable())) then
         if Cast(S.TigerPalm) then return "tiger_palm cooldowns 4"; end
      end
      -- invoke_xuen_the_white_tiger,target_if=max:target.time_to_die,if=variable.xuen_condition&!fight_style.dungeonslice&!fight_style.dungeonroute|variable.xuen_dungeonslice_condition&fight_style.Dungeonslice|variable.xuen_dungeonroute_condition&fight_style.dungeonroute
      if S.InvokeXuenTheWhiteTiger:IsReady() and (VarXuenCondition and not DungeonSlice or VarXuenDungeonsliceCondition and DungeonSlice or VarXuenDungeonrouteCondition and DungeonSlice) then
        if Cast(S.InvokeXuenTheWhiteTiger) then return "invoke_xuen_the_white_tiger cooldowns 6"; end
      end
      -- storm_earth_and_fire,target_if=max:target.time_to_die,if=variable.sef_condition&!fight_style.dungeonroute|variable.sef_dungeonroute_condition&fight_style.dungeonroute
      if S.StormEarthAndFire:IsReady() and (VarSefCondition and not DungeonSlice or VarSefDungeonrouteCondition and DungeonSlice) then
        if Cast(S.StormEarthAndFire) then return "storm_earth_and_fire cooldowns 8"; end
      end
      -- YUNO: we dont do that
      -- -- touch_of_karma
      -- if S.TouchofKarma:IsCastable() and not Settings.Windwalker.IgnoreToK then
      --   if Cast(S.TouchofKarma) then return "touch_of_karma cooldowns 10"; end
      -- end
      -- Note: All racials below use the same condition.
      if ((Monk.Xuen.Active and Monk.Xuen.ExpireTime - GetTime() > 15) or not S.InvokeXuenTheWhiteTiger:IsAvailable() and (not S.StormEarthAndFire:IsAvailable() and (S.StrikeoftheWindlord:CooldownUp() or not S.StrikeoftheWindlord:IsAvailable() and S.FistsofFury:CooldownUp()) or Player:BuffRemains(S.StormEarthAndFireBuff) > 10) or BossFightRemains < 20) then
        -- ancestral_call,if=buff.invoke_xuen_the_white_tiger.remains>15|!talent.invoke_xuen_the_white_tiger&(!talent.storm_earth_and_fire&(cooldown.strike_of_the_windlord.ready|!talent.strike_of_the_windlord&cooldown.fists_of_fury.ready)|buff.storm_earth_and_fire.remains>10)|fight_remains<20
        if S.AncestralCall:IsReady() then
          if Cast(S.AncestralCall) then return "ancestral_call cooldowns 12"; end
        end
        -- blood_fury,if=buff.invoke_xuen_the_white_tiger.remains>15|!talent.invoke_xuen_the_white_tiger&(!talent.storm_earth_and_fire&(cooldown.strike_of_the_windlord.ready|!talent.strike_of_the_windlord&cooldown.fists_of_fury.ready)|buff.storm_earth_and_fire.remains>10)|fight_remains<20
        if S.BloodFury:IsReady() then
          if Cast(S.BloodFury) then return "blood_fury cooldowns 14"; end
        end
        -- fireblood,if=buff.invoke_xuen_the_white_tiger.remains>15|!talent.invoke_xuen_the_white_tiger&(!talent.storm_earth_and_fire&(cooldown.strike_of_the_windlord.ready|!talent.strike_of_the_windlord&cooldown.fists_of_fury.ready)|buff.storm_earth_and_fire.remains>10)|fight_remains<20
        if S.Fireblood:IsReady() then
          if Cast(S.Fireblood) then return "fireblood cooldowns 16"; end
        end
        -- berserking,if=buff.invoke_xuen_the_white_tiger.remains>15|!talent.invoke_xuen_the_white_tiger&(!talent.storm_earth_and_fire&(cooldown.strike_of_the_windlord.ready|!talent.strike_of_the_windlord&cooldown.fists_of_fury.ready)|buff.storm_earth_and_fire.remains>10)|fight_remains<20
        if S.Berserking:IsReady() then
          if Cast(S.Berserking) then return "berserking cooldowns 18"; end
        end
      end
    end
  
  local function AoEOpener()
    -- slicing_winds
    if S.SlicingWinds:IsReady() then
      if Cast(S.SlicingWinds) then return "slicing_winds aoe_opener 2"; end
    end
    -- tiger_palm,if=chi<6
    if S.TigerPalm:IsReady() and (Player:Chi() < 6) then
      if Cast(S.TigerPalm) then return "tiger_palm aoe_opener 4"; end
    end
  end
  
  local function NormalOpener()
    -- tiger_palm,if=chi<6&combo_strike
    if S.TigerPalm:IsReady() and (Player:Chi() < 6 and ComboStrike(S.TigerPalm)) then
      if Cast(S.TigerPalm) then return "tiger_palm normal_opener 2"; end
    end
    -- rising_sun_kick,if=talent.ordered_elements
    if S.RisingSunKick:IsReady() and (S.OrderedElements:IsAvailable()) then
      if Cast(S.RisingSunKick) then return "rising_sun_kick normal_opener 4"; end
    end
  end
  
  local function DefaultAoE()
    -- tiger_palm,if=(energy>55&talent.inner_peace|energy>60&!talent.inner_peace)&combo_strike&chi.max-chi>=2&buff.teachings_of_the_monastery.stack<buff.teachings_of_the_monastery.max_stack&(talent.energy_burst&!buff.bok_proc.up)&!buff.ordered_elements.up|(talent.energy_burst&!buff.bok_proc.up)&!buff.ordered_elements.up&!cooldown.fists_of_fury.remains&chi<3|(prev.strike_of_the_windlord|cooldown.strike_of_the_windlord.remains)&cooldown.celestial_conduit.remains<2&buff.ordered_elements.up&chi<5&combo_strike
    if S.TigerPalm:IsReady() and ((Player:Energy() > 55 and S.InnerPeace:IsAvailable() or Player:Energy() > 60 and not S.InnerPeace:IsAvailable()) and ComboStrike(S.TigerPalm) and Player:ChiDeficit() >= 2 and Player:BuffStack(S.TeachingsoftheMonasteryBuff) < VarTotMMaxStacks and (S.EnergyBurst:IsAvailable() and Player:BuffDown(S.BlackoutKickBuff)) and Player:BuffDown(S.OrderedElementsBuff) or (S.EnergyBurst:IsAvailable() and Player:BuffDown(S.BlackoutKickBuff)) and Player:BuffDown(S.OrderedElementsBuff) and S.FistsofFury:CooldownUp() and Player:Chi() < 3 or (Player:PrevGCD(1, S.StrikeoftheWindlord) or S.StrikeoftheWindlord:CooldownDown()) and S.CelestialConduit:CooldownRemains() < 2 and Player:BuffUp(S.OrderedElementsBuff) and Player:Chi() < 5 and ComboStrike(S.TigerPalm)) then
      if Cast(S.TigerPalm) then return "tiger_palm default_aoe 2"; end
    end
    -- spinning_crane_kick,target_if=max:target.time_to_die,if=buff.dance_of_chiji.stack=2&combo_strike
    if S.SpinningCraneKick:IsReady() and (Player:BuffStack(S.DanceofChijiBuff) == 2 and ComboStrike(S.SpinningCraneKick)) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_aoe 8"; end
    end
    -- spinning_crane_kick,target_if=max:target.time_to_die,if=combo_strike&buff.chi_energy.stack>29&cooldown.fists_of_fury.remains<5
    if S.SpinningCraneKick:IsReady() and (ComboStrike(S.SpinningCraneKick) and Player:BuffStack(S.ChiEnergyBuff) > 29 and S.FistsofFury:CooldownRemains() < 5) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_aoe 10"; end
    end
    -- whirling_dragon_punch,target_if=max:target.time_to_die,if=buff.heart_of_the_jade_serpent_cdr.up&buff.dance_of_chiji.stack<2
    -- whirling_dragon_punch,target_if=max:target.time_to_die,if=buff.dance_of_chiji.stack<2
    if S.WhirlingDragonPunch:IsReady() and ((Player:BuffUp(S.HeartoftheJadeSerpentCDRBuff) and Player:BuffStack(S.DanceofChijiBuff) < 2) or (Player:BuffStack(S.DanceofChijiBuff) < 2)) then
      if Cast(S.WhirlingDragonPunch) then return "whirling_dragon_punch default_aoe 12"; end
    end
    -- slicing_winds,if=buff.heart_of_the_jade_serpent_cdr.up|buff.heart_of_the_jade_serpent_cdr_celestial.up
    if S.SlicingWinds:IsReady() and (Player:BuffUp(S.HeartoftheJadeSerpentCDRBuff) or Player:BuffUp(S.HeartoftheJadeSerpentCDRCelestialBuff)) then
      if Cast(S.SlicingWinds) then return "slicing_winds default_aoe 14"; end
    end
    -- celestial_conduit,if=buff.storm_earth_and_fire.up&cooldown.strike_of_the_windlord.remains&(!buff.heart_of_the_jade_serpent_cdr.up|debuff.gale_force.remains<5)&(talent.xuens_bond|!talent.xuens_bond&buff.invokers_delight.up)|fight_remains<15|fight_style.dungeonroute&buff.invokers_delight.up&cooldown.strike_of_the_windlord.remains&buff.storm_earth_and_fire.remains<8
    if S.CelestialConduit:IsReady() and (Player:BuffUp(S.StormEarthAndFireBuff) and S.StrikeoftheWindlord:CooldownDown() and (not Player:BuffUp(S.HeartoftheJadeSerpentCDRBuff) or Target:DebuffRemains(S.GaleForceDebuff) < 5) and (S.XuensBond:IsAvailable() or not S.XuensBond:IsAvailable() and Player:BuffUp(S.InvokersDelightBuff)) or BossFightRemains < 15 or DungeonSlice and Player:BuffUp(S.InvokersDelightBuff) and S.StrikeoftheWindlord:CooldownDown() and Player:BuffRemains(S.StormEarthAndFireBuff) < 8) then
      if Cast(S.CelestialConduit) then return "celestial_conduit default_aoe 16"; end
    end
    -- rising_sun_kick,target_if=max:target.time_to_die,if=cooldown.whirling_dragon_punch.remains<2&cooldown.fists_of_fury.remains>1&buff.dance_of_chiji.stack<2|!buff.storm_earth_and_fire.up&buff.pressure_point.up
    if S.RisingSunKick:IsReady() and (S.WhirlingDragonPunch:CooldownRemains() < 2 and S.FistsofFury:CooldownRemains() > 1 and Player:BuffStack(S.DanceofChijiBuff) < 2 or Player:BuffDown(S.StormEarthAndFireBuff) and Player:BuffUp(S.PressurePointBuff)) then
      if Cast(S.RisingSunKick) then return "rising_sun_kick default_aoe 18"; end
    end
    -- whirling_dragon_punch,target_if=max:target.time_to_die,if=!talent.revolving_whirl|talent.revolving_whirl&buff.dance_of_chiji.stack<2&active_enemies>2
    if S.WhirlingDragonPunch:IsReady() and (not S.RevolvingWhirl:IsAvailable() or S.RevolvingWhirl:IsAvailable() and Player:BuffStack(S.DanceofChijiBuff) < 2 and EnemiesCount8y > 2) then
      if Cast(S.WhirlingDragonPunch) then return "whirling_dragon_punch default_aoe 20"; end
    end
    -- blackout_kick,if=combo_strike&buff.bok_proc.up&chi<2&talent.energy_burst&energy<55
    if S.BlackoutKick:IsReady() and (ComboStrike(S.BlackoutKick) and Player:BuffUp(S.BlackoutKickBuff) and Player:Chi() < 2 and S.EnergyBurst:IsAvailable() and Player:Energy() < 55) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_aoe 22"; end
    end
    -- strike_of_the_windlord,target_if=max:target.time_to_die,if=(time>5|buff.invokers_delight.up&buff.storm_earth_and_fire.up)&(cooldown.invoke_xuen_the_white_tiger.remains>15|talent.flurry_strikes)
    if S.StrikeoftheWindlord:IsReady() and ((HL.CombatTime() > 5 or Player:BuffUp(S.InvokersDelightBuff) and Player:BuffUp(S.StormEarthAndFireBuff)) and (S.InvokeXuenTheWhiteTiger:CooldownRemains() > 15 or S.FlurryStrikes:IsAvailable())) then
      if Cast(S.StrikeoftheWindlord) then return "strike_of_the_windlord default_aoe 24"; end
    end
    -- slicing_winds
    if S.SlicingWinds:IsReady() then
      if Cast(S.SlicingWinds) then return "slicing_winds default_aoe 26"; end
    end
    -- blackout_kick,if=buff.teachings_of_the_monastery.stack=8&talent.shadowboxing_treads
    if S.BlackoutKick:IsReady() and (Player:BuffStack(S.TeachingsoftheMonasteryBuff) == 8 and S.ShadowboxingTreads:IsAvailable()) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_aoe 28"; end
    end
    -- Show toast notification if enabled and stacks are high enough
    if Player:BuffStack(S.TheEmperorsCapacitorBuff) >= 19 then
      ShowCJLToastIfEnabled(Player:BuffStack(S.TheEmperorsCapacitorBuff))
    end
    -- crackling_jade_lightning,target_if=max:target.time_to_die,if=buff.the_emperors_capacitor.stack>19&combo_strike&talent.power_of_the_thunder_king&cooldown.invoke_xuen_the_white_tiger.remains>10
    if S.CracklingJadeLightning:IsReady() and (Player:BuffStack(S.TheEmperorsCapacitorBuff) > 19 and ComboStrike(S.CracklingJadeLightning) and S.PoweroftheThunderKing:IsAvailable() and S.InvokeXuenTheWhiteTiger:CooldownRemains() > 10) then
      if Cast(S.CracklingJadeLightning) then return "crackling_jade_lightning default_aoe 30"; end
    end
    -- fists_of_fury,target_if=max:target.time_to_die,if=(talent.flurry_strikes|talent.xuens_battlegear&(cooldown.invoke_xuen_the_white_tiger.remains>5&fight_style.patchwerk|cooldown.invoke_xuen_the_white_tiger.remains>9)|cooldown.invoke_xuen_the_white_tiger.remains>10)
    if S.FistsofFury:IsReady() and (S.FlurryStrikes:IsAvailable() or S.XuensBattlegear:IsAvailable() and (S.InvokeXuenTheWhiteTiger:CooldownRemains() > 5 and not Player:IsInDungeonArea() or S.InvokeXuenTheWhiteTiger:CooldownRemains() > 9) or S.InvokeXuenTheWhiteTiger:CooldownRemains() > 10) then
      if Cast(S.FistsofFury) then return "fists_of_fury default_aoe 32"; end
    end
    -- tiger_palm,if=combo_strike&energy.time_to_max<=gcd.max*3&talent.flurry_strikes&buff.wisdom_of_the_wall_flurry.up&chi<6
    if S.TigerPalm:IsReady() and (ComboStrike(S.TigerPalm) and Player:EnergyTimeToMax() <= Player:GCD() * 3 and S.FlurryStrikes:IsAvailable() and Player:BuffUp(S.WisdomoftheWallFlurryBuff) and Player:Chi() < 6) then
      if Cast(S.TigerPalm) then return "tiger_palm default_aoe 34"; end
    end
    -- spinning_crane_kick,target_if=max:target.time_to_die,if=combo_strike&chi>5
    if S.SpinningCraneKick:IsReady() and (ComboStrike(S.SpinningCraneKick) and Player:Chi() > 5) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_aoe 36"; end
    end
    -- spinning_crane_kick,target_if=max:target.time_to_die,if=combo_strike&buff.dance_of_chiji.up&buff.chi_energy.stack>29&cooldown.fists_of_fury.remains<5
    if S.SpinningCraneKick:IsReady() and (ComboStrike(S.SpinningCraneKick) and Player:BuffUp(S.DanceofChijiBuff) and Player:BuffStack(S.ChiEnergyBuff) > 29 and S.FistsofFury:CooldownRemains() < 5) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_aoe 38"; end
    end
    -- rising_sun_kick,if=buff.pressure_point.up&cooldown.fists_of_fury.remains>2
    if S.RisingSunKick:IsReady() and (Player:BuffUp(S.PressurePointBuff) and S.FistsofFury:CooldownRemains() > 2) then
      if Cast(S.RisingSunKick) then return "rising_sun_kick default_aoe 40"; end
    end
    -- blackout_kick,if=talent.shadowboxing_treads&talent.courageous_impulse&combo_strike&buff.bok_proc.stack=2
    if S.BlackoutKick:IsReady() and (S.ShadowboxingTreads:IsAvailable() and S.CourageousImpulse:IsAvailable() and ComboStrike(S.BlackoutKick) and Player:BuffStack(S.BlackoutKickBuff) == 2) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_aoe 42"; end
    end
    -- spinning_crane_kick,target_if=max:target.time_to_die,if=combo_strike&buff.dance_of_chiji.up
    if S.SpinningCraneKick:IsReady() and (ComboStrike(S.SpinningCraneKick) and Player:BuffUp(S.DanceofChijiBuff)) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_aoe 44"; end
    end
    -- spinning_crane_kick,target_if=max:target.time_to_die,if=combo_strike&buff.ordered_elements.up&talent.crane_vortex&active_enemies>2
    if S.SpinningCraneKick:IsReady() and (ComboStrike(S.SpinningCraneKick) and Player:BuffUp(S.OrderedElementsBuff) and S.CraneVortex:IsAvailable() and EnemiesCount8y > 2) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_aoe 46"; end
    end
    -- tiger_palm,if=combo_strike&energy.time_to_max<=gcd.max*3&talent.flurry_strikes&buff.ordered_elements.up
    if S.TigerPalm:IsReady() and (ComboStrike(S.TigerPalm) and Player:EnergyTimeToMax() <= Player:GCD() * 3 and S.FlurryStrikes:IsAvailable() and Player:BuffUp(S.OrderedElementsBuff)) then
      if Cast(S.TigerPalm) then return "tiger_palm default_aoe 48"; end
    end
    -- tiger_palm,if=combo_strike&chi.deficit>=2&(!buff.ordered_elements.up|energy.time_to_max<=gcd.max*3)
    if S.TigerPalm:IsReady() and (ComboStrike(S.TigerPalm) and Player:ChiDeficit() >= 2 and (Player:BuffDown(S.OrderedElementsBuff) or Player:EnergyTimeToMax() <= Player:GCD() * 3)) then
      if Cast(S.TigerPalm) then return "tiger_palm default_aoe 50"; end
    end
    -- jadefire_stomp,target_if=max:target.time_to_die,if=talent.Singularly_Focused_Jade|talent.jadefire_harmony
    if S.JadefireStomp:IsReady() and (S.SingularlyFocusedJade:IsAvailable() or S.JadefireHarmony:IsAvailable()) then
      if Cast(S.JadefireStomp) then return "jadefire_stomp default_aoe 52"; end
    end
    -- spinning_crane_kick,target_if=max:target.time_to_die,if=combo_strike&!buff.ordered_elements.up&talent.crane_vortex&active_enemies>2&chi>4
    if S.SpinningCraneKick:IsReady() and (ComboStrike(S.SpinningCraneKick) and Player:BuffDown(S.OrderedElementsBuff) and S.CraneVortex:IsAvailable() and EnemiesCount8y > 2 and Player:Chi() > 4) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_aoe 54"; end
    end
    -- blackout_kick,if=combo_strike&cooldown.fists_of_fury.remains&(buff.teachings_of_the_monastery.stack>3|buff.ordered_elements.up)&(talent.shadowboxing_treads|buff.bok_proc.up)
    if S.BlackoutKick:IsReady() and (ComboStrike(S.BlackoutKick) and S.FistsofFury:CooldownDown() and (Player:BuffStack(S.TeachingsoftheMonasteryBuff) > 3 or Player:BuffUp(S.OrderedElementsBuff)) and (S.ShadowboxingTreads:IsAvailable() or Player:BuffUp(S.BlackoutKickBuff))) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_aoe 56"; end
    end
    -- blackout_kick,if=combo_strike&!cooldown.fists_of_fury.remains&chi<3
    if S.BlackoutKick:IsReady() and (ComboStrike(S.BlackoutKick) and S.FistsofFury:CooldownUp() and Player:Chi() < 3) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_aoe 58"; end
    end
    -- blackout_kick,if=talent.shadowboxing_treads&talent.courageous_impulse&combo_strike&buff.bok_proc.up
    if S.BlackoutKick:IsReady() and (S.ShadowboxingTreads:IsAvailable() and S.CourageousImpulse:IsAvailable() and ComboStrike(S.BlackoutKick) and Player:BuffUp(S.BlackoutKickBuff)) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_aoe 60"; end
    end
    -- spinning_crane_kick,if=combo_strike&(chi>3|energy>55)
    if S.SpinningCraneKick:IsReady() and (ComboStrike(S.SpinningCraneKick) and (Player:Chi() > 3 or Player:Energy() > 55)) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_aoe 62"; end
    end
    -- blackout_kick,if=combo_strike&(buff.ordered_elements.up|buff.bok_proc.up&chi.deficit>=1&talent.energy_burst)&cooldown.fists_of_fury.remains
    if S.BlackoutKick:IsReady() and (ComboStrike(S.BlackoutKick) and (Player:BuffUp(S.OrderedElementsBuff) or (Player:BuffUp(S.BlackoutKickBuff) and Player:ChiDeficit() >= 1 and S.EnergyBurst:IsAvailable())) and S.FistsofFury:CooldownDown()) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_aoe 64"; end
    end
    -- blackout_kick,if=combo_strike&cooldown.fists_of_fury.remains&(chi>2|energy>60|buff.bok_proc.up)
    if S.BlackoutKick:IsReady() and (ComboStrike(S.BlackoutKick) and S.FistsofFury:CooldownDown() and (Player:Chi() > 2 or Player:Energy() > 60 or Player:BuffUp(S.BlackoutKickBuff))) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_aoe 66"; end
    end
    -- jadefire_stomp,target_if=max:debuff.acclamation.stack
    if S.JadefireStomp:IsReady() then
      if Cast(S.JadefireStomp) then return "jadefire_stomp default_aoe 68"; end
    end
    -- tiger_palm,if=combo_strike&buff.ordered_elements.up&chi.deficit>=1
    if S.TigerPalm:IsReady() and (ComboStrike(S.TigerPalm) and Player:BuffUp(S.OrderedElementsBuff) and Player:ChiDeficit() >= 1) then
      if Cast(S.TigerPalm) then return "tiger_palm default_aoe 70"; end
    end
    -- chi_burst,if=!buff.ordered_elements.up
    if S.ChiBurst:IsReady() and (Player:BuffDown(S.OrderedElementsBuff)) then
      if Cast(S.ChiBurst) then return "chi_burst default_aoe 72"; end
    end
    -- chi_burst
    if S.ChiBurst:IsReady() then
      if Cast(S.ChiBurst) then return "chi_burst default_aoe 74"; end
    end
    -- spinning_crane_kick,target_if=max:target.time_to_die,if=combo_strike&buff.ordered_elements.up&talent.hit_combo
    if S.SpinningCraneKick:IsReady() and (ComboStrike(S.SpinningCraneKick) and Player:BuffUp(S.OrderedElementsBuff) and S.HitCombo:IsAvailable()) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_aoe 76"; end
    end
    -- blackout_kick,if=buff.ordered_elements.up&!talent.hit_combo&cooldown.fists_of_fury.remains
    if S.BlackoutKick:IsReady() and (Player:BuffUp(S.OrderedElementsBuff) and not S.HitCombo:IsAvailable() and S.FistsofFury:CooldownDown()) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_aoe 78"; end
    end
    -- tiger_palm,if=prev.tiger_palm&chi<3&!cooldown.fists_of_fury.remains
    if S.TigerPalm:IsReady() and (Player:PrevGCD(1, S.TigerPalm) and Player:Chi() < 3 and S.FistsofFury:CooldownUp()) then
      if Cast(S.TigerPalm) then return "tiger_palm default_aoe 80"; end
    end
    -- Manually added: tiger_palm,if=chi=0 (avoids a potential profile stall)
    if S.TigerPalm:IsReady() and (Player:Chi() == 0) then
      if Cast(S.TigerPalm) then return "tiger_palm default_aoe 82"; end
    end
  end  

  local function DefaultCleave()
    -- spinning_crane_kick,if=buff.dance_of_chiji.stack=2&combo_strike
    if S.SpinningCraneKick:IsReady() and (Player:BuffStack(S.DanceofChijiBuff) == 2 and ComboStrike(S.SpinningCraneKick)) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_cleave 2"; end
    end
    -- rising_sun_kick,target_if=max:target.time_to_die,if=buff.pressure_point.up&active_enemies<4&cooldown.fists_of_fury.remains>4
    if S.RisingSunKick:IsReady() and (Player:BuffUp(S.PressurePointBuff) and EnemiesCount8y < 4 and S.FistsofFury:CooldownRemains() > 4) then
      if Cast(S.RisingSunKick) then return "rising_sun_kick default_cleave 4"; end
    end
    -- rising_sun_kick,target_if=max:target.time_to_die,if=cooldown.whirling_dragon_punch.remains<2&cooldown.fists_of_fury.remains>1&buff.dance_of_chiji.stack<2
    if S.RisingSunKick:IsReady() and (S.WhirlingDragonPunch:CooldownRemains() < 2 and S.FistsofFury:CooldownRemains() > 1 and Player:BuffStack(S.DanceofChijiBuff) < 2) then
      if Cast(S.RisingSunKick) then return "rising_sun_kick default_cleave 6"; end
    end
    -- spinning_crane_kick,target_if=max:target.time_to_die,if=combo_strike&buff.dance_of_chiji.stack=2&active_enemies>3
    if S.SpinningCraneKick:IsReady() and (ComboStrike(S.SpinningCraneKick) and Player:BuffStack(S.DanceofChijiBuff) == 2 and EnemiesCount8y > 3) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_cleave 8"; end
    end
    -- tiger_palm,if=(energy>55&talent.inner_peace|energy>60&!talent.inner_peace)&combo_strike&chi.max-chi>=2&buff.teachings_of_the_monastery.stack<buff.teachings_of_the_monastery.max_stack&(talent.energy_burst&!buff.bok_proc.up|!talent.energy_burst)&!buff.ordered_elements.up|(talent.energy_burst&!buff.bok_proc.up|!talent.energy_burst)&!buff.ordered_elements.up&!cooldown.fists_of_fury.remains&chi<3|(prev.strike_of_the_windlord|cooldown.strike_of_the_windlord.remains)&cooldown.celestial_conduit.remains<2&buff.ordered_elements.up&chi<5&combo_strike|(!buff.heart_of_the_jade_serpent_cdr.up|!buff.heart_of_the_jade_serpent_cdr_celestial.up)&combo_strike&chi.deficit>=2&!buff.ordered_elements.up
    if S.TigerPalm:IsReady() and ((Player:Energy() > 55 and S.InnerPeace:IsAvailable() or Player:Energy() > 60 and not S.InnerPeace:IsAvailable()) and ComboStrike(S.TigerPalm) and Player:ChiDeficit() >= 2 and Player:BuffStack(S.TeachingsoftheMonasteryBuff) < VarTotMMaxStacks and (S.EnergyBurst:IsAvailable() and Player:BuffDown(S.BlackoutKickBuff) or not S.EnergyBurst:IsAvailable()) and Player:BuffDown(S.OrderedElementsBuff) or (S.EnergyBurst:IsAvailable() and Player:BuffDown(S.BlackoutKickBuff) or not S.EnergyBurst:IsAvailable()) and Player:BuffDown(S.OrderedElementsBuff) and S.FistsofFury:CooldownUp() and Player:Chi() < 3 or (Player:PrevGCD(1, S.StrikeoftheWindlord) or S.StrikeoftheWindlord:CooldownDown()) and S.CelestialConduit:CooldownRemains() < 2 and Player:BuffUp(S.OrderedElementsBuff) and Player:Chi() < 5 and ComboStrike(S.TigerPalm) or (Player:BuffDown(S.HeartoftheJadeSerpentCDRBuff) or Player:BuffDown(S.HeartoftheJadeSerpentCDRCelestialBuff)) and ComboStrike(S.TigerPalm) and Player:ChiDeficit() >= 2 and Player:BuffDown(S.OrderedElementsBuff)) then
      if Cast(S.TigerPalm) then return "tiger_palm default_cleave 10"; end
    end
    -- whirling_dragon_punch,target_if=max:target.time_to_die,if=buff.heart_of_the_jade_serpent_cdr.up&buff.dance_of_chiji.stack<2
    -- whirling_dragon_punch,target_if=max:target.time_to_die,if=buff.dance_of_chiji.stack<2
    if S.WhirlingDragonPunch:IsReady() and ((Player:BuffUp(S.HeartoftheJadeSerpentCDRBuff) and Player:BuffStack(S.DanceofChijiBuff) < 2) or (Player:BuffStack(S.DanceofChijiBuff) < 2)) then
      if Cast(S.WhirlingDragonPunch) then return "whirling_dragon_punch default_cleave 16"; end
    end
    -- slicing_winds,if=buff.heart_of_the_jade_serpent_cdr.up|buff.heart_of_the_jade_serpent_cdr_celestial.up
    if S.SlicingWinds:IsReady() and (Player:BuffUp(S.HeartoftheJadeSerpentCDRBuff) or Player:BuffUp(S.HeartoftheJadeSerpentCDRCelestialBuff)) then
      if Cast(S.SlicingWinds) then return "slicing_winds default_cleave 18"; end
    end
    -- celestial_conduit,if=buff.storm_earth_and_fire.up&cooldown.strike_of_the_windlord.remains&(!buff.heart_of_the_jade_serpent_cdr.up|debuff.gale_force.remains<5)&(talent.xuens_bond|!talent.xuens_bond&buff.invokers_delight.up)|fight_remains<15|fight_style.dungeonroute&buff.invokers_delight.up&cooldown.strike_of_the_windlord.remains&buff.storm_earth_and_fire.remains<8
    if S.CelestialConduit:IsReady() and (Player:BuffUp(S.StormEarthAndFireBuff) and S.StrikeoftheWindlord:CooldownDown() and (not Player:BuffUp(S.HeartoftheJadeSerpentCDRBuff) or Target:DebuffRemains(S.GaleForceDebuff) < 5) and (S.XuensBond:IsAvailable() or not S.XuensBond:IsAvailable() and Player:BuffUp(S.InvokersDelightBuff)) or BossFightRemains < 15 or Player:IsInDungeonArea() == false and Player:BuffUp(S.InvokersDelightBuff) and S.StrikeoftheWindlord:CooldownDown() and Player:BuffRemains(S.StormEarthAndFireBuff) < 8) then
      if Cast(S.CelestialConduit) then return "celestial_conduit default_cleave 20"; end
    end
    -- rising_sun_kick,target_if=max:target.time_to_die,if=!pet.xuen_the_white_tiger.active&prev.tiger_palm&time<5|buff.heart_of_the_jade_serpent_cdr_celestial.up&buff.pressure_point.up&cooldown.fists_of_fury.remains&(talent.glory_of_the_dawn|active_enemies<3)
    if S.RisingSunKick:IsReady() and (not Monk.Xuen.Active and Player:PrevGCD(1, S.TigerPalm) and HL.CombatTime() < 5 or Player:BuffUp(S.HeartoftheJadeSerpentCDRCelestialBuff) and Player:BuffUp(S.PressurePointBuff) and S.FistsofFury:CooldownDown() and (S.GloryoftheDawn:IsAvailable() or EnemiesCount8y < 3)) then
      if Cast(S.RisingSunKick) then return "rising_sun_kick default_cleave 22"; end
    end
    -- fists_of_fury,target_if=max:target.time_to_die,if=buff.heart_of_the_jade_serpent_cdr_celestial.up
    if S.FistsofFury:IsReady() and (Player:BuffUp(S.HeartoftheJadeSerpentCDRCelestialBuff)) then
      if Cast(S.FistsofFury) then return "fists_of_fury default_cleave 24"; end
    end
    -- whirling_dragon_punch,target_if=max:target.time_to_die,if=buff.heart_of_the_jade_serpent_cdr_celestial.up
    if S.WhirlingDragonPunch:IsReady() and (Player:BuffUp(S.HeartoftheJadeSerpentCDRCelestialBuff)) then
      if Cast(S.WhirlingDragonPunch) then return "whirling_dragon_punch default_cleave 26"; end
    end
    -- strike_of_the_windlord,target_if=max:target.time_to_die,if=talent.gale_force&buff.invokers_delight.up&(buff.bloodlust.up|!buff.heart_of_the_jade_serpent_cdr_celestial.up)
    if S.StrikeoftheWindlord:IsReady() and (S.GaleForce:IsAvailable() and Player:BuffUp(S.InvokersDelightBuff) and (Player:BloodlustUp() or Player:BuffDown(S.HeartoftheJadeSerpentCDRCelestialBuff))) then
      if Cast(S.StrikeoftheWindlord) then return "strike_of_the_windlord default_cleave 28"; end
    end
    -- fists_of_fury,target_if=max:target.time_to_die,if=buff.power_infusion.up&buff.bloodlust.up
    if S.FistsofFury:IsReady() and (Player:PowerInfusionUp() and Player:BloodlustUp()) then
      if Cast(S.FistsofFury) then return "fists_of_fury default_cleave 30"; end
    end
    -- rising_sun_kick,target_if=max:target.time_to_die,if=buff.power_infusion.up&buff.bloodlust.up&active_enemies<3
    if S.RisingSunKick:IsReady() and (Player:PowerInfusionUp() and Player:BloodlustUp() and EnemiesCount8y < 3) then
      if Cast(S.RisingSunKick) then return "rising_sun_kick default_cleave 32"; end
    end
    -- blackout_kick,if=buff.teachings_of_the_monastery.stack=8&(active_enemies<3|talent.shadowboxing_treads)
    if S.BlackoutKick:IsReady() and (Player:BuffStack(S.TeachingsoftheMonasteryBuff) == 8 and (EnemiesCount8y < 3 or S.ShadowboxingTreads:IsAvailable())) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_cleave 34"; end
    end
    -- whirling_dragon_punch,target_if=max:target.time_to_die,if=!talent.revolving_whirl|talent.revolving_whirl&buff.dance_of_chiji.stack<2&active_enemies>2|active_enemies<3
    if S.WhirlingDragonPunch:IsReady() and (not S.RevolvingWhirl:IsAvailable() or S.RevolvingWhirl:IsAvailable() and Player:BuffStack(S.DanceofChijiBuff) < 2 and EnemiesCount8y > 2 or EnemiesCount8y < 3) then
      if Cast(S.WhirlingDragonPunch) then return "whirling_dragon_punch default_cleave 36"; end
    end
    -- strike_of_the_windlord,if=time>5&(cooldown.invoke_xuen_the_white_tiger.remains>15|talent.flurry_strikes)&(cooldown.fists_of_fury.remains<2|cooldown.celestial_conduit.remains<10)
    if S.StrikeoftheWindlord:IsReady() and (HL.CombatTime() > 5 and (S.InvokeXuenTheWhiteTiger:CooldownRemains() > 15 or S.FlurryStrikes:IsAvailable()) and (S.FistsofFury:CooldownRemains() < 2 or S.CelestialConduit:CooldownRemains() < 10)) then
      if Cast(S.StrikeoftheWindlord) then return "strike_of_the_windlord default_cleave 38"; end
    end
    -- slicing_winds
    if S.SlicingWinds:IsReady() then
      if Cast(S.SlicingWinds) then return "slicing_winds default_cleave 40"; end
    end
    -- Show toast notification if enabled and stacks are high enough
    if Player:BuffStack(S.TheEmperorsCapacitorBuff) >= 19 then
      ShowCJLToastIfEnabled(Player:BuffStack(S.TheEmperorsCapacitorBuff))
    end
    -- crackling_jade_lightning,target_if=max:target.time_to_die,if=buff.the_emperors_capacitor.stack>19&combo_strike&talent.power_of_the_thunder_king&cooldown.invoke_xuen_the_white_tiger.remains>10
    if S.CracklingJadeLightning:IsReady() and (Player:BuffStack(S.TheEmperorsCapacitorBuff) > 19 and ComboStrike(S.CracklingJadeLightning) and S.PoweroftheThunderKing:IsAvailable() and S.InvokeXuenTheWhiteTiger:CooldownRemains() > 10) then
      if Cast(S.CracklingJadeLightning) then return "crackling_jade_lightning default_cleave 42"; end
    end
    -- spinning_crane_kick,target_if=max:target.time_to_die,if=combo_strike&buff.dance_of_chiji.stack=2
    if S.SpinningCraneKick:IsReady() and (ComboStrike(S.SpinningCraneKick) and Player:BuffStack(S.DanceofChijiBuff) == 2) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_cleave 44"; end
    end
    -- tiger_palm,if=combo_strike&energy.time_to_max<=gcd.max*3&talent.flurry_strikes&active_enemies<5&buff.wisdom_of_the_wall_flurry.up&active_enemies<4
    if S.TigerPalm:IsReady() and (ComboStrike(S.TigerPalm) and Player:EnergyTimeToMax() <= Player:GCD() * 3 and S.FlurryStrikes:IsAvailable() and EnemiesCount8y < 5 and Player:BuffUp(S.WisdomoftheWallFlurryBuff) and EnemiesCount8y < 4) then
      if Cast(S.TigerPalm) then return "tiger_palm default_cleave 46"; end
    end
    -- fists_of_fury,target_if=max:target.time_to_die,if=(talent.flurry_strikes|talent.xuens_battlegear|!talent.xuens_battlegear&(cooldown.strike_of_the_windlord.remains>1|buff.heart_of_the_jade_serpent_cdr.up|buff.heart_of_the_jade_serpent_cdr_celestial.up))&(talent.flurry_strikes|talent.xuens_battlegear&(cooldown.invoke_xuen_the_white_tiger.remains>5&fight_style.patchwerk|cooldown.invoke_xuen_the_white_tiger.remains>9)|cooldown.invoke_xuen_the_white_tiger.remains>10)
    if S.FistsofFury:IsReady() and ((S.FlurryStrikes:IsAvailable() or S.XuensBattlegear:IsAvailable() or not S.XuensBattlegear:IsAvailable() and (S.StrikeoftheWindlord:CooldownRemains() > 1 or Player:BuffUp(S.HeartoftheJadeSerpentCDRBuff) or Player:BuffUp(S.HeartoftheJadeSerpentCDRCelestialBuff))) and (S.FlurryStrikes:IsAvailable() or S.XuensBattlegear:IsAvailable() and (S.InvokeXuenTheWhiteTiger:CooldownRemains() > 5 and not Player:IsInDungeonArea() or S.InvokeXuenTheWhiteTiger:CooldownRemains() > 9) or S.InvokeXuenTheWhiteTiger:CooldownRemains() > 10)) then
      if Cast(S.FistsofFury) then return "fists_of_fury default_cleave 48"; end
    end
    -- tiger_palm,if=combo_strike&energy.time_to_max<=gcd.max*3&talent.flurry_strikes&active_enemies<5&buff.wisdom_of_the_wall_flurry.up
    if S.TigerPalm:IsReady() and (ComboStrike(S.TigerPalm) and Player:EnergyTimeToMax() <= Player:GCD() * 3 and S.FlurryStrikes:IsAvailable() and EnemiesCount8y < 5 and Player:BuffUp(S.WisdomoftheWallFlurryBuff)) then
      if Cast(S.TigerPalm) then return "tiger_palm default_cleave 50"; end
    end
    -- spinning_crane_kick,target_if=max:target.time_to_die,if=combo_strike&buff.dance_of_chiji.up&buff.chi_energy.stack>29
    if S.SpinningCraneKick:IsReady() and (ComboStrike(S.SpinningCraneKick) and Player:BuffUp(S.DanceofChijiBuff) and Player:BuffStack(S.ChiEnergyBuff) > 29) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_cleave 52"; end
    end
    -- rising_sun_kick,target_if=max:target.time_to_die,if=chi>4&(active_enemies<3|talent.glory_of_the_dawn)|chi>2&energy>50&(active_enemies<3|talent.glory_of_the_dawn)|cooldown.fists_of_fury.remains>2&(active_enemies<3|talent.glory_of_the_dawn)
    if S.RisingSunKick:IsReady() and (Player:Chi() > 4 and (EnemiesCount8y < 3 or S.GloryoftheDawn:IsAvailable()) or Player:Chi() > 2 and Player:Energy() > 50 and (EnemiesCount8y < 3 or S.GloryoftheDawn:IsAvailable()) or S.FistsofFury:CooldownRemains() > 2 and (EnemiesCount8y < 3 or S.GloryoftheDawn:IsAvailable())) then
      if Cast(S.RisingSunKick) then return "rising_sun_kick default_cleave 54"; end
    end
    -- blackout_kick,if=talent.shadowboxing_treads&talent.courageous_impulse&combo_strike&buff.bok_proc.stack=2
    if S.BlackoutKick:IsReady() and (S.ShadowboxingTreads:IsAvailable() and S.CourageousImpulse:IsAvailable() and ComboStrike(S.BlackoutKick) and Player:BuffStack(S.BlackoutKickBuff) == 2) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_cleave 56"; end
    end
    -- blackout_kick,if=buff.teachings_of_the_monastery.stack=4&!talent.knowledge_of_the_broken_temple&talent.shadowboxing_treads&active_enemies<3
    if S.BlackoutKick:IsReady() and (Player:BuffStack(S.TeachingsoftheMonasteryBuff) == 4 and not S.KnowledgeoftheBrokenTemple:IsAvailable() and S.ShadowboxingTreads:IsAvailable() and EnemiesCount8y < 3) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_cleave 58"; end
    end
    -- spinning_crane_kick,target_if=max:target.time_to_die,if=combo_strike&buff.dance_of_chiji.up
    if S.SpinningCraneKick:IsReady() and (ComboStrike(S.SpinningCraneKick) and Player:BuffUp(S.DanceofChijiBuff)) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_cleave 60"; end
    end
    -- blackout_kick,if=talent.shadowboxing_treads&talent.courageous_impulse&combo_strike&buff.bok_proc.up
    if S.BlackoutKick:IsReady() and (S.ShadowboxingTreads:IsAvailable() and S.CourageousImpulse:IsAvailable() and ComboStrike(S.BlackoutKick) and Player:BuffUp(S.BlackoutKickBuff)) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_cleave 62"; end
    end
    -- tiger_palm,if=combo_strike&energy.time_to_max<=gcd.max*3&talent.flurry_strikes&active_enemies<5
    if S.TigerPalm:IsReady() and (ComboStrike(S.TigerPalm) and Player:EnergyTimeToMax() <= Player:GCD() * 3 and S.FlurryStrikes:IsAvailable() and EnemiesCount8y < 5) then
      if Cast(S.TigerPalm) then return "tiger_palm default_cleave 64"; end
    end
    -- tiger_palm,if=combo_strike&chi.deficit>=2&(!buff.ordered_elements.up|energy.time_to_max<=gcd.max*3)
    if S.TigerPalm:IsReady() and (ComboStrike(S.TigerPalm) and Player:ChiDeficit() >= 2 and (Player:BuffDown(S.OrderedElementsBuff) or Player:EnergyTimeToMax() <= Player:GCD() * 3)) then
      if Cast(S.TigerPalm) then return "tiger_palm default_cleave 66"; end
    end
    -- blackout_kick,if=combo_strike&cooldown.fists_of_fury.remains&buff.teachings_of_the_monastery.stack>3&cooldown.rising_sun_kick.remains
    if S.BlackoutKick:IsReady() and (ComboStrike(S.BlackoutKick) and S.FistsofFury:CooldownDown() and Player:BuffStack(S.TeachingsoftheMonasteryBuff) > 3 and S.RisingSunKick:CooldownDown()) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_cleave 68"; end
    end
    -- jadefire_stomp,if=talent.Singularly_Focused_Jade|talent.jadefire_harmony
    if S.JadefireStomp:IsReady() and (S.SingularlyFocusedJade:IsAvailable() or S.JadefireHarmony:IsAvailable()) then
      if Cast(S.JadefireStomp) then return "jadefire_stomp default_cleave 70"; end
    end
    -- blackout_kick,if=combo_strike&cooldown.fists_of_fury.remains&(buff.teachings_of_the_monastery.stack>3|buff.ordered_elements.up)&(talent.shadowboxing_treads|buff.bok_proc.up|buff.ordered_elements.up)
    if S.BlackoutKick:IsReady() and (ComboStrike(S.BlackoutKick) and S.FistsofFury:CooldownDown() and (Player:BuffStack(S.TeachingsoftheMonasteryBuff) > 3 or Player:BuffUp(S.OrderedElementsBuff)) and (S.ShadowboxingTreads:IsAvailable() or Player:BuffUp(S.BlackoutKickBuff) or Player:BuffUp(S.OrderedElementsBuff))) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_cleave 72"; end
    end
    -- spinning_crane_kick,target_if=max:target.time_to_die,if=combo_strike&!buff.ordered_elements.up&talent.crane_vortex&active_enemies>2&chi>4
    if S.SpinningCraneKick:IsReady() and (ComboStrike(S.SpinningCraneKick) and Player:BuffDown(S.OrderedElementsBuff) and S.CraneVortex:IsAvailable() and EnemiesCount8y > 2 and Player:Chi() > 4) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_cleave 74"; end
    end
    -- chi_burst,if=!buff.ordered_elements.up
    if S.ChiBurst:IsReady() and (Player:BuffDown(S.OrderedElementsBuff)) then
      if Cast(S.ChiBurst) then return "chi_burst default_cleave 76"; end
    end
    -- blackout_kick,if=combo_strike&(buff.ordered_elements.up|buff.bok_proc.up&chi.deficit>=1&talent.energy_burst)&cooldown.fists_of_fury.remains
    if S.BlackoutKick:IsReady() and (ComboStrike(S.BlackoutKick) and (Player:BuffUp(S.OrderedElementsBuff) or Player:BuffUp(S.BlackoutKickBuff) and Player:ChiDeficit() >= 1 and S.EnergyBurst:IsAvailable()) and S.FistsofFury:CooldownDown()) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_cleave 78"; end
    end
    -- blackout_kick,if=combo_strike&cooldown.fists_of_fury.remains&(chi>2|energy>60|buff.bok_proc.up)
    if S.BlackoutKick:IsReady() and (ComboStrike(S.BlackoutKick) and S.FistsofFury:CooldownDown() and (Player:Chi() > 2 or Player:Energy() > 60 or Player:BuffUp(S.BlackoutKickBuff))) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_cleave 80"; end
    end
    -- jadefire_stomp,target_if=max:debuff.acclamation.stack
    if S.JadefireStomp:IsReady() then
      if Cast(S.JadefireStomp) then return "jadefire_stomp default_cleave 82"; end
    end
    -- tiger_palm,if=combo_strike&buff.ordered_elements.up&chi.deficit>=1
    if S.TigerPalm:IsReady() and (ComboStrike(S.TigerPalm) and Player:BuffUp(S.OrderedElementsBuff) and Player:ChiDeficit() >= 1) then
      if Cast(S.TigerPalm) then return "tiger_palm default_cleave 84"; end
    end
    -- chi_burst
    if S.ChiBurst:IsReady() then
      if Cast(S.ChiBurst) then return "chi_burst default_cleave 86"; end
    end
    -- spinning_crane_kick,if=combo_strike&buff.ordered_elements.up&talent.hit_combo
    if S.SpinningCraneKick:IsReady() and (ComboStrike(S.SpinningCraneKick) and Player:BuffUp(S.OrderedElementsBuff) and S.HitCombo:IsAvailable()) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_cleave 88"; end
    end
    -- blackout_kick,if=buff.ordered_elements.up&!talent.hit_combo&cooldown.fists_of_fury.remains
    if S.BlackoutKick:IsReady() and (Player:BuffUp(S.OrderedElementsBuff) and not S.HitCombo:IsAvailable() and S.FistsofFury:CooldownDown()) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_cleave 90"; end
    end
    -- tiger_palm,if=prev.tiger_palm&chi<3&!cooldown.fists_of_fury.remains
    if S.TigerPalm:IsReady() and (Player:PrevGCD(1, S.TigerPalm) and Player:Chi() < 3 and S.FistsofFury:CooldownUp()) then
      if Cast(S.TigerPalm) then return "tiger_palm default_cleave 92"; end
    end
    -- Manually added: tiger_palm,if=chi=0 (avoids a potential profile stall)
    if S.TigerPalm:IsReady() and (Player:Chi() == 0) then
      if Cast(S.TigerPalm) then return "tiger_palm default_cleave 94"; end
    end
  end  

  local function DefaultST()
    -- fists_of_fury,if=buff.heart_of_the_jade_serpent_cdr_celestial.up|buff.heart_of_the_jade_serpent_cdr.up
    if S.FistsofFury:IsReady() and (Player:BuffUp(S.HeartoftheJadeSerpentCDRCelestialBuff) or Player:BuffUp(S.HeartoftheJadeSerpentCDRBuff)) then
      if Cast(S.FistsofFury) then return "fists_of_fury default_st 2"; end
    end
    -- rising_sun_kick,if=buff.pressure_point.up&!buff.heart_of_the_jade_serpent_cdr.up&buff.heart_of_the_jade_serpent_cdr_celestial.up|buff.invokers_delight.up|buff.bloodlust.up|buff.pressure_point.up&cooldown.fists_of_fury.remains|buff.power_infusion.up
    if S.RisingSunKick:IsReady() and (Player:BuffUp(S.PressurePointBuff) and Player:BuffDown(S.HeartoftheJadeSerpentCDRBuff) and Player:BuffUp(S.HeartoftheJadeSerpentCDRCelestialBuff) or Player:BuffUp(S.InvokersDelightBuff) or Player:BloodlustUp() or Player:BuffUp(S.PressurePointBuff) and S.FistsofFury:CooldownDown() or Player:PowerInfusionUp()) then
      if Cast(S.RisingSunKick) then return "rising_sun_kick default_st 4"; end
    end
    -- whirling_dragon_punch,if=!buff.heart_of_the_jade_serpent_cdr_celestial.up&!buff.dance_of_chiji.stack=2
    if S.WhirlingDragonPunch:IsReady() and (Player:BuffDown(S.HeartoftheJadeSerpentCDRCelestialBuff) and Player:BuffStack(S.DanceofChijiBuff) ~= 2) then
      if Cast(S.WhirlingDragonPunch) then return "whirling_dragon_punch default_st 6"; end
    end
    -- slicing_winds,if=buff.heart_of_the_jade_serpent_cdr.up|buff.heart_of_the_jade_serpent_cdr_celestial.up
    if S.SlicingWinds:IsReady() and (Player:BuffUp(S.HeartoftheJadeSerpentCDRBuff) or Player:BuffUp(S.HeartoftheJadeSerpentCDRCelestialBuff)) then
      if Cast(S.SlicingWinds) then return "slicing_winds default_st 8"; end
    end
    -- celestial_conduit,if=buff.storm_earth_and_fire.up&(!buff.heart_of_the_jade_serpent_cdr.up|debuff.gale_force.remains<5)&cooldown.strike_of_the_windlord.remains&(talent.xuens_bond|!talent.xuens_bond&buff.invokers_delight.up)|fight_remains<15|fight_style.dungeonroute&buff.invokers_delight.up&cooldown.strike_of_the_windlord.remains&buff.storm_earth_and_fire.remains<8|fight_remains<10
    if S.CelestialConduit:IsReady() and (Player:BuffUp(S.StormEarthAndFireBuff) and (Player:BuffDown(S.HeartoftheJadeSerpentCDRBuff) or Target:DebuffRemains(S.GaleForceDebuff) < 5) and S.StrikeoftheWindlord:CooldownDown() and (S.XuensBond:IsAvailable() or not S.XuensBond:IsAvailable() and Player:BuffUp(S.InvokersDelightBuff)) or BossFightRemains < 15 or Player:IsInDungeonArea() == false and Player:BuffUp(S.InvokersDelightBuff) and S.StrikeoftheWindlord:CooldownDown() and Player:BuffRemains(S.StormEarthAndFireBuff) < 8 or BossFightRemains < 10) then
      if Cast(S.CelestialConduit) then return "celestial_conduit default_st 10"; end
    end
    -- spinning_crane_kick,if=buff.dance_of_chiji.stack=2&combo_strike
    if S.SpinningCraneKick:IsReady() and (Player:BuffStack(S.DanceofChijiBuff) == 2 and ComboStrike(S.SpinningCraneKick)) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_st 12"; end
    end
    -- tiger_palm,if=(energy>55&talent.inner_peace|energy>60&!talent.inner_peace)&combo_strike&chi.max-chi>=2&buff.teachings_of_the_monastery.stack<buff.teachings_of_the_monastery.max_stack&(talent.energy_burst&!buff.bok_proc.up|!talent.energy_burst)&!buff.ordered_elements.up|(talent.energy_burst&!buff.bok_proc.up|!talent.energy_burst)&!buff.ordered_elements.up&!cooldown.fists_of_fury.remains&chi<3|(prev.strike_of_the_windlord|!buff.heart_of_the_jade_serpent_cdr_celestial.up)&combo_strike&chi.deficit>=2&!buff.ordered_elements.up
    -- Note: APL line has old 'target_if=min:debuff.mark_of_the_crane.remains' condition. I assume that remains by error, so we're removing it.
    if S.TigerPalm:IsReady() and ((Player:Energy() > 55 and S.InnerPeace:IsAvailable() or Player:Energy() > 60 and not S.InnerPeace:IsAvailable()) and ComboStrike(S.TigerPalm) and Player:ChiDeficit() >= 2 and Player:BuffStack(S.TeachingsoftheMonasteryBuff) < VarTotMMaxStacks and (S.EnergyBurst:IsAvailable() and Player:BuffDown(S.BlackoutKickBuff) or not S.EnergyBurst:IsAvailable()) and Player:BuffDown(S.OrderedElementsBuff) or (S.EnergyBurst:IsAvailable() and Player:BuffDown(S.BlackoutKickBuff) or not S.EnergyBurst:IsAvailable()) and Player:BuffDown(S.OrderedElementsBuff) and S.FistsofFury:CooldownUp() and Player:Chi() < 3 or (Player:PrevGCD(1, S.StrikeoftheWindlord) or Player:BuffDown(S.HeartoftheJadeSerpentCDRCelestialBuff)) and ComboStrike(S.TigerPalm) and Player:ChiDeficit() >= 2 and Player:BuffDown(S.OrderedElementsBuff)) then
      if Cast(S.TigerPalm) then return "tiger_palm default_st 14"; end
    end
    -- rising_sun_kick,if=!pet.xuen_the_white_tiger.active&prev.tiger_palm&time<5|buff.storm_earth_and_fire.up&talent.ordered_elements
    if S.RisingSunKick:IsReady() and (not Monk.Xuen.Active and Player:PrevGCD(1, S.TigerPalm) and HL.CombatTime() < 5 or Player:BuffUp(S.StormEarthAndFireBuff) and S.OrderedElements:IsAvailable()) then
      if Cast(S.RisingSunKick) then return "rising_sun_kick default_st 20"; end
    end
    -- strike_of_the_windlord,if=talent.celestial_conduit&!buff.invokers_delight.up&!buff.heart_of_the_jade_serpent_cdr_celestial.up&cooldown.fists_of_fury.remains<5&cooldown.invoke_xuen_the_white_tiger.remains>15|fight_remains<12
    if S.StrikeoftheWindlord:IsReady() and (S.CelestialConduit:IsAvailable() and Player:BuffDown(S.InvokersDelightBuff) and Player:BuffDown(S.HeartoftheJadeSerpentCDRCelestialBuff) and S.FistsofFury:CooldownRemains() < 5 and S.InvokeXuenTheWhiteTiger:CooldownRemains() > 15 or BossFightRemains < 12) then
      if Cast(S.StrikeoftheWindlord) then return "strike_of_the_windlord default_st 22"; end
    end
    -- strike_of_the_windlord,if=talent.gale_force&buff.invokers_delight.up&(buff.bloodlust.up|!buff.heart_of_the_jade_serpent_cdr_celestial.up)
    if S.StrikeoftheWindlord:IsReady() and (S.GaleForce:IsAvailable() and Player:BuffUp(S.InvokersDelightBuff) and (Player:BloodlustUp() or Player:BuffDown(S.HeartoftheJadeSerpentCDRCelestialBuff))) then
      if Cast(S.StrikeoftheWindlord) then return "strike_of_the_windlord default_st 24"; end
    end
    -- strike_of_the_windlord,if=time>5&talent.flurry_strikes
    if S.StrikeoftheWindlord:IsReady() and (HL.CombatTime() > 5 and S.FlurryStrikes:IsAvailable()) then
      if Cast(S.StrikeoftheWindlord) then return "strike_of_the_windlord default_st 26"; end
    end
    -- fists_of_fury,if=buff.power_infusion.up&buff.bloodlust.up&time>5
    if S.FistsofFury:IsReady() and (Player:PowerInfusionUp() and Player:BloodlustUp() and HL.CombatTime() > 5) then
      if Cast(S.FistsofFury) then return "fists_of_fury default_st 28"; end
    end
    -- blackout_kick,if=buff.teachings_of_the_monastery.stack>3&buff.ordered_elements.up&cooldown.rising_sun_kick.remains>1&cooldown.fists_of_fury.remains>2
    if S.BlackoutKick:IsReady() and (Player:BuffStack(S.TeachingsoftheMonasteryBuff) > 3 and Player:BuffUp(S.OrderedElementsBuff) and S.RisingSunKick:CooldownRemains() > 1 and S.FistsofFury:CooldownRemains() > 2) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_st 30"; end
    end
    -- tiger_palm,if=combo_strike&energy.time_to_max<=gcd.max*3&talent.flurry_strikes&buff.power_infusion.up&buff.bloodlust.up
    if S.TigerPalm:IsReady() and (ComboStrike(S.TigerPalm) and Player:EnergyTimeToMax() <= Player:GCD() * 3 and S.FlurryStrikes:IsAvailable() and Player:PowerInfusionUp() and Player:BloodlustUp()) then
      if Cast(S.TigerPalm) then return "tiger_palm default_st 32"; end
    end
    -- blackout_kick,if=buff.teachings_of_the_monastery.stack>4&cooldown.rising_sun_kick.remains>1&cooldown.fists_of_fury.remains>2
    if S.BlackoutKick:IsReady() and (Player:BuffStack(S.TeachingsoftheMonasteryBuff) > 4 and S.RisingSunKick:CooldownRemains() > 1 and S.FistsofFury:CooldownRemains() > 2) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_st 34"; end
    end
    -- whirling_dragon_punch,if=!buff.heart_of_the_jade_serpent_cdr_celestial.up&!buff.dance_of_chiji.stack=2|buff.ordered_elements.up|talent.knowledge_of_the_broken_temple
    if S.WhirlingDragonPunch:IsReady() and (Player:BuffDown(S.HeartoftheJadeSerpentCDRCelestialBuff) and Player:BuffStack(S.DanceofChijiBuff) ~= 2 or Player:BuffUp(S.OrderedElementsBuff) or S.KnowledgeoftheBrokenTemple:IsAvailable()) then
      if Cast(S.WhirlingDragonPunch) then return "whirling_dragon_punch default_st 36"; end
    end
    -- Show toast notification if enabled and stacks are high enough
    if Player:BuffStack(S.TheEmperorsCapacitorBuff) >= 19 then
      ShowCJLToastIfEnabled(Player:BuffStack(S.TheEmperorsCapacitorBuff))
    end
    -- crackling_jade_lightning,if=buff.the_emperors_capacitor.stack>19&!buff.heart_of_the_jade_serpent_cdr.up&!buff.heart_of_the_jade_serpent_cdr_celestial.up&combo_strike&(!fight_style.dungeonslice|target.time_to_die>20)&cooldown.invoke_xuen_the_white_tiger.remains>10|buff.the_emperors_capacitor.stack>15&!buff.heart_of_the_jade_serpent_cdr.up&!buff.heart_of_the_jade_serpent_cdr_celestial.up&combo_strike&(!fight_style.dungeonslice|target.time_to_die>20)&cooldown.invoke_xuen_the_white_tiger.remains<10&cooldown.invoke_xuen_the_white_tiger.remains>2
    if S.CracklingJadeLightning:IsReady() and (Player:BuffStack(S.TheEmperorsCapacitorBuff) > 19 and Player:BuffDown(S.HeartoftheJadeSerpentCDRBuff) and Player:BuffDown(S.HeartoftheJadeSerpentCDRCelestialBuff) and ComboStrike(S.CracklingJadeLightning) and (not Player:IsInDungeonArea() or Target:TimeToDie() > 20) and S.InvokeXuenTheWhiteTiger:CooldownRemains() > 10 or Player:BuffStack(S.TheEmperorsCapacitorBuff) > 15 and Player:BuffDown(S.HeartoftheJadeSerpentCDRBuff) and Player:BuffDown(S.HeartoftheJadeSerpentCDRCelestialBuff) and ComboStrike(S.CracklingJadeLightning) and (not Player:IsInDungeonArea() or Target:TimeToDie() > 20) and S.InvokeXuenTheWhiteTiger:CooldownRemains() < 10 and S.InvokeXuenTheWhiteTiger:CooldownRemains() > 2) then
      if Cast(S.CracklingJadeLightning) then return "crackling_jade_lightning default_st 38"; end
    end
    -- slicing_winds,if=target.time_to_die>10
    if S.SlicingWinds:IsReady() and (Target:TimeToDie() > 10) then
      if Cast(S.SlicingWinds) then return "slicing_winds default_st 40"; end
    end
    -- fists_of_fury,if=(talent.xuens_battlegear|!talent.xuens_battlegear&(cooldown.strike_of_the_windlord.remains>1|buff.heart_of_the_jade_serpent_cdr.up|buff.heart_of_the_jade_serpent_cdr_celestial.up))&(talent.xuens_battlegear&cooldown.invoke_xuen_the_white_tiger.remains>5|cooldown.invoke_xuen_the_white_tiger.remains>10)&(!buff.invokers_delight.up|buff.invokers_delight.up&cooldown.strike_of_the_windlord.remains>4&cooldown.celestial_conduit.remains)|fight_remains<5|talent.flurry_strikes
    if S.FistsofFury:IsReady() and ((S.XuensBattlegear:IsAvailable() or not S.XuensBattlegear:IsAvailable() and (S.StrikeoftheWindlord:CooldownRemains() > 1 or Player:BuffUp(S.HeartoftheJadeSerpentCDRBuff) or Player:BuffUp(S.HeartoftheJadeSerpentCDRCelestialBuff))) and (S.XuensBattlegear:IsAvailable() and S.InvokeXuenTheWhiteTiger:CooldownRemains() > 5 or S.InvokeXuenTheWhiteTiger:CooldownRemains() > 10) and (Player:BuffDown(S.InvokersDelightBuff) or Player:BuffUp(S.InvokersDelightBuff) and S.StrikeoftheWindlord:CooldownRemains() > 4 and S.CelestialConduit:CooldownDown()) or BossFightRemains < 5 or S.FlurryStrikes:IsAvailable()) then
      if Cast(S.FistsofFury) then return "fists_of_fury default_st 42"; end
    end
    -- rising_sun_kick,if=chi>4|chi>2&energy>50|cooldown.fists_of_fury.remains>2
    if S.RisingSunKick:IsReady() and (Player:Chi() > 4 or Player:Chi() > 2 and Player:Energy() > 50 or S.FistsofFury:CooldownRemains() > 2) then
      if Cast(S.RisingSunKick) then return "rising_sun_kick default_st 44"; end
    end
    -- tiger_palm,if=combo_strike&energy.time_to_max<=gcd.max*3&talent.flurry_strikes&buff.wisdom_of_the_wall_flurry.up
    if S.TigerPalm:IsReady() and (ComboStrike(S.TigerPalm) and Player:EnergyTimeToMax() <= Player:GCD() * 3 and S.FlurryStrikes:IsAvailable() and Player:BuffUp(S.WisdomoftheWallFlurryBuff)) then
      if Cast(S.TigerPalm) then return "tiger_palm default_st 50"; end
    end
    -- blackout_kick,if=combo_strike&talent.energy_burst&buff.bok_proc.up&chi<5&(buff.heart_of_the_jade_serpent_cdr.up|buff.heart_of_the_jade_serpent_cdr_celestial.up)
    if S.BlackoutKick:IsReady() and (ComboStrike(S.BlackoutKick) and S.EnergyBurst:IsAvailable() and Player:BuffUp(S.BlackoutKickBuff) and Player:Chi() < 5 and (Player:BuffUp(S.HeartoftheJadeSerpentCDRBuff) or Player:BuffUp(S.HeartoftheJadeSerpentCDRCelestialBuff))) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_st 46"; end
    end
    -- spinning_crane_kick,if=combo_strike&buff.bloodlust.up&buff.heart_of_the_jade_serpent_cdr.up&buff.dance_of_chiji.up
    if S.SpinningCraneKick:IsReady() and (ComboStrike(S.SpinningCraneKick) and Player:BloodlustUp() and Player:BuffUp(S.HeartoftheJadeSerpentCDRBuff) and Player:BuffUp(S.DanceofChijiBuff)) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_st 48"; end
    end
    -- tiger_palm,if=combo_strike&chi.deficit>=2&energy.time_to_max<=gcd.max*3
    if S.TigerPalm:IsReady() and (ComboStrike(S.TigerPalm) and Player:ChiDeficit() >= 2 and Player:EnergyTimeToMax() <= Player:GCD() * 3) then
      if Cast(S.TigerPalm) then return "tiger_palm default_st 52"; end
    end
    -- blackout_kick,if=buff.teachings_of_the_monastery.stack>7&talent.memory_of_the_monastery&!buff.memory_of_the_monastery.up&cooldown.fists_of_fury.remains
    if S.BlackoutKick:IsReady() and (Player:BuffStack(S.TeachingsoftheMonasteryBuff) > 7 and S.MemoryoftheMonastery:IsAvailable() and Player:BuffDown(S.MemoryoftheMonasteryBuff) and S.FistsofFury:CooldownDown()) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_st 54"; end
    end
    -- spinning_crane_kick,if=(buff.dance_of_chiji.stack=2|buff.dance_of_chiji.remains<2&buff.dance_of_chiji.up)&combo_strike&!buff.ordered_elements.up
    if S.SpinningCraneKick:IsReady() and ((Player:BuffStack(S.DanceofChijiBuff) == 2 or Player:BuffRemains(S.DanceofChijiBuff) < 2 and Player:BuffUp(S.DanceofChijiBuff)) and ComboStrike(S.SpinningCraneKick) and Player:BuffDown(S.OrderedElementsBuff)) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_st 56"; end
    end
    -- whirling_dragon_punch
    if S.WhirlingDragonPunch:IsReady() then
      if Cast(S.WhirlingDragonPunch) then return "whirling_dragon_punch default_st 58"; end
    end
    -- spinning_crane_kick,if=buff.dance_of_chiji.stack=2&combo_strike
    if S.SpinningCraneKick:IsReady() and (Player:BuffStack(S.DanceofChijiBuff) == 2 and ComboStrike(S.SpinningCraneKick)) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_st 60"; end
    end
    -- blackout_kick,if=talent.courageous_impulse&combo_strike&buff.bok_proc.stack=2
    if S.BlackoutKick:IsReady() and (S.CourageousImpulse:IsAvailable() and ComboStrike(S.BlackoutKick) and Player:BuffStack(S.BlackoutKickBuff) == 2) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_st 62"; end
    end
    -- blackout_kick,if=combo_strike&buff.ordered_elements.up&cooldown.rising_sun_kick.remains>1&cooldown.fists_of_fury.remains>2
    if S.BlackoutKick:IsReady() and (ComboStrike(S.BlackoutKick) and Player:BuffUp(S.OrderedElementsBuff) and S.RisingSunKick:CooldownRemains() > 1 and S.FistsofFury:CooldownRemains() > 2) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_st 64"; end
    end
    -- tiger_palm,if=combo_strike&energy.time_to_max<=gcd.max*3&talent.flurry_strikes
    if S.TigerPalm:IsReady() and (ComboStrike(S.TigerPalm) and Player:EnergyTimeToMax() <= Player:GCD() * 3 and S.FlurryStrikes:IsAvailable()) then
      if Cast(S.TigerPalm) then return "tiger_palm default_st 66"; end
    end
    -- spinning_crane_kick,if=combo_strike&buff.dance_of_chiji.up&(buff.ordered_elements.up|energy.time_to_max>=gcd.max*3&talent.sequenced_strikes&talent.energy_burst|!talent.sequenced_strikes|!talent.energy_burst|buff.dance_of_chiji.remains<=gcd.max*3)
    if S.SpinningCraneKick:IsReady() and (ComboStrike(S.SpinningCraneKick) and Player:BuffUp(S.DanceofChijiBuff) and (Player:BuffUp(S.OrderedElementsBuff) or Player:EnergyTimeToMax() >= Player:GCD() * 3 and S.SequencedStrikes:IsAvailable() and S.EnergyBurst:IsAvailable() or not S.SequencedStrikes:IsAvailable() or not S.EnergyBurst:IsAvailable() or Player:BuffRemains(S.DanceofChijiBuff) <= Player:GCD() * 3)) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_st 68"; end
    end
    -- tiger_palm,if=combo_strike&energy.time_to_max<=gcd.max*3&talent.flurry_strikes
    -- Note: Identical line two lines above.
    -- jadefire_stomp,if=talent.Singularly_Focused_Jade|talent.jadefire_harmony
    if S.JadefireStomp:IsReady() and (S.SingularlyFocusedJade:IsAvailable() or S.JadefireHarmony:IsAvailable()) then
      if Cast(S.JadefireStomp) then return "jadefire_stomp default_st 70"; end
    end
    -- chi_burst,if=!buff.ordered_elements.up
    if S.ChiBurst:IsReady() and (Player:BuffDown(S.OrderedElementsBuff)) then
      if Cast(S.ChiBurst) then return "chi_burst default_st 72"; end
    end
    -- blackout_kick,if=combo_strike&(buff.ordered_elements.up|buff.bok_proc.up&chi.deficit>=1&talent.energy_burst)&cooldown.fists_of_fury.remains
    if S.BlackoutKick:IsReady() and (ComboStrike(S.BlackoutKick) and (Player:BuffUp(S.OrderedElementsBuff) or Player:BuffUp(S.BlackoutKickBuff) and Player:ChiDeficit() >= 1 and S.EnergyBurst:IsAvailable()) and S.FistsofFury:CooldownDown()) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_st 74"; end
    end
    -- blackout_kick,if=combo_strike&cooldown.fists_of_fury.remains&(chi>2|energy>60|buff.bok_proc.up)
    if S.BlackoutKick:IsReady() and (ComboStrike(S.BlackoutKick) and S.FistsofFury:CooldownDown() and (Player:Chi() > 2 or Player:Energy() > 60 or Player:BuffUp(S.BlackoutKickBuff))) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_st 76"; end
    end
    -- jadefire_stomp
    if S.JadefireStomp:IsReady() then
      if Cast(S.JadefireStomp) then return "jadefire_stomp default_st 78"; end
    end
    -- tiger_palm,if=combo_strike&buff.ordered_elements.up&chi.deficit>=1
    if S.TigerPalm:IsReady() and (ComboStrike(S.TigerPalm) and Player:BuffUp(S.OrderedElementsBuff) and Player:ChiDeficit() >= 1) then
      if Cast(S.TigerPalm) then return "tiger_palm default_st 80"; end
    end
    -- chi_burst
    if S.ChiBurst:IsReady() then
      if Cast(S.ChiBurst) then return "chi_burst default_st 82"; end
    end
    -- spinning_crane_kick,if=combo_strike&buff.ordered_elements.up&talent.hit_combo
    if S.SpinningCraneKick:IsReady() and (ComboStrike(S.SpinningCraneKick) and Player:BuffUp(S.OrderedElementsBuff) and S.HitCombo:IsAvailable()) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick default_st 84"; end
    end
    -- blackout_kick,if=buff.ordered_elements.up&!talent.hit_combo&cooldown.fists_of_fury.remains
    if S.BlackoutKick:IsReady() and (Player:BuffUp(S.OrderedElementsBuff) and not S.HitCombo:IsAvailable() and S.FistsofFury:CooldownDown()) then
      if Cast(S.BlackoutKick) then return "blackout_kick default_st 86"; end
    end
    -- tiger_palm,if=prev.tiger_palm&chi<3&!cooldown.fists_of_fury.remains
    if S.TigerPalm:IsReady() and (Player:PrevGCD(1, S.TigerPalm) and Player:Chi() < 3 and S.FistsofFury:CooldownUp()) then
      if Cast(S.TigerPalm) then return "tiger_palm default_st 88"; end
    end
    -- Manually added: tiger_palm,if=chi=0 (avoids a potential profile stall)
    if S.TigerPalm:IsReady() and (Player:Chi() == 0) then
      if Cast(S.TigerPalm) then return "tiger_palm default_st 90"; end
    end
  end

  local function Fallback()
    -- spinning_crane_kick,if=chi>5&combo_strike
    if S.SpinningCraneKick:IsReady() and (Player:Chi() > 5 and ComboStrike(S.SpinningCraneKick)) then
      if Cast(S.SpinningCraneKick) then return "spinning_crane_kick fallback 2"; end
    end
    -- blackout_kick,if=combo_strike&chi>3
    if S.BlackoutKick:IsReady() and (ComboStrike(S.BlackoutKick) and Player:Chi() > 3) then
      if Cast(S.BlackoutKick) then return "blackout_kick fallback 4"; end
    end
    -- tiger_palm,if=combo_strike&chi>5
    if S.TigerPalm:IsReady() and (ComboStrike(S.TigerPalm) and Player:Chi() > 5) then
      if Cast(S.TigerPalm) then return "tiger_palm fallback 6"; end
    end
  end


  -- APL Main
  local function APL()
      Enemies5y = Player:GetEnemiesInMeleeRange(5) -- Multiple Abilities
      Enemies8y = Player:GetEnemiesInMeleeRange(8) -- Multiple Abilities

      if AoEON() then
          EnemiesCount8y = #Enemies8y
      else
          EnemiesCount8y = 1
      end
      
      if M.TargetIsValid() or Player:AffectingCombat() then
          -- Calculate fight_remains
          BossFightRemains = HL.BossFightRemains()
          FightRemains = BossFightRemains
          if FightRemains == 11111 then
            FightRemains = HL.FightRemains(Enemies8y, false)
          end

          -- Check Target Range
          IsInMeleeRange = Target:IsInMeleeRange(5)

          -- Get CombatTime
          CombatTime = HL.CombatTime()

          -- Check DungeonSlice
          DungeonSlice = Player:IsInDungeonArea()
      end

      -- Celestial Conduit with Unity Within management
      if Player:IsChanneling(S.CelestialConduit) and S.UnityWithin:IsAvailable() then
          if Player:ChannelRemains() <= 2 then
              if M.ForceCastDisplay(S.CelestialConduit, 6) then
                  return "Optimizing Unity Within timing"
              end
          end
      end

      -- Slicing Winds management
      if Player:IsChanneling(S.SlicingWinds) and not S.SlicingWinds:IsQueued() then
          if M.ForceCastDisplay(S.SlicingWinds) then
              return "Slicing Winds"
          end
      end
      

      if not Player:AffectingCombat() then
          local ShouldReturn = Utilities(); 
          if ShouldReturn then 
              return ShouldReturn; 
          end
      end

      local ShouldReturn = Defensives();
      if ShouldReturn then
          return ShouldReturn;
      end

      if M.TargetIsValid() then
          -- Trinkets
          local shouldReturn = MainAddon.TrinketDPS()
          if shouldReturn then
              return shouldReturn
          end  

          -- Precombat
          if not Player:AffectingCombat() then
            local ShouldReturn = Precombat(); if ShouldReturn then return ShouldReturn; end
          end

          -- potion handling
          if Target:IsInRange(8) then
              if MainAddon.UsePotion() then
                  MainAddon.SetTopColor(1, "Combat Potion")
              end
          end

          -- YUNO: 2nd charge handling of SEF with advanced conditions (no locals)
          if S.StormEarthAndFire:IsAvailable() and S.StormEarthAndFire:Charges() == 1 and not Player:BuffUp(S.StormEarthAndFireBuff) then
            if (not S.InvokeXuenTheWhiteTiger:IsAvailable() or (not S.InvokeXuenTheWhiteTiger:CooldownUp() and S.InvokeXuenTheWhiteTiger:CooldownRemains(nil, true) >= 85))
              and (not S.PoweroftheThunderKing:IsAvailable() or Player:BuffStack(S.TheEmperorsCapacitorBuff) >= 16)
              and (not S.CelestialConduit:IsAvailable() or not S.CelestialConduit:CooldownUp())
            then
              if Cast(S.StormEarthAndFire) then return "Storm Earth and Fire 2nd charge"; end
            end
          end

          -- variable,name=has_external_pi,value=cooldown.invoke_power_infusion_0.duration>0
          -- Note: Not handling external buffs.
          -- call_action_list,name=trinkets
          local ShouldReturn = Trinkets(); if ShouldReturn then return ShouldReturn; end
          -- Custom ToD Logic
          local ShouldReturn = ToDLogic(); if ShouldReturn then return ShouldReturn; end
          -- call_action_list,name=aoe_opener,if=time<3&active_enemies>2
          if HL.CombatTime() < 3 and EnemiesCount8y > 2 then
            local ShouldReturn = AoEOpener(); if ShouldReturn then return ShouldReturn; end
          end
          -- call_action_list,name=normal_opener,if=time<4&active_enemies<3
          if HL.CombatTime() < 3 and EnemiesCount8y < 3 then
            local ShouldReturn = NormalOpener(); if ShouldReturn then return ShouldReturn; end
          end
          -- call_action_list,name=cooldowns,if=talent.storm_earth_and_fire
          if S.StormEarthAndFire:IsAvailable() then
            local ShouldReturn = Cooldowns(); if ShouldReturn then return ShouldReturn; end
          end
          -- call_action_list,name=default_aoe,if=active_enemies>=5
          if AoEON() and EnemiesCount8y >= 5 then
            local ShouldReturn = DefaultAoE(); if ShouldReturn then return ShouldReturn; end
          end
          -- call_action_list,name=default_cleave,if=active_enemies>1&(time>7|!talent.celestial_conduit)&active_enemies<5
          if AoEON() and EnemiesCount8y > 1 and (CombatTime > 7 or not S.CelestialConduit:IsAvailable()) and EnemiesCount8y < 5 then
            local ShouldReturn = DefaultCleave(); if ShouldReturn then return ShouldReturn; end
          end
          -- call_action_list,name=default_st,if=active_enemies<2
          if not AoEON() or EnemiesCount8y < 2 then
            local ShouldReturn = DefaultST(); if ShouldReturn then return ShouldReturn; end
          end
          -- call_action_list,name=fallback
          local ShouldReturn = Fallback(); if ShouldReturn then return ShouldReturn; end
          -- Manually added Pool filler
          if Cast(S.PoolEnergy) then return "Pool Energy"; end
      end
  end

  local function Init()
  end
  M.SetAPL(269, APL, Init)

  -- Touch of Death if spell is blocked
  local OldCooldownRemains
  OldCooldownRemains = HL.AddCoreOverride("Spell.CooldownUp",
          function(self)
            if MainAddon.PlayerSpecID() == 269 then
                if self == S.TouchofDeath and self:IsBlocked() then
                    return false
                end
            end
            local BaseCheck = OldCooldownRemains(self)
            return BaseCheck
          end
  , 269);

  local WWlOldIsReady
  WWlOldIsReady = HL.AddCoreOverride("Spell.IsReady",
    function(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
      if MainAddon.PlayerSpecID() == 269 then
        
        -- movement check (stand-still threshold) (only for APLcustom)
        if GetSetting("rotation_mode", "APLcustom") == "APLcustom" then
          local DPSMovingValue_check = GetSetting('DPSMovingValue_check', false)
          local DPSMovingValue_spin = GetSetting('DPSMovingValue_spin', 0.5)
          local DPSMovingValueSpells = GetSetting('DPSMovingValueSpells', {})

          if DPSMovingValue_check then
            local stillTime = Player:IsStandingStillFor()

            -- only block these spells when their checkbox is enabled
            if (self == S.FistsofFury             and DPSMovingValueSpells['fists']) or
               (self == S.InvokeXuenTheWhiteTiger and DPSMovingValueSpells['xuen']) or
               (self == S.StormEarthAndFire       and DPSMovingValueSpells['sef']) or
               (self == S.StrikeoftheWindlord     and DPSMovingValueSpells['winds']) or
               (self == S.WhirlingDragonPunch     and DPSMovingValueSpells['wdp']) then

              if stillTime <= DPSMovingValue_spin then
                return false, ("Must stand still %.1fs"):format(DPSMovingValue_spin)
              end
            end
          end
        end
  
        -- meleeratio setting (only for APLcustom)
        if GetSetting("rotation_mode", "APLcustom") == "APLcustom" then
          local meleeratioEnabled = GetSetting('meleeratio_check', true)
          local meleeratioValue = GetSetting('meleeratio_spin', 30)
          local meleeratioSpells = GetSetting('meleeratiospells', {})

          if meleeratioEnabled and BossFightRemains == 11111 then
            -- total enemies in 40y
            local enemiesAll = Player:GetEnemiesInRange(40)
            local enemiesCount = #enemiesAll
            -- total in melee (8y)
            local enemiesMeleeCount = #Player:GetEnemiesInMeleeRange(8)

            local currentRatio = 0
            if enemiesCount > 0 then
              currentRatio = (enemiesMeleeCount / enemiesCount) * 100
            end

            -- only block these spells when their checkbox is enabled
            if (self == S.FistsofFury             and meleeratioSpells['ratioFists']) or
               (self == S.InvokeXuenTheWhiteTiger and meleeratioSpells['ratioXuen']) or
               (self == S.StormEarthAndFire       and meleeratioSpells['ratioStormEarthFire']) or
               (self == S.StrikeoftheWindlord     and meleeratioSpells['ratioWindlord']) or
               (self == S.WhirlingDragonPunch     and meleeratioSpells['ratioWDP']) then

              if currentRatio <= meleeratioValue then
                return false, ("Not enough enemies in melee range (%.0f%%)"):format(currentRatio)
              end
            end
          end
        end

        -- ------------------------------------
        --        Custom Syncs start
        -- ------------------------------------
        local xuencd = S.InvokeXuenTheWhiteTiger:CooldownRemains(nil, true)
        local sefcd = S.StormEarthAndFire:CooldownRemains(nil, true)
        local sefcharges = S.StormEarthAndFire:Charges()
        local windscd = S.StrikeoftheWindlord:CooldownRemains(nil, true)

        --- Invoke Xuen The White Tiger sync
        if self == S.InvokeXuenTheWhiteTiger
        and GetSetting("rotation_mode", "APLcustom") == "APLcustom"
        then
            -- block xuen if sef is ready in 25s or less
            if sefcd > 0 and sefcd <= 25 then
                return false, "Sync: wait for Storm Earth and Fire (ready in " .. sefcd .. "s)"
            end
        end

        --- Storm Earth and Fire sync
        if self == S.StormEarthAndFire
        and GetSetting("rotation_mode", "APLcustom") == "APLcustom"
        then
            -- block SEF if we only have one charge and if Xuen is ready in less then 85s
            if sefcharges == 1 and xuencd <= 85 then
                return false, "Sync: wait for Xuen The White Tiger (ready in " .. xuencd .. "s)"
            end
        end

        -- ------------------------------------
        --        Custom Syncs end
        -- ------------------------------------
  
        -- Fists of Fury melee check
        if self == S.FistsofFury then
          if not Target:IsInMeleeRange(5) then
            return false, "Fists of Fury Out of Range"
          end
        end

  
        if self == S.TouchofDeath and Target:NPCID() == 120651 then
          return false
        end

      end
  
      local BaseCheck, Reason = WWlOldIsReady(self, TargetUnit, Range, ignoreChannel, skipCC, ignoreSettings, BypassRecovery, ignoreResources)
      return BaseCheck, Reason
    end
  , 269)  

  local WWOldSpellIsCastable
  WWOldSpellIsCastable = HL.AddCoreOverride("Spell.IsCastable",
  function (self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
      local BaseCheck, Reason = WWOldSpellIsCastable(self, ignoreSettings, ignoreMovement, bypassRecovery, ignoreChannel, ignoreResources, ignoreLinkedSpells)
      if MainAddon.PlayerSpecID() == 269 then
        if self == S.ChiBurst then
            return BaseCheck and not Player:IsCasting(self)
        else
            return BaseCheck, Reason
        end
      end
      return BaseCheck, Reason
  end
  , 269)
end